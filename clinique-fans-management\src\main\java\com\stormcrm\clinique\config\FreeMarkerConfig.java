package com.stormcrm.clinique.config;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

import freemarker.ext.jsp.TaglibFactory;

/**
 * 配置在FreeMarker模版文件中使用SpringSecurity的标签
 *
 * <AUTHOR>
 */
@Configuration
public class FreeMarkerConfig implements WebMvcConfigurer {

    private final FreeMarkerConfigurer configurer;

    public FreeMarkerConfig(FreeMarkerConfigurer configurer) {
        this.configurer = configurer;
    }

    @PostConstruct
    public void freeMarkerConfigurer() {
        List<String> tag = new ArrayList<>();
        tag.add("/lib/security.tld");
        TaglibFactory taglibFactory = configurer.getTaglibFactory();
        taglibFactory.setClasspathTlds(tag);
        if (taglibFactory.getObjectWrapper() == null) {
            taglibFactory.setObjectWrapper(configurer.getConfiguration().getObjectWrapper());
        }
    }

}
