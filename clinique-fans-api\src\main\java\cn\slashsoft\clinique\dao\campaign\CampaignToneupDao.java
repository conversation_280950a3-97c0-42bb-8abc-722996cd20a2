package cn.slashsoft.clinique.dao.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignToneupDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignToneupLog;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

/**
 * 黄油变粉安瓶级保湿补光申领
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignToneupDao {

    /**
     * 保存日志
     *
     * @param campaignToneupLog 日志
     */
    @Insert("INSERT INTO `campaign_toneup_log`(" +
            "   `type`," +
            "   `unique_id`, " +
            "   `source`," +
            "   `page`" +
            ") " +
            "VALUES (" +
            "   #{type}, " +
            "   #{uniqueId}, " +
            "   #{source}," +
            "   #{page}" +
            ")")
    void insertLog(CampaignToneupLog campaignToneupLog);

    /**
     * 获取是否已经审领
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    @Select("SELECT " +
            "   COUNT(*) " +
            "FROM " +
            "   `campaign_toneup_detail` " +
            "WHERE " +
            "   `phone_number`=#{phoneNumber} " +
            "LIMIT 1")
    int hasDetail(String phoneNumber);

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    @Select("SELECT " +
            "   `name`, `phone_number`, `city`, `store`, `follow` " +
            "FROM " +
            "   `campaign_toneup_detail` " +
            "WHERE " +
            "   `phone_number`=#{phoneNumber} " +
            "LIMIT 1")
    CampaignToneupDetail getDetail(String phoneNumber);

    /**
     * 获取手机号码
     *
     * @param unionid 开放平台唯一编号
     * @return 手机号码
     */
    @Select("SELECT " +
            "    decryptPhone(`c`.`phone_number`) phone_number " +
            "FROM " +
            "   `wechat` `w` " +
            "       INNER JOIN " +
            "   `customer` `c` " +
            "       ON `w`.`customer_id`=`c`.`id` " +
            "WHERE " +
            "   `w`.`unionid`=#{unionid}")
    String getPhoneNumberByUnionid(String unionid);

    /**
     * 扣库存
     *
     * @param campaignToneupDetail 申领信息
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_toneup_stock` " +
            "SET " +
            "   `stock`=`stock`-1, " +
            "   `total`=`total`+1 " +
            "WHERE " +
            "   `city`=#{city} " +
            "   AND `store`=#{store} " +
            "   AND `stock`>0")
    int updateStock(CampaignToneupDetail campaignToneupDetail);

    /**
     * 定入申领信息
     *
     * @param campaignToneupDetail 申领信息
     */
    @Insert("INSERT INTO `campaign_toneup_detail`(" +
            "   `type`, " +
            "   `unique_id`, " +
            "   `name`, " +
            "   `phone_number`, " +
            "   `city`, " +
            "   `store`, " +
            "   `source` " +
            ") " +
            "VALUES (" +
            "   #{type}, " +
            "   #{uniqueId}, " +
            "   #{name}, " +
            "   #{phoneNumber}, " +
            "   #{city}, " +
            "   #{store}, " +
            "   #{source} " +
            ")")
    void insetDetail(CampaignToneupDetail campaignToneupDetail);

    /**
     * 更新是否关注
     *
     * @param phoneNumber 手机号码
     * @param isFollow    是否关注
     */
    @Update("UPDATE " +
            "   `campaign_toneup_detail` " +
            "SET " +
            "   `follow`=#{isFollow} " +
            "WHERE " +
            "   `phone_number`=#{phoneNumber}")
    void setFollow(@Param("phoneNumber") String phoneNumber, @Param("isFollow") boolean isFollow);


}
