package com.stormcrm.clinique.dao.oauth;

import com.stormcrm.clinique.oauth.domain.OauthUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
public interface OauthUserDao {

    /**
     * 获取管理员信息
     *
     * @param username 用户名
     * @return 管理员信息
     */
    @Select("SELECT " +
            "   `id`, " +
            "   `username`, " +
            "   `password`, " +
            "   `phone`, " +
            "   `locked`, " +
            "   `status` " +
            "FROM " +
            "   `oauth_user` " +
            "WHERE " +
            "   `status`=1 AND " +
            "   `username`=#{username} " +
            "LIMIT 1")
    OauthUser getByUsername(String username);


    /**
     * 获取管理员信息
     *
     * @param number 手机号码
     * @return 管理员信息
     */
    @Select("SELECT " +
            "   `id`, " +
            "   `username`, " +
            "   `password`, " +
            "   `phone`, " +
            "   `locked`, " +
            "   `status` " +
            "FROM " +
            "   `oauth_user` " +
            "WHERE " +
            "   `status`=1 AND " +
            "   `phone`=#{number} " +
            "LIMIT 1")
    OauthUser getByPhone(String number);

    @Select("SELECT EXISTS(SELECT 1 FROM oauth_user WHERE phone=#{number})")
    boolean checkPhoneExists(@Param("number") String number);

}
