package com.stormcrm.clinique.dao.campaign;

import com.stormcrm.clinique.domain.campaign.CampaignH5;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * campaign H5
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignH5Dao {
	
    /**
     * 获取活动
     *
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `campaign_h5` " +
            "WHERE " +
            "  `code` = #{campaignCode} " +
            " AND status = 1 " +
            " and start_time <= now() and end_time >= now() "  +
            "LIMIT 1")
    CampaignH5 getValidCampaign(String campaignCode);


}
