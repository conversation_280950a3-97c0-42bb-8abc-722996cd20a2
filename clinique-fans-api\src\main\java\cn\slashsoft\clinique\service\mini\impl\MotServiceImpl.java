package cn.slashsoft.clinique.service.mini.impl;

import java.util.Date;

import cn.slashsoft.clinique.dao.mini.CustomerDao;
import cn.slashsoft.clinique.service.mini.PointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import cn.slashsoft.clinique.dao.mini.StoreDao;
import cn.slashsoft.clinique.dao.mini.WechatDao;
import cn.slashsoft.clinique.service.mini.MotService;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.StringUtil;

/**
 * 发送MOT
 *
 * <AUTHOR>
 */
@Service
public class MotServiceImpl implements MotService {

    @Value("${wechat.template.get-points}")
    private String templateGetPoints;

    @Value("${wechat.template.get-coupon}")
    private String templateGetCoupon;    


    @Value("${wechat.template.store-exchange-express-success}")
    private String templateStoreExchangeExpressSuccess;

    @Value("${wechat.template.ebci-sign-success}")
    private String templateEbciSignSuccess;

    @Value("${wechat.template.ebci-sign-apply-success}")
    private String templateEbciSignApplySuccess;

    @Value("${wechat.template.campaign-enroll-success}")
    private String templateCampaignEnrollSuccess;

    @Value("${wechat.template.testimony-draw-blindbox-success}")
    private String testimonyDrawBlindboxSuccess;

    private final WechatDao wechatDao;
    private final CustomerDao customerDao;
    private final StoreDao storeDao;
    private final PointService pointService;
    private final OutsideService outsideService;

    public MotServiceImpl(WechatDao wechatDao, StoreDao storeDao, OutsideService outsideService, PointService pointService, CustomerDao customerDao) {
        this.wechatDao = wechatDao;
        this.storeDao = storeDao;
        this.outsideService = outsideService;
        this.pointService = pointService;
        this.customerDao = customerDao;
    }

    /**
     * 获得积分
     *
     * @param customerId 顾客编号
     * @param points     获得的积分
     * @param reason 原因
     */
    @Override
    public void getPoints(long customerId, int points, String reason) {

        String openid = wechatDao.getOfficialOpenidByCustomerId(customerId);
        if (StringUtil.isNullOrEmpty(openid)) {
            openid = outsideService.getOpenidByUnionid(customerId);
            if (StringUtil.isNullOrEmpty(openid)) {
                return;
            }
        }

        int remainingPointTotal = pointService.getRemainingPointTotal(customerId);

        String data = "{" +
                "\"first\":{\"value\":\"\"}," +
                "\"keyword1\":{\"value\":\"" + DateUtil.parseString(new Date()) + "\"}," +
                "\"keyword2\":{\"value\":\"" + points + " C币\"}," +
                "\"keyword3\":{\"value\":\"" + reason + "\"}," +
                "\"keyword4\":{\"value\":\"" + remainingPointTotal + " C币\"}," +
                "\"remark\":{\"value\":\"\"}" +
                "}";

        outsideService.templateSendWithMp((short) 7, openid, templateGetPoints, data, "wx93d4fc2221a8408d", "/pages/pointDetail/pointDetail?source=mot");
    }

    /**
     * 获取礼券
     *
     * @param customerId 顾客编号
     * @param couponName 礼券名称
     * @param source 来源
     */
    @Override
    public void getCoupon(long customerId, String couponName, String source) {

        String openid = wechatDao.getOfficialOpenidByCustomerId(customerId);
        if (StringUtil.isNullOrEmpty(openid)) {
            openid = outsideService.getOpenidByUnionid(customerId);
            if (StringUtil.isNullOrEmpty(openid)) {
                return;
            }
        }

        String name = customerDao.getNameById(customerId);

        String data = "{" +
                "\"first\":{\"value\":\"亲爱的C粉，您的礼品卡券已到账\"}," +
                "\"keyword1\":{\"value\":\"" + name + "\"}," +
                "\"keyword2\":{\"value\":\"" + couponName + "\"}," +
                "\"keyword3\":{\"value\":\"" + DateUtil.parseString(new Date()) + "\"}," +
                "\"remark\":{\"value\":\"请前往卡券中心完成您的领奖信息，逾期视作放弃领取。\"}" +
                "}";

        outsideService.templateSendWithMp((short) 8, openid, templateGetCoupon, data, "wxc0099da8e25e2948", "/pages/coupon/coupon?source=" + source);

    }

    /**
     * 到店取货类订单兑换成功发送模版消息
     *
     * @param customerId  顾客编号
     * @param giftId      礼品编号
     * @param areaPlaceId 门店编号
     */
    @Override
    public void exchangePickupSend(long customerId, long giftId, long areaPlaceId) {

        String openid = wechatDao.getOfficialOpenidByCustomerId(customerId);
        if (StringUtil.isNullOrEmpty(openid)) {
            openid = outsideService.getOpenidByUnionid(customerId);
            if (StringUtil.isNullOrEmpty(openid)) {
                return;
            }
        }

        String giftName = storeDao.getStoreGiftNameById(giftId);
        String areaPlaceName = storeDao.getStoreAreaPlaceNameById(areaPlaceId);

        String data = "{" +
                "\"first\":{\"value\":\"礼品申领成功通知\"}," +
                "\"keyword1\":{\"value\":\"" + giftName + "\"}," +
                "\"keyword2\":{\"value\":\"" + areaPlaceName + "\"}," +
                "\"keyword3\":{\"value\":\"待通知\"}," +
                "\"remark\":{\"value\":\"您已成功兑换C粉圈好礼，受疫情影响所有礼品将延迟到柜时间。\\n立即前往“倩碧会员中心”小程序中“我的礼券”查看礼品状态，届时请凭礼品卡券至柜台领取哦！点击前往>>\"}" +
                "}";

       outsideService.templateSendWithMp((short)1 , openid, testimonyDrawBlindboxSuccess, data, "wxc0099da8e25e2948", "pages/coupon/coupon?source=cfans_sign");
  
    }



    /**
     * 到店取货类订单兑换成功发送模版消息
     *
     * @param customerId 顾客编号
     * @param giftId     礼品编号
     */
    @Override
    public void exchangeExpress(long customerId, long giftId) {

        String openid = wechatDao.getOfficialOpenidByCustomerId(customerId);
        if (StringUtil.isNullOrEmpty(openid)) {
            openid = outsideService.getOpenidByUnionid(customerId);
            if (StringUtil.isNullOrEmpty(openid)) {
                return;
            }
        }

        String giftName = storeDao.getStoreGiftNameById(giftId);

        String data = "{" +
                "\"first\":{\"value\":\"礼品申领成功通知\"}," +
                "\"keyword1\":{\"value\":\"" + giftName + "\"}," +
                "\"keyword2\":{\"value\":\"快递寄送\"}," +
                "\"keyword3\":{\"value\":\"待通知\"}," +
                "\"remark\":{\"value\":\"您已成功兑换C粉圈好礼，受疫情影响所有礼品将延迟发货时间，届时注意查收发货通知模板消息哦！\"}" +
                "}";

        outsideService.templateSend((short) 2, openid, templateStoreExchangeExpressSuccess, data);
    }

    /**
     * EBCI打卡成功
     *
     * @param customerId 顾客编号
     */
    @Override
    public void ebciSign(long customerId) {

        String openid = wechatDao.getOfficialOpenidByCustomerId(customerId);
        if (StringUtil.isNullOrEmpty(openid)) {
            openid = outsideService.getOpenidByUnionid(customerId);
            if (StringUtil.isNullOrEmpty(openid)) {
                return;
            }
        }

        String data = "{" +
                "\"first\":{\"value\":\"签到成功通知\"}," +
                "\"keyword1\":{\"value\":\"" + DateUtil.parseString(new Date()) + "\"}," +
                "\"keyword2\":{\"value\":\"7天\"}," +
                "\"keyword3\":{\"value\":\"100\"}," +
                "\"keyword4\":{\"value\":\"美白肤诊室-7日美白打卡挑战赛\"}," +
                "\"remark\":{\"value\":\"恭喜您已成功完成7日美白大作战的打卡任务！你的活动奖励100积分已到账！\"}" +
                "}";

        outsideService.templateSendWithMp((short) 3, openid, templateEbciSignSuccess, data, "wx93d4fc2221a8408d", "pages/campaignEbciSign/campaignEbciSign?source=mot");
    }

    /**
     * EBCI打卡5天成功
     *
     * @param customerId 顾客编号
     */
    @Override
    public void ebciSign5(long customerId) {
        String openid = wechatDao.getOfficialOpenidByCustomerId(customerId);
        if (StringUtil.isNullOrEmpty(openid)) {
            openid = outsideService.getOpenidByUnionid(customerId);
            if (StringUtil.isNullOrEmpty(openid)) {
                return;
            }
        }

        String data = "{" +
                "\"first\":{\"value\":\"活动报名成功通知\"}," +
                "\"keyword1\":{\"value\":\"即日 - 4月30日\"}," +
                "\"keyword2\":{\"value\":\"只差2天你就成功啦，我们已经准备好了100积分和302美白镭射瓶14日体验装，等你来拿！\"}," +
                "\"remark\":{\"value\":\"\"}" +
                "}";

        outsideService.templateSendWithMp((short) 3, openid, templateCampaignEnrollSuccess, data, "wx93d4fc2221a8408d", "pages/campaignEbciSign/campaignEbciSign?source=mot");
    }

    /**
     * EBCI打卡成功，申领成功
     *
     * @param openid 公众号OpenId
     * @param phoneNumber 手机号码
     * @param store  门店
     */
    @Override
    public void ebciSignApply(String openid, String phoneNumber, String store) {

        String data = "{" +
                "\"first\":{\"value\":\"礼品申领成功通知\"}," +
                "\"keyword1\":{\"value\":\"14日新品302美白镭射瓶试用装\"}," +
                "\"keyword2\":{\"value\":\"" + store + "\"}," +
                "\"keyword3\":{\"value\":\"即日 - " + DateUtil.parseDayString(DateUtil.addDay(new Date(), 10)) + "\"}," +
                "\"remark\":{\"value\":\"你的新品14日体验装申领成功！请记得在" + DateUtil.parseDayString(DateUtil.addDay(new Date(), 10)) + "前领取~ 4月30日起将开启美白招募官活动，响应招募让我们一起见证你的美白蜕变之旅，更有重磅福利等你来拿！\"}" +
                "}";

        outsideService.templateSendWithMp((short) 4, openid, templateEbciSignApplySuccess, data, "wx93d4fc2221a8408d", "pages/campaignEbciSignApply/campaignEbciSignApply?source=mot");
    }

    /**
     * 证言活动 领取成功
     *
     * @param customerId
     * @param pwd   盲盒密语
     * @param store  门店
     */
    @Override
    public void testimonyDrawSuccess(Long customerId, String pwd, String store) {
        String openid = wechatDao.getOfficialOpenidByCustomerId(customerId);
        if (StringUtil.isNullOrEmpty(openid)) {
            openid = outsideService.getOpenidByUnionid(customerId);
            if (StringUtil.isNullOrEmpty(openid)) {
                return;
            }
        }
        String data = "{" +
                "\"first\":{\"value\":\"礼品申领成功通知\"}," +
                "\"keyword1\":{\"value\":\"亲爱的C粉，感谢参与#万人美白证言墙#活动，您的盲盒已在专柜等待领取\"}," +
                "\"keyword2\":{\"value\":\"礼品名称：C粉盲盒（领取密语："+ pwd + "）\"}," +
                "\"keyword3\":{\"value\":\"领取柜台：[" + store + "]\"}," +
                "\"keyword4\":{\"value\":\"领取时间：2020/10/31前\"}," +
                "\"remark\":{\"value\":\"请于2020/10/31前凭盲盒密语【"+pwd+"】至上述专柜领取您的盲盒，逾期视作放弃。赶快前往领取吧！点此核销>>\"}" +
                "}";
        outsideService.templateSendWithMp((short) 4, openid, testimonyDrawBlindboxSuccess, data, "wx93d4fc2221a8408d", "testimony/testimony?source=mot&wirteoff=yes");
    }

    /**
     * 三日打卡，完成发券提醒
     *
     * @param customerId
     */
    @Override
    public void day3TaskFinishNotice(Long customerId) {
        String openid = wechatDao.getOfficialOpenidByCustomerId(customerId);
        if (StringUtil.isNullOrEmpty(openid)) {
            openid = outsideService.getOpenidByUnionid(customerId);
            if (StringUtil.isNullOrEmpty(openid)) {
                return;
            }
        }
        
    	 String data = "{" +
                 "\"first\":{\"value\":\"恭喜打卡成功！记得在7日内到倩碧柜台，领取你的惊喜礼品！\"}," +
                 "\"keyword1\":{\"value\":\"C粉圈3日打卡礼\"}," +
                 "\"keyword2\":{\"value\":\"待选择\"}," +
                 "\"keyword3\":{\"value\":\""+DateUtil.parseCnDayString(new Date())+"\"}," +
                 "\"remark\":{\"value\":\"点击前往“会员中心-我的礼券”查看你的礼品！>>\"}" +
                 "}";

        outsideService.templateSendWithMp((short)13 , openid, testimonyDrawBlindboxSuccess, data, "wxc0099da8e25e2948", "pages/coupon/coupon?source=cfans_sign");
    }
}
