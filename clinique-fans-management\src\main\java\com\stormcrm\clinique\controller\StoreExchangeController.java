package com.stormcrm.clinique.controller;

import com.stormcrm.clinique.service.StoreExchangeService;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 商城兑礼管理
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("store/exchange")
public class StoreExchangeController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    private final StoreExchangeService storeExchangeService;

    public StoreExchangeController(StoreExchangeService storeExchangeService) {
        this.storeExchangeService = storeExchangeService;
    }

    /**
     * 页面模版
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('STORE_EXCHANGE')")
    @GetMapping("")
    public String index(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/store/exchange/index";
    }









}
