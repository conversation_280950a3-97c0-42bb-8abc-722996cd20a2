package com.stormcrm.clinique.dao;


import com.stormcrm.clinique.domain.*;

import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/* 笔记
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface NoteDao {
    /**
     * 保存笔记
     *
     * @param note 笔记
     */
    @Insert("INSERT INTO `note`(" +
            "   `customer_id`, " +
            "   `title`, " +
            "   `content`, " +
            "   `status`, " +
            "   `updateTime`, " +
            "   `createTime` " +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{title}, " +
            "   #{content}, " +
            "   #{status}, " +
            "   #{updateTime}, " +
            "   #{createTime} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertNote(Note note);

    /**
     * 删除笔记
     *
     * @param id 笔记id
     */
    @Delete("DELETE FROM `note`" +
            " WHERE " +
            " `id`=#{id} "
    )
    int delNote(long id);

    /**
     * 保存笔记图片
     *
     * @param photo 笔记的图片
     */
    @Insert("INSERT INTO `note_photo` (" +
            "   `note_id`, " +
            "   `photo`, " +
            "   `status`, " +
            "   `createTime` " +
            " ) " +
            "VALUES (" +
            "   #{noteId}, " +
            "   #{photo}, " +
            "   #{status}, " +
            "   #{createTime} " +
            " )")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertNotePhoto(NotePhoto photo);

    /**
     * 保存图片标签
     *
     * @param photoTag 图片标签
     */
    @Insert("INSERT INTO `note_photo_tag`(" +
            "   `note_id`, " +
            "   `photo_id`, " +
            "   `tag_id`, " +
            "   `tag_title`, " +
            "   `tag_type`, " +
            "   `position`, " +
            "   `status` " +
//            "   `createTime` " +
            ") " +
            "VALUES (" +
            "   #{noteId}, " +
            "   #{photoId}, " +
            "   #{tagId}, " +
            "   #{title}, " +
            "   #{type}, " +
            "   `position`, " +
            "   #{status} " +
//            "   #{createTime} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertNotePhotoTag(NotePhotoTag photoTag);

    /**
     * 保存笔记评论
     *
     * @param noteDiscuss 笔记评论
     */
    @Insert("INSERT INTO `note_discuss`(" +
            "   `note_id`, " +
            "   `customer_id`, " +
            "   `content`, " +
            "   `status`, " +
            "   `createTime` " +
            ") " +
            "VALUES (" +
            "   #{noteId}, " +
            "   #{customerId}, " +
            "   #{content}, " +
            "   #{status}, " +
            "   #{createTime} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertDiscuss(NoteDiscuss noteDiscuss);

    /**
     * 保存阅读笔记日志
     *
     * @param log 日志
     */
    @Insert("INSERT INTO `note_read_log`(" +
            "   `note_id`, " +
            "   `customer_id`, " +
            "   `createTime` " +
            ") " +
            "VALUES (" +
            "   #{noteId}, " +
            "   #{customerId}, " +
            "   #{createTime} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertReadLog(NoteReadLog log);

    /**
     * 保存拔草笔记日志
     *
     * @param log 拔草日志
     */
    @Insert("INSERT INTO `note_like_log`(" +
            "   `note_id`, " +
            "   `customer_id`, " +
            "   `createTime` " +
            ") " +
            "VALUES (" +
            "   #{noteId}, " +
            "   #{customerId}, " +
            "   #{createTime} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertLikeLog(NoteLikeLog log);

    /**
     * 保存分享笔记日志
     *
     * @param log 分享日志
     */
    @Insert("INSERT INTO `note_share_log`(" +
            "   `note_id`, " +
            "   `share_customer_id`, " +
            "   `customer_id`, " +
            "   `createTime` " +
            ") " +
            "VALUES (" +
            "   #{noteId}, " +
            "   #{shareCustomerId}, " +
            "   #{customerId}, " +
            "   #{createTime} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertShareLog(NoteShareLog log);

    /**
     * 笔记是否拔草
     *
     * @param noteId 笔记id
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `note_like` " +
            "WHERE " +
            "   `customer_id`=#{customerId} AND " +
            "   `note_id`=#{noteId} " +
            "LIMIT 1")
    NoteLikeLog liked(Long noteId, Long customerId);

    /**
     * 添加标签
     *
     * @param noteTag 笔记标签
     */
    @Insert("INSERT INTO `note_tag`(" +
            "   `type`, " +
            "   `image`, " +
            "   `title`, " +
            "   `top`, " +
            "   `top_index`, " +
            "   `top_search`, " +
            "   `point_type_id`, " +
            "   `createTime` " +
            ") " +
            "VALUES (" +
            "   #{type}, " +
            "   #{image}, " +
            "   #{title}, " +
            "   #{top}, " +
            "   #{topIndex}, " +
            "   #{topSearch}, " +
            "   #{pointTypeId}, " +
            "   now() " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertNoteTag(NoteTag noteTag);

    /**
     * 修改标签
     *
     * @param noteTag 笔记标签
     */
    @Update("UPDATE `note_tag` set " +
            "   `type` = #{type} , " +
            "   `image` = #{image} , " +
            "   `title`= #{title} , " +
            "   `updateTime`= now() , " +
            "   `top` = #{top} ," +
            "   `top_index` = #{topIndex} ," +
            "   `top_search` = #{topSearch} ," +
            "   `point_type_id` = #{pointTypeId} " +
            "  WHERE id=#{id} ")
    int updateNoteTag(NoteTag noteTag);

    /**
     * 修改标签
     *
     * @param noteTag 笔记标签
     */
    @Update("UPDATE `note_tag` set " +
            "   `type` = #{type} , " +
            "   `title`= #{title} , " +
            "   `updateTime`= now() , " +
            "   `top` = #{top} ," +
            "   `top_index` = #{topIndex} ," +
            "   `top_search` = #{topSearch} ," +
            "   `point_type_id` = #{pointTypeId} " +
            "  WHERE id=#{id} ")
    int updateNoteTagWithoutImage(NoteTag noteTag);

    /**
     * 删除标签
     *
     * @param id 标签id
     */
    @Delete("DELETE FROM `note_tag` " +
            "  WHERE id=#{id} ")
    int deleteNoteTag(Long id);

    /**
     * 查询所有 笔记 -分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 活动列表
     */
    @SelectProvider(type = NoteProvider.class, method = "getPage")
    List<Note> getPage(
            @Param("pageIndex") int pageIndex,
            @Param("pageSize") int pageSize,
            @Param("generalSearch") String generalSearch,
            @Param("status") Short status
    );

    /**
     * 查询所有 笔记 带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    @SelectProvider(type = NoteProvider.class, method = "getPageCount")
    int getPageCount(
            @Param("generalSearch") String generalSearch,
            @Param("status") Short status
    );

    /**
     * 查询所有 标签 -分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param top           置顶
     * @return 活动列表
     */
    @SelectProvider(type = NoteProvider.class, method = "getTagPage")
    List<NoteTag> getTagPage(
            @Param("pageIndex") int pageIndex,
            @Param("pageSize") int pageSize,
            @Param("generalSearch") String generalSearch,
            @Param("top") Short top
    );

    /**
     * 查询所有 标签 带分页的记录数
     *
     * @param generalSearch 搜索
     * @param top           状态
     * @return 记录数
     */
    @SelectProvider(type = NoteProvider.class, method = "getTagPageCount")
    int getTagPageCount(
            @Param("generalSearch") String generalSearch,
            @Param("top") Short top
    );

    /**
     * 获取标签
     *
     * @param id 标签id
     */
    @Select(" SELECT nt.*,\n" +
            " pt.name,pt.reward_point,\n" +
            " pt.reward_active_start,pt.reward_active_end,pt.reward_max,pt.reward_max_forever\n" +
            " FROM note_tag nt \n" +
            " LEFT JOIN point_type pt\n" +
            " ON nt.point_type_id = pt.id\n" +
            " where nt.id=#{id}")
    NoteTag getTag(Long id);

    /**
     * 获取用户笔记评论列表
     *
     * @param customerId 客户id
     * @param start      起始
     */
    @Select("SELECT " +
            "   `note_id`, `customer_id` " +
            "FROM " +
            "   `note_discuss` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT #{start}, 30")
    ArrayList<NoteDiscuss> getCustomerDiscussList(Long customerId, int start);

    /**
     * 获取笔记列表
     *
     * @param start 起始
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `note` " +
            "WHERE " +
            "   `status`= 1 " +
            "LIMIT #{start}, 30")
    ArrayList<Note> getNoteList(int start);

    /**
     * 获取用户笔记列表
     *
     * @param customerId 客户id
     * @param start      起始
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `note` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT #{start}, 30")
    ArrayList<Note> getCustomerNoteList(Long customerId, int start);

    /**
     * 获取笔记详情
     *
     * @param id 笔记id
     */
    @Select("SELECT " +
            "   a.*, b.`photo` `coverPhoto`, " +
            "   `w`.`nick_name` `ownerName`, " +
            "   `w`.`avatar_url` `avatarUrl` , " +
            "   `w`.`wechat_mini_openid` `wechatMiniOpenid` , " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id ) `likeCount`, " +
            " ( SELECT count(`id`) from `note_read_log` WHERE `note_id`= a.id ) `readCount`  " +
            "FROM " +
            "   `note` a " +
            " LEFT JOIN " +
            "    `note_photo` b " +
            " ON a.id=b.note_id " +
            "       LEFT JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "   a.`id`=#{id}  " +
            "LIMIT 1")
    Note getNote(Long id);

    /**
     * 获取单条笔记
     *
     * @param id 笔记id
     */
    @Select("SELECT " +
            "   a.*, " +
            "   `w`.`unionid` `unionid`,  " +
            "   `w`.`wechat_mini_openid` `wechatMiniOpenid`,  " +
            "   `w`.`wechat_official_openid` `wechatOfficialOpenid`  " +
            "FROM " +
            "   `note` a " +
            "       LEFT JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "   a.`id`=#{id}  " +
            "LIMIT 1")
    Note getNoteById(Long id);

    /**
     * 携带 查询携带奖励标签的 记录
     * <p>
     * 未 得到CB类型为：   CBTypeEnum （point_type） 24
     */
    @Select("select n.* \n" +
            "from note n\n" +
            "left join note_photo_tag npt\n" +
            "on n.id=npt.note_id\n" +
            "where npt.tag_id=24 and n.status>=2 and date(n.createTime) >= '2020-10-28' and date(n.createTime) <= '2020-11-15';")
    List<Note> getNotesLossAddCBByNoteTag();

    /**
     * 审核成功
     *
     * @param id 编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `note` " +
            "SET " +
            "   `status`=1 " +
            "WHERE " +
            "   `id`=#{id} " +
            "   AND (`status`=2 OR `status`=0)")
    int accept(long id);

    /**
     * 审核成功
     *
     * @param id 编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `note` " +
            "SET " +
            "   `status`=#{status} " +
            "WHERE " +
            "   `id`=#{id} ")
    int acceptByStatus(long id, int status);

    /**
     * 审核成功
     *
     * @param id 编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_2408_note` " +
            "SET " +
            "   `status`=#{status} " +
            "WHERE " +
            "   `note_id`=#{noteId} AND campaign_flag = #{campaignCode} ")
    int acceptCampByStatus(long noteId, int status,String campaignCode);

    /**
     * 审核失败
     *
     * @param id 编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `note` " +
            "SET " +
            "   `status`=1 " +
            "WHERE " +
            "   `id`=#{Id} ")
    int reject(long id);

    /**
     * 审核失败
     *
     * @param id 编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_2408_note` " +
            "SET " +
            "   `status`=1 " +
            "WHERE " +
            "   `note_id`=#{Id} AND campaign_flag = #{campaignCode} " +
            "")
    int rejectCamp(long id,String campaignCode);


    /**
     * 获取笔记图片
     *
     * @param noteId 笔记id
     */
    @Select("SELECT " +
            "  * " +
            "FROM " +
            "   `note_photo` " +
            "WHERE " +
            "   `note_id`=#{noteId} "
    )
    ArrayList<NotePhoto> getPhotos(Long noteId);


    /**
     * 查询一个 note 中关联的 tag
     *
     * @param noteId note id
     */
    @Select("SELECT npt.* ,nt.point_type_id\n" +
            "FROM `note_photo_tag` as npt\n" +
            "LEFT JOIN note_tag as nt\n" +
            "ON npt.tag_id=nt.id\n" +
            "WHERE npt.`note_id`=#{noteId} AND npt.`tag_id`<>0 ")
    List<NotePhotoTag> getNoteTags(Long noteId);

    /**
     * 查询一个 note 中关联的 tag
     *
     * @param noteId note id
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `note_photo_tag` " +
            "WHERE " +
            "   `note_id`=#{noteId} AND " +
            "   `tag_id`=#{tagId} " +
            "LIMIT 1")
    NotePhotoTag getNoteTag(Long noteId, Long tagId);

    /**
     * 查询所有 笔记 图 带分页的记录数
     *
     * @param noteId 笔记id
     * @return 记录数
     */
    @SelectProvider(type = NoteProvider.class, method = "getPhotoPage")
    List<NotePhoto> getPhotoPage(
            @Param("pageIndex") int pageIndex,
            @Param("pageSize") int pageSize,
            @Param("noteId") Long noteId
    );

    /**
     * 查询所有 笔记 图 带分页的记录数
     *
     * @param noteId 笔记id
     * @return 记录数
     */
    @SelectProvider(type = NoteProvider.class, method = "getPhotoPageCount")
    int getPhotoPageCount(
            @Param("noteId") Long noteId
    );

    /**
     * 删除图片
     *
     * @param id 图片id
     */
    @Delete("DELETE FROM `note_photo` " +
            "  WHERE id=#{id} ")
    int deletePhoto(long id);

    /**
     * 获取 图片
     *
     * @param id 图片id
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `note_photo`"
            + " where id= #{id} ")
    NotePhoto getPhoto(long id);

    /**
     * 获取笔记图片 的标签
     *
     * @param photoId 图片id
     */
    @Select("SELECT " +
            "   a.*," +
            "   `a`.`tag_title` `title`, " +
            "   `a`.`tag_type` `type` " +
            "FROM " +
            "   `note_photo_tag` a " +
            "WHERE " +
            "   a.`photo_id`=#{photoId} AND" +
            "   a.`note_id`=#{noteId}"
    )
    List<NotePhotoTag> getPhotoTags(Long noteId, Long photoId);

    /**
     * 笔记管理 评论分页查询
     *
     * @param pageIndex 起始页
     * @param pageSize  分页数
     * @param noteId    笔记id
     * @return 记录
     */
    @SelectProvider(type = NoteProvider.class, method = "getDiscussPage")
    List<NoteDiscuss> getDiscussPage(
            @Param("pageIndex") int pageIndex,
            @Param("pageSize") int pageSize,
            @Param("generalSearch") String generalSearch,
            @Param("status") Short status,
            @Param("noteId") Long noteId
    );

    /**
     * @param generalSearch 查询条件
     * @param status        状态
     * @param noteId        笔记id
     * @return amount
     */
    @SelectProvider(type = NoteProvider.class, method = "getDiscussPageCount")
    int getDiscussPageCount(
            @Param("generalSearch") String generalSearch,
            @Param("status") Short status,
            @Param("noteId") Long noteId);


    /**
     * @param noteDiscussId 评论id
     * @return 评论记录
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `note_discuss` " +
            "WHERE " +
            "   `id`=#{noteDiscussId} ")
    NoteDiscuss getDiscussById(@Param("noteDiscussId") long noteDiscussId);

    /**
     * 审核成功
     *
     * @param noteDiscussId 编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `note_discuss` " +
            "SET " +
            "   `status`=1 " +
            "WHERE " +
            "   `id`=#{noteDiscussId} " +
            "   AND (`status`=2 OR `status`=0)")
    int acceptDiscuss(long noteDiscussId);

    @Update("UPDATE " +
            "   `campaign_2408_note_discuss` " +
            "SET " +
            "   `status`=1 " +
            "WHERE " +
            "   `discuss_id`=#{noteDiscussId} and campaign_flag = #{campCode} " +
            "   AND (`status`=2 OR `status`=0)")
    int acceptCampDiscuss(long noteDiscussId,String campCode);

    /**
     * 审核失败
     *
     * @param noteDiscussId 编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `note_discuss` " +
            "SET " +
            "   `status`=2 " +
            "WHERE " +
            "   `id`=#{noteDiscussId} ")
    int rejectDiscuss(long noteDiscussId);

    /**
     * 审核失败
     *
     * @param noteDiscussId 编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_2408_note_discuss` " +
            "SET " +
            "   `status`=2 " +
            "WHERE " +
            "   `discuss_id`=#{noteDiscussId} and campaign_flag = #{campCode} ")
    int rejectCampDiscuss(long noteDiscussId,String campCode);


    /**
     * 查询一个 note 中关联的 tag
     * getPhotoTags   getNoteTags
     *
     * @param noteId note id
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `note_photo_tag` " +
            "WHERE " +
            "   `note_id`=#{noteId} AND " +
            "   `tag_id`!=0 ")
    ArrayList<NoteTag> getTagListByNoteId(Long noteId);

    /**
     * 获取笔记的全部评论 用在详细页面
     *
     * @param noteId 笔记id
     * @return 评论记录
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `note_discuss` " +
            "WHERE " +
            "   `note_id`=#{noteId} ")
    ArrayList<NoteDiscuss> getDiscussByNoteId(Long noteId);

    /**
     * 删除笔记 評論
     *
     * @param noteDiscussId 笔记評論id
     */
    @Delete("DELETE FROM `note_discuss`" +
            " WHERE " +
            " `id`=#{id} "
    )
    int deleteDiscuss(long noteDiscussId);

    /**
     * 删除笔记 評論
     */
    @Delete("DELETE FROM `campaign_2408_note_discuss`" +
            " WHERE " +
            " `discuss_id`=#{noteId}  AND campaign_flag = #{campaignCode} "
    )
    int deleteCamDiscuss(long noteId,String campaignCode);

}
