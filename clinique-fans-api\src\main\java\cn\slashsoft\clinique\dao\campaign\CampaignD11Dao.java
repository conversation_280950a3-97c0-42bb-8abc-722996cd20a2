package cn.slashsoft.clinique.dao.campaign;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

@Repository
@Mapper
public interface CampaignD11Dao {

    /**
     * 通过code查询客户姓名
     *
     * @param code SMS下发的唯一签到码
     * @return 客户姓名
     */
    @Select("SELECT " +
            "   customer_name " +
            "FROM " +
            "   `double_eleven_customer_signin` " +
            "WHERE " +
            "   `code`=#{code} " +
            "limit 1")
    String getCustomerNameByCode(String code);

    /**
     * 更新记录用，也可能不用，直接用code更新，就怕code有重复的情况。
     *
     * @param code SMS下发的唯一签到码
     * @return id 数据库记录中的id
     */
    @Select("SELECT " +
            "   id " +
            "FROM " +
            "   `double_eleven_customer_signin` " +
            "WHERE " +
            "   `code`=#{code} " +
            "limit 1")
    int getRowIdByCode(String code);

    /**
     * 签到
     *
     * @param code 顾客手机接收的code
     */
    @Update("UPDATE " +
            "   `double_eleven_customer_signin` " +
            "SET " +
            "   `status`=1 " +
            "WHERE " +
            "   `code`=#{code}")
    int customerSignIn(@Param("code") String code);

    /**
     * 因为要返回已签到的状态，在更新sign 前，
     *
     * @param code SMS下发的唯一签到码
     * @return id 数据库记录中的id
     */
    @Select("SELECT " +
            "   status " +
            "FROM " +
            "   `double_eleven_customer_signin` " +
            "WHERE " +
            "   `code`=#{code} " +
            "limit 1")
    Integer getSignStatusByCode(String code);
}
