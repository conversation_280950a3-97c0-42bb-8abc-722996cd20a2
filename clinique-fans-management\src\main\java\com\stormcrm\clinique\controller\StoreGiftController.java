package com.stormcrm.clinique.controller;

import com.stormcrm.clinique.domain.StoreGift;
import com.stormcrm.clinique.enums.StoreGiftTypeEnum;
import com.stormcrm.clinique.service.StoreGiftService;
import com.stormcrm.clinique.util.ImageUtil;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.util.VerifyUtil;
import com.stormcrm.clinique.vo.Result;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 商城礼品管理
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("store/gift")
public class StoreGiftController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    private final StoreGiftService storeGiftService;

    public StoreGiftController(StoreGiftService storeGiftService) {
        this.storeGiftService = storeGiftService;
    }

    /**
     * 页面模版
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('STORE_GIFT')")
    @GetMapping("")
    public String index(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/store/gift/index";
    }

    /**
     * 查询所有
     *
     * @return 列表
     */
    @PreAuthorize("hasAuthority('STORE_GIFT')")
    @PostMapping("get-all")
    @ResponseBody
    public String getAll() {

        List<StoreGift> storeGiftList = storeGiftService.getAll();

        JSONObject mata = new JSONObject();
        mata.put("page", 1);
        mata.put("pages", 1);
        mata.put("perpage", -1);
        mata.put("total", storeGiftList.size());
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("mata", mata);
        result.put("data", storeGiftList);

        return JSONObject.toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }

    /**
     * 查询所有-分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 列表
     */
    @PreAuthorize("hasAuthority('STORE_GIFT')")
    @PostMapping("get-page")
    @ResponseBody
    public String getPage(
            @RequestParam(value = "pagination[page]", required = false) int page,
            @RequestParam(value = "pagination[perpage]", required = false) int perpage,
            @RequestParam(value = "query[generalSearch]", required = false) String generalSearch,
            @RequestParam(value = "query[status]", required = false) Boolean status
    ) {

        List<StoreGift> storeGiftList = storeGiftService.getPage(page, perpage, generalSearch, status);
        int count = storeGiftService.getPageCount(generalSearch, status);

        JSONObject mata = new JSONObject();
        mata.put("page", page);
        mata.put("pages", (count + perpage - 1) / perpage);
        mata.put("perpage", perpage);
        mata.put("total", count);
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("mata", mata);
        result.put("data", storeGiftList);

        return JSONObject.toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }

    /**
     * 增加模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('STORE_GIFT_ADD')")
    @RequestMapping("add")
    public String add(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/store/gift/add";
    }

    /**
     * 新增
     *
     * @param name        名称
     * @param imageFile   图片
     * @param description 说明
     * @param points      兑换积分
     * @param stock       库存
     * @return 结果
     */
    @PreAuthorize("hasAuthority('STORE_GIFT_ADD')")
    @PostMapping(value = "add-submit", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String addSubmit(
            @RequestParam(value = "form-name", required = false) String name,
            @RequestParam(value = "form-image-file", required = false) MultipartFile imageFile,
            @RequestParam(value = "form-description", required = false) String description,
            @RequestParam(value = "form-points", required = false) Integer points,
            @RequestParam(value = "form-stocks", required = false) Integer stocks
    ) {

        // 验证
        if (!VerifyUtil.required(name)) {
            return ResultUtil.verifyFailToJson("form-name", "这是必填字段");
        }

        // 验证
        if (null == imageFile || imageFile.isEmpty()) {
            return ResultUtil.verifyFailToJson("form-image-file", "这是必填字段");
        }

        // 验证
        if (!VerifyUtil.required(points)) {
            return ResultUtil.verifyFailToJson("form-points", "这是必填字段");
        }

        // 验证
        if (!VerifyUtil.required(stocks)) {
            return ResultUtil.verifyFailToJson("form-stocks", "这是必填字段");
        }

        // 生成对象
        StoreGift storeGift = new StoreGift();
        storeGift.setStoreGiftTypeId(StoreGiftTypeEnum.STORE.getId());
        storeGift.setName(name);
        storeGift.setImageUrl(ImageUtil.save("/w/image/", imageFile));
        storeGift.setDescription(description);
        storeGift.setPoints(points);
        storeGift.setStocks(stocks);

        // 传到Service服务中保存
        Result result = storeGiftService.save(storeGift);

        // 返回绍果给前端
        return JSONObject.toJSONString(result);
    }

    /**
     * 编辑模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('STORE_GIFT_EDIT')")
    @GetMapping("edit/{id}")
    public String edit(@PathVariable long id, Model model) {
        model.addAttribute("staticDomain", staticDomain);
        // 读数据库
        StoreGift storeGift = storeGiftService.getById(id);
        if (null == storeGift) {
            return "m/fans/common/empty";
        }
        model.addAttribute("id", id);
        model.addAttribute("storeGift", storeGift);
        return "m/fans/store/gift/edit";
    }

    /**
     * 编辑
     *
     * @param id          编号
     * @param name        名称
     * @param imageFile   图片
     * @param description 说明
     * @param points      兑换积分
     * @param stock       库存
     * @return 结果
     */
    @PreAuthorize("hasAuthority('STORE_GIFT_EDIT')")
    @PostMapping(value = "edit-submit/{id}", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String editSubmit(
            @PathVariable("id") long id,
            @RequestParam(value = "form-name", required = false) String name,
            @RequestParam(value = "form-image-file", required = false) MultipartFile imageFile,
            @RequestParam(value = "form-description", required = false) String description,
            @RequestParam(value = "form-points", required = false) Integer points,
            @RequestParam(value = "form-stocks", required = false) Integer stocks
    ) {

        // 验证
        if (!VerifyUtil.required(name)) {
            return ResultUtil.verifyFailToJson("form-name", "这是必填字段");
        }

        // 验证
        if (!VerifyUtil.required(points)) {
            return ResultUtil.verifyFailToJson("form-points", "这是必填字段");
        }

        // 验证
        if (!VerifyUtil.required(stocks)) {
            return ResultUtil.verifyFailToJson("form-stocks", "这是必填字段");
        }

        // 生成对象
        StoreGift storeGift = new StoreGift();
        storeGift.setId(id);
        storeGift.setName(name);
        if (null != imageFile && !imageFile.isEmpty()) {
            storeGift.setImageUrl(ImageUtil.save("/w/image/", imageFile));
        }
        storeGift.setDescription(description);
        storeGift.setPoints(points);
        storeGift.setStocks(stocks);

        // 传到Service服务中保存
        Result result = storeGiftService.update(storeGift);

        // 返回绍果给前端
        return JSONObject.toJSONString(result);
    }

    /**
     * 上架
     *
     * @param id 胸章的自动编号
     * @return 影响的行数
     */
    @PreAuthorize("hasAuthority('STORE_GIFT_UPPER')")
    @GetMapping("upper/{id}")
    @ResponseBody
    public String upper(@PathVariable("id") long id) {

        if (0 == storeGiftService.upper(id)) {
            return ResultUtil.failToJson();
        }

        return ResultUtil.successToJson();
    }

    /**
     * 下架
     *
     * @param id 胸章的自动编号
     * @return 影响的行数
     */
    @PreAuthorize("hasAuthority('STORE_GIFT_LOWER')")
    @GetMapping("lower/{id}")
    @ResponseBody
    public String lower(@PathVariable("id") long id) {

        if (0 == storeGiftService.lower(id)) {
            return ResultUtil.failToJson();
        }

        return ResultUtil.successToJson();

    }

    /**
     * 逻辑删除
     *
     * @param id 胸章的自动编号
     * @return 影响的行数
     */
    @PreAuthorize("hasAuthority('STORE_GIFT_DEL')")
    @GetMapping("del/{id}")
    @ResponseBody
    public String del(@PathVariable("id") long id) {

        if (0 == storeGiftService.del(id)) {
            return ResultUtil.failToJson();
        }

        return ResultUtil.successToJson();

    }

}
