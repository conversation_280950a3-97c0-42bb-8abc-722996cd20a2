package cn.slashsoft.clinique.enums;

import lombok.Getter;

/**
 * 商城订单状态
 *
 * <AUTHOR>
 */
public enum StoreExchangeLogisticsEnum {

    // 兑换类别
    WAITING_RECEIVE             ((short) 1, "待领取"),
    RECEIVED                    ((short) 2, "已领取"),
    WAITING_EXPRESS             ((short) 3, "待邮寄"),
    EXPRESSED                   ((short) 4, "已领取"),;

    @Getter
    private final short id;

    @Getter
    private final String name;

    StoreExchangeLogisticsEnum(short id, String name) {
        this.id = id;
        this.name = name;
    }
}
