package cn.slashsoft.clinique.dao.mini;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.mini.TTestimonyProduct;

@Repository
@Mapper
public interface TTestimonyProductDao {

    @Insert("INSERT INTO `t_testimony_product`(" +
            "   `product_code`," +
            "   `product_name` " +
            ") " +
            "VALUES (" +
            "   #{productCode}, " +
            "   #{productName} " +
            ")")
    void insertTTestimonyProduct(TTestimonyProduct tTestimonyProduct);

    @Update("UPDATE " +
            "   `t_testimony_product` " +
            "SET " +
            "   `product_code` = #{productCode}, " +
            "   `product_name` = #{productName}, " +
            "   `update_time` = #{updateTime} " +
            "WHERE " +
            "   `id`=#{id} ")
    void updateTTestimonyProduct(TTestimonyProduct tTestimonyProduct);

    @Select("SELECT " +
            "   `id`, " +
            "   `product_code`, " +
            "   `product_name`, " +
            "   `create_time`, " +
            "   `update_time` " +
            "FROM " +
            "   `t_testimony_product` " +
            "WHERE " +
            "   `id`=#{id} " +
            "LIMIT 1 ")
    TTestimonyProduct getTTestimonyProduct(Long id);
}
