package com.stormcrm.clinique.oauth.config;

import com.stormcrm.clinique.oauth.service.OauthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    private final OauthService oauthService;
    private final FailureHandler failureHandler;
    private final SuccessHandler successHandler;

    public WebSecurityConfig(OauthService oauthService, FailureHandler failureHandler, SuccessHandler successHandler) {
        this.oauthService = oauthService;
        this.failureHandler = failureHandler;
        this.successHandler = successHandler;
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
                // 关闭AJAX跨域验证
                .csrf().disable()
                // 请求授权配置
                .authorizeRequests()
                // 资源文件
                .antMatchers("/m/**").permitAll()
                // 登录页面
                .antMatchers("/login", "/login/form").permitAll()
                // 所有页面都要登录
                .anyRequest().authenticated()
                .and()
                // 登录
                .formLogin()
                .loginPage("/fans-management/login")
                .loginProcessingUrl("/login-submit")
                .failureHandler(failureHandler)
                .successHandler(successHandler)
                .and()
                // 退出
                .logout()
                .logoutUrl("/fans-management/logout")
                .logoutSuccessUrl("/login?logout")
        ;
    }

    /**
     * 全局配置
     */
    @Autowired
    public void configureGlobal(AuthenticationManagerBuilder auth) throws Exception {
        auth
                // 设置UserDetailsService
                .userDetailsService(oauthService)
                // 使用BCrypt进行密码的hash
                .passwordEncoder(bCryptPasswordEncoder());
    }

    /**
     * 装载BCrypt密码编码器 密码加密
     *
     * @return 密码编码器
     */
    @Bean
    public BCryptPasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }

}
