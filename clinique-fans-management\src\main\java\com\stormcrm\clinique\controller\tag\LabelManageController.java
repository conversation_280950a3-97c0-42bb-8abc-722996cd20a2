package com.stormcrm.clinique.controller.tag;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.stormcrm.clinique.domain.tag.Label;
import com.stormcrm.clinique.enums.ResultEnum;
import com.stormcrm.clinique.service.tag.RuleService;
import com.stormcrm.clinique.service.tag.LabelService;
import com.stormcrm.clinique.util.ResultUtil;

/**
 * 标签包管理
 *
 * <AUTHOR>
 */

@Controller
@RequestMapping("/tags")
public class LabelManageController {

	@Resource
    RuleService filterService;
	@Resource 
	LabelService labelService;

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    

    @PreAuthorize("hasAuthority('TAGS')")
	@RequestMapping("/index/{id}")
	public String index(Model model,
			@PathVariable Long id) {
        model.addAttribute("staticDomain", staticDomain);
        model.addAttribute("labelId", id);
        
        return "m/fans/bi/tags";
	}
	
	@RequestMapping("/get-all")
	@ResponseBody
	public String getAll() {
		List<Label> all = labelService.getAll();
		return ResultUtil.customer(ResultEnum.SUCCESS, "ok", all);
	}

}
