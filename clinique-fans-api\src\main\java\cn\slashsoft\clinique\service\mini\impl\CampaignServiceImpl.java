package cn.slashsoft.clinique.service.mini.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import cn.slashsoft.clinique.dao.mini.CampaignDao;
import cn.slashsoft.clinique.domain.mini.Campaign;
import cn.slashsoft.clinique.domain.mini.CampaignLog;
import cn.slashsoft.clinique.service.mini.CampaignService;

/**
 * 与活动相关
 *
 * <AUTHOR>
 */
@Service
public class CampaignServiceImpl implements CampaignService {

    private final CampaignDao campaignDao;

    public CampaignServiceImpl(CampaignDao campaignDao) {
        this.campaignDao = campaignDao;
    }

    /**
     * 获取新客活动排除名单
     * @param openId 小程序openId
     * @return 数量
     */
    @Override
    public int getCampaignNewFreeExclude(String openId) {
        return campaignDao.getCampaignNewFreeExclude(openId);
    }

    /**
     * 获取排除名单
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    @Override
    public int getCampaignC520Exclude(String phoneNumber) {
        return campaignDao.getCampaignC520Exclude(phoneNumber);
    }

    /**
     * 获取有效活动信息，按更新时间倒序排列
     * 活动资讯
     *
     * @return 活动信息
     */
    @Override
    public List<Campaign> getCampaign() {
        return campaignDao.getCampaign();
    }

    /**
     * 写入日志
     *
     * @param campaignLog 日志
     */
    @Override
    public void setCampaignLog(CampaignLog campaignLog) {
        campaignDao.insertCampaignLog(campaignLog);
    }
}
