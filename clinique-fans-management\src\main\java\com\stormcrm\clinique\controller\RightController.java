package com.stormcrm.clinique.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 积分权益
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("right")
public class RightController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    /**
     * 权益-天猫
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('RIGHT_TMALL')")
    @RequestMapping("tmall")
    public String tmall(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/right/tmall/index";
    }


}
