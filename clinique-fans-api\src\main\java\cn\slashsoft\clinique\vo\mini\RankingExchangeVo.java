package cn.slashsoft.clinique.vo.mini;

import cn.slashsoft.clinique.domain.mini.RankingGift;
import cn.slashsoft.clinique.domain.mini.StoreArea;
import cn.slashsoft.clinique.domain.mini.StoreAreaPlace;
import lombok.Data;

import java.util.List;

/**
 * 商城兑换明细页面返回数据
 *
 * <AUTHOR>
 */
@Data
public class RankingExchangeVo {

    private Long ranking;
    private Integer pointTotal;
    private List<StoreArea> storeAreaList;
    private List<StoreAreaPlace> storeAreaPlaceList;
    private RankingGift rankingGift;

}
