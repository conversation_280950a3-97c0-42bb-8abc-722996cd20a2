package cn.slashsoft.clinique.service.mini.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import cn.slashsoft.clinique.dao.mini.KocDao;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTopDetail;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTopDetailImage;
import cn.slashsoft.clinique.domain.campaign.ugc.KocWinner;
import cn.slashsoft.clinique.service.mini.KocService;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.service.outside.OutsideService;

/**
 * Laser 达人榜
 *
 * <AUTHOR>
 */
@Service
public class KocServiceImpl implements KocService {

    private final KocDao kocDao;
    private final WechatService wechatService;
    private final OutsideService outsideService;

    public KocServiceImpl(Koc<PERSON>ao kocDao, WechatService wechatService, OutsideService outsideService) {
        this.kocDao = kocDao;
        this.wechatService = wechatService;
        this.outsideService = outsideService;
    }

    /**
     * 获取入选koc TOP的名单
     *
     * @return 名单
     */
    @Override
    public List<KocTopDetail> getKocTopDetailList(Long id) {
    	List<KocTopDetail> list = new ArrayList<KocTopDetail>();
    	switch(id.intValue()) {
    		case 9999997:
    			list =  kocDao.getLaserTalentsTopDetailList();
    			break;
			default:
				int stage = kocDao.getUgcNewerStageByCampaign(id);
				if(stage > 0) {
					list = kocDao.getCampaignUgcTopDetailList(id, stage);
				}
    	}
    	return list;
    }

    /**
     * 获取入选koc TOP的图片
     *
     * @param talentsTopId TOP信息编号
     * @return 图片
     */
    @Override
    public List<KocTopDetailImage> getKocTopDetailImageList(Long id) {
    	List<KocTopDetailImage> list = new ArrayList<KocTopDetailImage>();
    	switch(id.intValue()) {
    		case 9999997:
    			list =  kocDao.getLaserTalentsTopDetailImageList();
    			break;
			default:
				int stage = kocDao.getUgcNewerStageByCampaign(id);
				if(stage > 0) {
					list = kocDao.getCampaignUgcTopDetailImageList(id, stage);
				}
    	}
    	return list;
    }

	@Override
	public List<KocWinner> getWinnerList(Long id) {
    	return kocDao.getWinnerList(id);
	}

}
