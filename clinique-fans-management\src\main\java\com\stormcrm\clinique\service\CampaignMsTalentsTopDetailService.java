package com.stormcrm.clinique.service;

import com.stormcrm.clinique.domain.CampaignMsTalentsTopDetail;
import com.stormcrm.clinique.vo.Result;

import java.util.List;

/**
 * 新品体验官活动
 *
 * <AUTHOR>
 */
public interface CampaignMsTalentsTopDetailService {


    /**
     * 查询所有活动
     * @param id TOP5编号
     * @return 活动列表
     */
    List<CampaignMsTalentsTopDetail> getAll(long id);

    /**
     * 查询信息
     *
     * @param detailId 自动编号
     * @return 信息
     */
    CampaignMsTalentsTopDetail getById(long detailId);

    /**
     * 保存
     *
     * @param campaignMsTalentsTopDetail 信息
     * @return 影响的行数
     */
    Result save(CampaignMsTalentsTopDetail campaignMsTalentsTopDetail);

    /**
     * 更新
     *
     * @param campaignMsTalentsTopDetail 信息
     * @return 影响的行数
     */
    Result update(CampaignMsTalentsTopDetail campaignMsTalentsTopDetail);

    /**
     * 更新
     *
     * @param id 编号
     * @return 影响的行数
     */
    int delete(long id);
}
