package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.CampaignLaserTalents;
import com.stormcrm.clinique.domain.CampaignLaserTalentsImage;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 新品体验官活动
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignLaserTalentsDao {

    /**
     * 查询所有活动-分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 活动列表
     */
    @SelectProvider(type = CampaignLaserTalentsProvider.class, method = "getPage")
    List<CampaignLaserTalents> getPage(
            @Param("pageIndex") int pageIndex,
            @Param("pageSize") int pageSize,
            @Param("generalSearch") String generalSearch,
            @Param("status") Short status
    );

    /**
     * 查询所有活动带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    @SelectProvider(type = CampaignLaserTalentsProvider.class, method = "getPageCount")
    int getPageCount(
            @Param("generalSearch") String generalSearch,
            @Param("status") Short status
    );

    /**
     * 获取美白达人官信息
     *
     * @param id 编号
     * @return 美白达人官信息
     */
    @Select("SELECT " +
            "   `t`.`id`," +
            "   `w`.`wechat_mini_openid`, " +
            "   `w`.`nick_name`, " +
            "   `w`.`avatar_url`, " +
            "   `t`.`customer_id`," +
            "   `t`.`status` " +
            "FROM " +
            "   `campaign_laser_talents` `t` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `t`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "   `t`.`id`=#{id} " +
            "LIMIT 1")
    CampaignLaserTalents getById(long id);

    /**
     * 获取美白达人官信息
     *
     * @param idList 编号
     * @return 美白达人官信息
     */
    @Select({"<script>" +
            "SELECT ",
            "   `t`.`id`,",
            "   `w`.`wechat_mini_openid`, ",
            "   `w`.`nick_name`, ",
            "   `w`.`avatar_url`, ",
            "   `t`.`customer_id`,",
            "   `t`.`status` ",
            "FROM ",
            "   `campaign_laser_talents` `t` ",
            "       INNER JOIN ",
            "   `wechat` `w` ",
            "       ON `t`.`customer_id`=`w`.`customer_id` ",
            "WHERE ",
            "   `t`.`id` IN <foreach collection='idList' item='id' open='(' separator=',' close=')'>#{id}</foreach>" +
                    "</script>"})
    List<CampaignLaserTalents> getByIds(@Param("idList") List<Long> idList);

    /**
     * 获取图片
     *
     * @param id 顾客活动信息编号
     * @return 图片地址
     */
    @Select("SELECT " +
            "   `image_url` " +
            "FROM " +
            "   `campaign_laser_talents_image` " +
            "WHERE " +
            "   `talents_id`=#{id}")
    List<CampaignLaserTalentsImage> getImageById(long id);

    /**
     * 审核成功
     *
     * @param id 编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_laser_talents` " +
            "SET " +
            "   `alert`=1," +
            "   `status`=4," +
            "   `examine_time`=NOW() " +
            "WHERE " +
            "   `id`=#{id} " +
            "   AND (`status`=2 OR `status`=3)")
    int accept(long id);

    /**
     * 审核失败
     *
     * @param id 编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_laser_talents` " +
            "SET " +
            "   `alert`=1," +
            "   `status`=3," +
            "   `examine_time`=NOW() " +
            "WHERE " +
            "   `id`=#{Id} " +
            "   AND `status`=2")
    int reject(long id);

    /**
     * 停用所有图片
     *
     * @param id 编号
     */
    @Update("UPDATE " +
            "   `campaign_laser_talents_image` " +
            "SET " +
            "   `status`=0 " +
            "WHERE " +
            "   `talents_id`=#{id}")
    void setImageStatus(long id);

}
