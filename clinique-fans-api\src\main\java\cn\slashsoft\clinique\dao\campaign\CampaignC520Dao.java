package cn.slashsoft.clinique.dao.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignAllViewLog;
import cn.slashsoft.clinique.domain.campaign.CampaignC520;
import cn.slashsoft.clinique.domain.campaign.CampaignC520Log;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

/**
 * 520活动
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignC520Dao {

    /**
     * 获取排除名单
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    @Select("SELECT " +
            "   COUNT(`id`) " +
            "FROM " +
            "   `campaign_c520_exclude` " +
            "WHERE " +
            "    `phone_number`=#{phoneNumber} " +
            "limit 1")
    int getCampaignC520Exclude(String phoneNumber);

    /**
     * 获取520活动信息
     *
     * @param customerId 顾客编号
     * @return 520活动信息
     */
    @Select("SELECT " +
            "   `k`.`id`," +
            "   `w`.`avatar_url`," +
            "   `w`.`nick_name`," +
            "   `k`.`is_apply`," +
            "   `k`.`apply_time`," +
            "   `k`.`is_receive`," +
            "   `k`.`receive_time`," +
            "   `k`.`status` " +
            "FROM " +
            "   `campaign_c520` `k` " +
            "       INNER JOIN " +
            "   `wechat` `w`" +
            "       ON `k`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "   `k`.`customer_id`=#{customerId} " +
            "LIMIT 1")
    CampaignC520 getCampaignC520(long customerId);

    /**
     * 写入520活动信息
     *
     * @param campaignC520 520活动信息
     */
    @Insert("INSERT INTO `campaign_c520`(" +
            "   `customer_id`" +
            ") " +
            "VALUES (" +
            "   #{customerId}" +
            ")")
    void insertCampaignC520(CampaignC520 campaignC520);

    /**
     * 申领
     * @param customerId 顾客编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_c520` " +
            "SET " +
            "   `is_apply`=1," +
            "   `apply_time`=NOW() " +
            "WHERE " +
            "   `customer_id`=#{customerId}" +
            "   AND `status`=1")
    int applyCampaignC520(long customerId);

    /**
     * 核销
     * @param customerId 顾客编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_c520` " +
            "SET " +
            "   `is_receive`=1," +
            "   `receive_time`=NOW() " +
            "WHERE " +
            "   `customer_id`=#{customerId}" +
            "   AND `status`=1")
    int receiveCampaignC520(long customerId);

    /**
     * 写入活动操作日志
     * @param campaignC520Log 操作日志
     */
    @Insert("INSERT INTO `campaign_c520_log`(" +
            "   `customer_id`, " +
            "   `status`, " +
            "   `content`" +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{status}, " +
            "   #{content}" +
            ")")
    void insertCampaignC520Log(CampaignC520Log campaignC520Log);

    /**
     * 写入访问日志
     *
     * @param campaignAllViewLog 日志
     */
    @Insert("INSERT INTO `campaign_c520_view_log`(" +
            "   `customer_id`," +
            "   `source`," +
            "   `page`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{source}," +
            "   #{page}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertCampaignC520ViewLog(CampaignAllViewLog campaignAllViewLog);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Update("UPDATE " +
            "   `campaign_c520_view_log` " +
            "SET " +
            "   `end_time`=NOW()," +
            "   `diff_second`=TimeStampDiff(SECOND,`start_time`,NOW()) " +
            "WHERE " +
            "   `id`=#{id}")
    void setCampaignC520ViewLog(long id);

}
