package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignToneupDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignToneupLog;
import cn.slashsoft.clinique.service.mini.SnsService;
import cn.slashsoft.clinique.service.campaign.CampaignToneupService;
import cn.slashsoft.clinique.util.CookieUtil;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.util.VerifyUtil;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * 黄油变粉安瓶级保湿补光申领
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/external")
public class CampaignToneupExternalController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    @Resource
    private HttpServletResponse response;

    private final CampaignToneupService campaignToneupService;
    private final SnsService snsService;

    public CampaignToneupExternalController(CampaignToneupService campaignToneupService, SnsService snsService) {
        this.campaignToneupService = campaignToneupService;
        this.snsService = snsService;
    }

    @GetMapping("/campaign-toneup-launch/{source}.html")
    public String launch(
            @PathVariable("source") String source,
            Model model
    ){

        String phoneNumber = CookieUtil.getCookie("phoneNumber", request);

        // 读取是否填写过
        if (!StringUtil.isNullOrEmpty(phoneNumber) && 0 < campaignToneupService.hasDetail(phoneNumber)) {
            return "redirect:/external/campaign-toneup/" + source + ".html";
        }

        String sessionId = request.getSession().getId().replace("-", "");

        // 保存日志
        CampaignToneupLog campaignToneupLog = new CampaignToneupLog();
        campaignToneupLog.setType((short) 2);
        campaignToneupLog.setUniqueId(sessionId);
        campaignToneupLog.setSource(source);
        campaignToneupLog.setPage("launch");
        campaignToneupService.insertLog(campaignToneupLog);

        model.addAttribute("serviceName", "external");
        model.addAttribute("source", source);
      
            model.addAttribute("staticDomain", staticDomain);
        return "w/fans/official/toneup/launch/index";

    }

    @GetMapping("/campaign-toneup/{source}.html")
    public String index(
            @PathVariable("source") String source,
            Model model
    ) {

        String phoneNumber = CookieUtil.getCookie("phoneNumber", request);

        CampaignToneupDetail campaignToneupDetail = null;
        // 读取是否填写过
        if (!StringUtil.isNullOrEmpty(phoneNumber)) {
            campaignToneupDetail = campaignToneupService.getDetail(phoneNumber);
        }

        String sessionId = request.getSession().getId().replace("-", "");

        // 保存日志
        CampaignToneupLog campaignToneupLog = new CampaignToneupLog();
        campaignToneupLog.setType((short) 2);
        campaignToneupLog.setUniqueId(sessionId);
        campaignToneupLog.setSource(source);
        campaignToneupLog.setPage("index");
        campaignToneupService.insertLog(campaignToneupLog);

        model.addAttribute("campaignToneupDetail", campaignToneupDetail);
        model.addAttribute("serviceName", "external");
        model.addAttribute("source", source);

        return "w/fans/official/toneup/external/index";
    }

    @GetMapping("/campaign-toneup-verify-code/{phoneNumber}")
    @ResponseBody
    public String getVerifyCode(
            @PathVariable("phoneNumber") String phoneNumber
    ) {
        return "{\"code\":" + snsService.sendVerifyCode(phoneNumber) + "}";
    }

    @PostMapping("/campaign-toneup-submit/{source}.html")
    @ResponseBody
    public String submit(
            @PathVariable("source") String source,
            @RequestParam("formName") String name,
            @RequestParam("formPhoneNumber") String phoneNumber,
            @RequestParam("formVerifyCode") String verifyCode,
            @RequestParam("formCity") String city,
            @RequestParam("formStore") String store
    ) {

        if (!VerifyUtil.required(name)) {
            return "{\"code\":9,\"message\":\"请输入姓名\"}";
        }

        if (!VerifyUtil.required(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.isPhoneNumber(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.required(verifyCode)) {
            return "{\"code\":9,\"message\":\"请输入验证码\"}";
        }

        if (!VerifyUtil.isVerifyCode(verifyCode)) {
            return "{\"code\":9,\"message\":\"验证码格式错误\"}";
        }

        if (!VerifyUtil.required(city)) {
            return "{\"code\":9,\"message\":\"请选择城市\"}";
        }

        if (!VerifyUtil.required(store)) {
            return "{\"code\":9,\"message\":\"请选择柜台\"}";
        }

        // 验证手机是否一致
        String sessionPhoneNumber = (String) request.getSession().getAttribute("verifyPhoneNumber");
        if (!phoneNumber.equals(sessionPhoneNumber)) {
            return "{\"code\":9,\"message\":\"请重新获取验证码\"}";
        }

        // 验证是否正确
        String sessionVerifyCode = (String) request.getSession().getAttribute("verifyCode");
        if (!verifyCode.equals(sessionVerifyCode)) {
            return "{\"code\":9,\"message\":\"验证码错误\"}";
        }

        // 验证码是否过期
        Date sessionVerifyTime = DateUtil.valueOf((String) request.getSession().getAttribute("verifyTime"));
        if (DateUtil.earlierThanNow(sessionVerifyTime)) {
            return "{\"code\":9,\"message\":\"验证码已过期\"}";
        }

        String sessionId = request.getSession().getId().replace("-", "");

        // 申领
        CampaignToneupDetail campaignToneupDetail = new CampaignToneupDetail();
        campaignToneupDetail.setType((short) 2);
        campaignToneupDetail.setUniqueId(sessionId);
        campaignToneupDetail.setName(name);
        campaignToneupDetail.setPhoneNumber(phoneNumber);
        campaignToneupDetail.setCity(city);
        campaignToneupDetail.setStore(store);
        campaignToneupDetail.setSource(source);

        // 0:成功，1：手机号码已经领过了，2：库存不足
        switch (campaignToneupService.submit(campaignToneupDetail)) {
            case 0:
                snsService.toneup(phoneNumber, store);
                CookieUtil.addCookie("phoneNumber", phoneNumber, response);
                return "{\"code\":0}";
            case 1:
                return "{\"code\":1}";
            case 2:
                return "{\"code\":2}";
            default:
                return "{\"code\":3}";
        }

    }

}
