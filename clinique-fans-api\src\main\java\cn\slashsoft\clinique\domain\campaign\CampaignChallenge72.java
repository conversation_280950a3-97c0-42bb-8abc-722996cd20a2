package cn.slashsoft.clinique.domain.campaign;

import lombok.Data;

import java.util.Date;

/**
 * 挑战72小时
 * <AUTHOR>
 */
@Data
public class CampaignChallenge72 {

    private Long id;
    private Long customerId;

    private Boolean firstRound;
    private Date firstRoundTime;

    private Boolean secondRound;
    private Date secondRoundTime;

    private Boolean thirdRound;
    private Date thirdRoundTime;

    private Boolean result;
    private Boolean resultExperience;
    private Boolean resultShare;
    private Date resultTime;

    private Boolean inviteDone;

    private String source;

}
