package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.VideoTag;
import com.stormcrm.clinique.domain.VideoTagDefine;
import com.stormcrm.clinique.vo.EditVideoWithTagVo;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
@Mapper
public interface VideoTagDao {

    /**
     * 保存
     *
     * @param videoTag Video obj
     */
    @Insert("INSERT INTO `video_tag`(" +
            "   `video_id`, " +
            "   `title`, " +
            "   `status`, " +
            "   `tag_id`, " +
            "   `tag_type`, " +
            "   `type` " +
            ") " +
            "VALUES (" +
            "   #{videoId}, " +
            "   #{title}, " +
            "   #{status}, " +
            "   #{tagId}, " +
            "   #{tagType}, " +
            "   #{type} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertVideoTag(VideoTag videoTag);

    /**
     * 保存
     *
     * @param videoTagDefine Video Tag Define obj
     */
    @Insert("INSERT INTO `video_tag_define`(" +
            "   `title`, " +
            "   `image`, " +
            "   `top`, " +
            "   `type` " +
            ") " +
            "VALUES (" +
            "   #{title}, " +
            "   #{image}, " +
            "   #{top}, " +
            "   #{type} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertVideoTagDefine(VideoTagDefine videoTagDefine);

    /**
     * 修改
     *
     * @param videoTag 视频tag obj
     */
    @Update("UPDATE `video_tag` set " +
            "   `video_id` = #{video_id} , " +
            "   `title` = #{title} , " +
            "   `status` = #{status} , " +
            "   `type` = #{type} " +
            "  WHERE id=#{id} ")
    int updateVideoTag(VideoTag videoTag);

    /**
     * 修改
     *
     * @param videoTagDefine 视频tag obj
     */
    @UpdateProvider(type = VideoTagProvider.class, method = "update")
    int updateVideoTagDefine(VideoTagDefine videoTagDefine);

    /**
     * 删除视频标签
     *
     * @param id 视频标签id
     */
    @Delete("DELETE FROM `video_tag`" +
            " WHERE " +
            " `id`=#{id} "
    )
    int delVideoTag(long id);

    /**
     * 根据视频id删除全部视频标签
     *
     * @param videoTagDefineId 视频标签id
     */
    @Delete("DELETE FROM `video_tag_define`" +
            " WHERE " +
            " `id`=#{videoTagDefineId} "
    )
    int deleteVideoTagDefine(Long videoTagDefineId);

    /**
     * 获取视频标签列表
     *
     * @param start 起始记录数
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `video_tag` " +
            "WHERE " +
            "   `status`= 1 " +
            "LIMIT #{start}, 30")
    ArrayList<VideoTag> getVideoTagList(int start);

    /**
     * 获取视频标签
     *
     * @param id 视频id
     */
    @Select("SELECT " +
            "  a.* " +
            "FROM " +
            "   `video_tag` as a " +
            "WHERE " +
            "   a.`id`=#{id} " +
            "LIMIT 1")
    VideoTag getVideoTag(Long id);

    /**
     * 获取视频标签定义
     *
     * @param id 视频id
     */
    @Select("SELECT " +
            "  a.* " +
            "FROM " +
            "   `video_tag_define` as a " +
            "WHERE " +
            "   a.`id`=#{id} " +
            "LIMIT 1")
    VideoTagDefine getVideoTagDefine(Long id);

    /**
     * 查询所有 视频 -分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param type        状态
     * @return 活动列表
     */
    @SelectProvider(type = VideoTagProvider.class, method = "getPage")
    List<VideoTagDefine> getPage(
            @Param("pageIndex") int pageIndex,
            @Param("pageSize") int pageSize,
            @Param("generalSearch") String generalSearch,
            @Param("type") Short type
    );

    /**
     * 查询所有 视频 带分页的记录数
     *
     * @param generalSearch 搜索
     * @param type        状态
     * @return 记录数
     */
    @SelectProvider(type = VideoTagProvider.class, method = "getPageCount")
    int getPageCount(
            @Param("generalSearch") String generalSearch,
            @Param("type") Short type
    );

    /**
     * 查询所有 视频 带分页的记录数
     *
     * @param videoId 视频id
     * @return 记录数
     */
    @Select("SELECT " +
            "  a.*, " +
            "  p.* " +
            "FROM " +
            "   `video_tag` as a " +
            "       LEFT JOIN " +
            "   `video_tag_define` `p` " +
            "       ON `a`.`tag_id`=`p`.`id` " +
            "WHERE " +
            "   a.`video_id`=#{videoId} ")
    ArrayList<VideoTag> getVideoTagByVideoId(Long videoId);

    /**
     * 获取标签列表
     * <p>
     * video_tag_define copy from note_tag
     * video_tag 保存video和tag的关系
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `video_tag_define` ")
    ArrayList<VideoTagDefine> getTagDefineList();

    /**
     * 复选框中被选中的标签和全部标签的集合
     *
     * @param videoId 视频id
     * @return 复选框中被选中的标签和全部标签的集合
     */
    @Select("SELECT " +
            "   `l`.`id`," +
            "   `l`.`type`," +
            "   `l`.`image`," +
            "   `l`.`title`," +
            "   `l`.`createTime`," +
            "   `p`.`id` `selected`, " +
            "   `l`.`top` " +
            " FROM " +
            "   `video_tag_define` `l` " +
            "       LEFT JOIN " +
            "   `video_tag` `p` " +
            "       ON `l`.`id`=`p`.`tag_id` " +
            " AND " +
            "   `p`.`video_id`=#{videoId} ")
    ArrayList<EditVideoWithTagVo> getTagListSelected(long videoId);


    /**
     * 根据视频id删除全部视频标签
     *
     * @param videoId 视频id
     */
    @Delete("DELETE FROM `video_tag`" +
            " WHERE " +
            " `video_id`=#{videoId} "
    )
    void delAllTagsByVideoId(Long videoId);
}
