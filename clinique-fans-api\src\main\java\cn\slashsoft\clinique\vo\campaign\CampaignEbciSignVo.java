package cn.slashsoft.clinique.vo.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignEbciSignLog;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * EBCI 7日打卡活动
 *
 * <AUTHOR>
 */
@Data
public class CampaignEbciSignVo {

    private Date startTime;
    private Date endTime;

    private Short level;

    /**
     * 是否申领
     */
    private Boolean apply;
    /**
     * 是否完成
     */
    private Boolean complete;
    /**
     * 今天是否已经签到
     */
    private Boolean signed;
    /**
     * 当前是否可以签到
     */
    private Boolean sign;
    /**
     * 是否已经断掉
     */
    private Boolean broken;
    /**
     * 是否选择了邀请
     */
    private Boolean inviting;
    /**
     * 是否邀请成功
     */
    private Boolean invited;

    private List<CampaignEbciSignLog> record;

}
