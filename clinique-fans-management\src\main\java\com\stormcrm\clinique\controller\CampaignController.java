package com.stormcrm.clinique.controller;

import com.stormcrm.clinique.domain.Campaign;
import com.stormcrm.clinique.service.CampaignService;
import com.stormcrm.clinique.util.ImageUtil;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.util.VerifyUtil;
import com.stormcrm.clinique.vo.Result;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 活动管理
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("campaign")
public class CampaignController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;

    private final CampaignService campaignService;

    public CampaignController(CampaignService campaignService) {
        this.campaignService = campaignService;
    }

    /**
     * 页面模版
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('CAMPAIGN')")
    @GetMapping("")
    public String index(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/campaign/index";
    }

    /**
     * 查询所有
     *
     * @return 列表
     */
    @PreAuthorize("hasAuthority('CAMPAIGN')")
    @PostMapping("get-all")
    @ResponseBody
    public String getAll() {

        List<Campaign> campaignList = campaignService.getAll();

        JSONObject mata = new JSONObject();
        mata.put("page", 1);
        mata.put("pages", 1);
        mata.put("perpage", -1);
        mata.put("total", campaignList.size());
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("mata", mata);
        result.put("data", campaignList);

        return JSONObject.toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }

    /**
     * 查询所有-分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 列表
     */
    @PreAuthorize("hasAuthority('CAMPAIGN')")
    @PostMapping("get-page")
    @ResponseBody
    public String getPage(
            @RequestParam(value = "pagination[page]", required = false) int page,
            @RequestParam(value = "pagination[perpage]", required = false) int perpage,
            @RequestParam(value = "query[generalSearch]", required = false) String generalSearch,
            @RequestParam(value = "query[status]", required = false) Boolean status
    ) {

        List<Campaign> campaignList = campaignService.getPage(page, perpage, generalSearch, status);
        int count = campaignService.getPageCount(generalSearch, status);

        JSONObject mata = new JSONObject();
        mata.put("page", page);
        mata.put("pages", (count + perpage - 1) / perpage);
        mata.put("perpage", perpage);
        mata.put("total", count);
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("mata", mata);
        result.put("data", campaignList);

        return JSONObject.toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }

    /**
     * 增加模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_ADD')")
    @RequestMapping("add")
    public String add(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/campaign/add";
    }

    /**
     * 新增
     *
     * @param name      名称
     * @param imageFile 图片
     * @param linkUrl   页面
     * @return 结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_ADD')")
    @PostMapping(value = "add-submit", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String addSubmit(
            @RequestParam(value = "form-name", required = false) String name,
            @RequestParam(value = "form-image-file", required = false) MultipartFile imageFile,
            @RequestParam(value = "form-link-url", required = false) String linkUrl
    ) {

        // 验证
        if (!VerifyUtil.required(name)) {
            return ResultUtil.verifyFailToJson("form-name", "这是必填字段");
        }

        // 验证
        if (null == imageFile || imageFile.isEmpty()) {
            return ResultUtil.verifyFailToJson("form-image-file", "这是必填字段");
        }

        // 验证
        if (!VerifyUtil.required(linkUrl)) {
            return ResultUtil.verifyFailToJson("form-link-url", "这是必填字段");
        }

        // 生成对象
        Campaign campaign = new Campaign();
        campaign.setName(name);
        campaign.setImageUrl(ImageUtil.save("/w/image/", imageFile));
        campaign.setLinkUrl(linkUrl);

        // 传到Service服务中保存
        Result result = campaignService.save(campaign);

        // 返回绍果给前端
        return JSONObject.toJSONString(result);
    }

    /**
     * 编辑模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_EDIT')")
    @GetMapping("edit/{id}")
    public String edit(@PathVariable long id, Model model) {
        model.addAttribute("staticDomain", staticDomain);
        // 读数据库
        Campaign campaign = campaignService.getById(id);
        if (null == campaign) {
            return "m/fans/common/empty";
        }
        model.addAttribute("id", id);
        model.addAttribute("campaign", campaign);
        return "m/fans/campaign/edit";
    }

    /**
     * 编辑
     *
     * @param id        编号
     * @param name      名称
     * @param imageFile 图片
     * @param linkUrl   页面
     * @return 结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_EDIT')")
    @PostMapping(value = "edit-submit/{id}", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String editSubmit(
            @PathVariable("id") long id,
            @RequestParam(value = "form-name", required = false) String name,
            @RequestParam(value = "form-image-file", required = false) MultipartFile imageFile,
            @RequestParam(value = "form-link-url", required = false) String linkUrl
    ) {

        // 验证
        if (!VerifyUtil.required(name)) {
            return ResultUtil.verifyFailToJson("form-name", "这是必填字段");
        }

        // 验证
        if (!VerifyUtil.required(linkUrl)) {
            return ResultUtil.verifyFailToJson("form-link-url", "这是必填字段");
        }

        // 生成对象
        Campaign campaign = new Campaign();
        campaign.setId(id);
        campaign.setName(name);
        if (null != imageFile && !imageFile.isEmpty()) {
            campaign.setImageUrl(ImageUtil.save("/w/image/", imageFile));
        }
        campaign.setLinkUrl(linkUrl);

        // 传到Service服务中保存
        Result result = campaignService.update(campaign);

        // 返回绍果给前端
        return JSONObject.toJSONString(result);
    }

    /**
     * 上架
     *
     * @param id 胸章的自动编号
     * @return 影响的行数
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UPPER')")
    @GetMapping("upper/{id}")
    @ResponseBody
    public String upper(@PathVariable("id") long id) {

        if (0 == campaignService.upper(id)) {
            return ResultUtil.failToJson();
        }

        return ResultUtil.successToJson();
    }

    /**
     * 下架
     *
     * @param id 胸章的自动编号
     * @return 影响的行数
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_LOWER')")
    @GetMapping("lower/{id}")
    @ResponseBody
    public String lower(@PathVariable("id") long id) {

        if (0 == campaignService.lower(id)) {
            return ResultUtil.failToJson();
        }

        return ResultUtil.successToJson();

    }

    /**
     * 逻辑删除
     *
     * @param id 胸章的自动编号
     * @return 影响的行数
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_DEL')")
    @GetMapping("del/{id}")
    @ResponseBody
    public String del(@PathVariable("id") long id) {

        if (0 == campaignService.del(id)) {
            return ResultUtil.failToJson();
        }

        return ResultUtil.successToJson();

    }

}
