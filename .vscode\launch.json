{"configurations": [{"type": "java", "name": "CliniqueMemberManagementApplication", "request": "launch", "mainClass": "cn.slashsoft.clinique.CliniqueMemberManagementApplication", "projectName": "clinique-member-management"}, {"type": "java", "name": "CliniqueFansManagementApplication", "request": "launch", "mainClass": "com.stormcrm.clinique.CliniqueFansManagementApplication", "projectName": "clinique-fans-management"}, {"type": "java", "name": "CliniqueGatewayApplication", "request": "launch", "mainClass": "cn.slashsoft.cliniquegateway.CliniqueGatewayApplication", "projectName": "clinique-gateway"}, {"type": "java", "name": "CliniqueEurekaApplication", "request": "launch", "mainClass": "cn.slashsoft.clinique.CliniqueEurekaApplication", "projectName": "clinique-eureka"}, {"name": "Java: Current File", "type": "java", "request": "launch", "mainClass": "com.morning.Main", "vmArgs": "-Dfile.encoding=UTF-8 -Dserver.port=9203 "}]}