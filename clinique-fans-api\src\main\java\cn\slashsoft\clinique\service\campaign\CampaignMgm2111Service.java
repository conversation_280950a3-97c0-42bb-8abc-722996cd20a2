package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.mgm2111.Detail;
import cn.slashsoft.clinique.domain.campaign.mgm2111.Invite;
import cn.slashsoft.clinique.domain.campaign.mgm2111.ViewLog;
import cn.slashsoft.clinique.vo.Result;

import java.util.List;

/**
 * 2021年11月裂变活动
 *
 * <AUTHOR>
 */
public interface CampaignMgm2111Service {

    /**
     * 获取活动信息
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @return 活动信息
     */
    Detail getDetailWithCreate(long customerId, String source);

    /**
     * 获取活动信息
     *
     * @param customerId 顾客编号
     * @return 活动信息
     */
    Detail getDetail(long customerId);

    /**
     * 获取活动信息
     *
     * @param customerId 顾客编号
     * @return 活动信息
     */
    Detail getDetailWithAvatar(long customerId);

    /**
     * 获取邀请排名
     *
     * @return 用户列表
     */
    List<Invite> getInviteByRank();

    /**
     * 领取签到的礼品
     *
     * @param customerId            会员编号
     * @param unionid               开放平台唯一编号
     * @param signCoupon5CampaignId 签到5次的礼品编号
     * @param signCoupon8CampaignId 签到8次的礼品编号
     * @return 处理结果
     */
    Result getSignCoupon(long customerId, String unionid, long signCoupon5CampaignId, long signCoupon8CampaignId);

    /**
     * 领取邀请的礼品
     *
     * @param customerId              会员编号
     * @param unionid                 开放平台唯一编号
     * @param inviteCoupon3CampaignId 邀请3次的礼品编号
     * @param inviteCoupon8CampaignId 邀请8次的礼品编号
     * @return 处理结果
     */
    Result getInviteCoupon(long customerId, String unionid, long inviteCoupon3CampaignId, long inviteCoupon8CampaignId);

    /**
     * 写入经纬度
     * @param customerId 顾客编号
     * @param latitude 经度
     * @param longitude 纬度
     */
    void insertLocation(long customerId, String latitude, String longitude);

    /**
     * 写入访问日志
     *
     * @param viewLog 日志
     * @return 日志的编号
     */
    long insertViewLog(ViewLog viewLog);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    void setSignViewLog(long id);

    /**
     * 邀请
     *
     * @param inviterMiniOpenid 邀请者
     * @param inviteeMiniOpenid 被邀请者
     */
    void invite(String inviterMiniOpenid, String inviteeMiniOpenid);

}
