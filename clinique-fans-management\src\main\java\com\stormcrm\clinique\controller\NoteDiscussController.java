package com.stormcrm.clinique.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import com.stormcrm.clinique.domain.Note;
import com.stormcrm.clinique.domain.NoteDiscuss;
import com.stormcrm.clinique.service.NoteService;
import com.stormcrm.clinique.util.ResultUtil;

@Controller
public class NoteDiscussController {

    private final NoteService noteService;

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;

    public NoteDiscussController(NoteService noteService) {
        this.noteService = noteService;
    }

    /**
     * 笔记评论列表
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('NOTE_DISCUSS')")
    @RequestMapping("note/discuss")
    public String index(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/note-discuss/index";
    }

    /**
     * 笔记评论列表
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('NOTE_DISCUSS')")
    @RequestMapping("note/discuss/{id}")
    public String index(@PathVariable long id, Model model) {
        Note note = noteService.getNote(id);

        model.addAttribute("id", id);
        model.addAttribute("note", note);
        model.addAttribute("staticDomain", staticDomain);

        return "m/fans/note-discuss/index";
    }

    /**
     * 查询所有-分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 列表
     */
    @PreAuthorize("hasAuthority('NOTE_INDEX')")
    @PostMapping("note/discuss/get-page")
    @ResponseBody
    public String getPage(
            @RequestParam(value = "pagination[page]", required = false) Integer page,
            @RequestParam(value = "pagination[perpage]", required = false) Integer perpage,
            @RequestParam(value = "query[generalSearch]", required = false) String generalSearch,
            @RequestParam(value = "query[status]", required = false) Short status,
            @RequestParam(value = "note", required = false) Long noteId
    ) {

        if (null == page) {
            page = 1;
        }

        if (null == perpage) {
            perpage = 10;
        }

        List<NoteDiscuss> noteList = noteService.getDiscussPage(page, perpage, generalSearch, status, noteId);
        int count = noteService.getDiscussPageCount(generalSearch, status, noteId);

        JSONObject mata = new JSONObject();
        mata.put("page", page);
        mata.put("pages", (count + perpage - 1) / perpage);
        mata.put("perpage", perpage);
        mata.put("total", count);
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("meta", mata);
        result.put("data", noteList);

        return JSONObject.toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }


    /**
     * 逻辑删除
     *
     * @param id 胸章的自动编号
     * @return 影响的行数
     */
    @PreAuthorize("hasAuthority('NOTE_DISCUSS_DEL')")
    @GetMapping("note/discuss/del/{id}")
    @ResponseBody
    public String del(@PathVariable("id") long id) {
        if (0 == noteService.deleteDiscuss(id)) {
            return ResultUtil.failToJson();
        }

        return ResultUtil.successToJson();

    }


    /**
     * 同意
     *
     * @param id 自动编号
     * @return 处理结果
     */
    @PreAuthorize("hasAuthority('NOTE_DISCUSS_ACCEPT')")
    @GetMapping("note/discuss/accept/{id}")
    @ResponseBody
    public String accept(@PathVariable("id") long id) {
        if (0 == noteService.acceptDiscuss(id)) {
            return ResultUtil.failToJson();
        }
        return ResultUtil.successToJson();
    }

    /**
     * 批量同意
     *
     * @param ids 自动编号
     * @return 处理结果
     */
    @PreAuthorize("hasAuthority('NOTE_DISCUSS_ACCEPT')")
    @GetMapping("note/discuss/accept-batch/{ids}")
    @ResponseBody
    public String acceptBatch(@PathVariable("ids") String ids) {
        if (0 == noteService.acceptDiscussBatch(ids)) {
            return ResultUtil.failToJson();
        }
        return ResultUtil.successToJson();
    }

    /**
     * 拒绝
     *
     * @param id 自动编号
     * @return 处理结果
     */
    @PreAuthorize("hasAuthority('NOTE_DISCUSS_REJECT')")
    @GetMapping("note/discuss/reject/{id}")
    @ResponseBody
    public String reject(@PathVariable("id") long id) {
        if (0 == noteService.rejectDiscuss(id)) {
            return ResultUtil.failToJson();
        }
        return ResultUtil.successToJson();
    }

    /**
     * 批量拒绝
     *
     * @param ids 自动编号
     * @return 处理结果
     */
    @PreAuthorize("hasAuthority('NOTE_DISCUSS_REJECT')")
    @GetMapping("note/discuss/reject-batch/{ids}")
    @ResponseBody
    public String rejectBatch(@PathVariable("ids") String ids) {
        if (0 == noteService.rejectDiscussBatch(ids)) {
            return ResultUtil.failToJson();
        }
        return ResultUtil.successToJson();
    }
}
