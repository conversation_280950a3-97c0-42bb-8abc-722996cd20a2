package cn.slashsoft.clinique.service.mini;

import cn.slashsoft.clinique.domain.mini.Day3Task;
import cn.slashsoft.clinique.vo.mini.Day3TaskDetailVo;

/**
 * 任务
 *
 * <AUTHOR>
 */
public interface TaskService {

    /**
     * 创建三天打卡任务
     *
     * @param Day3Task
     */
    void addTask(Day3Task task);
    

    /**
     * 更新三天打卡任务
     *
     * @param Day3Task
     */
    void editTask(Day3Task task);
    

    /**
     * 获取用户三天打卡任务
     *
     * @return Day3Task
     */
    Day3Task getTask( String unionid, Long customerId);


	Day3TaskDetailVo getDay3TaskDetail(Long customerId);


	int day3TaskNotice(Long customerId, int status);


	int day3TaskFinish(Long customerId);



}
