package cn.slashsoft.clinique.service.campaign.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import cn.slashsoft.clinique.dao.campaign.CampaignEbci202102Dao;
import cn.slashsoft.clinique.dao.campaign.CampaignEbciDao;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciAD;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciCity;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciLog;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciStock;
import cn.slashsoft.clinique.domain.campaign.CampaignTemp;
import cn.slashsoft.clinique.service.campaign.CampaignEbciService;

/**
 * 302美白镭射瓶
 *
 * <AUTHOR>
 */
@Service
public class CampaignEbciServiceImpl implements CampaignEbciService {
	@Resource
    private CampaignEbci202102Dao campaignEbci202102Dao;

	@Resource
    private CampaignEbciDao campaignEbciDao;


    /**
     * 保存日志
     *
     * @param campaignEbciLog 日志
     */
    @Override
    public void insertLog(CampaignEbciLog campaignEbciLog) {
    	campaignEbci202102Dao.insertLog(campaignEbciLog);
    }

    /**
     * 获取手机号码
     *
     * @param unionid 开放平台唯一编号
     * @return 手机号码
     */
    @Override
    public String getPhoneNumberByUnionid(String unionid) {
        return campaignEbci202102Dao.getPhoneNumberByUnionid(unionid);
    }

    /**
     * 获取手机号码
     *
     * @param openid 公众号唯一编号
     * @return 手机号码
     */
    @Override
    public String getPhoneNumberByOpenid(String openid) {
        return campaignEbci202102Dao.getPhoneNumberByOpenid(openid);
    }

    /**
     * 获取是否已经审领
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    @Override
    public int hasDetail(String phoneNumber) {
        return campaignEbci202102Dao.hasDetail(phoneNumber);
    }

    /**
     * 获取是否已经审领
     *
     * @param openid 公众号唯一编号
     * @return 数量
     */
    @Override
    public int hasDetailByOpenid(String openid) {
        return campaignEbci202102Dao.hasDetailByOpenid(openid);
    }

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    @Override
    public CampaignEbciDetail getDetail(String phoneNumber) {
        return campaignEbci202102Dao.getDetail(phoneNumber);
    }

    /**
     * 申领
     *
     * @param campaignEbciDetail 资料
     * @return 0:成功，1：手机号码已经领过了，2：库存不足, 3： 其他错误
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int submit(CampaignEbciDetail campaignEbciDetail) {
        // 扣库存
        //if (0 == campaignEbciDao.updateStock(campaignEbciDetail)) {
        //    return 2;
        //}

    	if(campaignEbci202102Dao.getDetail(campaignEbciDetail.getPhoneNumber()) != null) {
    		return 1;
    	}

    	//CampaignEbciStock store = campaignEbci202102Dao.getByStoreName(campaignEbciDetail);
        //if (null == store) {
            //Tmall 线下活动
       // } else if(!store.getStatus()) {
    	//	return 2;
    	//}

        // 写入记录
        try {
        	campaignEbci202102Dao.insetDetail(campaignEbciDetail);
            return 0;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return 3;
        }

    }
   

    /**
     * 更新是否关注
     *
     * @param campaignEbciDetail 申领信息
     */
    @Override
    public void setFollow(CampaignEbciDetail campaignEbciDetail) {
    	campaignEbci202102Dao.setFollow(campaignEbciDetail);
    }

    /**
     * 获取审领信息
     *
     * @param openid 公众号唯一编号
     * @return 审领信息
     */
    @Override
    public CampaignEbciDetail getDetailByOpenid(String openid) {
        return campaignEbci202102Dao.getDetailByOpenid(openid);
    }

    /**
     * 根据场景值获取门店
     *
     * @param scene 场景值
     * @return 门店
     */
    @Override
    public CampaignEbciStock getByScene(String scene) {
        return campaignEbci202102Dao.getByScene(scene);
    }

    /**
     * 根据门店名称获取门店状态
     *
     * @param scene 场景值
     * @return 门店
     */
    @Override
    public CampaignEbciStock getByStoreName(CampaignEbciDetail campaignEbciDetail) {
        return campaignEbci202102Dao.getByStoreName(campaignEbciDetail);
    }
    /**
     * 更新核销信息
     *
     * @param campaignEbciDetail 核销信息
     */
    @Override
    public void setReceive(CampaignEbciDetail campaignEbciDetail) {
    	campaignEbci202102Dao.setReceive(campaignEbciDetail);
    }

    /**
     * 写入ad信息
     *
     * @param campaignEbciAD ad
     * @return ad
     */
    @Override
    public void addAD(CampaignEbciAD campaignEbciAD) {
    	campaignEbciDao.addAD(campaignEbciAD);
    }

    /**
     * 根据手机号获取ad
     *
     * @param phoneNumber 手机号
     * @return ad
     */
    @Override
    public CampaignEbciAD getADByPhoneNumber(String phoneNumber) {
    	return campaignEbciDao.getADByPhoneNumber(phoneNumber);
    }

    /**
     * 更新ad信息
     *
     * @param CampaignEbciAD ad
     * @return ad
     */
    @Override
    public void setAD(CampaignEbciAD campaignEbciAD) {
    	campaignEbciDao.setAD(campaignEbciAD);
    }
    /**
     * 临时记录需求
     *
     * @param campaignEbciDetail 资料
     * @return 0:成功，1：手机号码已经领过了，2：库存不足, 3： 其他错误
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int tmpsubmit(CampaignEbciDetail campaignEbciDetail) {
    	//CampaignEbciStock store = campaignEbciDao.getByStoreName(campaignEbciDetail);
    	//if(!store.getStatus()) {
    	//	return 2;
    	//}
    	if(campaignEbciDao.getTmpDetail(campaignEbciDetail.getPhoneNumber()) != null) {
    		//已经领取
    		return 1;
    	}
    	CampaignTemp temp = campaignEbciDao.getStoreTemp(campaignEbciDetail);
    	if(temp == null) {
        	CampaignTemp temp1 = campaignEbciDao.getOnlineTemp(campaignEbciDetail);
        	if(temp1 != null) {
        		//买赠礼
        		return 12;
        	}
        	CampaignTemp temp2 = campaignEbciDao.getExpressTemp(campaignEbciDetail);
        	if(temp2 != null) {
        		//发快递
        		return 13;
        	}
    		//无领取资格
    		return 2;
    	}else {
            // 写入记录
            try {
                campaignEbciDao.insetTmpDetail(campaignEbciDetail);
                return 0;
            } catch (Exception e) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return 3;
            }
    	}

    }

	@Override
	public CampaignEbciCity getEbciCity(String city, long campaign) {		
		return campaignEbci202102Dao.getCity(city, campaign);
	}
}
