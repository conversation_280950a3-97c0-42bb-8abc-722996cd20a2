package com.stormcrm.clinique.service.tag.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.stormcrm.clinique.tag.dao.TagDao;
import com.stormcrm.clinique.domain.tag.Tag;
import com.stormcrm.clinique.service.tag.TagService;
/**
 * 
 *
 * <AUTHOR>
 */

@Service
public class TagServiceImpl implements TagService {

	@Resource
	TagDao tagDao;

	@Override
	public Tag getTag(String unionid, int categoryId) {
		return tagDao.getLabel(unionid, categoryId);
	}

	@Override
	public int addTag(Tag tag) {
		Tag t = this.getTag(tag.getUnionid(), tag.getCategoryId());
		if(null != t) {
			if(tag.getLabelId() != t.getLabelId() 
					|| tag.getStatus() != t.getStatus()
					|| !tag.getRemark().equals(t.getRemark())) {
				tag.setId(t.getId());
				return tagDao.updateLabel(tag);
			}			
		}else {
			tagDao.addLabel(tag);
		}
		return 1;
	}

}
