package cn.slashsoft.clinique.service.campaign.impl;

import cn.slashsoft.clinique.dao.campaign.Campaign2408Dao;
import cn.slashsoft.clinique.dao.mini.PointDao;
import cn.slashsoft.clinique.dao.mini.WechatDao;
import cn.slashsoft.clinique.domain.campaign.CampaignH5;
import cn.slashsoft.clinique.domain.campaign.lottery2408.*;
import cn.slashsoft.clinique.domain.mini.PointTransaction;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.enums.CBTypeEnum;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.campaign.Campaign2408Service;
import cn.slashsoft.clinique.service.campaign.CampaignH5Service;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.RedisLock;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.vo.Result;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class Campaign2408ServiceImpl implements Campaign2408Service {
    @Resource
    private CampaignH5Service campaignH5Service;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private RedisLock redisLock;

    private final PointDao pointDao;
    private final Campaign2408Dao campaign2408Dao;
    private final OutsideService outsideService;
    private final WechatDao wechatDao;


    public Campaign2408ServiceImpl(Campaign2408Dao campaign2408Dao,PointDao pointDao,OutsideService outsideService,WechatDao wechatDao) {
        this.campaign2408Dao = campaign2408Dao;
        this.pointDao = pointDao;
        this.outsideService = outsideService;
        this.wechatDao = wechatDao;
    }

    @Override
    public CampaignUserLotteryAwardLogs getUserLotteryInfo(Long uid, String campaignFlag){
        return campaign2408Dao.getUserLotteryInfo(uid,campaignFlag);
    }


    @Override
    public HashMap getActivityRanking(Long customerId,CampaignH5 camp,String rankType){
        List<RankingLists> list;
        RankingLists userRank;
        if ("like".equals(rankType)){
            //取排行榜
            list = campaign2408Dao.getActivityLikeRanking(camp.getStartTime(),camp.getEndTime());
            //取我的最高一条
            userRank = campaign2408Dao.getActivityLikeUserRanking(customerId,camp.getStartTime(),camp.getEndTime());
        } else {
            //取排行榜
            list = campaign2408Dao.getActivityCommentRanking(camp.getStartTime(),camp.getEndTime());
            //取我的最高一条
            userRank = campaign2408Dao.getActivityCommentUserRanking(customerId,camp.getStartTime(),camp.getEndTime());
        }
//        if (ObjectUtils.isEmpty(list)){
//            throw new RuntimeException("获取不到排行榜信息");
//        }
        HashMap res = new HashMap();
        res.put("userRank",userRank);
        res.put("rankList",list);
        return res;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public String getAward(Long uid, String campaignFlag){
        //开启redis锁
        redisLock.setLockName("Campaign2408ServiceImpl:getLottery");
        // requestId 确保每一个请求生成的都不一样，这里使用 uuid，也可以使用其他分布式唯一 id 方案
        String requestId = UUID.randomUUID().toString().replace("-", "");
        int expireTime = 30000;
        redisLock.lock(requestId, expireTime);
        // 开启续命线程，
        Thread watchDog = redisLock.watchDog(expireTime, requestId);
        watchDog.setDaemon(true);
        watchDog.start();
        try {
//            Boolean lock = stringRedisTemplate.opsForValue().setIfAbsent("Campaign2408ServiceImpl-" + campaignFlag, "lock", 1, TimeUnit.SECONDS);
//            if (null == lock || !lock) {
//                throw new RuntimeException("业务繁忙");
//            }

            CampaignH5 camp = campaignH5Service.getValidCampaign(campaignFlag);
            if (ObjectUtils.isEmpty(camp)){
                return ResultUtil.customer(ResultEnum.FAILED, "活动不在有效期");
            }
            //先校验是否有领取机会
            CampaignUserLotteryAwardLogs loggs = campaign2408Dao.getUserLotteryInfo(uid,campaignFlag);
            if (!ObjectUtils.isEmpty(loggs)){
                return ResultUtil.customer(ResultEnum.FAILED, "您已抽奖过,请查看奖品");
            }

            //获取奖品config
            List<CampaignLotteryAward> lotteryAwardList = campaign2408Dao.getActivityAward(campaignFlag);
            if (ObjectUtils.isEmpty(lotteryAwardList)){
                return ResultUtil.customer(ResultEnum.FAILED, "数据错误1");
            }

            //获取微信
            Wechat wechatUser = wechatDao.getWechatInfoByCustomerId(uid);
            if (ObjectUtils.isEmpty(wechatUser) || StringUtil.isNull(wechatUser.getUnionid())) {
                return ResultUtil.customer(ResultEnum.FAILED, "获取不到微信信息");
            }

            //数据定义
            Map jsonContent = new HashMap();

            //这里简述下抽奖逻辑
            //把奖品之和累加 假设当前概率 :  空:100, 积分50, 券10
            //则在他们的和 160中, rand一个数字
            //如果这个数字在 0 - 100之间,则为空,在100-150之间,则为积分,在150-160之间,则为券
            CampaignLotteryAward finalAward = new CampaignLotteryAward();
            Random rand = new Random();
            //设置总抽奖概率
            Integer sumRandNum = 0;
            for (CampaignLotteryAward lotteryAwardInfo : lotteryAwardList) {
                sumRandNum += lotteryAwardInfo.getLotteryProbability();
                jsonContent.put(lotteryAwardInfo.getId(),lotteryAwardInfo.getLotteryProbability());
            }

            //设置当前概率
            Integer nowRand = 0;
            //开始随机
            int randNum;
            if (sumRandNum > 1){
                // rand是从0开始的, 手动+1
                randNum = rand.nextInt(sumRandNum) + 1;
            }else {
                // rand是从0开始的, 手动+1
                randNum = 1;
            }

            for (CampaignLotteryAward sortLotteryAwardInfo : lotteryAwardList) {
                //正常抽奖概率
                nowRand += sortLotteryAwardInfo.getLotteryProbability();
                //正常抽奖逻辑
                if (nowRand >= randNum){
                    //先验证一下最大数量
                    CampaignMaxLotteryConfig maxConfig = campaign2408Dao.getLotteryMaxConfig(sortLotteryAwardInfo.getId(),sortLotteryAwardInfo.getCampaignFlag());
                    //先验证上限
                    if (!ObjectUtils.isEmpty(maxConfig)){
                        //获取今天开始结束
                        Date startTime = DateUtil.getFirstSecondOfDay(new Date());
                        Date endTime = DateUtil.getLastSecondOfDay(new Date());
                        Integer dayNum = campaign2408Dao.getLotteryNumByTime(sortLotteryAwardInfo.getId(),sortLotteryAwardInfo.getCampaignFlag(),startTime,endTime);
                        if (dayNum >= maxConfig.getDayMax()){
                            continue;
                        }
                        //在判断这段时间内最大
                        Integer allNum = campaign2408Dao.getLotteryNumByTime(sortLotteryAwardInfo.getId(),sortLotteryAwardInfo.getCampaignFlag(),maxConfig.getStartTime(),maxConfig.getEndTime());
                        if (allNum >= maxConfig.getAllMax()){
                            continue;
                        }
                    }
                    //最终奖品
                    finalAward = sortLotteryAwardInfo;
                    break;
                }
            }
            //!!!这里已经得到了最终奖品finalAward!!!
            if (ObjectUtils.isEmpty(finalAward)){
                return ResultUtil.customer(ResultEnum.FAILED, "数据错误2");
            }
            //先扣库存
            Integer isResidue = campaign2408Dao.lotteryAwardResidueInventoryById(finalAward.getId());
            if (isResidue <= 0){
                return ResultUtil.customer(ResultEnum.FAILED, "数据错误3");
            }

            //再给奖励
            if (finalAward.getType() == 0){
                //谢谢参与
            }else if(finalAward.getType() == 1){
                //发币
                PointTransaction pointTransaction = new PointTransaction();
                pointTransaction.setCustomerId(uid);
                pointTransaction.setPointTypeId(CBTypeEnum.CAMPAIGN2408.getId());
                pointTransaction.setPoints(finalAward.getCoinNum());
                pointTransaction.setRemainingPoints(finalAward.getCoinNum());
                pointTransaction.setStartTime(new Date());
                pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(new Date()));
                pointTransaction.setForeignId(CBTypeEnum.CAMPAIGN2408.getId());
                pointTransaction.setForeignMasterId(camp.getId());
                pointTransaction.setForeignDetailId(0L);
                pointDao.insertPointTransaction(pointTransaction);
            }else if(finalAward.getType() == 2){
                //发券
                Result drawResult = outsideService.getCoupon(wechatUser.getUnionid(), finalAward.getCouponId());
                if (0 != drawResult.getCode()) {
                    return ResultUtil.customer(ResultEnum.FAILED, "发放礼券失败");
                }
            } else {
                return ResultUtil.customer(ResultEnum.FAILED, "数据错误4");
            }
            //简单做一个记录,以便后期校验
            jsonContent.put("randNum",randNum);
            jsonContent.put("nowRand",nowRand);
            jsonContent.put("sumRandNum",sumRandNum);
            //发放成功,数据留存
            Integer issuccess = campaign2408Dao.setLotteryAward(uid,campaignFlag,finalAward.getId(),finalAward.getAwardName(),finalAward.getAwardImg(),finalAward.getType(),finalAward.getCouponId(),finalAward.getCoinNum(),jsonContent.toString());
            if (issuccess <= 0){
                return ResultUtil.customer(ResultEnum.FAILED, "数据错误5");
            }
            //返回数据
            CampaignUserLotteryAwardLogs res = campaign2408Dao.getUserLotteryInfo(uid,campaignFlag);
            return ResultUtil.customer(ResultEnum.SUCCESS, res);
        } catch (Exception e){
            return ResultUtil.customer(ResultEnum.FAILED, e.getMessage());
        } finally {
            watchDog.interrupt();
            redisLock.unlock(requestId);
        }
    }

    @Override
    public Integer setTracking(CampaignTrackingDTO param){
        return campaign2408Dao.setTracking(param.getCampaignCode(),param.getEvent(),param.getEventId(),param.getEventExt(),param.getUnionid());
    }

}
