package cn.slashsoft.clinique.service.mini;

import cn.slashsoft.clinique.domain.campaign.CampaignH5Detail;

/**
 * 验证码
 *
 * <AUTHOR>
 */
public interface SnsService {

    /**
     * 发送短信验证码
     * @param phoneNumber 手机号码
     */
    int sendVerifyCode(String phoneNumber);

    /**
     * 到店取货类订单兑换成功发送短信验证码
     *
     * @param customerId  顾客编号
     * @param areaPlaceId 门店编号
     */
    void exchangePickupSend(long customerId, Long areaPlaceId);

    /**
     * 邮寄类订单兑换成功发送短信验证码
     *
     * @param customerId 顾客编号
     */
    void exchangeExpress(long customerId);

    /**
     * 黄油变粉安瓶级保湿补光申领成功短信
     * @param phoneNumber 手机号码
     * @param store 门店
     */
    void toneup(String phoneNumber, String store);

    /**
     * 302美白镭射瓶微信端申请成功短信
     * @param phoneNumber 手机号码
     * @param store 门店
     */
    void ebci(String phoneNumber, String store);

    /**
     * 302美白镭射瓶非微信端申请成功短信
     * @param phoneNumber 手机号码
     * @param store 门店
     */
    void ebciExternal(String phoneNumber, String store);

    /**
     * 302美白镭射瓶微信端申请成功短信
     * @param phoneNumber 手机号码
     * @param store 门店
     */
    void ms(String phoneNumber, String store);

    /**
     * 302美白镭射瓶非微信端申请成功短信
     * @param phoneNumber 手机号码
     * @param store 门店
     */
    void msExternal(String phoneNumber, String store);

	/**
	 * 302美白镭射瓶非微信端申请成功短信
	 *
	 * @param phoneNumber 手机号码
	 * @param store       门店
	 */
	void ebcfExternal(String phoneNumber, String store);

	void sendMessage(String phoneNumber, CampaignH5Detail campaignH5Detail, Long snsId);
}
