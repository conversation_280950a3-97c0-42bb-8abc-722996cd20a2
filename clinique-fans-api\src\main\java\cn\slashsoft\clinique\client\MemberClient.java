package cn.slashsoft.clinique.client;

import cn.slashsoft.clinique.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 会员中心服务
 *
 * <AUTHOR>
 */
@FeignClient("clinique-member-api")
public interface MemberClient {

    /**
     * 发放优惠券
     *
     * @param unionid          开放平台唯一编号
     * @param couponCampaignId 优惠券活动编号
     * @return 处理结果
     */
    @GetMapping("/open-api/ls1en-set4wer-sd4fa-sd2fe-coupon/receive/{couponCampaignId}/{unionid}")
    String receive(
            @PathVariable("unionid") String unionid,
            @PathVariable("couponCampaignId") long couponCampaignId
    );

    /**
     * 发放优惠券
     * @param unionid 开放平台唯一编号
     * @param couponCampaignId 优惠券活动编号
     * @param storeCode 门店编号
     * @return 处理结果
     */
    @GetMapping("/open-api/ls1en-set4wer-sd4fa-sd2fe-coupon/receive-with-store/{couponCampaignId}/{unionid}/{storeCode}")
    String receiveWithStore(
            @PathVariable("unionid") String unionid,
            @PathVariable("couponCampaignId") long couponCampaignId,
            @PathVariable("storeCode") String storeCode
    );

    /**
     * 发放优惠券-C粉圈兑礼，优惠券相应的时间写死的
     * @param unionid 开放平台唯一编号
     * @param couponCampaignId 优惠券活动编号
     * @param storeCode 门店编号
     * @return 处理结果
     */
    @GetMapping("/open-api/ls1en-set4wer-sd4fa-sd2fe-coupon/receive-with-store-for-c/{couponCampaignId}/{unionid}/{storeCode}")
    String receiveWithStoreForC(
            @PathVariable("unionid") String unionid,
            @PathVariable("couponCampaignId") long couponCampaignId,
            @PathVariable("storeCode") String storeCode
    );

}
