package cn.slashsoft.clinique.util;

import javax.servlet.http.HttpServletRequest;

/**
 * 服务器工具类
 * <AUTHOR>
 */
public class ServerUtil {

    /**
     * 获取域名 http://www.slashsoft.cn/
     *
     * @param request 请求
     * @return http://www.slashsoft.cn，https://www.slashsoft.cn:8443
     */
    public static String getDomain(HttpServletRequest request) {
        String url = request.getHeader("scheme") + "://" + request.getServerName();
        String port = request.getHeader("port");
        if ("80".equals(port) || "443".equals(port)) {
            return url;
        }
        return url + ":" + port;
    }

    /**
     * 获取域名和路径 http://www.slashsoft.cn/abc
     *
     * @param request 请求
     * @return http://www.slashsoft.cn/abc
     */
    public static String getContextPath(HttpServletRequest request) {
        return getDomain(request) + request.getContextPath();
    }

    /**
     * 获取域名、路径和页面 http://www.slashsoft.cn/abc/def.html
     *
     * @param request 请求
     * @return http://www.slashsoft.cn/abc/def.html
     */
    public static String getServletPath(HttpServletRequest request) {
        return getContextPath(request) + request.getServletPath();
    }

    /**
     * 获取路径、页面 /abc/def.html
     *
     * @param request 请求
     * @return /abc/def.html
     */
    public static String getPath(HttpServletRequest request) {
        return request.getContextPath() + request.getServletPath();
    }

    /**
     * 获取页面 def.html
     *
     * @param request 请求
     * @return def.html
     */
    public static String getPage(HttpServletRequest request) {
        return request.getServletPath();
    }

    /**
     * 获取参数 ?s=123
     *
     * @param request 请求
     * @return ?s=123
     */
    public static String getQuery(HttpServletRequest request) {
        return !StringUtil.isNullOrEmpty(request.getQueryString()) ? "?" + request.getQueryString() : "";
    }

    /**
     * 获取路径、页面和参数 /abc/def.html?s=123
     *
     * @param request 请求
     * @return /abc/def.html?s=123
     */
    public static String getPathAndQuery(HttpServletRequest request) {
        return getPath(request) + getQuery(request);
    }

    /**
     * 获取域名、路径、页面和参数 http://www.slashsoft.cn/abc/def.html?s=123
     *
     * @param request 请求
     * @return http://www.slashsoft.cn/abc/def.html?s=123
     */
    public static String getFullUrl(HttpServletRequest request) {
        return getDomain(request) + getPathAndQuery(request);
    }

    /**
     * 获取域名、路径、页面和参数 http://www.slashsoft.cn/abc/def.html?s=123
     *
     * @param request 请求
     * @return http://www.slashsoft.cn/abc/def.html?s=123
     */
    public static String getFullUrl(HttpServletRequest request, String root) {
        return getDomain(request) + root + getPathAndQuery(request);
    }

    /**
     * 获取静态资源文件位置
     * @return 静态资源文件位置
     */
    public static String getStaticPath(){
        return System.getProperty("user.dir") + "/static";
    }
}
