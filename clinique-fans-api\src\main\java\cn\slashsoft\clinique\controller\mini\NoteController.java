package cn.slashsoft.clinique.controller.mini;

import cn.slashsoft.clinique.domain.mini.*;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.NoteService;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.util.BooleanUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.mini.*;
import io.netty.util.internal.StringUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 活动相关控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class NoteController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;
    @Resource
    private WechatService wechatService;
    @Resource
    private OutsideService outsideService;
    @Resource
    private NoteService noteService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取点赞排名，从高到底
     *
     * @param tagId 标签编号
     * @param size  数量
     * @return 排名列表
     */
    @PostMapping("/note/tag/get-ranking-list/{tag}/{size}")
    public String getRankingList(
            @PathVariable("tag") String tagIds,
            @PathVariable("size") int size
    ) {
        boolean status = BooleanUtil.valueOf(stringRedisTemplate.opsForValue().get("NOTE_RANKING_REAL_TIME_STATUS"));
        if (status) {
            return ResultUtil.customer(ResultEnum.SUCCESS, noteService.getRankingList(tagIds, size));
        } else {
            return ResultUtil.customer(ResultEnum.SUCCESS, noteService.getRankingListFromCache(tagIds, size));
        }
    }

    /**
     * 获取标签列表
     *
     * @return 标签列表
     * @request key 搜索关键词
     * @request type 搜索类型
     * @request top - yes or no 是否返回置顶，当此参数不为no时，其他两个参数无效
     */
    @PostMapping("/note/tag/get-data")
    public String getTagList(@RequestBody TagSearch tagSearch) {
        List<NoteTag> noteTagList = null;

        // 获取缓存中的顾客编号
        Long customerId = 0L;
        if (request.getAttribute("customerId") != null) {
            customerId = (Long) request.getAttribute("customerId");
        }

        if (tagSearch.getTop().equals("no")) {
            if (customerId.intValue() != 0) {
                NoteTagSearchLog log = new NoteTagSearchLog();
                log.setCustomerId(customerId);
                log.setTagType(tagSearch.getType());
                log.setTagTitle(tagSearch.getKey());
                log.setTagId(0L);
                noteService.insertTagSearchLog(log);
            }

            noteTagList = noteService.getTagList(tagSearch.getKey(), tagSearch.getType());
        } else if (tagSearch.getTop().equals("index")) {
            noteTagList = noteService.getIndexTopTagList(tagSearch.getType());
        } else if (tagSearch.getTop().equals("search")) {
            noteTagList = noteService.getSearchTopTagList();
        } else {
            noteTagList = noteService.getTopTagList();
        }

        // 获取缓存中的顾客编号
        //long customerId = (long) request.getAttribute("customerId");
        //String phoneNumber = customerService.getCustomerPhoneNumberById(customerId);

        NoteTagVo noteTagVo = new NoteTagVo();
        noteTagVo.setTagList(noteTagList);
        noteTagVo.setTagGroupList(noteService.getNoteTagGroupList());

        return ResultUtil.customer(ResultEnum.SUCCESS, noteTagVo);
    }

    /**
     * 获取笔记列表
     *
     * @return 笔记列表
     * @request type 搜索类型
     */
    @PostMapping("/note/get-data/{group}/{tag}/{page}/{size}")
    public String getNoteList(
            @PathVariable("group") int group,
            @PathVariable("tag") Long tagId,
            @PathVariable("page") int page,
            @PathVariable("size") int pageSize,
            @RequestParam(value = "sort", required = false) String sort,
            @RequestParam(value = "sign", required = false, defaultValue = "0") String sign) {
        // 获取缓存中的顾客编号
        Long customerId = 0L;
        if (request.getAttribute("customerId") != null) {
            customerId = (Long) request.getAttribute("customerId");
        }
        int all = 1;
        if (tagId.intValue() < 0) {
            all = noteService.getKocCount();
        } else {
            if(1 == group){
                all = noteService.getNoteCountByGroup(tagId);
            }
            else{
                all = noteService.getNoteCount(tagId);
            }
        }

        NoteListVo noteListVo = new NoteListVo();

        int pages = all / pageSize;
        if (all % pageSize > 0) {
            pages++;
        }
        int intSign = 0;
        try {
            if (!StringUtil.isNullOrEmpty(sign)) {
                intSign = Integer.getInteger(sign).intValue();
            }
        } catch (Exception e) {

        }

        if (page > pages) {
            noteListVo.setNoteList(new ArrayList<Note>());
        } else {
            List<Note> noteList = new ArrayList<Note>();
            if (tagId.intValue() < 0) {
                noteList = noteService.getKocList(customerId, page, pageSize);
            } else {
                if(1 == group){
                    noteList = noteService.getNoteListByGroup(tagId, customerId, page, pageSize, sort, intSign);
                }
                else{
                    noteList = noteService.getNoteList(tagId, customerId, page, pageSize, sort, intSign);
                }

            }
            noteListVo.setNoteList(noteList);
        }

        //List<CIndexBanner> ci = noteService.GetCindexBanner();
        //noteListVo.setBannerList(ci);

        noteListVo.setPage(page);
        noteListVo.setAllCount(all);
        noteListVo.setPageSize(pageSize);

        return ResultUtil.customer(ResultEnum.SUCCESS, noteListVo);
    }

    /**
     * 获取笔记列表
     *
     * @return banner列表
     * @request type 搜索类型
     */
    @PostMapping("/note/get-banner")
    public String getNoteIndexBannerList() {
        NoteListVo noteListVo = new NoteListVo();
        noteListVo.setWindow(null);
        List<CIndexBanner> ci = noteService.GetCindexBanner();
        noteListVo.setBannerList(ci);
        return ResultUtil.customer(ResultEnum.SUCCESS, noteListVo);
    }

    /**
     * 获取用户笔记列表
     *
     * @return 标签列表
     * @request type 搜索类型
     */
    @PostMapping("/note/my/get-data")
    public String getMyNoteList() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        if (customerId == 0) {
            return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
        }
        List<Note> noteList = noteService.getCustomerNoteList(customerId, 0);

        NoteListVo noteListVo = new NoteListVo();
        noteListVo.setNoteList(noteList);

        return ResultUtil.customer(ResultEnum.SUCCESS, noteListVo);
    }

    /**
     * 获取用户拔草笔记列表
     *
     * @return 标签列表
     * @request type 搜索类型
     */
    @PostMapping("/note/mylike/get-data")
    public String getMyLikeNoteList() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        if (customerId == 0) {
            return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
        }
        List<Note> noteList = noteService.getCustomerLikeNoteList(customerId, 0);

        NoteListVo noteListVo = new NoteListVo();
        noteListVo.setNoteList(noteList);

        return ResultUtil.customer(ResultEnum.SUCCESS, noteListVo);
    }

    /**
     * 拔草笔记
     *
     * @return 影响行数
     * @request id 笔记id
     */
    @PostMapping("/note/like/{id}")
    public String NoteLike(@PathVariable("id") Long Id,
                           @RequestParam(value = "type", required = false) String noteOrKoc) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        if (customerId == 0) {
            return ResultUtil.customer(ResultEnum.UNREGISTER);
        }

        NoteLikeLog log = new NoteLikeLog();
        log.setCustomerId(customerId);
        log.setNoteId(Id);
        if ("note".equals(noteOrKoc)) {
            return ResultUtil.customer(ResultEnum.SUCCESS, noteService.insertLikeLog(log));
        } else {
            return ResultUtil.customer(ResultEnum.SUCCESS, noteService.insertKocLikeLog(log));
        }
    }

    /**
     * 阅读笔记
     *
     * @return 影响行数
     * @request id 笔记id
     */
    @PostMapping("/note/read/{id}")
    public String NoteRead(
            @PathVariable("id") Long Id,
            @RequestParam(value = "openid", required = false) String shareOpenId) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        if (customerId == 0) {
            return ResultUtil.customer(ResultEnum.SUCCESS, false);
        }

        NoteReadLog log = new NoteReadLog();
        log.setCustomerId(customerId);
        log.setNoteId(Id);
        return ResultUtil.customer(ResultEnum.SUCCESS, noteService.insertReadLog(log, shareOpenId));
    }

    /**
     * 删除拔草
     *
     * @return 影响行数
     * @request id 笔记id
     */
    @PostMapping("/note/unlike/{id}")
    public String NoteNotLike(@PathVariable("id") Long Id,
                              @RequestParam(value = "type", required = false) String noteOrKoc) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        if (customerId == 0) {
            return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
        }

        NoteLikeLog log = new NoteLikeLog();
        log.setCustomerId(customerId);
        log.setNoteId(Id);
        if ("note".equals(noteOrKoc)) {
            return ResultUtil.customer(ResultEnum.SUCCESS, noteService.unlikeNote(log));
        } else {
            return ResultUtil.customer(ResultEnum.SUCCESS, noteService.unlikeKoc(log));
        }

    }

    /**
     * 分享笔记日志
     *
     * @return 影响行数
     * @request id 笔记id
     */
    @PostMapping("/note/share/{id}")
    public String NoteShare(@PathVariable("id") Long Id) {

        // 获取缓存中的顾客编号
        Long customerId = (Long) request.getAttribute("customerId");

        if (customerId.intValue() == 0) {
            return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
        }

        NoteShareLog log = new NoteShareLog();
        log.setCustomerId(customerId);

        String shareSign = "";

        log.setShareSign(shareSign);
        log.setNoteId(Id);
        int count = noteService.insertShareLog(log);
        if (count > 0) {
            return ResultUtil.customer(ResultEnum.SUCCESS, shareSign);
        }
        return ResultUtil.customer(ResultEnum.SUCCESS, ResultEnum.FAILED);
    }

    /**
     * 保存位置信息
     *
     * @return 影响行数
     * @request id 笔记id
     */
    @PostMapping("/note/address-log")
    public String NoteAddress(
            @RequestBody NoteAddressBody address
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        //if (StringUtil.isNullOrEmpty(address.getContent()) || address.getNoteId() == 0) {
        //	return ResultUtil.customer(ResultEnum.PARAM_ERROR);
        //}

        if (customerId == 0) {
            return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
        }
        NoteTagAddressLog tagAddress = new NoteTagAddressLog();
        tagAddress.setCustomerId(customerId);
        tagAddress.setLatitude(address.getLatitude());
        tagAddress.setLongitude(address.getLongitude());
        tagAddress.setAddress(address.getAddress());

        return ResultUtil.customer(ResultEnum.SUCCESS, noteService.insertTagAddressLog(tagAddress));
    }

    /**
     * 笔记评论
     *
     * @return 影响行数
     * @request id 笔记id
     */
    @PostMapping("/note/discuss")
    public String NoteDiscuss(@RequestBody NoteDiscussBody discuss) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        if (StringUtil.isNullOrEmpty(discuss.getContent()) || discuss.getNoteId() == 0) {
            return ResultUtil.customer(ResultEnum.PARAM_ERROR);
        }

        if (customerId == 0) {
            return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
        }

        String openid = (String) request.getAttribute("miniOpenid");
        if (!outsideService.securityMsgSecCheck(openid, discuss.getContent())) {
            return ResultUtil.customer(ResultEnum.OTHER, "请不要发布与笔记无关的内容哦");
        }

        NoteDiscuss noteDiscuss = new NoteDiscuss();
        noteDiscuss.setCustomerId(customerId);
        noteDiscuss.setNoteId(discuss.getNoteId());
        noteDiscuss.setContent(discuss.getContent());
        noteDiscuss.setStatus(1);
        List<String> keys = noteService.filterDiscuss(noteDiscuss);
        if (keys.size() == 0) {
            return ResultUtil.customer(ResultEnum.SUCCESS, noteService.insertDiscuss(noteDiscuss));
        } else {
            return ResultUtil.customer(ResultEnum.PARAM_ERROR, String.join(",", keys));
        }
    }

    /**
     * 获取用户笔记评论列表
     *
     * @return 标签列表
     * @request type 搜索类型
     */
    @PostMapping("/note/mydiscuss/get-data")
    public String getMyNoteDiscussList() {
        // 获取缓存中的顾客编号
        Long customerId = (long) request.getAttribute("customerId");
        if (customerId.intValue() == 0) {
            return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
        }
        List<NoteDiscussMy> list = noteService.getCustomerDiscussList(customerId);


        return ResultUtil.customer(ResultEnum.SUCCESS, list);
    }

    /**
     * 获取笔记评论列表
     *
     * @return 标签列表
     * @request type 搜索类型
     */
    @PostMapping("/note/discuss-data/{id}/{page}/{pageSize}")
    public String getNoteDiscussList(
            @PathVariable("id") Long Id,
            @PathVariable("page") int page,
            @PathVariable("pageSize") int pageSize) {

        if (Id == null || Id.intValue() == 0) {
            return ResultUtil.customer(ResultEnum.PARAM_ERROR);
        }


        // 获取缓存中的顾客编号
        //Long customerId = 0L ;
        //if(request.getAttribute("customerId") != null) {
        //	customerId =(Long) request.getAttribute("customerId");
        //}
        int allCount = noteService.getNoteDiscussCount(Id);
        List<NoteDiscuss> list = noteService.getNoteDiscussList(Id, page, pageSize);

        NoteDiscussListVo nv = new NoteDiscussListVo();
        nv.setAllCount(allCount);
        nv.setPage(page);
        nv.setPageSize(pageSize);
        nv.setNoteDiscussList(list);

        return ResultUtil.customer(ResultEnum.SUCCESS, nv);
    }

    /**
     * 获取笔记
     *
     * @return 影响行数
     * @request id 笔记id
     */
    @PostMapping("/note/detail/{id}")
    public String getNote(@PathVariable("id") Long Id,
                          @RequestParam(value = "type", required = false) String noteOrKoc) {

        // 获取缓存中的顾客编号
        Long customerId = 0L;
        if (request.getAttribute("customerId") != null) {
            customerId = (Long) request.getAttribute("customerId");
        }
        ;

        Note note = null;
        if ("note".equals(noteOrKoc)) {
            note = noteService.getNote(Id, customerId);
        } else {
            note = noteService.getKoc(Id, customerId);
        }

        if (note == null) {
            note = new Note();
            note.setId(0L);
        }

        if (0 < customerId) {
            Wechat wechat = wechatService.getWechatInfoByCustomerId(customerId);
            note.setMyAvatarUrl(wechat.getAvatarUrl());
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, note);

    }

    /**
     * 保存笔记
     *
     * @return 影响条数
     * @request title
     * @request content
     * @request photos
     */
    @PostMapping("/note/save")
    public String saveNote(@RequestBody NoteSave noteSave) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        if (customerId == 0) {
            return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
        }

        Note note = new Note();
        note.setTitle(noteSave.getTitle());
        note.setContent(noteSave.getContent());
        note.setCustomerId(customerId);

        Long result = (long) 0;
        Long noteId = noteSave.getId();
        String message = "insert Fail: " + noteId;
        if (noteId != null && noteId > 0) {
            Note note1 = noteService.getNoteById(noteId, customerId);
            if(note1==null){
                return ResultUtil.customer(ResultEnum.FAILED,"编辑失败");
            }

            message = " update Fail: " + noteId;
            note.setId(noteId);
            result = noteService.updateNote(note);
            if (result == 0) {
                return ResultUtil.customer(ResultEnum.FAILED, message);
            } else {
                noteService.delPhotosByNote(noteId);
                noteService.delPhotoTagsByNote(noteId);
            }
        } else {
            result = noteService.insertNote(note);
            if (result == 0) {
                return ResultUtil.customer(ResultEnum.FAILED, message);
            }
            noteId = note.getId();
            noteService.insertCamNote(note.getCustomerId(),note.getTitle(),note.getContent(),noteId,note.getStatus());

        }

        ArrayList<NotePhoto> photos = noteSave.getPhotos();
        for (NotePhoto photo : photos) {
            photo.setNoteId(noteId);
            Long photoResult = noteService.insertPhoto(photo);
            if (photoResult == 0) {
                message = " photo add Fail " + photo.getPhoto();
                return ResultUtil.customer(ResultEnum.FAILED, message);
            }
            ArrayList<NotePhotoTag> tags = photo.getTags();
            for (NotePhotoTag tag : tags) {
                tag.setNoteId(noteId);
                tag.setPhotoId(photo.getId());
                noteService.insertNotePhotoTag(tag);
            }
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, noteId);
    }

    /**
     * 删除笔记
     *
     * @return 影响行数
     * @request id 笔记id
     */
    @PostMapping("/note/del/{id}")
    public String NoteDel(@PathVariable("id") Long Id) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        if (customerId == 0) {
            return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, noteService.delNote(Id, customerId));
    }
}
