package cn.slashsoft.clinique.controller.campaign;

import java.net.URLEncoder;
import java.util.Date;
import java.util.logging.Logger;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.slashsoft.clinique.service.campaign.CampaignEbciTmallClubService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import cn.slashsoft.clinique.domain.campaign.CampaignEbciDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciLog;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciStock;
import cn.slashsoft.clinique.service.campaign.CampaignEbciService;
import cn.slashsoft.clinique.service.mini.SnsService;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.util.CookieUtil;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.ServerUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.util.VerifyUtil;

/**
 * 302美白镭射瓶
 *
 * <AUTHOR> Berg
 * 
 */
@Controller
@RequestMapping("/external")
public class CampaignEbciAdController {
    @Value("${wechat.official.authorize}")
    private String urlAuthorize;

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;

    private final Logger logger = Logger.getLogger( CampaignEbciAdController.class.getName());
    @Resource
    private HttpServletRequest request;

    @Resource
    private HttpServletResponse response;

    @Resource
    private CampaignEbciService campaignEbciService;
    
    @Resource
    private SnsService snsService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private OutsideService outsideService;

    @Resource
    private CampaignEbciTmallClubService campaignEbciTmallClubService;

   
    //-----------campaign-ebci-2020---------------------------------------------------------------------------------------------------

    @GetMapping("/campaign-ebci-2020--/lauch/{source}.html")
    public void auth(
            @PathVariable("source") String source,
            @RequestParam(value = "gdt_vid", required = false) String clickId) {
    	try {
    		String click_id_url = "";
    		if(!StringUtil.isNullOrEmpty(clickId)) {
    			click_id_url ="?gdt_vid="+ clickId ;
    		}
	    	request.getSession().setAttribute(
	    			"officialOauthPath",
	    			ServerUtil.getDomain(request) + "/fans/external/campaign-ebci-2020/" + source + ".html" + click_id_url);
	        String redirectUri = URLEncoder.encode(
	        		ServerUtil.getDomain(request) + "/fans/official/auth" + ServerUtil.getQuery(request),
	        		"UTF-8");
	        response.sendRedirect(urlAuthorize + redirectUri);
    	}catch(Exception e) {

    	}
    }
    @GetMapping("/campaign-ebci-2020--/{source}.html")
    public String index(
            @PathVariable("source") String source,
            @RequestParam(value = "gdt_vid", required = false) String clickId,
            Model model
    ) {

       // Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciStartTime"));
       // Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciEndTime"));
        Date endTime;
        if (source.startsWith("tmall-club-")) {
            endTime = DateUtil.valueOf("2020-09-20 23:59:59");
        } else {
            endTime = DateUtil.valueOf("2020-10-09 23:59:59");
            endTime = DateUtil.valueOf("2020-10-09 10:59:59");
        }
    	//Date endTime = DateUtil.valueOf("2020-08-23 23:59:59");
       // if(DateUtil.laterThanNow(startTime)){
       //     model.addAttribute("notStart", true);
       // }
       // else{
            model.addAttribute("notStart", false);
      //  }

        if(DateUtil.earlierThanNow(endTime)){
            model.addAttribute("isEnd", true);
        }
        else{
            model.addAttribute("isEnd", false);
        }

        String phoneNumber = CookieUtil.getCookie("phoneNumber", request);

        CampaignEbciDetail campaignEbciDetail = null;
        // 读取是否填写过
        if (!StringUtil.isNullOrEmpty(phoneNumber)) {
            model.addAttribute("isApplied", true);
        }

        String sessionId = request.getSession().getId().replace("-", "");

        String openid =  (String) request.getSession().getAttribute("officialOpenid");

        if(StringUtil.isNullOrEmpty(openid)) {
        	try {
        		String click_id_url = "";
        		if(!StringUtil.isNullOrEmpty(clickId)) {
        			click_id_url ="?gdt_vid="+ clickId ;
        		}
        		response.sendRedirect(ServerUtil.getDomain(request) +"/fans/external/campaign-ebci-2020/lauch/"+source+".html" + click_id_url);
        	}catch(Exception e) {

        	}
	        return "";
        }


        // 保存日志
        CampaignEbciLog campaignEbciLog = new CampaignEbciLog();
        campaignEbciLog.setType((short) 3);
        campaignEbciLog.setUniqueId(openid);
        campaignEbciLog.setSource(source);
        campaignEbciLog.setClickId(StringUtil.isNullOrEmpty(clickId)?"":clickId);
        campaignEbciLog.setPage("index");
        campaignEbciService.insertLog(campaignEbciLog);

        model.addAttribute("campaignEbciDetail", campaignEbciDetail);
        model.addAttribute("serviceName", "fans/external");
        model.addAttribute("clickId", StringUtil.isNullOrEmpty(clickId)?"":clickId);
        model.addAttribute("source", source);
            model.addAttribute("staticDomain", staticDomain);
        if (source.startsWith("tmall-club-")) {
            return "w/fans/official/ebci/external/clubindex";
        }
        else {
            return "w/fans/official/ebci/external/index";
        }
    }

    @GetMapping("/campaign-ebci-verify-code-2020--/{phoneNumber}")
    @ResponseBody
    public String getVerifyCode(
            @PathVariable("phoneNumber") String phoneNumber
    ) {
        return "{\"code\":" + snsService.sendVerifyCode(phoneNumber) + "}";
    }

    @PostMapping("/campaign-ebci-submit-2020/{source}.html")
    @ResponseBody
    public String submit(
            @PathVariable("source") String source,
            @RequestParam(value = "gdt_vid", required = false) String clickId,
            @RequestParam("formName") String name,
            @RequestParam("formPhoneNumber") String phoneNumber,
            @RequestParam("formVerifyCode") String verifyCode,
            @RequestParam("formCity") String city,
            @RequestParam("formStore") String store
    ) {

        //Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciStartTime"));
        //Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciEndTime"));

       // if(DateUtil.laterThanNow(startTime)){
       //     return "{\"code\":9,\"message\":\"活动未开始\"}";
       // }

        Date endTime;
        if (source.startsWith("tmall-club-")) {
            endTime = DateUtil.valueOf("2020-09-20 23:59:59");
        } else {
            endTime = DateUtil.valueOf("2020-10-09 23:59:59");
        }
//    	Date endTime = DateUtil.valueOf("2020-10-09 23:59:59");
    	//Date endTime = DateUtil.valueOf("2020-08-23 23:59:59");
        if(DateUtil.earlierThanNow(endTime)){
            return "{\"code\":8,\"message\":\"活动已结束\"}";
        }

        if (!VerifyUtil.required(name)) {
            return "{\"code\":9,\"message\":\"请输入姓名\"}";
        }

        if (!VerifyUtil.required(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.isPhoneNumber(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.required(verifyCode)) {
            return "{\"code\":9,\"message\":\"请输入验证码\"}";
        }

        if (!VerifyUtil.isVerifyCode(verifyCode)) {
            return "{\"code\":9,\"message\":\"验证码格式错误\"}";
        }

        if (!VerifyUtil.required(city)) {
            return "{\"code\":9,\"message\":\"请选择城市\"}";
        }

        if (!VerifyUtil.required(store)) {
            return "{\"code\":9,\"message\":\"请选择柜台\"}";
        }

        // 验证手机是否一致
        String sessionPhoneNumber = (String) request.getSession().getAttribute("verifyPhoneNumber");
        if (!phoneNumber.equals(sessionPhoneNumber)) {
            return "{\"code\":9,\"message\":\"请重新获取验证码\"}";
        }

        // 验证是否正确
        String sessionVerifyCode = (String) request.getSession().getAttribute("verifyCode");
        if (!verifyCode.equals(sessionVerifyCode)) {
            return "{\"code\":9,\"message\":\"验证码错误\"}";
        }

        // 验证码是否过期
        Date sessionVerifyTime = DateUtil.valueOf((String) request.getSession().getAttribute("verifyTime"));
        if (DateUtil.earlierThanNow(sessionVerifyTime)) {
            return "{\"code\":9,\"message\":\"验证码已过期\"}";
        }

        String sessionId = request.getSession().getId().replace("-", "");
        String openid =  (String) request.getSession().getAttribute("officialOpenid");


        // 申领
        CampaignEbciDetail campaignEbciDetail = new CampaignEbciDetail();
        campaignEbciDetail.setType((short) 3);
        campaignEbciDetail.setUniqueId(openid);
        campaignEbciDetail.setName(name);
        campaignEbciDetail.setPhoneNumber(phoneNumber);
        campaignEbciDetail.setCity(city);
        campaignEbciDetail.setStore(store);
        campaignEbciDetail.setSource(source);

        CampaignEbciStock stock = campaignEbciService.getByStoreName(campaignEbciDetail);
        if(stock != null) {
        	campaignEbciDetail.setStoreId(stock.getStoreId());
        }else {
        	campaignEbciDetail.setStoreId("0");
        }

        Integer ret;
        if (source.startsWith("tmall-club-")) {
            ret = campaignEbciTmallClubService.submit(campaignEbciDetail);
        } else {
            ret = campaignEbciService.submit(campaignEbciDetail);
        }
        // 0:成功，1：手机号码已经领过了，2：库存不足
        switch (ret) {
            case 0:
                if (!source.startsWith("tmall-club-")) snsService.ebciExternal(phoneNumber, store);
                CookieUtil.addCookie("phoneNumber", phoneNumber, response);

                if(!StringUtil.isNullOrEmpty(clickId)){
                    outsideService.qqMarketing(
                            ServerUtil.getDomain(request) +"/fans/external/campaign-ebci-2020/" + source + ".html",
                    		clickId,
                    		name,
                    		phoneNumber,
                    		city,
                    		store
                    );
                }
                return "{\"code\":0}";
            case 1:
                return "{\"code\":1}";
            case 2:
                return "{\"code\":2}";
            default:
                return "{\"code\":3}";
        }
    }

    //-----------campaign-2020---------------------------------------------------------------------------------------------------

    @GetMapping("/campaign-2020--/lauch/{source}.html")
    public void tmpauth(
            @PathVariable("source") String source) {
    	try {
	    	request.getSession().setAttribute(
	    			"officialOauthPath",
	    			ServerUtil.getDomain(request) + "/fans/external/campaign-2020/" + source + ".html");
	        String redirectUri = URLEncoder.encode(
	        		ServerUtil.getDomain(request) + "/fans/official/auth" + ServerUtil.getQuery(request),
	        		"UTF-8");
	        response.sendRedirect(urlAuthorize + redirectUri);
    	}catch(Exception e) {

    	}
    }
    @GetMapping("/campaign-2020--/{source}.html")
    public String tmpindex(
            @PathVariable("source") String source,
            Model model
    ) {

        model.addAttribute("isEnd", true);


        String phoneNumber = CookieUtil.getCookie("phoneNumber", request);

        CampaignEbciDetail campaignEbciDetail = null;
        // 读取是否填写过
        if (!StringUtil.isNullOrEmpty(phoneNumber)) {
            model.addAttribute("isApplied", true);
        }

        //String sessionId = request.getSession().getId().replace("-", "");

        String openid =  (String) request.getSession().getAttribute("officialOpenid");

        if(StringUtil.isNullOrEmpty(openid)) {
        	try {
        		response.sendRedirect(ServerUtil.getDomain(request) +"/fans/external/campaign-2020/lauch/"+source+".html");
        	}catch(Exception e) {

        	}
	        return "请从微信登录";
        }

        model.addAttribute("campaignEbciDetail", campaignEbciDetail);
        model.addAttribute("serviceName", "fans/external");
        model.addAttribute("source", source);

        return "w/fans/official/ebci/external/tmpindex";
    }

    @PostMapping("/campaign-submit-2020--/{source}.html")
    @ResponseBody
    public String tmpsubmit(
            @PathVariable("source") String source,
            @RequestParam("formName") String name,
            @RequestParam("formPhoneNumber") String phoneNumber,
            @RequestParam("formVerifyCode") String verifyCode,
            @RequestParam("formCity") String city,
            @RequestParam("formStore") String store
    ) {


        if (!VerifyUtil.required(name)) {
            return "{\"code\":9,\"message\":\"请输入姓名\"}";
        }

        if (!VerifyUtil.required(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.isPhoneNumber(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.required(verifyCode)) {
            return "{\"code\":9,\"message\":\"请输入验证码\"}";
        }

        if (!VerifyUtil.isVerifyCode(verifyCode)) {
            return "{\"code\":9,\"message\":\"验证码格式错误\"}";
        }

        if (!VerifyUtil.required(city)) {
            return "{\"code\":9,\"message\":\"请选择城市\"}";
        }

        if (!VerifyUtil.required(store)) {
            return "{\"code\":9,\"message\":\"请选择柜台\"}";
        }

        // 验证手机是否一致
        String sessionPhoneNumber = (String) request.getSession().getAttribute("verifyPhoneNumber");
        if (!phoneNumber.equals(sessionPhoneNumber)) {
            return "{\"code\":9,\"message\":\"请重新获取验证码\"}";
        }

        // 验证是否正确
        String sessionVerifyCode = (String) request.getSession().getAttribute("verifyCode");
        if (!verifyCode.equals(sessionVerifyCode)) {
            return "{\"code\":9,\"message\":\"验证码错误\"}";
        }

        // 验证码是否过期
        Date sessionVerifyTime = DateUtil.valueOf((String) request.getSession().getAttribute("verifyTime"));
        if (DateUtil.earlierThanNow(sessionVerifyTime)) {
            return "{\"code\":9,\"message\":\"验证码已过期\"}";
        }

        String sessionId = request.getSession().getId().replace("-", "");
        String openid =  (String) request.getSession().getAttribute("officialOpenid");


        CampaignEbciDetail campaignEbciDetail = new CampaignEbciDetail();
        campaignEbciDetail.setType((short) 9);
        campaignEbciDetail.setUniqueId(openid);
        campaignEbciDetail.setName(name);
        campaignEbciDetail.setPhoneNumber(phoneNumber);
        campaignEbciDetail.setCity(city);
        campaignEbciDetail.setStore(store);
        campaignEbciDetail.setSource(source);

        CampaignEbciStock stock = campaignEbciService.getByStoreName(campaignEbciDetail);
        if(stock != null) {
        	campaignEbciDetail.setStoreId(stock.getStoreId());
        }else {
        	campaignEbciDetail.setStoreId("0");
        }


        // 0:成功，1：手机号码已经领过了，2：库存不足
        switch (campaignEbciService.tmpsubmit(campaignEbciDetail)) {
            case 0:
                CookieUtil.addCookie("phoneNumber", phoneNumber, response);

                return "{\"code\":0}";
            case 1:
                return "{\"code\":1}";
            case 2:
                return "{\"code\":2}";
            case 12:
                return "{\"code\":12}";
            case 13:
                return "{\"code\":13}";
            default:
                return "{\"code\":3}";
        }
    }

    /** -- campaign-2020-other -------------------------------*/
    @GetMapping("/campaign-2020-other--/{source}.html")
    public String nowxindex(
            @PathVariable("source") String source,
            Model model
    ) {
        Date endTime;
        if (source.startsWith("tmall-club-")) {
            endTime = DateUtil.valueOf("2020-09-20 23:59:59");
        } else {
            endTime = DateUtil.valueOf("2020-10-09 23:59:59");
        }

        if(DateUtil.earlierThanNow(endTime)){
            model.addAttribute("isEnd", true);
        }
        else{
            model.addAttribute("isEnd", false);
        }

        String phoneNumber = CookieUtil.getCookie("phoneNumber", request);

        CampaignEbciDetail campaignEbciDetail = null;
        // 读取是否填写过
        if (!StringUtil.isNullOrEmpty(phoneNumber)) {
            model.addAttribute("isApplied", true);
        }

        String sessionId = request.getSession().getId().replace("-", "");
        // 保存日志
        CampaignEbciLog campaignEbciLog = new CampaignEbciLog();
        campaignEbciLog.setType((short) 3);
        campaignEbciLog.setUniqueId(sessionId);
        campaignEbciLog.setSource(source);
        campaignEbciLog.setPage("index");
        campaignEbciService.insertLog(campaignEbciLog);


        model.addAttribute("campaignEbciDetail", campaignEbciDetail);
        model.addAttribute("serviceName", "fans/external");
        model.addAttribute("source", source);
            model.addAttribute("staticDomain", staticDomain);
        if (source.startsWith("tmall-club-")) {
            return "w/fans/official/ebci/external/clubxindex";
        }
        else {
            return "w/fans/official/ebci/external/nowxindex";
        }
    }

    @PostMapping("/campaign-submit-other-2020--/{source}.html")
    @ResponseBody
    public String nowxsubmit(
            @PathVariable("source") String source,
            @RequestParam("formName") String name,
            @RequestParam("formPhoneNumber") String phoneNumber,
            @RequestParam("formVerifyCode") String verifyCode,
            @RequestParam("formCity") String city,
            @RequestParam("formStore") String store
    ) {
        Date endTime;
        if (source.startsWith("tmall-club-")) {
            endTime = DateUtil.valueOf("2020-09-20 23:59:59");
        } else {
            endTime = DateUtil.valueOf("2020-10-09 23:59:59");
        }

        if(DateUtil.earlierThanNow(endTime)){
            return "{\"code\":8,\"message\":\"活动已结束\"}";
        }
        if (!VerifyUtil.required(name)) {
            return "{\"code\":9,\"message\":\"请输入姓名\"}";
        }

        if (!VerifyUtil.required(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.isPhoneNumber(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.required(verifyCode)) {
            return "{\"code\":9,\"message\":\"请输入验证码\"}";
        }

        if (!VerifyUtil.isVerifyCode(verifyCode)) {
            return "{\"code\":9,\"message\":\"验证码格式错误\"}";
        }

        if (!VerifyUtil.required(city)) {
            return "{\"code\":9,\"message\":\"请选择城市\"}";
        }

        if (!VerifyUtil.required(store)) {
            return "{\"code\":9,\"message\":\"请选择柜台\"}";
        }

        // 验证手机是否一致
        String sessionPhoneNumber = (String) request.getSession().getAttribute("verifyPhoneNumber");
        if (!phoneNumber.equals(sessionPhoneNumber)) {
            return "{\"code\":9,\"message\":\"请重新获取验证码\"}";
        }

        // 验证是否正确
        String sessionVerifyCode = (String) request.getSession().getAttribute("verifyCode");
        if (!verifyCode.equals(sessionVerifyCode)) {
            return "{\"code\":9,\"message\":\"验证码错误\"}";
        }

        // 验证码是否过期
        Date sessionVerifyTime = DateUtil.valueOf((String) request.getSession().getAttribute("verifyTime"));
        if (DateUtil.earlierThanNow(sessionVerifyTime)) {
            return "{\"code\":9,\"message\":\"验证码已过期\"}";
        }

        String sessionId = request.getSession().getId().replace("-", "");

        CampaignEbciDetail campaignEbciDetail = new CampaignEbciDetail();
        campaignEbciDetail.setType((short) 9);
        campaignEbciDetail.setUniqueId(sessionId);
        campaignEbciDetail.setName(name);
        campaignEbciDetail.setPhoneNumber(phoneNumber);
        campaignEbciDetail.setCity(city);
        campaignEbciDetail.setStore(store);
        campaignEbciDetail.setSource(source);

        CampaignEbciStock stock = campaignEbciService.getByStoreName(campaignEbciDetail);
        if(stock != null) {
        	campaignEbciDetail.setStoreId(stock.getStoreId());
        }else {
        	campaignEbciDetail.setStoreId("0");
        }

        Integer ret;
        if (source.startsWith("tmall-club-")) {
            ret = campaignEbciTmallClubService.submit(campaignEbciDetail);
        } else {
            ret = campaignEbciService.submit(campaignEbciDetail);
        }
        // 0:成功，1：手机号码已经领过了，2：库存不足
        switch (ret) {
            case 0:
                if (!source.startsWith("tmall-club-")) snsService.ebciExternal(phoneNumber, store);
                CookieUtil.addCookie("phoneNumber", phoneNumber, response);

                return "{\"code\":0}";
            case 1:
                return "{\"code\":1}";
            case 2:
                return "{\"code\":2}";
            case 12:
                return "{\"code\":12}";
            case 13:
                return "{\"code\":13}";
            default:
                return "{\"code\":3}";
        }
    }
    //------------------------------------------------------------------------------------------------
    // @GetMapping("/campaign-ebci-launch/{source}.html")
     public String launch(
             @PathVariable("source") String source,
             Model model
     ){

         String phoneNumber = CookieUtil.getCookie("phoneNumber", request);

         // 读取是否填写过
        // if (!StringUtil.isNullOrEmpty(phoneNumber) && 0 < campaignEbciService.hasDetail(phoneNumber)) {
         //    return "redirect:/external/campaign-ebci-result/" + source + ".html";
        // }

         String sessionId = request.getSession().getId().replace("-", "");

         // 保存日志
         CampaignEbciLog campaignEbciLog = new CampaignEbciLog();
         campaignEbciLog.setType((short) 3);
         campaignEbciLog.setUniqueId(sessionId);
         campaignEbciLog.setSource(source);
         campaignEbciLog.setPage("launch");
         campaignEbciService.insertLog(campaignEbciLog);

         model.addAttribute("serviceName", "external");
         model.addAttribute("source", source);
        
             model.addAttribute("staticDomain", staticDomain);
         return "w/fans/official/ebci/launch/index";

     }
    //@GetMapping("/campaign-ebci-result/{source}.html")
    public String result(
            @PathVariable("source") String source,
            Model model
    ) {

        String phoneNumber = CookieUtil.getCookie("phoneNumber", request);

        if(StringUtil.isNullOrEmpty(phoneNumber)){
            return "redirect:/external/campaign-ebci/" + source + ".html";
        }

        // 读取申领信息
        CampaignEbciDetail campaignEbciDetail = campaignEbciService.getDetail(phoneNumber);
        if(null == campaignEbciDetail){
            CookieUtil.removeCookie("phoneNumber", response);
            return "redirect:/external/campaign-ebci/" + source + ".html";
        }

        String sessionId = request.getSession().getId().replace("-", "");

        // 保存日志
        CampaignEbciLog campaignEbciLog = new CampaignEbciLog();
        campaignEbciLog.setType((short) 3);
        campaignEbciLog.setUniqueId(sessionId);
        campaignEbciLog.setSource(source);
        campaignEbciLog.setPage("result");
        campaignEbciService.insertLog(campaignEbciLog);

        model.addAttribute("campaignEbciDetail", campaignEbciDetail);
            model.addAttribute("staticDomain", staticDomain);
        return "w/fans/official/ebci/external/success";

    }

}
