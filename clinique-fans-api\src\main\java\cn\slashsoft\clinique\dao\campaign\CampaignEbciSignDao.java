package cn.slashsoft.clinique.dao.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignEbciSign;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciSignDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciSignLog;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciSignViewLog;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * EBCI 7日打卡活动
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignEbciSignDao {

    /**
     * 写入签到信息
     *
     * @param campaignEbciSign 签到信息
     */
    @Insert("INSERT INTO `campaign_ebci_sign`(" +
            "   `customer_id` " +
            ") " +
            "VALUES (" +
            "   #{customerId}" +
            ")")
    void insertCampaignEbciSign(CampaignEbciSign campaignEbciSign);

    /**
     * 写入签到明细
     *
     * @param campaignEbciSignDetail 签到明细
     */
    @Insert("INSERT INTO `campaign_ebci_sign_detail`(" +
            "   `customer_id`, " +
            "   `rounds`, " +
            "   `level` " +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{rounds}," +
            "   #{level}" +
            ")")
    void insertCampaignEbciSignDetail(CampaignEbciSignDetail campaignEbciSignDetail);

    /**
     * 写入签到日志
     *
     * @param campaignEbciSignLog 签到日志
     */
    @Insert("INSERT INTO `campaign_ebci_sign_log`(" +
            "   `customer_id`, " +
            "   `type`, " +
            "   `content` " +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{type}," +
            "   #{content}" +
            ")")
    void insertCampaignEbciSignLog(CampaignEbciSignLog campaignEbciSignLog);

    /**
     * 写入签到日志
     *
     * @param campaignEbciSignLog 签到日志
     */
    @Insert("INSERT INTO `campaign_ebci_sign_log`(" +
            "   `customer_id`, " +
            "   `type`, " +
            "   `content`," +
            "   `invite_customer_id` " +
            ") " +
            "SELECT " +
            "   `customer_id`," +
            "   #{type}," +
            "   #{content}," +
            "   #{inviteCustomerId} " +
            "FROM " +
            "   `wechat` " +
            "WHERE " +
            "   `wechat_mini_openid`=#{wechatMiniOpenid}")
    void insertCampaignEbciSignLogWithOpenid(CampaignEbciSignLog campaignEbciSignLog);

    /**
     * 获取签到信息
     *
     * @param customerId 顾客编号
     * @return 签到信息
     */
    @Select("SELECT " +
            "   `apply`, " +
            "   `rounds`, " +
            "   `level`, " +
            "   `level_time`, " +
            "   `inviting`, " +
            "   `invite_total` " +
            "FROM " +
            "   `campaign_ebci_sign` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1 ")
    CampaignEbciSign getCampaignEbciSign(long customerId);

    /**
     * 获取签到日志
     * @param customerId 顾客编号
     * @return 签到日志
     */
    @Select("SELECT" +
            "   `l`.`type`," +
            "   `w`.`nick_name`," +
            "   `w`.`avatar_url`," +
            "   `i`.`nick_name` `invite_nick_name`," +
            "   `i`.`avatar_url` `invite_avatar_url`," +
            "   `l`.`content`, " +
            "   `l`.`create_time` " +
            "FROM " +
            "   `campaign_ebci_sign_log` `l` " +
            "       inner join " +
            "   `wechat` `w` " +
            "       on `l`.`customer_id`=`w`.`customer_id` " +
            "       left join " +
            "   `wechat` `i` " +
            "       on `l`.`invite_customer_id`=`i`.`customer_id` " +
            "WHERE " +
            "   `l`.`customer_id`=#{customerId} " +
            "ORDER BY " +
            "   `l`.`id` DESC")
    List<CampaignEbciSignLog> getCampaignEbciSignLog(long customerId);

    /**
     * 签到
     *
     * @param campaignEbciSign 签到信息
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ebci_sign` " +
            "SET " +
            "   `level`=`level`+1," +
            "   `level_time`=NOW()," +
            "   `inviting`=0," +
            "   `invite_total`=0 " +
            "WHERE " +
            "   `customer_id`=#{customerId}" +
            "   AND `level`=#{level}")
    int sign(CampaignEbciSign campaignEbciSign);

    /**
     * 重置
     *
     * @param customerId 顾客编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ebci_sign` " +
            "SET " +
            "   `rounds`=`rounds`+1," +
            "   `level`=0," +
            "   `level_time`=NULL " +
            "WHERE " +
            "   `customer_id`=#{customerId}" +
            "   AND `inviting`=0 " +
            "   AND `invite_total`=0 " +
            "   AND `level`>0 " +
            "   AND `level_time` IS NOT NULL " +
            "   AND DATE_FORMAT(`level_time`,'%Y-%m-%d')<>DATE_FORMAT(NOW(),'%Y-%m-%d')" +
            "   AND DATE_FORMAT(`level_time`,'%Y-%m-%d')<>DATE_FORMAT(DATE_SUB(NOW(),INTERVAL 1 DAY),'%Y-%m-%d')")
    int reset(long customerId);

    /**
     * 邀请
     *
     * @param customerId 顾客编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ebci_sign` " +
            "SET " +
            "   `inviting`=1 " +
            "WHERE " +
            "   `customer_id`=#{customerId}" +
            "   AND `inviting`=0 " +
            "   AND `level`>0 " +
            "   AND `level_time` IS NOT NULL " +
            "   AND DATE_FORMAT(`level_time`,'%Y-%m-%d')<>DATE_FORMAT(NOW(),'%Y-%m-%d')" +
            "   AND DATE_FORMAT(`level_time`,'%Y-%m-%d')<>DATE_FORMAT(DATE_SUB(NOW(),INTERVAL 1 DAY),'%Y-%m-%d')")
    int inviting(long customerId);

    /**
     * 继续
     *
     * @param customerId 顾客编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ebci_sign` " +
            "SET " +
            "   `level_time`=DATE_SUB(NOW(),INTERVAL 1 DAY), " +
            "   `inviting`=0," +
            "   `invite_total`=0 " +
            "WHERE " +
            "   `customer_id`=#{customerId}" +
            "   AND `inviting`=1 " +
            "   AND `invite_total`>0 " +
            "   AND `level`>0 " +
            "   AND `level_time` IS NOT NULL " +
            "   AND DATE_FORMAT(`level_time`,'%Y-%m-%d')<>DATE_FORMAT(NOW(),'%Y-%m-%d')" +
            "   AND DATE_FORMAT(`level_time`,'%Y-%m-%d')<>DATE_FORMAT(DATE_SUB(NOW(),INTERVAL 1 DAY),'%Y-%m-%d')")
    int invited(long customerId);

    /**
     * 申领
     *
     * @param customerId 顾客编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ebci_sign` " +
            "SET " +
            "   `apply`=1 " +
            "WHERE " +
            "   `customer_id`=#{customerId}" +
            "   AND `apply`=0 " +
            "   AND `level`=7 ")
    int apply(long customerId);

    /**
     * 写入访问日志
     *
     * @param campaignEbciSignViewLog 日志
     */
    @Insert("INSERT INTO `campaign_ebci_sign_view_log`(" +
            "   `customer_id`, " +
            "   `source`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{source}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertCampaignEbciSignViewLog(CampaignEbciSignViewLog campaignEbciSignViewLog);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Update("UPDATE " +
            "   `campaign_ebci_sign_view_log` " +
            "SET " +
            "   `end_time`=NOW()," +
            "   `diff_second`=TimeStampDiff(SECOND,`start_time`,NOW()) " +
            "WHERE " +
            "   `id`=#{id}")
    void setCampaignEbciSignViewLog(long id);

    /**
     * 助力
     *
     * @param openid 小程序openid
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ebci_sign` " +
            "SET " +
            "   `invite_total`=`invite_total`+1 " +
            "WHERE " +
            "   `customer_id`=(SELECT `customer_id` FROM `wechat` WHERE `wechat_mini_openid`=#{openid})" +
            "   AND `inviting`=1 ")
    int invite(String openid);

}
