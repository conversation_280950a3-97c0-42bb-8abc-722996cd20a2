package cn.slashsoft.clinique.dao.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignViewLog;
import cn.slashsoft.clinique.domain.campaign.ntf.*;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * NTF
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignNtfDao {

    /**
     * 获取活动信息
     *
     * @param customerId 顾客编号
     * @return 活动信息
     */
    @Select("SELECT " +
            "   `id`, " +
            "   `member_type`, " +
            "   `back`, " +
            "   `draw_times`, " +
            "   `invite_times` " +
            "FROM " +
            "   `campaign_ntf` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    CampaignNtf getInfo(long customerId);

    /**
     * 获取我的抽奖记录
     * @param customerId 顾客编号
     * @return 我的抽奖记录列表
     */
    @Select("SELECT " +
            "   `group`," +
            "   `name`," +
            "   `description`," +
            "   `image_url` " +
            "FROM " +
            "   `campaign_ntf_record` " +
            "WHERE " +
            "   `customer_id`=#{customerId}")
    List<CampaignNtfRecord> getCampaignNtfRecord(long customerId);

    /**
     * 获取被邀请者头像
     * @param miniOpenid 邀请者openid
     * @return 头像
     */
    @Select("SELECT " +
            "   `w`.`avatar_url` " +
            "FROM " +
            "   `campaign_ntf_invite` `c` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `c`.`invitee_mini_openid`=`w`.`wechat_mini_openid` " +
            "WHERE " +
            "   `c`.`inviter_mini_openid`=#{miniOpenid} " +
            "ORDER BY " +
            "   `c`.`id` " +
            "LIMIT 9")
    List<String> getInviteeAvatarUrl(String miniOpenid);

    /**
     * 写入活动信息
     *
     * @param campaignNtf 活动信息
     */
    @Insert("INSERT INTO `campaign_ntf`(" +
            "   `customer_id`," +
            "   `member_type`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{memberType}" +
            ")")
    void insertInfo(CampaignNtf campaignNtf);

    /**
     * 领取回柜礼
     * @param id 活动信息编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ntf` " +
            "SET " +
            "   `back`=1," +
            "   `back_time`=NOW() " +
            "WHERE " +
            "   `id`=#{id} " +
            "   AND `back`=0")
    int back(long id);

    /**
     * 开启盲盒
     * @param id 活动信息编号
     * @param drawTimes 抽奖次数
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ntf` " +
            "SET " +
            "   `draw_times`=`draw_times`+1 " +
            "WHERE " +
            "   `id`=#{id} " +
            "   AND `back`=1 " +
            "   AND `draw_times`*3<=`invite_times` " +
            "   AND `draw_times`=#{drawTimes} " +
            "LIMIT 1")
    int draw(
            @Param("id") long id,
            @Param("drawTimes") short drawTimes
    );

    /**
     * 更新邀请数量
     *
     * @param miniOpenid 小程序唯一编号
     */
    @Update("UPDATE " +
            "   `campaign_ntf` " +
            "SET " +
            "   `invite_times`=`invite_times`+1 " +
            "WHERE " +
            "   `customer_id`=(" +
            "       SELECT " +
            "           `customer_id` " +
            "       FROM " +
            "           `wechat` " +
            "       WHERE " +
            "           `wechat_mini_openid`=#{miniOpenid} " +
            "       LIMIT 1" +
            "   )")
    void invite(String miniOpenid);

    /**
     * 读取回柜礼品
     * @return 回柜礼品
     */
    @Select("SELECT " +
            "   `id`," +
            "   `group`," +
            "   `name`," +
            "   `description`," +
            "   `image_url`," +
            "   `coupon_id`," +
            "   `stocks` " +
            "FROM " +
            "   `campaign_ntf_back_gift` " +
            "LIMIT 1")
    CampaignNtfBackGift getBackGift();

    /**
     * 扣除回柜礼品库存
     * @param id 礼品编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ntf_back_gift` " +
            "SET " +
            "   `stocks`=`stocks`-1," +
            "   `exchange_total`=`exchange_total`+1 " +
            "WHERE " +
            "   `id`=#{id} " +
            "   AND `stocks`>0")
    int updateBackGift(short id);

    /**
     * 读取门店白名单
     * @param id 门店编号
     * @return 是否存在
     */
    @Select("SELECT " +
            "   COUNT(`id`) " +
            "FROM " +
            "   `campaign_ntf_consume_counter` " +
            "WHERE " +
            "   `counter_id`=#{id}")
    int getConsumeCounter(long id);

    /**
     * 读取回柜礼品
     * @return 回柜礼品
     */
    @Select("SELECT " +
            "   `id`," +
            "   `group`," +
            "   `name`," +
            "   `description`," +
            "   `image_url`," +
            "   `coupon_id`," +
            "   `stocks` " +
            "FROM " +
            "   `campaign_ntf_consume_gift` " +
            "LIMIT 1")
    CampaignNtfConsumeGift getConsumeGift();

    /**
     * 扣除回柜礼品库存
     * @param id 礼品编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ntf_consume_gift` " +
            "SET " +
            "   `stocks`=`stocks`-1," +
            "   `exchange_total`=`exchange_total`+1 " +
            "WHERE " +
            "   `id`=#{id} " +
            "   AND `stocks`>0")
    int updateConsumeGift(short id);

    /**
     * 读取抽奖礼品
     *
     * @return 抽奖礼品列表
     */
    @Select("select " +
            "   `id`," +
            "   `type`," +
            "   `group`," +
            "   `name`," +
            "   `description`," +
            "   `image_url`," +
            "   `large_image_url`," +
            "   `coupon_id`," +
            "   `ratio`, " +
            "   `stocks` " +
            "from " +
            "   `campaign_ntf_draw_gift` " +
            "where " +
            "   `stocks`>0")
    List<CampaignNtfDrawGift> getDrawGift();

    /**
     * 扣除抽奖礼品库存
     * @param id 礼品编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ntf_draw_gift` " +
            "SET " +
            "   `stocks`=`stocks`-1," +
            "   `exchange_total`=`exchange_total`+1 " +
            "WHERE " +
            "   `id`=#{id} " +
            "   AND `stocks`>0")
    int updateDrawGift(short id);



    /**
     * 写入抽奖记录
     * @param campaignNtfRecord 抽奖记录
     * @return 影响的行
     */
    @Insert("INSERT INTO `campaign_ntf_record`(" +
            "   `customer_id`, " +
            "   `latitude`, " +
            "   `longitude`, " +
            "   `counter_id`, " +
            "   `distance`, " +
            "   `type`, " +
            "   `gift_id`, " +
            "   `coupon_code`, " +
            "   `group`, " +
            "   `name`, " +
            "   `description`, " +
            "   `image_url`" +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{latitude}, " +
            "   #{longitude}, " +
            "   #{counterId}, " +
            "   #{distance}, " +
            "   #{type}, " +
            "   #{giftId}, " +
            "   #{couponCode}, " +
            "   #{group}, " +
            "   #{name}, " +
            "   #{description}, " +
            "   #{imageUrl} " +
            ")")
    int insertCampaignNtfRecord(CampaignNtfRecord campaignNtfRecord);

    /**
     * 邀请
     * @param inviterMiniOpenid 邀请者
     * @param inviteeMiniOpenid 被邀请者
     */
    @Insert("INSERT INTO `campaign_ntf_invite`(" +
            "   `inviter_mini_openid`, " +
            "   `invitee_mini_openid`" +
            ") " +
            "VALUES (" +
            "   #{inviterMiniOpenid}, " +
            "   #{inviteeMiniOpenid}" +
            ")")
    void insertInvite(
            @Param("inviterMiniOpenid") String inviterMiniOpenid,
            @Param("inviteeMiniOpenid") String inviteeMiniOpenid
    );

    /**
     * 写入访问日志
     *
     * @param campaignViewLog 日志
     */
    @Insert("INSERT INTO `campaign_ntf_view_log`(" +
            "   `customer_id`," +
            "   `source`," +
            "   `page`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{source}," +
            "   #{page}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertViewLog(CampaignViewLog campaignViewLog);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Update("UPDATE " +
            "   `campaign_ntf_view_log` " +
            "SET " +
            "   `end_time`=NOW()," +
            "   `diff_second`=TimeStampDiff(SECOND,`start_time`,NOW()) " +
            "WHERE " +
            "   `id`=#{id} " +
            "LIMIT 1")
    void setViewLog(long id);

}
