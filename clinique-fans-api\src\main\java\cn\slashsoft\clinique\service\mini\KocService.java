package cn.slashsoft.clinique.service.mini;

import java.util.List;

import cn.slashsoft.clinique.domain.campaign.ugc.KocTopDetail;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTopDetailImage;
import cn.slashsoft.clinique.domain.campaign.ugc.KocWinner;

/**
 * Laser Focus 达人榜
 *
 * <AUTHOR>
 */
public interface KocService {


	/**
	 * 获取入选koc TOP的名单
	 *
	 * @return 名单
	 */
	List<KocTopDetail> getKocTopDetailList(Long id);

	/**
	 * 获取入选koc TOP的图片
	 *
	 * @param talentsTopId TOP信息编号
	 * @return 图片
	 */
	List<KocTopDetailImage> getKocTopDetailImageList(Long id);
	List<KocWinner> getWinnerList(Long id);

}
