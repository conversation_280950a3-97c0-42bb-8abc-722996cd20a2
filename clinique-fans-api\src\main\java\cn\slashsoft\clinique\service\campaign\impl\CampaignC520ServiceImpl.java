package cn.slashsoft.clinique.service.campaign.impl;

import cn.slashsoft.clinique.dao.campaign.CampaignC520Dao;
import cn.slashsoft.clinique.domain.campaign.CampaignAllViewLog;
import cn.slashsoft.clinique.domain.campaign.CampaignC520;
import cn.slashsoft.clinique.domain.campaign.CampaignC520Log;
import cn.slashsoft.clinique.service.campaign.CampaignC520Service;
import org.springframework.stereotype.Service;

/**
 * 520活动
 *
 * <AUTHOR>
 */
@Service
public class CampaignC520ServiceImpl implements CampaignC520Service {

    private final CampaignC520Dao campaignC520Dao;

    public CampaignC520ServiceImpl(CampaignC520Dao campaignC520Dao) {
        this.campaignC520Dao = campaignC520Dao;
    }

    /**
     * 获取排除名单
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    @Override
    public int getCampaignC520Exclude(String phoneNumber) {
        return campaignC520Dao.getCampaignC520Exclude(phoneNumber);
    }

    /**
     * 获取520活动信息
     *
     * @param customerId 顾客编号
     * @return 520活动信息
     */
    @Override
    public CampaignC520 getCampaignC520(long customerId) {
        CampaignC520 campaignC520 = campaignC520Dao.getCampaignC520(customerId);
        if (null == campaignC520) {
            campaignC520 = new CampaignC520();
            campaignC520.setCustomerId(customerId);
            campaignC520.setIsApply(false);
            campaignC520.setIsReceive(false);
            campaignC520.setStatus(true);
            campaignC520Dao.insertCampaignC520(campaignC520);
            // 写入操作日志
            CampaignC520Log campaignC520Log = new CampaignC520Log();
            campaignC520Log.setCustomerId(customerId);
            campaignC520Log.setStatus((short) 1);
            campaignC520Log.setContent("创建活动");
            campaignC520Dao.insertCampaignC520Log(campaignC520Log);
        }
        return campaignC520;
    }

    /**
     * 申领
     *
     * @param customerId 顾客编号
     * @return 处理结果
     */
    @Override
    public boolean applyCampaignC520(long customerId) {
        if (0 == campaignC520Dao.applyCampaignC520(customerId)) {
            return false;
        }
        // 写入操作日志
        CampaignC520Log campaignC520Log = new CampaignC520Log();
        campaignC520Log.setCustomerId(customerId);
        campaignC520Log.setStatus((short) 2);
        campaignC520Log.setContent("立即解锁");
        campaignC520Dao.insertCampaignC520Log(campaignC520Log);
        return true;
    }

    /**
     * 核销
     *
     * @param customerId 顾客编号
     * @return 处理结果
     */
    @Override
    public boolean receiveCampaignC520(long customerId) {
        if (0 == campaignC520Dao.receiveCampaignC520(customerId)) {
            return false;
        }
        // 写入操作日志
        CampaignC520Log campaignC520Log = new CampaignC520Log();
        campaignC520Log.setCustomerId(customerId);
        campaignC520Log.setStatus((short) 3);
        campaignC520Log.setContent("核销");
        campaignC520Dao.insertCampaignC520Log(campaignC520Log);
        return true;
    }

    /**
     * 写入访问开始日志
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @param page       页面
     * @return 访问日志编号
     */
    @Override
    public long insertCampaignC520ViewLog(long customerId, String source, String page) {
        CampaignAllViewLog campaignAllViewLog = new CampaignAllViewLog();
        campaignAllViewLog.setCustomerId(customerId);
        campaignAllViewLog.setSource(source);
        campaignAllViewLog.setPage(page);
        campaignC520Dao.insertCampaignC520ViewLog(campaignAllViewLog);
        return campaignAllViewLog.getId();
    }

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Override
    public void setCampaignC520ViewLog(long id) {
        campaignC520Dao.setCampaignC520ViewLog(id);
    }

}
