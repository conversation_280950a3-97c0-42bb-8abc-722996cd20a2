package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignEbciDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciLog;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciStock;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciAD;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciCity;
import cn.slashsoft.clinique.domain.campaign.CampaignTemp;

/**
 * 302美白镭射瓶
 *
 * <AUTHOR>
 */
public interface CampaignEbciService {

    /**
     * 保存日志
     *
     * @param campaignEbciLog 日志
     */
    void insertLog(CampaignEbciLog campaignEbciLog);

    /**
     * 获取手机号码
     *
     * @param unionid 开放平台唯一编号
     * @return 手机号码
     */
    String getPhoneNumberByUnionid(String unionid);

    /**
     * 获取手机号码
     *
     * @param openid 公众号唯一编号
     * @return 手机号码
     */
    String getPhoneNumberByOpenid(String openid);

    /**
     * 获取是否已经审领
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    int hasDetail(String phoneNumber);

    /**
     * 获取是否已经审领
     *
     * @param openid 公众号唯一编号
     * @return 数量
     */
    int hasDetailByOpenid(String openid);

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    CampaignEbciDetail getDetail(String phoneNumber);

    /**
     * 申领
     *
     * @param campaignEbciDetail 资料
     * @return 0:成功，1，帐户已经领过了，2：手机号码已经领过了，3：库存不足
     */
    int submit(CampaignEbciDetail campaignEbciDetail);

    /**
     * 申领
     *
     * @param campaignEbciDetail 资料
     * @return 0:成功，1，帐户已经领过了，2：手机号码已经领过了，3：库存不足
     */
    int tmpsubmit(CampaignEbciDetail campaignEbciDetail);

    /**
     * 更新是否关注
     *
     * @param campaignEbciDetail 申领信息
     */
    void setFollow(CampaignEbciDetail campaignEbciDetail);

    /**
     * 获取审领信息
     * @param openid 公众号唯一编号
     * @return 审领信息
     */
    CampaignEbciDetail getDetailByOpenid(String openid);

    /**
     * 跟据场景值获取门店
     *
     * @param scene 场景值
     * @return 门店
     */
    CampaignEbciStock getByScene(String scene); /**
     * 根据门店名称获取门店状态
    *
    * @param scene 场景值
    * @return 门店
    */
   public CampaignEbciStock getByStoreName(CampaignEbciDetail campaignEbciDetail);
    /**
     * 写入ad信息
     *
     * @param CampaignEbciAD ad
     * @return ad
     */
    void addAD(CampaignEbciAD campaignEbciAD);
    /**
     * 跟据手机号获取ad
     *
     * @param phoneNumber 手机号
     * @return ad
     */
    CampaignEbciAD getADByPhoneNumber(String phoneNumber);
    /**
     * 更新ad信息
     *
     * @param CampaignEbciAD ad
     * @return ad
     */
    void setAD(CampaignEbciAD campaignEbciAD);

    /**
     * 更新核销信息
     *
     * @param campaignEbciDetail 核销信息
     */
    void setReceive(CampaignEbciDetail campaignEbciDetail);

    CampaignEbciCity getEbciCity(String city, long campaign);
    
     
}
