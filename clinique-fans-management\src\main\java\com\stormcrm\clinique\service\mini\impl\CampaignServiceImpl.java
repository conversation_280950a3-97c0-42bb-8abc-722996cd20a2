package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.domain.Campaign;
import com.stormcrm.clinique.service.CampaignService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.vo.Result;
import com.stormcrm.clinique.dao.CampaignDao;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 活动管理
 *
 * <AUTHOR>
 */
@Service
public class CampaignServiceImpl implements CampaignService {

    private final CampaignDao campaignDao;

    public CampaignServiceImpl(CampaignDao campaignDao) {
        this.campaignDao = campaignDao;
    }

    /**
     * 查询所有活动
     *
     * @return 活动列表
     */
    @Override
    public List<Campaign> getAll() {
        return campaignDao.getAll();
    }

    /**
     * 查询所有活动列表的记录数
     *
     * @return 记录数
     */
    @Override
    public int getAllCount() {

        return campaignDao.getAllCount();
    }

    /**
     * 查询所有活动带分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索
     * @param status        状态
     * @return 活动列表分页
     */
    @Override
    public List<Campaign> getPage(int page, int perpage, String generalSearch, Boolean status) {
        return campaignDao.getPage((page - 1) * perpage, perpage, generalSearch, status);
    }

    /**
     * 查询所有活动带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    @Override
    public int getPageCount(String generalSearch, Boolean status) {
        return campaignDao.getPageCount(generalSearch, status);
    }

    /**
     * 查询
     *
     * @param id 自动编号
     * @return 胸章信息
     */
    @Override
    public Campaign getById(long id) {
        return campaignDao.getById(id);
    }

    /**
     * 保存
     *
     * @param campaign 信息
     * @return 影响的行数
     */
    @Override
    public Result save(Campaign campaign) {
        campaignDao.save(campaign);
        return ResultUtil.success("增加成功!");
    }

    /**
     * 更新
     *
     * @param campaign 信息
     * @return 影响的行数
     */
    @Override
    public Result update(Campaign campaign) {
        campaignDao.update(campaign);
        return ResultUtil.success("编辑成功!");
    }

    /**
     * 上架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Override
    public int upper(long id) {
        return campaignDao.upper(id);
    }

    /**
     * 下架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Override
    public int lower(long id) {
        return campaignDao.lower(id);
    }

    /**
     * 逻辑删除
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Override
    public int del(long id) {
        return campaignDao.del(id);
    }

}
