package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.Video;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
@Mapper
public interface VideoDao {
    /**
     * 保存
     *
     * @param video Video obj
     */
    @Insert("INSERT INTO `video`(" +
            "   `title`, " +
            "   `brand_logo`, " +
            "   `brand_name`, " +
            "   `file_url`, " +
            "   `cover_image`, " +
            "   `type`, " +
            "   `status` " +
            ") " +
            "VALUES (" +
            "   #{title}, " +
            "   #{brandLogo}, " +
            "   #{brandName}, " +
            "   #{fileUrl}, " +
            "   #{coverImage}, " +
            "   #{type}, " +
            "   #{status} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertVideo(Video video);

    /**
     * 删除视频
     *
     * @param id 视频id
     */
    @Delete("DELETE FROM `video`" +
            " WHERE " +
            " `id`=#{id} "
    )
    int delVideo(long id);

    /**
     * 获取视频列表
     *
     * @param start 起始记录数
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `video` " +
            "WHERE " +
            "   `status`= 1 " +
            "LIMIT #{start}, 30")
    ArrayList<Video> getVideoList(int start);

    /**
     * 获取视频详情
     *
     * @param id 视频id
     */
    @Select("SELECT " +
            "  a.* " +
            "FROM " +
            "   `video` as a " +
            "WHERE " +
            "   a.`id`=#{id} " +
            "LIMIT 1")
    Video getVideo(Long id);
    
    /**
     * 查询所有 视频 -分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param type        状态
     * @return 活动列表
     */
    @SelectProvider(type = VideoProvider.class, method = "getPage")
    List<Video> getPage(
            @Param("pageIndex") int pageIndex,
            @Param("pageSize") int pageSize,
            @Param("generalSearch") String generalSearch,
            @Param("type") Short type
    );

    /**
     * 查询所有 视频 带分页的记录数
     *
     * @param generalSearch 搜索
     * @param type        状态
     * @return 记录数
     */
    @SelectProvider(type = VideoProvider.class, method = "getPageCount")
    int getPageCount(
            @Param("generalSearch") String generalSearch,
            @Param("type") Short type
    );

    /**
     * 修改
     *
     * @param video 视频obj
     */
    @UpdateProvider(type = VideoProvider.class, method = "update")
    int updateVideo(Video video);

}
