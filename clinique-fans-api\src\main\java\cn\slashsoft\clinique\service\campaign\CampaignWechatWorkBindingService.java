package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignViewLog;
import cn.slashsoft.clinique.domain.campaign.CampaignWechatWorkBinding;
import cn.slashsoft.clinique.domain.campaign.CampaignWechatWorkBindingCounter;
import cn.slashsoft.clinique.domain.campaign.CampaignWechatWorkBindingPickup;
import cn.slashsoft.clinique.vo.Result;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业微信绑定活动
 *
 * <AUTHOR>
 */
public interface CampaignWechatWorkBindingService {

    /**
     * 获取门店
     *
     * @return 门店
     */
    List<CampaignWechatWorkBindingCounter> getCampaignWechatWorkBindingCounter();

    /**
     * 中奖名单
     * @return 中奖名单
     */
    List<CampaignWechatWorkBinding> getCampaignWechatWorkBindingList();

    /**
     * 获取活动信息
     *
     * @param officialOpenid 公众号唯一编号
     * @return 活动信息
     */
    CampaignWechatWorkBinding getCampaignWechatWorkBinding(
            String officialOpenid,
            String nickName,
            String avatarUrl
    );

    /**
     * 抽奖
     *
     * @param unionid 开放平台唯一编号
     * @param officialOpenid 公众号唯一编号
     * @return 奖品结果
     */
    Result draw(String unionid, String officialOpenid);

    /**
     * 领取专柜
     * @param officialOpenid 公众号唯一编号
     * @param name 姓名
     * @param city 城市
     * @param counter 门店
     * @return 处理结果
     */
    Result counter(String officialOpenid, String name, String city, String counter);

    /**
     * 核销码
     *
     * @param officialOpenid 公众号唯一编号
     */
    void pickup(String officialOpenid);

    /**
     * 快递信息
     * @param officialOpenid 公众号唯一编号
     * @param name 姓名
     * @param phoneNumber 手机号码
     * @param province 省份
     * @param city 城市
     * @param district 区域
     * @param address 具体地址
     */
    Result address(
            String officialOpenid,
            String name,
            String phoneNumber,
            String province,
            String city,
            String district,
            String address
    );

    /**
     * 写入访问日志
     *
     * @param campaignViewLog 日志
     * @return 日志编号
     */
    void setCampaignViewLog(CampaignViewLog campaignViewLog);

}
