package com.stormcrm.clinique.service.mini.impl;

import java.net.URLEncoder;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.stormcrm.clinique.dao.BSmotDao;
import com.stormcrm.clinique.domain.BSmotCampaign;
import com.stormcrm.clinique.domain.BSmotLog;
import com.stormcrm.clinique.domain.BSmotTemplate;
import com.stormcrm.clinique.domain.BSmotTestCustomer;
import com.stormcrm.clinique.service.BSmotService;
import com.stormcrm.clinique.util.HttpUtil;
import io.netty.util.internal.StringUtil;

/**
 * 笔记
 *
 * <AUTHOR>
 */
@Service
public class BSmotServiceImpl implements BSmotService {

	@Value("${wechat.official.template-send-with-mp}")
	private String templateSendWithMp;

	@Value("${main.datasource.druid.url}")
	private String sqlUrl;
	@Value("${main.datasource.druid.username}")
	private String sqlUser;
	@Value("${main.datasource.druid.password}")
	private String sqlPass;

	@Resource
	private BSmotDao bSmotDao;

	@Override
	public void addCampaign(BSmotCampaign bmotCampaign) {
		this.bSmotDao.addBSmotCampaign(bmotCampaign);

	}

	@Override
	public void updateCampaign(BSmotCampaign bmotCampaign) {
		this.bSmotDao.updateBSmotCampaign(bmotCampaign);

	}

	@Override
	public List<BSmotCampaign> getCampaignList() {
		return this.bSmotDao.getCampaigns();
	}

	@Override
	public BSmotCampaign getCampaign(Long id) {
		return this.bSmotDao.getCampaignById(id);
	}

	@Override
	public void addCampaignLog(BSmotLog bmotCampaignLog) {
		this.bSmotDao.addBSmotLog(bmotCampaignLog);

	}

	@Override
	public void updateCampaignLog(BSmotLog bmotCampaignLog) {
		this.bSmotDao.updateBSmotLog(bmotCampaignLog);

	}

	@Override
	public List<BSmotLog> getCampaignLogList(Long id) {
		return this.bSmotDao.getLogsByCampaign(id);
	}

	@Override
	public BSmotLog getCampaignLog(Long id) {
		return this.bSmotDao.getLogById(id);
	}

	@Override
	public void testCampaign(Long id, Short type) {
		try {
			// 测试发送
			List<BSmotLog> all = this.bSmotDao.getTestLogsByCampaign(id);
			if (all == null || all.size() == 0) {
				BSmotCampaign bsc = this.bSmotDao.getCampaignById(id);
				BSmotTemplate tem = this.bSmotDao.getTemplateById(bsc.getTemplateId());
				
				String sql = bsc.getSource();
				//System.out.println(sql);
				if (!StringUtil.isNullOrEmpty(sql)) {
					Class.forName("com.mysql.jdbc.Driver");
					Connection conn = DriverManager.getConnection(sqlUrl, sqlUser, sqlPass);
					Statement st = conn.createStatement();
System.out.println(sql);
					ResultSet rs = st.executeQuery(sql);
					if (rs != null) {
						ResultSetMetaData data = rs.getMetaData();
						for (int i = 1; i <= data.getColumnCount(); i++) {								
							String columnName = data.getColumnLabel(i);
							System.out.println("columnName-======================"+columnName);
						}
						//System.out.println(JSON.toJSONString(keyList));
						rs.last();
						if(rs.getRow() == 0) {
							return;
						}
						//System.out.println("Users Count: "+rs.getRow());
						rs.first();
						do {
							if(rs == null) {
								return;
							}
							BSmotLog bsl = new BSmotLog();

							JSONObject props = JSON.parseObject(bsc.getParam());
							for (String key : props.keySet()) {										
								JSONObject keyword = props.getJSONObject(key);
								String setValue = keyword.getString("value");
								System.out.println("setValue----------------------"+setValue);
								for (int i = 1; i <= data.getColumnCount(); i++) {								
									String columnName = data.getColumnLabel(i);
									System.out.println("columnName-======================"+columnName);
									if(columnName.equals("phone")) {
										bsl.setPhoneNumber(rs.getString("phone"));
									}else if(columnName.equals("openid")) {
										bsl.setWechatOfficialOpenid(rs.getString("openid"));
									}
									if( rs.getString(columnName) != null) {
										setValue = setValue.replace("{" + columnName + "}", rs.getString(columnName));
									}
																		
								}
								keyword.put("value", setValue);
								props.put(key, keyword);
							}							
							
							bsl.setCampaignId(id);
							bsl.setPagePath(bsc.getPagePath());
							bsl.setParam(JSON.toJSONString(props));
							bsl.setStatus((short)0);
							bsl.setTemplateCode(tem.getTemplateCode());
							this.bSmotDao.addBSmotLog(bsl);
						} while (rs.next());
					}
					conn.close();
				}

				all = this.bSmotDao.getTestLogsByCampaign(id);
			}
			if (all == null || all.size() == 0) {
				return;
			}
			List<BSmotTestCustomer> tests = this.bSmotDao.getTestCustomerByType(type);
			for (BSmotLog bs : all) {
				for (BSmotTestCustomer bc : tests) {
					if (bs.getType() == (short) 1 && !StringUtil.isNullOrEmpty(bc.getWechatOfficialOpenid()) ) {
						bs.setWechatOfficialOpenid(bc.getWechatOfficialOpenid());
						this.sendTemplate(bs, true);
					} else if (bs.getType() == (short) 2 && !StringUtil.isNullOrEmpty(bc.getPhoneNumber())) {
						bs.setPhoneNumber(bc.getPhoneNumber());
					}

				}

			}
		}  catch (SQLException | ClassNotFoundException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	/*
	 * 注意： 调用send之前必须调用 test。 test把所有发送数据生成到log，send只管发送
	 */
	@Override
	public void sendCampaign(Long id) {
		List<BSmotLog> all = this.bSmotDao.getLogsByCampaign(id);

		for (BSmotLog bs : all) {
			//bs.setWechatOfficialOpenid("okgOYt-EqdibX-dY8-5kfNp4o5JI");
			System.out.println("openid==========================="+bs.getWechatOfficialOpenid());
			this.sendTemplate(bs, false);
		}

	}

	private void sendTemplate(BSmotLog bsmot, Boolean test ) {
		try {
			HashMap<String, Object> postData = new HashMap<String, Object>();
			postData.put("openid", bsmot.getWechatOfficialOpenid());
			postData.put("templateID", bsmot.getTemplateCode());
			if (!StringUtil.isNullOrEmpty(bsmot.getWechatOfficialOpenid())) {
				postData.put("appkey", bsmot.getMiniappId());
				postData.put("pagepath", bsmot.getPagePath());
			}
			postData.put("data", bsmot.getParam());

			ArrayList<String> datas = new ArrayList<String>();
			for (String key : postData.keySet()) {
				datas.add(key + "=" + URLEncoder.encode((String) postData.get(key), "UTF8"));
			}
			System.out.println(String.join("&", datas));
			JSONObject response = JSON.parseObject(HttpUtil.postForm(templateSendWithMp, String.join("&", datas)));

			String result = "返回结果出错";

			if (null != response && response.getBooleanValue("IsSuccess")) {
				result = response.getJSONObject("ResultData").getString("result");
				bsmot.setResult(result);
				JSONObject json = JSON.parseObject(result);
				if (null != json) {
					bsmot.setResultCode(json.getIntValue("errcode") + "");
				}
				if(!test) {
					bsmot.setStatus((short)1);
				}
				this.updateCampaignLog(bsmot);
				
			}
		} catch (Exception ignored) {
		}
	}

	@Override
	public List<BSmotTemplate> getBSMotTemplateList() {		
		return this.bSmotDao.getTemplates();
	}

}
