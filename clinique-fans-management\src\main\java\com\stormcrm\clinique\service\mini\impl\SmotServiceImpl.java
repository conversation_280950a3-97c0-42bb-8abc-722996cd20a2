package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.dao.SmotDao;
import com.stormcrm.clinique.domain.Smot;
import com.stormcrm.clinique.service.SmotService;
import com.stormcrm.clinique.util.HttpUtil;
import com.stormcrm.clinique.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 发送订阅消息
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SmotServiceImpl implements SmotService {

    @Value("${wechat.mini.url.subscribe-message}")
    private String urlSubscribeMessage;

    private final SmotDao smotDao;
    private final StringRedisTemplate stringRedisTemplate;

    public SmotServiceImpl(SmotDao smotDao, StringRedisTemplate stringRedisTemplate) {
        this.smotDao = smotDao;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 发送一次性订阅消息
     *
     * @param type       类型
     * @param openid     小程序唯一编号
     * @param templateId 模版编号
     * @param page       页面，如果为空不跳转
     * @param data       数据
     * @return 是否成功
     */
    @Override
    public boolean send(short type, String openid, String templateId, String page, String data) {

        try {

            String accessToken = stringRedisTemplate.opsForValue().get("wechatMiniProgramAccessToken");

            String param = "{" +
                    "\"touser\":\"" + openid + "\"," +
                    "\"template_id\":\"" + templateId + "\"," +
                    (!StringUtil.isNullOrEmpty(page) ? "\"page\":\"" + page + "\"," : "") +
                    "\"data\":" + data +
                    "}";

            String result = HttpUtil.postJson(urlSubscribeMessage + accessToken, param);
            boolean status = false;

            if (!StringUtil.isNullOrEmpty(result)) {
                JSONObject json = JSON.parseObject(result);
                if (null != json && 0 == json.getIntValue("errcode")) {
                    status = true;
                }
            }

            Smot smot = new Smot();
            smot.setWechatMiniOpenid(openid);
            smot.setType(type);
            smot.setParam(data);
            smot.setResult(result);
            smot.setStatus(status);
            smotDao.insertSmot(smot);

            return status;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }
}
