package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.dao.CampaignLaserTalentsTopDao;
import com.stormcrm.clinique.domain.CampaignLaserTalentsTop;
import com.stormcrm.clinique.service.CampaignLaserTalentsTopService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.vo.Result;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 新品体验官活动
 *
 * <AUTHOR>
 */
@Service
public class CampaignLaserTalentsTopServiceImpl implements CampaignLaserTalentsTopService {

    private final CampaignLaserTalentsTopDao campaignLaserTalentsTopDao;

    public CampaignLaserTalentsTopServiceImpl(CampaignLaserTalentsTopDao campaignLaserTalentsTopDao) {
        this.campaignLaserTalentsTopDao = campaignLaserTalentsTopDao;
    }

    /**
     * 查询所有活动
     *
     * @return 活动列表
     */
    @Override
    public List<CampaignLaserTalentsTop> getAll() {
        return campaignLaserTalentsTopDao.getAll();
    }

    /**
     * 查询信息
     *
     * @param id 自动编号
     * @return 信息
     */
    @Override
    public CampaignLaserTalentsTop getById(long id) {
        return campaignLaserTalentsTopDao.getById(id);
    }

    /**
     * 保存
     *
     * @param campaignLaserTalentsTop 信息
     * @return 影响的行数
     */
    @Override
    public Result save(CampaignLaserTalentsTop campaignLaserTalentsTop) {
        campaignLaserTalentsTopDao.save(campaignLaserTalentsTop);
        return ResultUtil.success("增加成功!");
    }

    /**
     * 更新
     *
     * @param campaignLaserTalentsTop 信息
     * @return 影响的行数
     */
    @Override
    public Result update(CampaignLaserTalentsTop campaignLaserTalentsTop) {
        campaignLaserTalentsTopDao.update(campaignLaserTalentsTop);
        return ResultUtil.success("编辑成功!");
    }
}
