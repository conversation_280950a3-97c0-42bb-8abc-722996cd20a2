package com.stormcrm.clinique.controller;

import com.stormcrm.clinique.domain.RightLike;
import com.stormcrm.clinique.service.RightLikeService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.vo.Pagination;
import com.alibaba.fastjson.JSONObject;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 点赞积分权益
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("right")
public class RightLikeController {

    @Resource
    private HttpServletRequest request;
    @Value("${wechat.resources.static-domain}")
    private String staticDomain;

    private final RightLikeService rightLikeService;

    public RightLikeController(RightLikeService rightLikeService) {
        this.rightLikeService = rightLikeService;
    }

    /**
     * 权益-天猫
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('RIGHT_LIKE')")
    @RequestMapping("like")
    public String tmall(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/right/like/index";
    }

    /**
     * 查询所有的胸章
     *
     * @return 胸章
     */
    @PreAuthorize("hasAuthority('RIGHT_LIKE')")
    @RequestMapping("like/get-page")
    @ResponseBody
    public String getPage(
            @RequestBody(required = false) Pagination pagination
    ){

        String servletPath = request.getServletPath();

        int page = pagination.getPage();
        int size = pagination.getPerpage();

        int count = rightLikeService.getCount();
        int pages = count / page + 1;
        page = Math.min(page, pages);
        List<RightLike> rightLikes = rightLikeService.getPage(page, size);

        JSONObject mata = new JSONObject();
        mata.put("page", page);
        mata.put("pages", pages);
        mata.put("perpage", -1);
        mata.put("total", count);
        mata.put("sort", "desc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("mata", mata);
        result.put("data", rightLikes);

        return ResultUtil.toJsonString(result);
    }


}
