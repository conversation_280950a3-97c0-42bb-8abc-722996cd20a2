package cn.slashsoft.clinique.service.mini;

import cn.slashsoft.clinique.domain.mini.Account;
import cn.slashsoft.clinique.domain.mini.StoreExchange;

/**
 * 核销
 *
 * <AUTHOR>
 */
public interface VerificationService {

    /**
     * 跟据用户名获取帐户
     *
     * @param username 用户名
     * @return 帐户
     */
    Account getAccountByUsername(String username);

    /**
     * 跟据订单编号查找兑换订单
     *
     * @param order 订单编号
     * @return 订单
     */
    StoreExchange getStoreExchangeByOrder(String order);

    /**
     * 更新订单状态
     *
     * @param order 订单编号
     * @return 处理结果
     */
    int updateStoreExchangeLogisticsByOrder(String order);

}
