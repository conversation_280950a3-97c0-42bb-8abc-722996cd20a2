package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.Wechat;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 微信
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface WechatDao {

    /**
     * 跟据昵称查找顾客
     *
     * @param nickName 昵称
     * @return 顾客信息
     */
    @Select("SELECT " +
            "   `id`,`customer_id`,`avatar_url`,`nick_name` " +
            "FROM " +
            "   `wechat` " +
            "WHERE " +
            "   `nick_name`=#{nickName}"
    )
    List<Wechat> getByNickName(String nickName);

    /**
     * 跟据customerId查找顾客
     *
     * @param customer_id 
     * @return 顾客信息
     */
    @Select("SELECT " +
            "   `id`,`customer_id`,`avatar_url`,`nick_name` " +
            "FROM " +
            "   `wechat` " +
            "WHERE " +
            "   `customer_id`=#{customerId}"
    )
    Wechat getByCustomerId(String customerId);

    /**
     * 所有顾客
     *
     * @return 顾客
     
    @Select("SELECT " +
            "   `id`,`customer_id`,`avatar_url`,`nick_name` " +
            "FROM " +
            "   `wechat` limit 0,20 "
    )
    List<Wechat> getAll();
    */
    /**
          * 评论加分记录  
     *
     * @return 加分客户列表
     */
    @Select("SELECT " +
            "   `wechat`.`customer_id`,`wechat`.`avatar_url`,`wechat`.`nick_name`,"
            + " `point_transaction`.points, `point_transaction`.start_time as createTime " +
            "FROM  `point_transaction` join " +
            "   `wechat` on `point_transaction`.customer_id=`wechat`.customer_id "
            + " where `point_transaction`.point_type_id in (3,4) order by `point_transaction`.start_time desc "
            + " limit 0, 30 "
    )
    List<Wechat> getAll();
}
