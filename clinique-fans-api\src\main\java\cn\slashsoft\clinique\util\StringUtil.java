package cn.slashsoft.clinique.util;

/**
 * 字符串工具类
 *
 * <AUTHOR>
 */
public class StringUtil {

    /**
     * 字符串是否为 Null 或 空字符
     *
     * @param s 字符串
     * @return 是和否
     */
    public static boolean isNullOrEmpty(String s) {
        return null == s || s.isEmpty();
    }

    /**
     * 字符串是否为 Null
     *
     * @param s 字符串
     * @return 是和否
     */
    public static boolean isNull(String s) {
        return null == s;
    }

    /**
     * 字符为Null时输出空字符
     *
     * @param s 字符串
     * @return 非Null字符
     */
    public static String toEmptyString(String s) {
        if (isNull(s)) {
            return "";
        }
        return s;
    }

    /**
     * 替换字符串
     *
     * @param string   字符串
     * @param original 被替换的字符
     * @param target   要替换的字符
     * @return 替换完成的字符串
     */
    public static String replace(String string, String original, String target) {
        return string.replace(original, target);
    }

    /**
     * 取字符串左边拽定长度的字符串
     *
     * @param s      字符串
     * @param length 长度
     * @return 处理后的字符串
     */
    public static String left(String s, int length) {
        if (isNullOrEmpty(s)) {
            return s;
        }
        if (length <= 0) {
            return "";
        }
        if (s.length() < length) {
            return s;
        }
        return s.substring(0, length);
    }

    /**
     * 取字符串右边指定长度的字符串
     *
     * @param s      字符串
     * @param length 长度
     * @return 处理后和字符串
     */
    public static String right(String s, int length) {
        if (isNullOrEmpty(s)) {
            return s;
        }
        if (length <= 0) {
            return "";
        }
        if (s.length() <= length) {
            return s;
        }
        return s.substring(s.length() - length);
    }
}
