package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignEbciDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciLog;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciStock;

/**
 * 黄油变粉安瓶级保湿补光申领
 *
 * <AUTHOR>
 */
public interface CampaignEbciSignApplyService {

    /**
     * 保存日志
     *
     * @param campaignEbciLog 日志
     */
    void insertLog(CampaignEbciLog campaignEbciLog);

    /**
     * 获取手机号码
     *
     * @param unionid 开放平台唯一编号
     * @return 手机号码
     */
    String getPhoneNumberByUnionid(String unionid);

    /**
     * 获取手机号码
     *
     * @param openid 公众号唯一编号
     * @return 手机号码
     */
    String getPhoneNumberByOpenid(String openid);

    /**
     * 获取是否已经审领
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    int hasDetail(String phoneNumber);

    /**
     * 获取是否已经审领
     *
     * @param openid 公众号唯一编号
     * @return 数量
     */
    int hasDetailByOpenid(String openid);

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    CampaignEbciDetail getDetail(String phoneNumber);

    /**
     * 申领
     *
     * @param campaignEbciDetail 资料
     * @return 0:成功，1，帐户已经领过了，2：手机号码已经领过了，3：库存不足
     */
    int submit(CampaignEbciDetail campaignEbciDetail);

    /**
     * 更新是否关注
     *
     * @param campaignEbciDetail 申领信息
     */
    void setFollow(CampaignEbciDetail campaignEbciDetail);

    /**
     * 获取审领信息
     *
     * @param openid 公众号唯一编号
     * @return 审领信息
     */
    CampaignEbciDetail getDetailByOpenid(String openid);

    /**
     * 跟据场景值获取门店
     *
     * @param scene 场景值
     * @return 门店
     */
    CampaignEbciStock getByScene(String scene);

    /**
     * 更新核销信息
     *
     * @param campaignEbciDetail 核销信息
     */
    void setReceive(CampaignEbciDetail campaignEbciDetail);

    /**
     * 是否打卡满7天
     *
     * @param openid 公众号openid
     * @return 统计
     */
    int hadSigned(String openid);
}
