package com.stormcrm.clinique.service;

/**
 * 发送订阅消息
 * <AUTHOR>
 */
public interface SmotService {

    /**
     * 发送一次性订阅消息
     *
     * @param type       类型
     * @param openid     小程序唯一编号
     * @param templateId 模版编号
     * @param page       页面，如果为空不跳转
     * @param data       数据
     * @return 是否成功
     */
    boolean send(short type, String openid, String templateId, String page, String data);


}
