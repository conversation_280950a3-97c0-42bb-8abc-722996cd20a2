package cn.slashsoft.clinique.dao.campaign;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.campaign.CampaignH5;
import cn.slashsoft.clinique.domain.campaign.CampaignH5AD;
import cn.slashsoft.clinique.domain.campaign.CampaignH5City;
import cn.slashsoft.clinique.domain.campaign.CampaignH5Detail;
import cn.slashsoft.clinique.domain.campaign.CampaignH5Log;
import cn.slashsoft.clinique.domain.campaign.CampaignH5Stock;
import cn.slashsoft.clinique.domain.campaign.CdpTrail;

/**
 * campaign H5
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignH5Dao {
	
    
    /**
     * 保存日志
     *
     * @param campaignH5Log 日志
     */
    @Insert("INSERT INTO `campaign_h5_log`(" +
            "   `type`," +
            "   `unique_id`, " +
            "   `source`," +
            "   `click_id`," +
            "   `campaign_id`," +
            "   `page`" +
            ") " +
            "VALUES (" +
            "   #{type}, " +
            "   #{uniqueId}, " +
            "   #{source}," +
            "   #{clickId}, " +
            "   #{campaignId}, " +
            "   #{page}" +
            ")")
    void insertLog(CampaignH5Log campaignH5Log);

    /**
     * 获取是否已经审领
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    @Select("SELECT " +
            "   COUNT(*) " +
            "FROM " +
            "   `campaign_h5_detail` " +
            "WHERE " +
            "    `phone_number`=#{phoneNumber} " +
            " AND `campaign_id` = #{campaignId} "  +
            " LIMIT 1")
    int hasDetail(String phoneNumber, Long campaignId);

    /**
     * 获取是否已经审领
     *
     * @param openid 公众号唯一编号
     * @return 数量
     */
    @Select("SELECT " +
            "   COUNT(*) " +
            "FROM " +
            "   `campaign_h5_detail` " +
            "WHERE " +
            "   `unique_id`=#{openid} " +
            " AND `campaign_id` = #{campaignId} "  +
            "LIMIT 1")
    int hasDetailByOpenid(String openid, Long campaignId);
    
    @Select("SELECT " +
            "   `store_code` " +
            " FROM " +
            "   `campaign_h5_city` " +
            " WHERE " +
            "  `province` = #{province} "  +
            " AND `city` = #{city} "  +
            " LIMIT 1 ")
    String getStoreCode(String province, String city);
    /**
     * 获取活动
     *
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `campaign_h5` " +
            "WHERE " +
            "  `code` = #{campaignCode} "  +
            "LIMIT 1")
    CampaignH5 getCampaign( String campaignCode);

    /**
     * 获取活动
     *
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `campaign_h5` " +
            "WHERE " +
            "  `code` = #{campaignCode} " +
            " AND status = 1 " +
            " and start_time <= now() and end_time >= now() "  +
            "LIMIT 1")
    CampaignH5 getValidCampaign( String campaignCode);

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    @Select("SELECT " +
            "   `id`, `name`,  `phone_number`, `city`, `store`, `follow` " +
            "FROM " +
            "   `campaign_h5_detail` " +
            "WHERE " +
            "    `phone_number`=#{phoneNumber} " +
            " AND `campaign_id` = #{campaignId} "  +
            "LIMIT 1")
    CampaignH5Detail getDetail(String phoneNumber, Long campaignId);

    /**
     * 获取审领信息
     *
     * @param openid 公众号唯一编号
     * @return 审领信息
     */
    @Select("SELECT " +
            "   `id`, `name`,  `phone_number`, `city`, `store`, `follow`, `receive` " +
            "FROM " +
            "   `campaign_h5_detail` " +
            "WHERE " +
            "   `unique_id`=#{openid} " +
            " AND `campaign_id` = #{campaignId} "  +
            "LIMIT 1")
    CampaignH5Detail getDetailByOpenid(String openid, Long campaignId);

    /**
     * 获取手机号码
     *
     * @param unionid 开放平台唯一编号
     * @return 手机号码
     */
    @Select("SELECT " +
            "    decryptPhone(`c`.`phone_number`) phone_number " +
            "FROM " +
            "   `wechat` `w` " +
            "       INNER JOIN " +
            "   `customer` `c` " +
            "       ON `w`.`customer_id`=`c`.`id` " +
            "WHERE " +
            "   `w`.`unionid`=#{unionid}")
    String getPhoneNumberByUnionid(String unionid);

    /**
     * 获取手机号码
     *
     * @param openid 公众号唯一编号
     * @return 手机号码
     */
    @Select("SELECT " +
            "    decryptPhone(`c`.`phone_number`) phone_number " +
            "FROM " +
            "   `wechat` `w` " +
            "       INNER JOIN " +
            "   `customer` `c` " +
            "       ON `w`.`customer_id`=`c`.`id` " +
            "WHERE " +
            "   `w`.`wechat_official_openid`=#{openid}")
    String getPhoneNumberByOpenid(String openid);

    /**
     * 扣库存
     *
     * @param campaignH5Detail 申领信息
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_h5_stock` " +
            "SET " +
            "   `stock`=`stock`-1, " +
            "   `total`=`total`+1 " +
            "WHERE " +
            "   `city`=#{city} " +
            "   AND `store`=#{store} " +
            " AND `campaign_id` = #{campaignId} "  +
            "   AND `stock`>0")
    int updateStock(CampaignH5Detail campaignH5Detail, Long campaignId);

    /**
     * 定入申领信息
     *
     * @param campaignH5Detail 申领信息
     */
    @Insert("INSERT INTO `campaign_h5_detail`(" +
            "   `type`, " +
            "   `unique_id`, " +
            "   `name`, " +
            "   `phone_number`, " +
            "   `city`, " +
            "   `store`, " +
            "   `store_id`, " +
            "   `campaign_id`," +
            "   `source` " +
            ") " +
            "VALUES (" +
            "   #{type}, " +
            "   #{uniqueId}, " +
            "   #{name}, " +
            "   #{phoneNumber}, " +
            "   #{city}, " +
            "   #{store}, " +
            "   #{storeId}, " +
            "   #{campaignId}, " +
            "   #{source} " +
            ")")
    void insetDetail(CampaignH5Detail campaignH5Detail);

    /**
     * 更新是否关注
     *
     * @param campaignH5Detail 申领信息
     */
    @Update("UPDATE " +
            "   `campaign_h5_detail` " +
            "SET " +
            "   `follow`=#{follow}," +
            "   `follow_source`=#{followSource}," +
            "   `follow_first_time`=#{followFirstTime}," +
            "   `follow_last_time`=#{followLastTime}," +
            "   `follow_cancel_time`=#{followCancelTime}," +
            "   `bind`=#{bind}," +
            "   `bind_time`=#{bindTime} " +
            "WHERE " +
            "   `id`=#{id}")
    void setFollow(CampaignH5Detail campaignH5Detail);

    /**
     * 跟据场景值获取门店
     *
     * @param scene 场景值
     * @return 门店
     */
    @Select("SELECT " +
            "   `city`,`store`,`store_id` " +
            "FROM " +
            "   `campaign_h5_stock` " +
            "WHERE " +
            "   `scene`=#{scene} " +
            " AND `campaign_id` = #{campaignId} "  +
            "LIMIT 1")
    CampaignH5Stock getByScene(String scene, Long campaignId);

    /**
     * 跟据门店名称获取门店状态
     *
     * @param scene 场景值
     * @return 门店
     */
    @Select("SELECT " +
            "   `city`,`store`,`store_id`,`status` " +
            "FROM " +
            "   `campaign_h5_stock` " +
            "WHERE " +
            "   `city`=#{city} " +
            "   AND `store`=#{store} " +
            " AND `campaign_id` = #{campaignId} "  +
            "LIMIT 1")
    CampaignH5Stock getByStoreName(CampaignH5Detail campaignH5Detail);
    /**
     * 更新核销信息
     *
     * @param campaignH5Detail 核销信息
     */
    @Update("UPDATE " +
            "   `campaign_h5_detail` " +
            "SET " +
            "   `receive`=1, " +
            "   `receive_city`=#{receiveCity}, " +
            "   `receive_store`=#{receiveStore}, " +
            "   `receive_store_id`=#{receiveStoreId}, " +
            "   `receive_time`=NOW(), " +
            "   `scene`=#{scene}," +
            "   `cdp`=0 " +
            "WHERE " +
            "   `id`=#{id} ")
    void setReceive(CampaignH5Detail campaignH5Detail);

    /**
     * 写入ad信息
     *
     * @param CampaignH5AD ad
     * @return ad
     */
    @Insert("INSERT INTO `campaign_h5_ad`(" +
            "   `click_id`, " +
            "   `phone_number`, " +
            "   `campaign_id`, " +
            "   `data` " +
            ") " +
            "VALUES (" +
            "   #{clickId}, " +
            "   #{phoneNumber}, " +
            "   #{campaignId}, " +
            "   #{data} " +
            ")")
    void addAD(CampaignH5AD campaignH5AD) ;
    /**
     * 跟据手机号获取ad
     *
     * @param phoneNumber 手机号
     * @return ad
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `campaign_h5_ad` " +
            "WHERE " +
            "   `phone_number`=#{phoneNumber} " +
            " AND `campaign_id` = #{campaignId} "  +
            "LIMIT 1")
    CampaignH5AD getADByPhoneNumber(String phoneNumber, Long campaignId) ;
    /**
     * 更新ad信息
     *
     * @param CampaignH5AD ad
     * @return ad
     */ @Update("UPDATE " +
             "   `campaign_h5_ad` " +
             "SET " +
             "   `status`=1, " +
             "   `result`=#{result} " +
             "WHERE " +
             "   `id`=#{id} ")
    void setAD(CampaignH5AD campaignH5AD) ;

     /**
      * 获取是否已审领   临时需求
      *
      * @param phoneNumber 手机号码
      * @return 审领信息
      */
     @Select("SELECT " +
             "   `id`, `name`, `phone_number`, `city`, `store`, `follow` " +
             "FROM " +
             "   `campaign_h5_detail` " +
             "WHERE " +
             "   `phone_number`=#{phoneNumber} " +
             " AND `campaign_id` = #{campaignId} "  +
             "LIMIT 1")
     CampaignH5Detail getTmpDetail(String phoneNumber, Long campaignId);
     
  
     
     /**
      * 定入申领信息   
      *
      * @param campaignH5Detail 申领信息
      */
     @Insert("INSERT INTO `campaign_h5_detail`(" +
             "   `type`, " +
             "   `unique_id`, " +
             "   `name`, " +
             "   `phone_number`, " +
             "   `city`, " +
             "   `store`, " +
             "   `store_id`, " +
             "   `campaign_id`, " +
             "   `source` " +
             ") " +
             "VALUES (" +
             "   #{type}, " +
             "   #{uniqueId}, " +
             "   #{name}, " +
             "   #{phoneNumber}, " +
             "   #{city}, " +
             "   #{store}, " +
             "   #{storeId}, " +
             "   #{campaignId}, " +
             "   #{source} " +
             ")")
     void addDetail(CampaignH5Detail campaignH5Detail);


     @Select("SELECT count(id) from campaign_h5_city where  `campaign_id` = #{campaign} and status = 1 ")
     int getCityCount( long campaign);

     @Select("SELECT * from campaign_h5_city where  `campaign_id` = #{campaign} and status = 1 ")
     List<CampaignH5City> getCityList( long campaign);

     @Select("SELECT * from campaign_h5_city where `code` = #{city} AND `campaign_id` = #{campaign}  and status = 1 ")
     CampaignH5City getCity(String city, long campaign);

    @Select("SELECT * from campaign_h5_detail where id >=180076 and id <=180362 order by id desc")
	List<CampaignH5Detail> getAllDetails();

}
