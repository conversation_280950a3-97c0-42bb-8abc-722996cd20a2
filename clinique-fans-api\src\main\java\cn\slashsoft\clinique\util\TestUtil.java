package cn.slashsoft.clinique.util;

import cn.slashsoft.clinique.CliniqueFansApiApplication;
import cn.slashsoft.clinique.domain.campaign.lottery2408.CampaignLotteryAward;
import org.springframework.boot.SpringApplication;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;

/**
 * 日期工具类
 *
 * <AUTHOR>
 */
public class TestUtil {
    public static void main(String[] args) {

        Random rand = new Random();

        for (int i = 0; i < 1000; i++){

            //设置总抽奖概率
            Integer sumRandNum = 100;

            //开始随机
            int randNum;
            if (sumRandNum > 1){
                // rand是从0开始的, 手动+1
                randNum = rand.nextInt(sumRandNum - 1) + 1;
            }else {
                // rand是从0开始的, 手动+1
                randNum = 1;
            }
            if (randNum >= 100){
                System.out.println("抽中抽中抽中抽中抽中抽中抽中抽中抽中抽中抽中抽中抽中抽中抽中抽中抽中抽中抽中抽中抽中");
            }



            System.out.println(randNum);
        }

    }

}
