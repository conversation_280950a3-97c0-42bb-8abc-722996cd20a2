package cn.slashsoft.clinique.dao.campaign;

import cn.slashsoft.clinique.domain.campaign.mgm2111.Detail;
import cn.slashsoft.clinique.domain.campaign.mgm2111.Invite;
import cn.slashsoft.clinique.domain.campaign.mgm2111.ViewLog;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 2021年11月裂变活动
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignMgm2111Dao {

    /**
     * 获取活动信息
     *
     * @param customerId 顾客编号
     * @return 活动信息
     */
    @Select("SELECT " +
            "   `sign_total`," +
            "   `last_sign_time`," +
            "   `sign_coupon_5`," +
            "   `sign_coupon_8`," +
            "   `invite_rank`," +
            "   `invite_total`," +
            "   `invite_coupon_3`," +
            "   `invite_coupon_8` " +
            "FROM " +
            "   `campaign_mgm2111` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    Detail getDetailByCustomerId(long customerId);

    /**
     * 获取活动信息
     *
     * @param customerId 顾客编号
     * @return 活动信息
     */
    @Select("SELECT " +
            "   `w`.`avatar_url`," +
            "   `m`.`sign_total`," +
            "   `m`.`last_sign_time`," +
            "   `m`.`sign_coupon_5`," +
            "   `m`.`sign_coupon_8`," +
            "   `m`.`invite_rank`," +
            "   `m`.`invite_total`," +
            "   `m`.`invite_coupon_3`," +
            "   `m`.`invite_coupon_8` " +
            "FROM " +
            "   `campaign_mgm2111` `m` " +
            "       inner join " +
            "   `wechat` `w` " +
            "       on `m`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "   `m`.`customer_id`=#{customerId} " +
            "LIMIT 1")
    Detail getDetailWithAvatarByCustomerId(long customerId);

    /**
     * 获取邀请排名
     * @return 用户列表
     */
    @Select("select " +
            "   `w`.`nick_name`, " +
            "   `w`.`avatar_url`, " +
            "   `c`.`invite_rank`, " +
            "   `c`.`invite_total` " +
            "from " +
            "   `campaign_mgm2111` `c` " +
            "       inner join " +
            "   `wechat` `w` " +
            "       on `c`.`customer_id`=`w`.`customer_id` " +
            "order by " +
            "   `c`.`invite_rank`, `c`.`last_invite_time`, `c`.`id` " +
            "LIMIT 30")
    List<Invite> getInviteByRank();

    /**
     * 写入活动信息
     *
     * @param detail 活动信息
     */
    @Insert("INSERT INTO `campaign_mgm2111` (" +
            "   `customer_id`, " +
            "   `invite_rank`, " +
            "   `source` " +
            ") " +
            "SELECT " +
            "   #{customerId}, " +
            "   COUNT(`id`)+1, " +
            "   #{source} " +
            "FROM `campaign_mgm2111`")
    void insertDetail(Detail detail);

    /**
     * 写入签到日志
     * @param customerId 顾客编号
     * @param level 级别
     */
    @Insert("INSERT INTO `campaign_mgm2111_sign` (" +
            "   `customer_id`, " +
            "   `level` " +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{level} " +
            ")")
    void insertSign(long customerId, int level);

    /**
     * 更新签到次数
     * @param customerId 顾客编号
     */
    @Update("UPDATE " +
            "   `campaign_mgm2111` " +
            "SET " +
            "   `sign_total`=`sign_total`+1, " +
            "   `last_sign_time`=NOW() " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    void updateDetailSignTotal(long customerId);

    /**
     * 更新签到5次礼品状态
     * @param customerId 顾客编号
     * @param couponCode 优惠券编号
     */
    @Update("UPDATE " +
            "   `campaign_mgm2111` " +
            "SET " +
            "   `sign_coupon_5`=1, " +
            "   `sign_coupon_5_code`=#{couponCode}, " +
            "   `sign_coupon_5_time`=NOW() " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "   AND `sign_coupon_5`=0 " +
            "LIMIT 1")
    void updateDetailSignCoupon5(long customerId, String couponCode);

    /**
     * 更新签到8次礼品状态
     * @param customerId 顾客编号
     * @param couponCode 优惠券编号
     */
    @Update("UPDATE " +
            "   `campaign_mgm2111` " +
            "SET " +
            "   `sign_coupon_8`=1, " +
            "   `sign_coupon_8_code`=#{couponCode}, " +
            "   `sign_coupon_8_time`=NOW() " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "   AND `sign_coupon_8`=0 " +
            "LIMIT 1")
    void updateDetailSignCoupon8(long customerId, String couponCode);

    /**
     * 更新邀请3次礼品状态
     * @param customerId 顾客编号
     * @param couponCode 优惠券编号
     */
    @Update("UPDATE " +
            "   `campaign_mgm2111` " +
            "SET " +
            "   `invite_coupon_3`=1, " +
            "   `invite_coupon_3_code`=#{couponCode}, " +
            "   `invite_coupon_3_time`=NOW() " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "   AND `invite_coupon_3`=0 " +
            "LIMIT 1")
    void updateDetailInviteCoupon3(long customerId, String couponCode);

    /**
     * 更新邀请8次礼品状态
     * @param customerId 顾客编号
     * @param couponCode 优惠券编号
     */
    @Update("UPDATE " +
            "   `campaign_mgm2111` " +
            "SET " +
            "   `invite_coupon_8`=1, " +
            "   `invite_coupon_8_code`=#{couponCode}, " +
            "   `invite_coupon_8_time`=NOW() " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "   AND `invite_coupon_8`=0 " +
            "LIMIT 1")
    void updateDetailInviteCoupon8(long customerId, String couponCode);

    /**
     * 更新邀请数量
     *
     * @param miniOpenid 小程序唯一编号
     */
    @Update("UPDATE " +
            "   `campaign_mgm2111` " +
            "SET " +
            "   `invite_total`=`invite_total`+1 " +
            "WHERE " +
            "   `customer_id`=(" +
            "       SELECT " +
            "           `customer_id` " +
            "       FROM " +
            "           `wechat` " +
            "       WHERE " +
            "           `wechat_mini_openid`=#{miniOpenid} " +
            "       LIMIT 1" +
            "   )")
    void invite(String miniOpenid);

    /**
     * 邀请
     *
     * @param inviterMiniOpenid 邀请者
     * @param inviteeMiniOpenid 被邀请者
     */
    @Insert("INSERT INTO `campaign_mgm2111_invite`(" +
            "   `inviter_mini_openid`, " +
            "   `invitee_mini_openid`" +
            ") " +
            "VALUES (" +
            "   #{inviterMiniOpenid}, " +
            "   #{inviteeMiniOpenid}" +
            ")")
    void insertInvite(
            @Param("inviterMiniOpenid") String inviterMiniOpenid,
            @Param("inviteeMiniOpenid") String inviteeMiniOpenid
    );

    /**
     * 写入经纬度
     * @param customerId 顾客编号
     * @param latitude 经度
     * @param longitude 纬度
     */
    @Insert("INSERT INTO `campaign_mgm2111_location` (" +
            "   `customer_id`, " +
            "   `latitude`, " +
            "   `longitude`" +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{latitude}, " +
            "   #{longitude}" +
            ")")
    void insertLocation(long customerId, String latitude, String longitude);

    /**
     * 写入访问日志
     *
     * @param viewLog 日志
     */
    @Insert("INSERT INTO `campaign_mgm2111_view_log`(" +
            "   `customer_id`," +
            "   `source`," +
            "   `page`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{source}," +
            "   #{page}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertViewLog(ViewLog viewLog);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Update("UPDATE " +
            "   `campaign_mgm2111_view_log` " +
            "SET " +
            "   `end_time`=NOW()," +
            "   `diff_second`=TimeStampDiff(SECOND,`start_time`,NOW()) " +
            "WHERE " +
            "   `id`=#{id}")
    void setSignViewLog(long id);

}
