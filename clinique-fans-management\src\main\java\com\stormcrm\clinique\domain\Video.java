package com.stormcrm.clinique.domain;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;

@Data
public class Video {

	private Long id;

	private Date createTime;
	private Date updateTime;
	private String brandName;
	private String brandLogo;

	private String coverImage;
	private String fileUrl;
	private String title;

	private int liked;
	private int likeCount;
	private int read;

	private int status;
	private int type;

	private ArrayList<VideoTag> tags;

	public String toJson() {
		return "";
	}

}