package cn.slashsoft.clinique.service.mini.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import cn.slashsoft.clinique.dao.mini.ActionDao;
import cn.slashsoft.clinique.dao.mini.Day3TaskDao;
import cn.slashsoft.clinique.dao.mini.NoteDao;
import cn.slashsoft.clinique.dao.mini.PointDao;
import cn.slashsoft.clinique.dao.mini.WechatDao;
import cn.slashsoft.clinique.dao.official.WechatFriendDao;
import cn.slashsoft.clinique.domain.mini.Action;
import cn.slashsoft.clinique.domain.mini.CustomerTask;
import cn.slashsoft.clinique.domain.mini.Day3Task;
import cn.slashsoft.clinique.domain.mini.PointTransaction;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.domain.official.WechatFriend;
import cn.slashsoft.clinique.enums.PointTypeEnum;
import cn.slashsoft.clinique.service.mini.MotService;
import cn.slashsoft.clinique.service.mini.TaskService;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.vo.Result;
import cn.slashsoft.clinique.vo.mini.Day3TaskDetailVo;

/**
 * 与活动相关
 *
 * <AUTHOR>
 */
@Service
public class TaskServiceImpl implements TaskService {

	@Resource
	private Day3TaskDao taskDao;
	@Resource
	private WechatFriendDao wechatFriendDao;
	
	@Resource
	private PointDao pointDao;
	
	@Resource
	private WechatDao wechatDao;
	
	@Resource
	private ActionDao actionDao;
	
	@Resource
	private NoteDao noteDao;
	
	@Resource
	private OutsideService outsideService;
	
	@Resource
	private MotService motService;

	@Override
	public void addTask(Day3Task task) {
		Day3Task task1 = taskDao.getDay3TaskByUnionid(task.getUnionid());
		if(null ==  task1 ) {
			taskDao.createDay3Task(task);
		}
	}

	@Override
	public void editTask(Day3Task task) {
		taskDao.editDay3Task(task);
	}

	@Override
	public Day3Task getTask(String unionid, Long customerId) {
		Day3Task task = taskDao.getDay3TaskByUnionid(unionid);
		if (null != task && null == task.getCustomerId()) {
			task.setCustomerId(customerId);
			this.editTask(task);
		}
		return task;
	}

	@Override
	public Day3TaskDetailVo getDay3TaskDetail( Long customerId) {
		Wechat wechat = wechatDao.getWechatInfoByCustomerId(customerId);

		Day3TaskDetailVo taskVo = new Day3TaskDetailVo();

		Day3Task task = this.getTask(wechat.getUnionid(), customerId);
		if (null == task) {
			task = new Day3Task();
			task.setCreateTime(new Date());
			task.setCustomerId(customerId);
			task.setUnionid(wechat.getUnionid());
			task.setStatus((short) 1);
			task.setNotice(1);
			if(null == wechat.getWechatMiniOpenid()) {
				WechatFriend friend = wechatFriendDao.getByUnionid(wechat.getUnionid());
				if(null == friend || friend.getStatus() == 0) {
					task.setTaskType((short)2);
				}else {
					task.setTaskType((short)1);
				}		
				this.addTask(task);
			}else {
				WechatFriend friend = wechatFriendDao.getByOpenid(wechat.getWechatMiniOpenid());
				if(null == friend || friend.getStatus() == 0) {
					task.setTaskType((short)2);
				}else {
					task.setTaskType((short)1);
				}		
				this.addTask(task);
			}
		}
		taskVo.setNotice(task.getNotice());
		
		Day3Task dayTask1 = new Day3Task();
		dayTask1.setCreateTime(task.getCreateTime());
		dayTask1.setStatus(task.getStatus());
		dayTask1.setTaskType(task.getTaskType());	
		int task1Finished = 0;

		// 个人任务详情
		List<CustomerTask> tasks = new ArrayList<CustomerTask>();
		
		// 任务日志
		List<Action> taskLogs = actionDao.getDay3TaskActions(wechat.getWechatMiniOpenid());

		CustomerTask task11 = new CustomerTask();
		short taskId = (short)53;
		task11.setTitle("完善信息领50C币");
		task11.setPath("/pages/info/info?taskId=" + taskId);
		for(Action log :taskLogs) {
			if(log.getType() == taskId) {
	    		task11.setStartDate(log.getCreateTime());
				task11.setData((short)1 );
				break;
			}
		}
		if(null == task11.getData() || task11.getData() == (short)0) {
			//检测是否完成任务
			PointTransaction pt = new PointTransaction();	
			pt.setCustomerId(customerId);
	        pt.setPointTypeId(PointTypeEnum.INFO.getId());
	        if(pointDao.hasEditInfo(pt) >=1) {
				//增加任务日志
	        	Action action = new Action();
	        	action.setWechatOfficialOpenid(wechat.getWechatMiniOpenid());
	        	action.setType(taskId);
	        	actionDao.insertAction(action);
	    		task11.setStartDate(new Date());
	        	task11.setData((short)1 );
	        	task1Finished ++;
	        }else {
	        	task11.setData((short)0 );
	        }
			
		}else {
			task1Finished ++;
		}
		tasks.add(task11);
	
		if(null != task11.getStartDate()) {
			dayTask1.setStartDate(DateUtil.differentDayToNow(task11.getStartDate()));
		}
	
		CustomerTask task12 = new CustomerTask();
		taskId = (short)54;
		task12.setTitle("浏览C粉圈最新活动资讯");
		task12.setPath("/pages/campaign/campaign?taskId="+taskId+"&rest=0");
		task12.setData((short) 0);
		for(Action log :taskLogs) {
			if(log.getType() == taskId) {
	    		task12.setStartDate(log.getCreateTime());
				task12.setData((short)1 );
				break;
			}
		}
		//task12.setStartDate(new Date());
		tasks.add(task12);


		if (task.getTaskType() == 1) {
			CustomerTask task15 = new CustomerTask();
			taskId = (short)55;
			task15.setTitle("浏览C粉社区精彩笔记");
			task15.setPath("/pages/community/index/index?taskId="+taskId+"&rest=0");
			task15.setData((short) 0);
			for(Action log :taskLogs) {
				if(log.getType() == taskId) {
		    		task15.setStartDate(log.getCreateTime());
					task15.setData((short)1 );
					break;
				}
			}
			//task15.setStartDate(new Date());
			tasks.add(task15);
		} else {
			CustomerTask task15 = new CustomerTask();
			taskId = (short)56;
			task15.setTitle("关注Clinique倩碧微信公众号");
			task15.setPath("/pages/community/index/index?taskId=" + taskId);
			task15.setData((short) 0);
			for(Action log :taskLogs) {
				if(log.getType() == taskId) {
		    		task15.setStartDate(log.getCreateTime());
					task15.setData((short)1 );
					break;
				}
			}
			//检测是否完成任务
			if(null ==task15.getData() || task15.getData() == (short)0) {
				if(null == wechat.getWechatMiniOpenid()) {
					WechatFriend friend = wechatFriendDao.getByUnionid(wechat.getUnionid());
					if(null == friend || friend.getStatus() == 0) {
					}else {
						//增加任务日志
						Action action = new Action();
			        	action.setWechatOfficialOpenid(wechat.getWechatMiniOpenid());
			        	action.setType(taskId);
			        	actionDao.insertAction(action);
			    		task15.setStartDate(friend.getAttentionTime());
						task15.setData((short)1 );
					}
				}else {
					WechatFriend friend = wechatFriendDao.getByOpenid(wechat.getWechatMiniOpenid());
					if(null == friend || friend.getStatus() == 0) {
					}else {
						//增加任务日志
						Action action = new Action();
			        	action.setWechatOfficialOpenid(wechat.getWechatMiniOpenid());
			        	action.setType(taskId);
			        	actionDao.insertAction(action);
			    		task15.setStartDate(friend.getAttentionTime());
						task15.setData((short)1 );
					}	
				}
				
			}
			
			//task15.setStartDate(new Date());
			tasks.add(0, task15);
		}
		
		CustomerTask task14 = new CustomerTask();
		taskId = (short)57;
		task14.setTitle("浏览C币商城查看礼品清单");
		task14.setPath("/pages/store/store?taskId="+taskId+"&rest=0");
		task14.setData((short) 0);
		for(Action log :taskLogs) {
			if(log.getType() == taskId) {
	    		task14.setStartDate(log.getCreateTime());
				task14.setData((short)1 );
				break;
			}
		}
		//task14.setStartDate(new Date());
		tasks.add(task14);

		CustomerTask task13 = new CustomerTask();
		taskId = (short)58;
		task13.setTitle("浏览个人中心查看我的特权");
		task13.setPath("/pages/mine/mine?taskId=" + taskId+"&rest=0");
		task13.setData((short) 0);
		for(Action log :taskLogs) {
			if(log.getType() == taskId) {
	    		task13.setStartDate(log.getCreateTime());
				task13.setData((short)1 );
				break;
			}
		}
		/*
		//检测是否完成
		if(null ==task13.getData() || task13.getData() == (short)0) {
			//TODO 先决条件： 老新客特权活动在线, 即保证能看到三日打卡特权的用户至少有两个特权
			List<Action> specials = actionDao.getSpecialActions(wechat.getWechatMiniOpenid());
			if(specials.size() > 1 ) {
				//增加任务日志
				Action action = new Action();
	        	action.setWechatOfficialOpenid(wechat.getWechatMiniOpenid());
	        	action.setType(taskId);
	        	actionDao.insertAction(action);
	        	
	    		task13.setStartDate(new Date());
				task13.setData((short)1 );
			}
		}
		*/
		//task13.setStartDate(new Date());
		tasks.add(task13);
		dayTask1.setTasks(tasks);
		taskVo.setDay1(dayTask1);

		Day3Task dayTask2 = new Day3Task();
		dayTask2.setCreateTime(task.getCreateTime());
		dayTask2.setStatus(task.getStatus());
		dayTask2.setTaskType(task.getTaskType());		

		dayTask2.setStartDate(dayTask1.getStartDate() +1);
		//FOR TEST //TODO
		//dayTask2.setStartDate(dayTask1.getStartDate() );
		
		List<CustomerTask> tasks2 = new ArrayList<CustomerTask>();

		CustomerTask task2 = new CustomerTask();
		taskId = (short)59;
		task2.setTitle("阅读1篇精彩的笔记，并给它留言或点赞");
		task2.setPath("/pages/community/index/index?taskId="+ taskId);
		task2.setData((short) 0);

		int f1 = 0;
		for(CustomerTask taskCheck : tasks) {
			if(taskCheck.getData().intValue() > 0) {
				f1 ++;
			}
		}
		System.out.println("fdsfaffs---------------------"+f1);
		if(f1 == 5) {
			for(Action log :taskLogs) {
				if(log.getType() == taskId ) {
		    		task2.setStartDate(log.getCreateTime());
					task2.setData((short)1 );
					break;
				}
			}

			//已经开始的任务才执行检查
			if( dayTask2.getStartDate() <=0) {
				//检测是否完成
				if( null ==task2.getData() || task2.getData() == (short)0) {
					int liked = noteDao.getTodayLikeCount(customerId);
					if(liked <=0) {
						int discussCount = noteDao.getTodayDiscussCount(customerId);
						if(discussCount > 1) {
							//增加任务日志
							Action action = new Action();
				        	action.setWechatOfficialOpenid(wechat.getWechatMiniOpenid());
				        	action.setType(taskId);
				        	actionDao.insertAction(action);
				        	
							task2.setStartDate(new Date());
							task2.setData((short)1 );
						}
					}else {
						//增加任务日志
						Action action = new Action();
			        	action.setWechatOfficialOpenid(wechat.getWechatMiniOpenid());
			        	action.setType(taskId);
			        	actionDao.insertAction(action);
			        	
						task2.setStartDate(new Date());
						task2.setData((short)1 );
					}			
				}
			}
		}

		tasks2.add(task2);
		dayTask2.setTasks(tasks2);
		taskVo.setDay2(dayTask2);
		
		Day3Task dayTask3 = new Day3Task();;
		dayTask3.setCreateTime(task.getCreateTime());
		dayTask3.setStatus(task.getStatus());
		dayTask3.setTaskType(task.getTaskType());
		dayTask3.setStartDate(dayTask1.getStartDate() + 2);
		//FOR TEST //TODO
		//dayTask3.setStartDate(dayTask1.getStartDate() );
		
		List<CustomerTask> tasks3 = new ArrayList<CustomerTask>();

		CustomerTask task3 = new CustomerTask();
		taskId = (short)60;
		task3.setTitle("邀请1位好友加入C粉圈");
		task3.setPath("/pages/community/index/index?taskId="+taskId);
		task3.setData((short) 0);
		if(f1 == 5 && task2.getData().intValue() > 0) {
			for(Action log :taskLogs) {
				if(log.getType() == taskId) {
		    		task3.setStartDate(log.getCreateTime());
					task3.setData((short)1 );
					break;
				}
			}
			//已经开始的任务才执行检查
			if(dayTask3.getStartDate() <=0) {
				//检测是否完成
				if(null == task3.getData() || task3.getData() == (short)0) {
					int count = wechatDao.getTodayWechatShareRegisterCount(wechat.getWechatMiniOpenid());
					if(count > 0) {
						//增加任务日志
						Action action = new Action();
			        	action.setWechatOfficialOpenid(wechat.getWechatMiniOpenid());
			        	action.setType(taskId);
			        	actionDao.insertAction(action);
			        	
						task3.setStartDate(new Date());
						task3.setData((short)1 );
					}
					
				}
			}
		}
		if(task.getStatus() == (short)2) {
			Calendar ca = Calendar.getInstance();
			ca.setTime(task3.getStartDate());
			ca.set(Calendar.DAY_OF_MONTH, ca.get(Calendar.DAY_OF_MONTH) + 7);
			taskVo.setDate(ca.getTime());
		}

		tasks3.add(task3);
		dayTask3.setTasks(tasks3);
		taskVo.setDay3(dayTask3);

		return taskVo;
	}

	@Override
	public int day3TaskFinish(Long customerId) {
		Wechat wechat = wechatDao.getWechatInfoByCustomerId(customerId);
		Day3Task task = this.getTask(wechat.getUnionid(), wechat.getCustomerId());
		if(task.getStatus() != 2) {
			Result re = outsideService.getCoupon(wechat.getUnionid(), "50");
			if(re.getCode() == 0) {
				motService.day3TaskFinishNotice( customerId);
				return taskDao.finishDay3Task(wechat.getUnionid());
			}
		}
		return 0;
	}

	@Override
	public int day3TaskNotice(Long customerId, int status) {
		Wechat wechat = wechatDao.getWechatInfoByCustomerId(customerId);
		Day3Task task = this.getTask(wechat.getUnionid(), customerId);		
		task.setNotice(status);
		taskDao.noticeChangeDay3Task(task);
		
		return 1;
	}

}
