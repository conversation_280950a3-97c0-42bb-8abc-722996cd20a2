package cn.slashsoft.clinique.controller.mini;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.slashsoft.clinique.service.outside.AliyunOssService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.FileService;
import cn.slashsoft.clinique.util.ResultUtil;
import io.netty.util.internal.StringUtil;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 活动相关控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class FileController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;

    @Resource
    private HttpServletRequest request;

    @Resource
    private AliyunOssService aliyunOssService;

    @Resource
    private FileService fileService;

    /**
     * 上传头像
     * @param file 文件
     * @return 结果
     */
    @PostMapping("/upload-avatar")
    public String fileUpload(@RequestParam("file") MultipartFile file){
        try {
            return ResultUtil.successToJson(aliyunOssService.uploadImage(file.getInputStream(), "w/fans/avatar/"));
        }
        catch (Exception e){
            return ResultUtil.failToJson();
        }
    }

    static final Set<String> imageSuffix = new HashSet<>(Arrays.asList("jpg","jpeg","gif","png","webp"));

    /**
     * 图片上传
     *
     * @param file 图片
     * @return 处理结果
     */
    @PostMapping("/file/upload")
    public String upload(@RequestParam("file") MultipartFile file) {
        // 文件的原始名称
        String name = file.getOriginalFilename();
        if (name == null) {
            return ResultUtil.customer(ResultEnum.FAILED,"文件名称不能为空");
        }
        // 解析出文件后缀
        int index = name.lastIndexOf(".");
        if (index == -1) {
            return ResultUtil.customer(ResultEnum.FAILED,"文件后缀不能为空");
        }
        String suffix = name.substring(index + 1);
        if (!imageSuffix.contains(suffix.trim().toLowerCase())) {
            return ResultUtil.customer(ResultEnum.FAILED,"非法的文件类型");
        }
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        if(customerId == 0) {
        	return ResultUtil.customer(ResultEnum.FAILED);
        }
        String fileName = fileService.upload(customerId, file);
        if (StringUtil.isNullOrEmpty(fileName)) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, fileName);

    }
}
