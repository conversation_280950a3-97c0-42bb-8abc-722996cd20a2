package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignG520;

/**
 * 520活动
 *
 * <AUTHOR>
 */
public interface CampaignG520Service {

    /**
     * 获取520活动信息
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @param openid     小程序唯一编号
     * @return 520活动信息
     */
    CampaignG520 getCampaignG520(String openid, long customerId, String source);

    /**
     * 设置定位信息
     * @param customerId 顾客编号
     * @param locationAuthorize 是否授权
     */
    void setCampaignG520LocationAuthorize(long customerId, boolean locationAuthorize);

    /**
     * 写入访问开始日志
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @param page       页面
     * @return 访问日志编号
     */
    long insertCampaignViewLog(long customerId, String source, String page);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    void setCampaignViewLog(long id);

}
