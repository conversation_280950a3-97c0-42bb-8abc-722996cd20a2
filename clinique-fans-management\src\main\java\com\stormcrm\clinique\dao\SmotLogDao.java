package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.Smot;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * 模版消息
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface SmotLogDao {

    /**
     * 写入一次性订阅消息
     *
     * @param smot 一次性订阅消息
     */
    @Insert("INSERT INTO `smot`(" +
            "   `wechat_mini_openid`, " +
            "   `type`, " +
            "   `param`, " +
            "   `result`, " +
            "   `status`" +
            ") " +
            "VALUES (" +
            "   #{wechatMiniOpenid}, " +
            "   #{type}, " +
            "   #{param}, " +
            "   #{result}, " +
            "   #{status}" +
            ")")
    void insertSmot(Smot smot);

}
