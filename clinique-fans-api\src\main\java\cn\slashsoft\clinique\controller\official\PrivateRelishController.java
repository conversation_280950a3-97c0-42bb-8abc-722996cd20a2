package cn.slashsoft.clinique.controller.official;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import cn.slashsoft.clinique.domain.official.CustomerServiceStaff;
import cn.slashsoft.clinique.service.outside.PrivateRelishService;
/**
 *  美颜会 预约成功
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/official")
public class PrivateRelishController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Value("${wechat.oss.url}")
    private String urlOss;

    @Resource
    private HttpServletRequest request;

    private final PrivateRelishService prService;

    public PrivateRelishController(PrivateRelishService prService) {
        this.prService = prService;
    }

    /**
     *  预约
     *
     */
    @GetMapping("/private-relish/result")
    public String index(
    		Model model,
            @RequestParam("number") String phone,
            @RequestParam("code") String storeCode) {
    
            model.addAttribute("staticDomain", staticDomain);
        CustomerServiceStaff staff = this.prService.addPrivateRelish(phone, storeCode);
        
        if(staff != null) {
            model.addAttribute("qrcode", staff.getQrcode());
            model.addAttribute("store", staff.getName());
            return "w/fans/official/counter/privaterelish-result";
        }
        return "出错了，您的信息未找到";
    }



}
