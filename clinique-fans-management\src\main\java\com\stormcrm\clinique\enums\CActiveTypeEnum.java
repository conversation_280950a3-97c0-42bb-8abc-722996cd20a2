package com.stormcrm.clinique.enums;

import lombok.Getter;

/**
 * C粉值类别
 *
 * <AUTHOR> 
 */
public enum CActiveTypeEnum {

    NOTE_VIDEO_READ          ((short) 1, "阅读笔记/观看视频", 2,1),
    NOTE_VIDEO_LIKE             ((short) 2, "关注笔记关注视频", 2,1),
    NOTE_DISCUSS          ((short) 3, "发布评论", 2,1),
    NOTE_PUBLISH             ((short) 4, "成功发布笔记心得", 15,1),
    SHARE_READ             ((short) 5, "分享给好友，好友阅读笔记/观看视频", 10,1),
    SHARE_READ_BIND            ((short) 6, "分享给好友，好友阅读笔记/观看视频且绑定C粉圈", 20,1),
    LOGIN            ((short) 7, "登录", 2,1),
    LOGIN_3            ((short) 8, "连续三天登录", 5,1),
    LOGIN_7            ((short) 9, "连续七天登录", 10,1),
    LOGIN_NO           ((short) 10, "一天未登录", -1,1),
    LOGIN_NO_5           ((short) 11, "连续五天未登录", -15,1)
    ;
	
    @Getter
    private final short id;

    @Getter
    private final String name;

    @Getter
    private final int points;
    
    @Getter
    private final int max;

    CActiveTypeEnum(short id, String name, int points, int max) {
        this.id = id;
        this.name = name;
        this.points = points;
        this.max = max;
    }
}
