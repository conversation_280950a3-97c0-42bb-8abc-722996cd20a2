package cn.slashsoft.clinique.service.campaign.impl;

import cn.slashsoft.clinique.dao.campaign.CampaignMsTalentsDao;
import cn.slashsoft.clinique.domain.mini.Follow;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.domain.campaign.*;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.service.campaign.CampaignMsTalentsService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.RandomUtil;
import cn.slashsoft.clinique.util.ServerUtil;
import cn.slashsoft.clinique.util.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.Date;
import java.util.List;

/**
 * MS 达人榜
 *
 * <AUTHOR>
 */
@Service
public class CampaignMsTalentsServiceImpl implements CampaignMsTalentsService {

    private final CampaignMsTalentsDao campaignMsTalentsDao;
    private final WechatService wechatService;
    private final OutsideService outsideService;

    public CampaignMsTalentsServiceImpl(CampaignMsTalentsDao campaignMsTalentsDao, WechatService wechatService, OutsideService outsideService) {
        this.campaignMsTalentsDao = campaignMsTalentsDao;
        this.wechatService = wechatService;
        this.outsideService = outsideService;
    }

    /**
     * 获取达人榜信息
     *
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    @Override
    public CampaignMsTalents getCampaignMsTalentsAlert(long customerId) {
        return campaignMsTalentsDao.getCampaignMsTalentsWithImageCount(customerId);
    }

    /**
     * 获取达人榜信息
     *
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    @Override
    public CampaignMsTalents getCampaignMsTalents(long customerId) {

        CampaignMsTalents campaignMsTalents = campaignMsTalentsDao.getCampaignMsTalentsWithImageCount(customerId);

        if (null == campaignMsTalents) {
            // 生成活动信息
            campaignMsTalents = new CampaignMsTalents();
            campaignMsTalents.setCustomerId(customerId);
            campaignMsTalents.setAlert(false);
            campaignMsTalents.setStatus((short) 1);

            // 读取公众号openid
            Wechat wechat = wechatService.getWechatInfoByCustomerId(customerId);
            if (null != wechat && !StringUtil.isNullOrEmpty(wechat.getWechatOfficialOpenid())) {
                Follow follow = outsideService.isFollow(wechat.getWechatOfficialOpenid());
                if (null != follow) {
                    campaignMsTalents.setFollow(follow.getFollow());
                    campaignMsTalents.setFollowSource(follow.getFollowSource());
                    campaignMsTalents.setFollowFirstTime(follow.getFollowFirstTime());
                    campaignMsTalents.setFollowLastTime(follow.getFollowLastTime());
                    campaignMsTalents.setFollowCancelTime(follow.getFollowCancelTime());
                    campaignMsTalents.setBind(follow.getBind());
                    campaignMsTalents.setBindTime(follow.getBindTime());
                }
            }

            campaignMsTalentsDao.insertCampaignMsTalents(campaignMsTalents);

            // 写入日志
            CampaignMsTalentsLog campaignMsTalentsLog = new CampaignMsTalentsLog();
            campaignMsTalentsLog.setCustomerId(customerId);
            campaignMsTalentsLog.setStatus((short) 1);
            campaignMsTalentsLog.setContent("生成活动信息");
            campaignMsTalentsDao.insertCampaignMsTalentsLog(campaignMsTalentsLog);
        } else {
            // 状态为未上传时，将所有图片改为无效
            if (1 == campaignMsTalents.getStatus() && 0 < campaignMsTalents.getImageCount()) {
                campaignMsTalentsDao.updateCampaignMsTalentsImageStatus(customerId);
            }
        }

        return campaignMsTalents;
    }

    /**
     * 更新提醒状态
     *
     * @param customerId 顾客编号
     */
    @Override
    public void updateCampaignMsTalentsAlert(long customerId) {
        campaignMsTalentsDao.updateCampaignMsTalentsAlert(customerId);
    }

    /**
     * 获取图片
     *
     * @param talentsId 顾客活动信息编号
     * @return 图片地址
     */
    @Override
    public List<CampaignMsTalentsImage> getCampaignMsTalentsImageList(long talentsId) {
        return campaignMsTalentsDao.getCampaignMsTalentsImageList(talentsId);
    }

    /**
     * 获取当前TOP信息
     *
     * @return TOP信息
     */
    @Override
    public CampaignMsTalentsTop getCampaignMsTalentsTop() {
        return campaignMsTalentsDao.getCampaignMsTalentsTop();
    }

    /**
     * 获取入选TOP的名单
     *
     * @param talentsTopId TOP信息编号
     * @return 名单
     */
    @Override
    public List<CampaignMsTalentsTopDetail> getCampaignMsTalentsTopDetailList(long talentsTopId) {
        return campaignMsTalentsDao.getCampaignMsTalentsTopDetailList(talentsTopId);
    }

    /**
     * 获取入选TOP的图片
     *
     * @param talentsTopId TOP信息编号
     * @return 图片
     */
    @Override
    public List<CampaignMsTalentsTopDetailImage> getCampaignMsTalentsTopDetailImageList(long talentsTopId) {
        return campaignMsTalentsDao.getCampaignMsTalentsTopDetailImageList(talentsTopId);
    }

    /**
     * 保存图片
     *
     * @param customerId 顾客编号
     * @param file       要保存的图片
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean upload(long customerId, MultipartFile file) {

        // 读取活动信息
        CampaignMsTalents campaignMsTalents = campaignMsTalentsDao.getCampaignMsTalentsWithImageCount(customerId);
        if (null == campaignMsTalents) {
            return false;
        }

        CampaignMsTalentsLog campaignMsTalentsLog;
        switch (campaignMsTalents.getStatus()) {
            // 未上传
            case 1:
                // 变更状态为2审核中
                if (0 == campaignMsTalentsDao.updateCampaignMsTalentsWithUploadTime(customerId, (short) 1, (short) 2)) {
                    return false;
                }
                // 写入日志
                campaignMsTalentsLog = new CampaignMsTalentsLog();
                campaignMsTalentsLog.setCustomerId(customerId);
                campaignMsTalentsLog.setStatus((short) 2);
                campaignMsTalentsLog.setContent("状态从未上传变更为审核中");
                campaignMsTalentsDao.insertCampaignMsTalentsLog(campaignMsTalentsLog);
                break;
            // 审核中
            case 2:
                // 检查当前相片的数量，超过3则不再保存
                if (3 <= campaignMsTalents.getImageCount()) {
                    return false;
                }
                break;
            // 审核失败
            case 3:
                // 变更状态为2审核中
                if (0 == campaignMsTalentsDao.updateCampaignMsTalents(customerId, (short) 3, (short) 2)) {
                    return false;
                }
                // 原上传图片全部设为无效
                campaignMsTalentsDao.updateCampaignMsTalentsImageStatus(campaignMsTalents.getId());
                // 写入日志
                campaignMsTalentsLog = new CampaignMsTalentsLog();
                campaignMsTalentsLog.setCustomerId(customerId);
                campaignMsTalentsLog.setStatus((short) 2);
                campaignMsTalentsLog.setContent("状态从审核未通过变更为审核中");
                campaignMsTalentsDao.insertCampaignMsTalentsLog(campaignMsTalentsLog);
                break;
            default:
                return false;
        }

        // 保存图片
        String imageUrl = outsideService.uploadImageOss(file);
        if (StringUtil.isNullOrEmpty(imageUrl)) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }

        // 保存数据库
        CampaignMsTalentsImage campaignMsTalentsImage = new CampaignMsTalentsImage();
        campaignMsTalentsImage.setTalentsId(campaignMsTalents.getId());
        campaignMsTalentsImage.setImageUrl(imageUrl);
        campaignMsTalentsDao.insertCampaignMsTalentsImage(campaignMsTalentsImage);

        // 写入日志
        campaignMsTalentsLog = new CampaignMsTalentsLog();
        campaignMsTalentsLog.setCustomerId(customerId);
        campaignMsTalentsLog.setStatus((short) 2);
        campaignMsTalentsLog.setContent("上传图片");
        campaignMsTalentsDao.insertCampaignMsTalentsLog(campaignMsTalentsLog);

        return true;
    }

    /**
     * 保存图片
     *
     * @param path         目录
     * @param originalFile 要保存的图片
     * @return 保存成功的文件地址
     */
    private String saveImageFile(String path, MultipartFile originalFile) {

        try {
            Date now = new Date();

            // 获取静态资源文件的目录地址
            String staticPath = ServerUtil.getStaticPath();

            // 保存位置
            path = path + DateUtil.parseString(now, "yyyyMMdd");
            // 如果目录不存在，创建目录
            File folder = new File(staticPath + path);
            if (!folder.exists()) {
                if (folder.mkdirs()) {
                    throw new Exception("创建目录失败");
                }
            }

            // 获取上传文件名的后缀名
            String originalFileName = originalFile.getOriginalFilename();
            if (StringUtil.isNullOrEmpty(originalFileName)) {
                throw new Exception("上传的文件名为空");
            }
            // 生成文件名
            String fileName = path + "/" + DateUtil.parseString(now, "HHmmssSS") + RandomUtil.getNumberBetween(1000, 9999) + originalFileName.substring(originalFileName.lastIndexOf("."));

            File imageFile = new File(staticPath + fileName);

            originalFile.transferTo(imageFile);

            return fileName;
        } catch (Exception e) {
            return null;
        }

    }

    /**
     * 更新肤质
     *
     * @param customerId 顾客编号
     * @param skinType   肤质
     */
    @Override
    public void setCampaignMsTalentsSkinType(long customerId, short skinType) {
        campaignMsTalentsDao.setCampaignMsTalentsSkinType(customerId, skinType);
    }

    /**
     * 写入访问开始日志
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @param page       页面
     * @return 访问日志编号
     */
    @Override
    public long insertCampaignMsTalentsViewLog(long customerId, String source, String page) {
        CampaignMsTalentsViewLog campaignMsTalentsViewLog = new CampaignMsTalentsViewLog();
        campaignMsTalentsViewLog.setCustomerId(customerId);
        campaignMsTalentsViewLog.setSource(source);
        campaignMsTalentsViewLog.setPage(page);
        campaignMsTalentsDao.insertCampaignMsTalentsViewLog(campaignMsTalentsViewLog);
        return campaignMsTalentsViewLog.getId();
    }

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Override
    public void setCampaignMsTalentsViewLog(long id) {
        campaignMsTalentsDao.setCampaignMsTalentsViewLog(id);
    }

    /**
     * 写入达人榜访问记录
     *
     * @param campaignMsTalentsTopDetailLog 达人榜访问记录
     */
    @Override
    public void insertCampaignMsTalentsTopDetailLog(CampaignMsTalentsTopDetailLog campaignMsTalentsTopDetailLog) {
        campaignMsTalentsDao.insertCampaignMsTalentsTopDetailLog(campaignMsTalentsTopDetailLog);
    }

    /**
     * 写入达人榜图片访问记录
     *
     * @param campaignMsTalentsTopDetailImageLog 达人榜图片访问记录
     */
    @Override
    public void insertCampaignMsTalentsTopDetailImageLog(CampaignMsTalentsTopDetailImageLog campaignMsTalentsTopDetailImageLog) {
        campaignMsTalentsDao.insertCampaignMsTalentsTopDetailImageLog(campaignMsTalentsTopDetailImageLog);
    }

}
