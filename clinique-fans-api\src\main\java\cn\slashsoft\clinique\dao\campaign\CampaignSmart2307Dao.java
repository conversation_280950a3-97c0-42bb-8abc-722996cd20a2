package cn.slashsoft.clinique.dao.campaign;

import cn.slashsoft.clinique.domain.campaign.smart2307.Ranking;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 智能活动
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignSmart2307Dao {

    /**
     * 获取点选排名前15名
     *
     * @param tagId 标签编号
     * @return 排名列表
     */
    @Select("SELECT " +
            "   decryptName(`c`.`name`) `name`," +
            "   `w`.`avatar_url`," +
            "   `note`.`like_count` " +
            "FROM " +
            "(" +
            "   SELECT " +
            "       `customer_id`,MAX(`like_count`) `like_count` " +
            "   FROM " +
            "   (" +
            "       SELECT " +
            "           `note`.`customer_id`,`note`.`like_count` " +
            "       FROM " +
            "           `note_photo_tag` `tag` INNER JOIN `note` ON `tag`.`note_id`=`note`.`id` " +
            "       WHERE `tag`.`tag_id`=#{tagId} AND `note`.`status`=2 " +
            "       ORDER BY `note`.`like_count` DESC,`tag`.`createTime` DESC LIMIT 100 " +
            "   ) `note` " +
            "   GROUP BY `customer_id` " +
            "   ORDER BY `like_count` DESC LIMIT 15 " +
            ") `note` " +
            "INNER JOIN `customer` `c` ON `note`.`customer_id`=`c`.`id` " +
            "INNER JOIN `wechat` `w` ON `note`.`customer_id`=`w`.`customer_id`")
    List<Ranking> getTop15RankingList(int tagId);


    /**
     * 获取点选排名前15名
     *
     * @return 排名列表
     */
    @Select("SELECT " +
            "   decryptName(`c`.`name`) `name`," +
            "   `w`.`avatar_url`," +
            "   `r`.`like_count` " +
            "FROM " +
            "`campaign_smart_2303_ranking` `r` " +
            "INNER JOIN `customer` `c` ON `r`.`customer_id`=`c`.`id` " +
            "INNER JOIN `wechat` `w` ON `r`.`customer_id`=`w`.`customer_id` " +
            "WHERE `r`.`status`=1 " +
            "ORDER BY `r`.`like_count` DESC")
    List<Ranking> getTop15();

}
