package com.stormcrm.clinique.util;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * HTTP请求工具类
 *
 * <AUTHOR>
 */
public class HttpUtil {

    /**
     * GET方式请求接口
     *
     * @param url 接口地址
     * @return 返回值字符串
     * @throws IOException 读写异常
     */
    public static String get(String url) throws IOException {
        HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
        InputStream inputStream = connection.getInputStream();
        String result = read(connection.getInputStream());
        connection.disconnect();
        inputStream.close();
        return result;
    }

    /**
     * 请求商城获取信息接口
     *
     * @param url   链地址
     * @param param 参数
     * @return 字符串
     * @throws IOException 读写异常
     */
    public static String postMall(String url, String param) throws IOException {
        Map<String, String> header = new HashMap<>();
        header.put("version", "v2.0");
        header.put("access-token", "3ccfB8ab8NKjP8XZqDNqg2Xq8GYSwUd9");
        return post(url, param, "json", header);
    }

    /**
     * POST方式请求接口
     *
     * @param url   接口地址
     * @param param JSON格式的参数
     * @return 返回值字符串
     * @throws IOException 读写异常
     */
    public static String postJson(String url, String param) throws IOException {
        return post(url, param, "json");
    }

    /**
     * POST方式请求接口
     *
     * @param url   接口地址
     * @param param FORM格式的参数
     * @return 返回值字符串
     * @throws IOException 读写异常
     */
    public static String postForm(String url, String param) throws IOException {
        return post(url, param, "x-www-form-urlencoded");
    }

    /**
     * POST方式请求接口
     *
     * @param url    接口地址
     * @param param  参数
     * @param format 参数的格式
     * @return 返回值字符串
     * @throws IOException 读写异常
     */
    public static String post(String url, String param, String format) throws IOException {
        InputStream inputStream = postGetInputStream(url, param, format);
        return read(inputStream);
    }

    /**
     * POST方式请求接口
     *
     * @param url    接口地址
     * @param param  参数
     * @param format 参数的格式
     * @param header 请求头中的参数
     * @return 返回值字符串
     * @throws IOException 读写异常
     */
    public static String post(String url, String param, String format, Map<String, String> header) throws IOException {
        InputStream inputStream = postGetInputStream(url, param, format, header);
        return read(inputStream);
    }

    /**
     * POST方式请求接口
     *
     * @param url    接口地址
     * @param param  参数
     * @param format 参数的格式
     * @return 输入流
     * @throws IOException 读写异常
     */
    public static InputStream postGetInputStream(String url, String param, String format) throws IOException {
        return postGetInputStream(url, param, format, null);
    }

    /**
     * POST方式请求接口
     *
     * @param url    接口地址
     * @param param  参数
     * @param format 参数的格式
     * @param header 请求头中的参数
     * @return 输入流
     * @throws IOException 读写异常
     */
    public static InputStream postGetInputStream(String url, String param, String format, Map<String, String> header) throws IOException {
        HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/" + format);
        connection.setRequestProperty("Connection", "Keep-Alive");
        connection.setRequestProperty("Charset", "UTF-8");
        if (null != header && 0 < header.size()) {
            header.forEach(connection::setRequestProperty);
        }
        connection.setUseCaches(false);
        connection.setDoOutput(true);
        connection.setDoInput(true);
        connection.connect();
        DataOutputStream dataOutputStream = new DataOutputStream(connection.getOutputStream());
        dataOutputStream.write(param.getBytes(StandardCharsets.UTF_8));
        dataOutputStream.flush();
        dataOutputStream.close();
        return connection.getInputStream();
    }

    /**
     * POST方式请求接口
     *
     * @param url   接口地址
     * @param param JSON格式的参数
     * @return 输入流
     * @throws IOException 读写异常
     */
    public static InputStream postJsonGetInputStream(String url, String param) throws IOException {
        return postGetInputStream(url, param, "json");
    }

    /**
     * POST方式请求接口
     *
     * @param url   接口地址
     * @param param FORM格式的参数
     * @return 输入流
     * @throws IOException 读写异常
     */
    public static InputStream postFormGetInputStream(String url, String param) throws IOException {
        return postGetInputStream(url, param, "x-www-form-urlencoded");
    }

    /**
     * 从输入流中获取字符串
     *
     * @param inputStream 输入流
     * @return 返回值字符串
     * @throws IOException 读写异常
     */
    private static String read(InputStream inputStream) throws IOException {
        BufferedReader bufferedContentReader = new BufferedReader(new InputStreamReader(inputStream));
        StringBuilder buffer = new StringBuilder();
        String line;
        while ((line = bufferedContentReader.readLine()) != null) {
            buffer.append(line);
        }
        return buffer.toString();
    }

}
