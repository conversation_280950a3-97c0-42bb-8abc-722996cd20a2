package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.dao.VideoTagDao;
import com.stormcrm.clinique.domain.VideoTag;
import com.stormcrm.clinique.domain.VideoTagDefine;
import com.stormcrm.clinique.service.VideoTagService;
import com.stormcrm.clinique.vo.EditVideoWithTagVo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class VideoTagServiceImpl implements VideoTagService {

    private final VideoTagDao videoTagDao;

    public VideoTagServiceImpl(VideoTagDao videoTagDao) {
        this.videoTagDao = videoTagDao;
    }


    /**
     * 添加视频标签
     *
     * @param videoTagDefine 视频标签对象
     */
    @Override
    public void insert(VideoTagDefine videoTagDefine) {
        videoTagDao.insertVideoTagDefine(videoTagDefine);
    }

    /**
     * 更新视频标签
     *
     * @param videoTagDefine 视频标签对象
     */
    @Override
    public void update(VideoTagDefine videoTagDefine) {
        videoTagDao.updateVideoTagDefine(videoTagDefine);
    }

    /**
     * 获取单个视频标签
     *
     * @param id 视频标签id
     * @return 视频标签
     */
    @Override
    public VideoTagDefine get(Long id) {
        return videoTagDao.getVideoTagDefine(id);
    }

    /**
     * 根据视频标签id删除
     *
     * @param id 视频标签id
     * @return 1 or 0
     */
    @Override
    public int delete(Long id) {
        return videoTagDao.deleteVideoTagDefine(id);
    }

    /**
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索
     * @param type          状态
     * @return 标签列表
     */
    @Override
    public List<VideoTagDefine> getPage(int page, int perpage, String generalSearch, Short type) {
        return videoTagDao.getPage((page - 1) * perpage, perpage, generalSearch, type);
    }

    /**
     * @param generalSearch 搜索
     * @param type          状态
     * @return 分页总数
     */
    @Override
    public int getPageCount(String generalSearch, Short type) {
        return videoTagDao.getPageCount(generalSearch, type);
    }

    /**
     * 获取全部标签
     */
    @Override
    public ArrayList<VideoTagDefine> getTagDefineList() {
        return videoTagDao.getTagDefineList();
    }

    /**
     * 获取某个 视频的全部标签
     */
    @Override
    public ArrayList<VideoTag> getTagListByVideoId(Long videoId) {
        return videoTagDao.getVideoTagByVideoId(videoId);
    }

    /**
     * 复选框中被选中的标签和全部标签的集合
     *
     * @param videoId 视频id
     * @return 复选框中被选中的标签和全部标签的集合
     */
    @Override
    public ArrayList<EditVideoWithTagVo> getTagListSelected(long videoId) {
        return videoTagDao.getTagListSelected(videoId);
    }


}
