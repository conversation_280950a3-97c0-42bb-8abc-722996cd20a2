package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.dao.campaign.CampaignD11Dao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CampaignD11Service {

    private final CampaignD11Dao campaignD11Dao;

    public CampaignD11Service(CampaignD11Dao campaignD11Dao) {
        this.campaignD11Dao = campaignD11Dao;
    }

    public String getCustomerNameByCode(String code) {
        return campaignD11Dao.getCustomerNameByCode(code);
    }

    public String signIn(String code) {
        if (null == campaignD11Dao.getSignStatusByCode(code)) {
            return "error";
        }
        if (campaignD11Dao.getSignStatusByCode(code) == 1) {
            return "all ready signed";
        } else {
            campaignD11Dao.customerSignIn(code);
            return "done";
        }
    }

    public String signIn() {
        return "end";
    }
}
