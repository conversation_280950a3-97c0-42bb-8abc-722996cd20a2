package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.domain.CampaignMsTalentsTop;
import com.stormcrm.clinique.service.CampaignMsTalentsTopService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.vo.Result;
import com.stormcrm.clinique.dao.CampaignMsTalentsTopDao;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 新品体验官活动
 *
 * <AUTHOR>
 */
@Service
public class CampaignMsTalentsTopServiceImpl implements CampaignMsTalentsTopService {

    private final CampaignMsTalentsTopDao campaignMsTalentsTopDao;

    public CampaignMsTalentsTopServiceImpl(CampaignMsTalentsTopDao campaignMsTalentsTopDao) {
        this.campaignMsTalentsTopDao = campaignMsTalentsTopDao;
    }

    /**
     * 查询所有活动
     *
     * @return 活动列表
     */
    @Override
    public List<CampaignMsTalentsTop> getAll() {
        return campaignMsTalentsTopDao.getAll();
    }

    /**
     * 查询信息
     *
     * @param id 自动编号
     * @return 信息
     */
    @Override
    public CampaignMsTalentsTop getById(long id) {
        return campaignMsTalentsTopDao.getById(id);
    }

    /**
     * 保存
     *
     * @param campaignMsTalentsTop 信息
     * @return 影响的行数
     */
    @Override
    public Result save(CampaignMsTalentsTop campaignMsTalentsTop) {
        campaignMsTalentsTopDao.save(campaignMsTalentsTop);
        return ResultUtil.success("增加成功!");
    }

    /**
     * 更新
     *
     * @param campaignMsTalentsTop 信息
     * @return 影响的行数
     */
    @Override
    public Result update(CampaignMsTalentsTop campaignMsTalentsTop) {
        campaignMsTalentsTopDao.update(campaignMsTalentsTop);
        return ResultUtil.success("编辑成功!");
    }
}
