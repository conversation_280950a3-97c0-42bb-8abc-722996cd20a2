package com.stormcrm.clinique.tag.dao;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import com.stormcrm.clinique.domain.tag.TagTask;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
public interface TagTaskDao {

    @Insert("INSERT INTO tasks ("
            + "`name`, "
            + "`desc`, "
            + "`target`, "
            + "`label_id`, "
            + "`target_id`, "
            + "`run_type`, "
            + "`run_time`, "
            + "`action_start_time`, "
            + "`action_end_time`, "
            + "`action_count`, "
            + "`cycle_type`, "
            + "`cycle_interval`, "
            + "`cycle_value`, "
            + "`user`, "
            + "`create_time`, "
            + "`update_time`, "
            + "`progress`, "
            + "`status` "     
            + ") VALUES ("
            + "#{name}, "
            + "#{desc}, "
            + "#{target}, "
            + "#{labelId}, "
            + "#{targetId}, "
            + "#{runType}, "
            + "#{runTime}, "
            + "#{actionStartTime}, "
            + "#{actionEndTime}, "
            + "#{actionCount}, "
            + "#{cycleType}, "
            + "#{cycleInterval}, "
            + "#{cycleValue}, "
            + "1, "
            + "now(), "
            + "now(), "
            + "#{progress}, "
            + "1 " 
            + ") ")
    int addTagTask(TagTask task);

    @Update("UPDATE tasks  SET "
            + "`name`=#{name}, "
            + "`desc`= #{desc}, "
            + "`target`= #{target}, "
            + "`label_id`= #{labelId}, "
            + "`target_id`= #{targetId}, "
            + "`run_type`= #{runType}, "
            + "`run_time`= #{runTime}, "
            + "`action_start_time`= #{actionStartTime}, "
            + "`action_end_time`= #{actionEndTime}, "
            + "`action_count`= #{actionCount}, "
            + "`cycle_type`= #{cycleType}, "
            + "`cycle_interval`= #{cycleInterval}, "
            + "`cycle_value`= #{cycleValue}, "
            + "`update_time`=now(), "
            + "`progress`= #{progress} "
            + " WHERE id = #{id} ")
    int updateTagTask(TagTask task);
    
    @Delete("DELETE from tasks where id = #{taskId}")
    int removeTagTask(int taskId);
    
    @Select("select * from tasks ")
    List<TagTask> getTagTaskAll();

    @Select("select * from tasks where progress=#{progress} ")
    List<TagTask> getTagTaskByProgress(int progress);


    @Select("select * from tasks where target=#{target} ")
    List<TagTask> getTagTaskByTarget(int target);

    @Select("select * from tasks where id = #{taskId}")
    TagTask getTagTask(int taskId);

    @Select("select * from tasks where progress=#{progress} and target = #{target} ")
	List<TagTask> getTagTaskBy(int progress, int target);
}
