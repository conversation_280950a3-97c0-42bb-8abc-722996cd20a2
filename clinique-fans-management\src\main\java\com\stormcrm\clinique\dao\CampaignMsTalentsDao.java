package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.CampaignMsTalents;
import com.stormcrm.clinique.domain.CampaignMsTalentsImage;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 新品体验官活动
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignMsTalentsDao {

    /**
     * 查询所有活动-分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 活动列表
     */
    @SelectProvider(type = CampaignMsTalentsProvider.class, method = "getPage")
    List<CampaignMsTalents> getPage(
            @Param("pageIndex") int pageIndex,
            @Param("pageSize") int pageSize,
            @Param("generalSearch") String generalSearch,
            @Param("status") Short status
    );

    /**
     * 查询所有活动带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    @SelectProvider(type = CampaignMsTalentsProvider.class, method = "getPageCount")
    int getPageCount(
            @Param("generalSearch") String generalSearch,
            @Param("status") Short status
    );

    /**
     * 获取美白达人官信息
     *
     * @param id 编号
     * @return 美白达人官信息
     */
    @Select("SELECT " +
            "   `t`.`id`," +
            "   `w`.`wechat_mini_openid`, " +
            "   `w`.`nick_name`, " +
            "   `w`.`avatar_url`, " +
            "   `t`.`customer_id`," +
            "   `t`.`status` " +
            "FROM " +
            "   `campaign_ms_talents` `t` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `t`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "   `t`.`id`=#{id} " +
            "LIMIT 1")
    CampaignMsTalents getById(long id);

    /**
     * 获取美白达人官信息
     *
     * @param idList 编号
     * @return 美白达人官信息
     */
    @Select({"<script>" +
            "SELECT ",
            "   `t`.`id`,",
            "   `w`.`wechat_mini_openid`, ",
            "   `w`.`nick_name`, ",
            "   `w`.`avatar_url`, ",
            "   `t`.`customer_id`,",
            "   `t`.`status` ",
            "FROM ",
            "   `campaign_ms_talents` `t` ",
            "       INNER JOIN ",
            "   `wechat` `w` ",
            "       ON `t`.`customer_id`=`w`.`customer_id` ",
            "WHERE ",
            "   `t`.`id` IN <foreach collection='idList' item='id' open='(' separator=',' close=')'>#{id}</foreach>" +
                    "</script>"})
    List<CampaignMsTalents> getByIds(@Param("idList") List<Long> idList);

    /**
     * 获取图片
     *
     * @param id 顾客活动信息编号
     * @return 图片地址
     */
    @Select("SELECT " +
            "   `image_url` " +
            "FROM " +
            "   `campaign_ms_talents_image` " +
            "WHERE " +
            "   `talents_id`=#{id}")
    List<CampaignMsTalentsImage> getImageById(long id);

    /**
     * 审核成功
     *
     * @param id 编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ms_talents` " +
            "SET " +
            "   `alert`=1," +
            "   `status`=4," +
            "   `examine_time`=NOW() " +
            "WHERE " +
            "   `id`=#{id} " +
            "   AND (`status`=2 OR `status`=3)")
    int accept(long id);

    /**
     * 审核失败
     *
     * @param id 编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ms_talents` " +
            "SET " +
            "   `alert`=1," +
            "   `status`=3," +
            "   `examine_time`=NOW() " +
            "WHERE " +
            "   `id`=#{Id} " +
            "   AND `status`=2")
    int reject(long id);

    /**
     * 停用所有图片
     *
     * @param id 编号
     */
    @Update("UPDATE " +
            "   `campaign_ms_talents_image` " +
            "SET " +
            "   `status`=0 " +
            "WHERE " +
            "   `talents_id`=#{id}")
    void setImageStatus(long id);

}
