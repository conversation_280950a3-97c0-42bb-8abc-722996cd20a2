package com.stormcrm.clinique.domain;

import lombok.Data;

import java.util.Date;

/**
 * 商城兑换表
 *
 * <AUTHOR>
 */
@Data
public class StoreExchange {

    private Long id;
    private String exchangeOrder;
    private Long customerId;
    private Long storeGiftId;
    private String storeGiftName;
    private String imageUrl;
    private Short storeExchangeTypeId;
    private Short storeExchangeLogisticsId;
    private String storeExchangeLogisticsName;
    private String storeAreaPlaceName;
    private String storeAreaName;
    private String name;
    private String phoneNumber;
    private String province;
    private String city;
    private String district;
    private String address;
    private Integer points;
    private Boolean status;
    private Boolean recovery;
    private Date updateTime;
    private Date createTime;

}
