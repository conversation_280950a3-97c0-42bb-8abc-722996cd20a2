package com.stormcrm.clinique.oauth.phone;

import org.springframework.security.core.AuthenticationException;
/**
 * <AUTHOR>
 */
public class PhoneNumberNotFoundException extends AuthenticationException {

    private static final long serialVersionUID = 2021010401L;

    public PhoneNumberNotFoundException() {
        super("Phone number was not found.");
    }

    public PhoneNumberNotFoundException(String msg) {
        super(msg);
    }

    public PhoneNumberNotFoundException(String msg, Throwable t) {
        super(msg, t);
    }

}
