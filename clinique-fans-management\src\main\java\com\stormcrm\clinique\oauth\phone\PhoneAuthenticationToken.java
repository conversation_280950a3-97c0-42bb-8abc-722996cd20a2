package com.stormcrm.clinique.oauth.phone;

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * <AUTHOR>
 */
public class PhoneAuthenticationToken
        extends UsernamePasswordAuthenticationToken {

    private static final long serialVersionUID = 2021010402L;

    public PhoneAuthenticationToken(String number, String code) {
        super(number, code);
    }

    public PhoneAuthenticationToken(UserDetails userDetails) {
        super(userDetails, null, userDetails.getAuthorities());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String getCredentials() {
        return (String) super.getCredentials();
    }

}
