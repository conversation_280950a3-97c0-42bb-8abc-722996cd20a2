package cn.slashsoft.clinique.vo.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignMsTalentsImage;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * MS 达人榜
 *
 * <AUTHOR>
 */
@Data
public class CampaignMsTalentsVo {

    private Date startTime;
    private Date endTime;
    private String nickName;
    private String avatarUrl;
    private Boolean alert;
    private Short status;
    private Date uploadTime;
    private Date examineTime;
    private List<CampaignMsTalentsImage> talentsImageList;

}
