package com.stormcrm.clinique.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 会员中心服务
 *
 * <AUTHOR>
 */
@FeignClient("clinique-member-api")
public interface MemberClient {

    /**
     * C粉圈审核通过后发放美白币
     *
     * @param unionid 开放平唯一编号
     * @return 首页数据
     */
    @PostMapping("/api/campaign-study-society-2309/set-cfans/{unionid}")
    String setCfans(@PathVariable("unionid") String unionid);

}
