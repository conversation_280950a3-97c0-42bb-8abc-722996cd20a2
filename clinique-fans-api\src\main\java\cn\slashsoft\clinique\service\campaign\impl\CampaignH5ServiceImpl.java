package cn.slashsoft.clinique.service.campaign.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.logging.Logger;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import cn.slashsoft.clinique.dao.campaign.CampaignH5Dao;
import cn.slashsoft.clinique.domain.campaign.CampaignH5;
import cn.slashsoft.clinique.domain.campaign.CampaignH5AD;
import cn.slashsoft.clinique.domain.campaign.CampaignH5City;
import cn.slashsoft.clinique.domain.campaign.CampaignH5Detail;
import cn.slashsoft.clinique.domain.campaign.CampaignH5Log;
import cn.slashsoft.clinique.domain.campaign.CampaignH5Stock;
import cn.slashsoft.clinique.service.campaign.CampaignH5Service;

/**
 * Campaign H5
 *
 * <AUTHOR>
 */
@Service
public class CampaignH5ServiceImpl implements CampaignH5Service {
	@Resource
    private CampaignH5Dao campaignH5Dao;


    private final Logger logger = Logger.getLogger( CampaignH5ServiceImpl.class.getName());

    /**
     * 保存日志
     *
     * @param CampaignH5Log 日志
     */
    @Override
    public void insertLog(CampaignH5Log CampaignH5Log) {
    	campaignH5Dao.insertLog(CampaignH5Log);
    }

    /**
     * 获取手机号码
     *
     * @param unionid 开放平台唯一编号
     * @return 手机号码
     */
    @Override
    public String getPhoneNumberByUnionid(String unionid) {
        return campaignH5Dao.getPhoneNumberByUnionid(unionid);
    }

    /**
     * 获取手机号码
     *
     * @param openid 公众号唯一编号
     * @return 手机号码
     */
    @Override
    public String getPhoneNumberByOpenid(String openid) {
        return campaignH5Dao.getPhoneNumberByOpenid(openid);
    }

    /**
     * 获取是否已经审领
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    @Override
    public int hasDetail(String phoneNumber, Long campaignId) {
        return campaignH5Dao.hasDetail(phoneNumber, campaignId);
    }

    /**
     * 获取是否已经审领
     *
     * @param openid 公众号唯一编号
     * @return 数量
     */
    @Override
    public int hasDetailByOpenid(String openid, Long campaignId) {
        return campaignH5Dao.hasDetailByOpenid(openid,  campaignId);
    }

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    @Override
    public CampaignH5Detail getDetail(String phoneNumber, Long campaignId) {
        return campaignH5Dao.getDetail(phoneNumber, campaignId);
    }

    /**
     * 申领
     *
     * @param CampaignH5Detail 资料
     * @return 0:成功，1：手机号码已经领过了，2：库存不足, 3： 其他错误
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int submit(CampaignH5Detail campaignH5Detail) {
        // 扣库存
        //if (0 == CampaignH5Dao.updateStock(CampaignH5Detail)) {
        //    return 2;
        //}

    	if(campaignH5Dao.getDetail(campaignH5Detail.getPhoneNumber(), campaignH5Detail.getCampaignId()) != null) {
    		return 1;
    	}

    	//CampaignH5Stock store = campaignH5Dao.getByStoreName(CampaignH5Detail);
        //if (null == store) {
            //Tmall 线下活动
       // } else if(!store.getStatus()) {
    	//	return 2;
    	//}

        // 写入记录
        try {
        	campaignH5Dao.insetDetail(campaignH5Detail);
            return 0;
        } catch (Exception e) {
        	logger.info("submit: "+e.getMessage());
        	
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return 3;
        }

    }
   

    /**
     * 更新是否关注
     *
     * @param CampaignH5Detail 申领信息
     */
    @Override
    public void setFollow(CampaignH5Detail CampaignH5Detail) {
    	campaignH5Dao.setFollow(CampaignH5Detail);
    }

    /**
     * 获取审领信息
     *
     * @param openid 公众号唯一编号
     * @return 审领信息
     */
    @Override
    public CampaignH5Detail getDetailByOpenid(String openid, Long campaignId) {
        return campaignH5Dao.getDetailByOpenid(openid,  campaignId);
    }

    /**
     * 根据场景值获取门店
     *
     * @param scene 场景值
     * @return 门店
     */
    @Override
    public CampaignH5Stock getByScene(String scene, Long campaignId) {
        return campaignH5Dao.getByScene(scene, campaignId);
    }

    /**
     * 根据门店名称获取门店状态
     *
     * @param scene 场景值
     * @return 门店
     */
    @Override
    public CampaignH5Stock getByStoreName(CampaignH5Detail CampaignH5Detail) {
        return campaignH5Dao.getByStoreName(CampaignH5Detail);
    }
    /**
     * 更新核销信息
     *
     * @param CampaignH5Detail 核销信息
     */
    @Override
    public void setReceive(CampaignH5Detail CampaignH5Detail) {
    	campaignH5Dao.setReceive(CampaignH5Detail);
    }

    /**
     * 写入ad信息
     *
     * @param CampaignH5AD ad
     * @return ad
     */
    @Override
    public void addAD(CampaignH5AD CampaignH5AD) {
    	campaignH5Dao.addAD(CampaignH5AD);
    }

    /**
     * 根据手机号获取ad
     *
     * @param phoneNumber 手机号
     * @return ad
     */
    @Override
    public CampaignH5AD getADByPhoneNumber(String phoneNumber, Long campaignId) {
    	return campaignH5Dao.getADByPhoneNumber(phoneNumber,campaignId);
    }

    /**
     * 更新ad信息
     *
     * @param CampaignH5AD ad
     * @return ad
     */
    @Override
    public void setAD(CampaignH5AD CampaignH5AD) {
    	campaignH5Dao.setAD(CampaignH5AD);
    }
    /**
     * 临时记录需求
     *
     * @param CampaignH5Detail 资料
     * @return 0:成功，1：手机号码已经领过了，2：库存不足, 3： 其他错误
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int tmpsubmit(CampaignH5Detail campaignH5Detail) {
    	//CampaignH5Stock store = CampaignH5Dao.getByStoreName(CampaignH5Detail);
    	//if(!store.getStatus()) {
    	//	return 2;
    	//}
    	if(campaignH5Dao.getTmpDetail(campaignH5Detail.getPhoneNumber(), campaignH5Detail.getCampaignId()) != null) {
    		//已经领取
    		return 1;
    	}
    	
            // 写入记录
            try {
                campaignH5Dao.addDetail(campaignH5Detail);
                return 0;
            } catch (Exception e) {
            	logger.info("tmp submit: "+e.getMessage());
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return 3;
            }

    }

	@Override
	public CampaignH5City getCampaignCity(String city, long campaign) {		
		int all = campaignH5Dao.getCityCount(campaign);
		if(0 == all) {
			CampaignH5City ncity = new CampaignH5City();
			ncity.setCity("所有");
			return ncity;
		}
		return campaignH5Dao.getCity(city, campaign);
	}

	@Override
	public CampaignH5 getCampaign(String campaignCode) {
		return campaignH5Dao.getCampaign(campaignCode);
	}

    @Override
    public CampaignH5 getValidCampaign(String campaignCode) {
        return campaignH5Dao.getValidCampaign(campaignCode);
    }


    @Override
	public HashMap<String, Object> getCampaignCityList(long campaign) {
		List<CampaignH5City> list = campaignH5Dao.getCityList(campaign);
		
		HashMap<String, Object> citys = new HashMap<String, Object>();
		for(CampaignH5City city: list) {
			if("请选择".equals(city.getCity())) {
				continue;
			}
			if(citys.containsKey(city.getProvince())) {
				ArrayList<String> c = (ArrayList<String>)citys.get(city.getProvince());
				c.add(city.getCity()) ;
				citys.put(city.getProvince(), c);
			}else {
				ArrayList<String> c = new ArrayList<String>();
				c.add(city.getCity()) ;
				citys.put(city.getProvince(), c);
			}
			
		}
		return citys;
	}
}
