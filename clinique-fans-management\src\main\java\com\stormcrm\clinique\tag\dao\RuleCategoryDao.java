package com.stormcrm.clinique.tag.dao;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import com.stormcrm.clinique.domain.tag.RuleCategory;
import com.stormcrm.clinique.domain.tag.Label;
import com.stormcrm.clinique.domain.tag.LabelPackageRule;
import com.stormcrm.clinique.domain.tag.Rule;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
public interface RuleCategoryDao {

	@Select("SELECT "
			+ "c.*"
			+ " FROM rule_category c "
			+ " WHERE c.level = 1   and status = 1"
			)
	List<RuleCategory> getRuleCategoryList();
	
	@Select("SELECT "
			+ "c.*"
			+ " FROM rule_category c "
			+ " WHERE c.level = 2   and status = 1"
			)
	List<RuleCategory> getSubRuleCategoryList();
	
	@Insert("INSERT INTO label_rules ("
            + "`index`,"
            + "`join_type`,"
            + "`operate`,"
            + "`rule_id`,"
            + "`label_id`,"
            + "`category_id`,"
            + "`value`,"
            + "`create_time`"
            + ") VALUES ("
            + "#{index},"
            + "#{joinType},"
            + "#{operate},"
            + "#{ruleId},"
            + "#{labelId},"
            + "#{categoryId},"
            + "#{value},"
            + "now()"
            + ") ")
	int addFilter(LabelPackageRule filter);

	@Delete("DELETE FROM label_rules where label_id = #{id}")
	int deleteFiltersByLabel(int id);


	@Select("SELECT "
			+ "*"
			+ " FROM label_rules c "
			+ " WHERE  status = 1 and label_id = #{id} " +
			"  ORDER BY `index` "
			)
	List<LabelPackageRule> getRulesbyLabel(Label label);

	@Select("SELECT * from rules where status = 1 and id = #{ruleId} limit 1")
	Rule getRule(int ruleId);


	@Select("SELECT * from rules where status = 1")
	List<Rule> getRules();
}
