package cn.slashsoft.clinique.util;

import java.util.Random;

/**
 * 随机数工具类
 *
 * <AUTHOR>
 */
public class RandomUtil {

    /**
     * 从字符串中随机选择一个字符
     *
     * @param s 字符串
     * @return 字符
     */
    public static char getChar(String s) {
        return s.charAt((int) (Math.random() * s.length()));
    }

    /**
     * 获取随机数
     *
     * @param min 最小
     * @param max 最大
     * @return 数字
     */
    public static int getNumberBetween(int min, int max) {
        Random random = new Random();
        return random.nextInt(max - min) + min;
    }

    /**
     * 生成16位随机数，由yyMMddHHmmss+4位随机数
     *
     * @return 随机数
     */
    public static String getOrderNumber() {
        return "" + getNumberBetween(0, 9) + getNumberBetween(0, 9) + getNumberBetween(0, 9) + getNumberBetween(0, 9) + getNumberBetween(0, 9) + getNumberBetween(0, 9) + getNumberBetween(0, 9) + getNumberBetween(0, 9);
    }

    /**
     * 生成4位随机数的短信验证码
     *
     * @return 验证码
     */
    public static String getVerifyCode() {
        return "" + getNumberBetween(0, 9) + getNumberBetween(0, 9) + getNumberBetween(0, 9) + getNumberBetween(0, 9);
    }

    /**
     * 随机字符串
     *
     * @param length 长度
     * @return 指定长度的随机字符串
     */
    public static String getNumber(int length) {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < length; i++) {
            code.append(getChar("0123456789"));
        }
        return code.toString();
    }

}
