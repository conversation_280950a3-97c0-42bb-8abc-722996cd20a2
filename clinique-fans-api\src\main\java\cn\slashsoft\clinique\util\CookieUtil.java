package cn.slashsoft.clinique.util;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class CookieUtil {
    /**
     * 添加cookie
     * @param name 名称
     * @param value 值
     * @param domain 域名
     * @param maxage 有效期
     * @param path 路径
     * @param response 响应
     */
    public static void addCookie(String name, String value, String domain, int maxage, String path, HttpServletResponse response){
        Cookie cookie = new Cookie(name,value);
        if(domain!=null){
            cookie.setDomain(domain);
        }
        cookie.setMaxAge(maxage);
        cookie.setPath(path);
        response.addCookie(cookie);
    }

    /**
     * 往根下面存一个cookie
     * @param name 名称
     * @param value 值
     * @param maxage  有效期
     * @param response 响应
     */
    public static void addCookie(String name, String value, int maxage, HttpServletResponse response){
        addCookie(name, value, null, maxage, "/" , response);
    }
    /**
     * 往根下面存一个1个月的cookie
     * @param name cookie的key
     * @param value cookie的value
     * @param response 响应
     */
    public static void addCookie(String name, String value, HttpServletResponse response){
        addCookie(name, value, null, 2592000, "/", response);
    }

    /**
     * 从cookie值返回cookie值，如果没有返回 null
     * @param request 请求
     * @param name 名称
     * @return cookie的值
     */
    public static String getCookie(String name, HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (cookies == null) return null;
        for (int i = 0; i < cookies.length; i++) {
            if (cookies[i].getName().equals(name)) {
                return cookies[i].getValue();
            }
        }
        return null;
    }

    /**
     * 删除cookie
     * @param name 名称
     * @param domain 域名
     * @param path 路径
     * @param response 响应
     */
    public static void removeCookie(String name, String domain, String path, HttpServletResponse response) {
        addCookie(name, null, domain, 0, path, response);
    }

    /**
     * 删除根下面的cookie
     * @param name cookie的key
     * @param response 响应
     */
    public static void removeCookie(String name, HttpServletResponse response) {
        addCookie(name, null, 0, response);
    }

    /**
     * 删除根下面的所有cookie
     * @param request 请求
     * @param response 响应
     */
    public static void removeAll(HttpServletRequest request, HttpServletResponse response) {
        Cookie[] cookies = request.getCookies();
        if (cookies == null) return;
        for(Cookie cookie: cookies){
            addCookie(cookie.getName(), null, 0, response);
        }
    }

}
