package com.stormcrm.clinique.oauth.controller;

//import com.stormcrm.clinique.oauth.service.PhoneSmsCodeService;
import com.stormcrm.clinique.util.ResultUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 */
@Controller
@Validated
public class LoginController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;

    //@Autowired
   // private PhoneSmsCodeService phoneSmsCodeService;

    /**
     * 登录页面模版
     *
     * @return 模版文件
     */
    @RequestMapping("login")
    public String login(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/login/login";//"m/fans/login/phone"; // 
    }


    @RequestMapping("logout")
    public String logout(HttpServletRequest request, HttpServletResponse response) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            new SecurityContextLogoutHandler().logout(request, response, authentication);
        }
        return "redirect:/fans-management/";
    }

    //@GetMapping("phone/{number}")
    //@ResponseBody
   // public String code(@PathVariable("number") @NotBlank @Size(min = 11, max = 11) String number) {
        // send code to phone number
    //    return ResultUtil.successToJson(phoneSmsCodeService.sendPhoneSmsCode(number));
   // }
}
