package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignH5;
import cn.slashsoft.clinique.domain.campaign.lottery2408.CampaignTrackingDTO;
import cn.slashsoft.clinique.domain.campaign.lottery2408.CampaignUserLotteryAwardLogs;

import java.util.HashMap;

/**
 * 302美白镭射瓶
 *
 * <AUTHOR>
 */
public interface Campaign2408Service {

    CampaignUserLotteryAwardLogs getUserLotteryInfo(Long uid, String campaignFlag);

    HashMap getActivityRanking(Long customerId, CampaignH5 camp, String rankType);

    String getAward(Long uid, String campaignFlag);

    Integer setTracking(CampaignTrackingDTO param);
}
