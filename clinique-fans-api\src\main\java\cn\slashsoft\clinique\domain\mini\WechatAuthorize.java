package cn.slashsoft.clinique.domain.mini;

import lombok.Data;

import java.util.Date;

/**
 * 微信授权信息
 * <AUTHOR>
 */
@Data
public class WechatAuthorize {

    private Long id;
    /**
     * 开放平台唯一编号
     */
    private String unionid;
    /**
     * 公众号唯一编号
     */
    private String wechatOfficialOpenid;
    /**
     * 小程序唯一编号
     */
    private String wechatMiniOpenid;

    private String nickName;
    private String avatarUrl;
    private String gender;
    private String country;
    private String province;
    private String city;
    private String language;

    private String phoneNumber;
    /**
     * 分享者的小程序唯一编号
     */
    private String openid;
    private String campaign;

    private String source;

    private String platform;

    private Date createTime;

}
