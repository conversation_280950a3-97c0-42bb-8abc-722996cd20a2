package cn.slashsoft.clinique.domain.mini;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 柜台
 *
 * <AUTHOR>
 */
@Data
public class CounterLog {

    private Long id;
    private Short type;
    private String wechatOfficialOpenid;
    private BigDecimal latitude;
    private BigDecimal longitude;
    private String province;
    private String city;
    private String district;
    private String name;
    private String address;
    private Integer distance;

}
