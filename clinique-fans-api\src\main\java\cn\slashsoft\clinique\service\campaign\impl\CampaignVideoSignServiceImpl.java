package cn.slashsoft.clinique.service.campaign.impl;

import cn.slashsoft.clinique.dao.mini.PointDao;
import cn.slashsoft.clinique.dao.campaign.CampaignVideoSignDao;
import cn.slashsoft.clinique.domain.mini.PointTransaction;
import cn.slashsoft.clinique.domain.campaign.*;
import cn.slashsoft.clinique.enums.PointForeignEnum;
import cn.slashsoft.clinique.enums.PointTypeEnum;
import cn.slashsoft.clinique.service.campaign.CampaignVideoSignService;
import cn.slashsoft.clinique.util.DateUtil;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 视频签到活动
 *
 * <AUTHOR>
 */
@Service
public class CampaignVideoSignServiceImpl implements CampaignVideoSignService {

    private final CampaignVideoSignDao campaignVideoSignDao;
    private final PointDao pointDao;

    public CampaignVideoSignServiceImpl(CampaignVideoSignDao campaignVideoSignDao, PointDao pointDao) {
        this.campaignVideoSignDao = campaignVideoSignDao;
        this.pointDao = pointDao;
    }

    /**
     * 获取签到信息
     *
     * @param customerId 顾客编号
     * @return 签到信息
     */
    @Override
    public CampaignVideoSign getCampaignVideoSign(long customerId) {

        CampaignVideoSign campaignVideoSign = campaignVideoSignDao.getCampaignVideoSign(customerId);

        if (null == campaignVideoSign) {
            campaignVideoSign = new CampaignVideoSign();
            campaignVideoSign.setCustomerId(customerId);
            campaignVideoSign.setLevel((short) 0);

            campaignVideoSignDao.insertCampaignVideoSign(campaignVideoSign);
        }

        return campaignVideoSign;
    }

    /**
     * 获取视频列表
     *
     * @return 视频列表
     */
    @Override
    public List<CampaignVideoSignConfig> getCampaignVideoSignConfig() {
        return campaignVideoSignDao.getCampaignVideoSignConfig();
    }

    /**
     * 签到
     *
     * @param customerId 顾客编号
     * @param level      签到等级
     * @return 是否成功
     */
    @Override

    public boolean sign(long customerId, short level) {

        try {
            // 写入签到记录
            CampaignVideoSignDetail campaignVideoSignDetail = new CampaignVideoSignDetail();
            campaignVideoSignDetail.setCustomerId(customerId);
            campaignVideoSignDao.insertCampaignVideoSignDetail(campaignVideoSignDetail);
            // 更新签到统计
            campaignVideoSignDao.updateCampaignVideoSign(customerId);

            // 是否到7，发放积分
            if (7 == level) {
                // 发放积分
                Date now = new Date();
                PointTransaction pointTransaction = new PointTransaction();
                pointTransaction.setCustomerId(customerId);
                pointTransaction.setPointTypeId(PointTypeEnum.VIDEO_SIGN.getId());
                pointTransaction.setPoints(PointTypeEnum.VIDEO_SIGN.getPoints());
                pointTransaction.setRemainingPoints(PointTypeEnum.VIDEO_SIGN.getPoints());
                pointTransaction.setStartTime(now);
                pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
                pointTransaction.setForeignId(PointForeignEnum.VIDEO_SIGN.getId());
                pointTransaction.setForeignMasterId(0L);
                pointTransaction.setForeignDetailId(0L);
                pointDao.insertPointTransaction(pointTransaction);
            }

            return true;
        } catch (Exception e) {
            return false;
        }

    }

    /**
     * 写入访问日志
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @return 访问日志编号
     */
    @Override
    public long insertCampaignVideoSignViewLog(long customerId, String source) {

        CampaignVideoSignViewLog campaignVideoSignViewLog = new CampaignVideoSignViewLog();
        campaignVideoSignViewLog.setCustomerId(customerId);
        campaignVideoSignViewLog.setSource(source);
        campaignVideoSignDao.insertCampaignVideoSignViewLog(campaignVideoSignViewLog);

        return campaignVideoSignViewLog.getId();

    }

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Override
    public void setCampaignVideoSignViewLog(long id) {
        campaignVideoSignDao.setCampaignVideoSignViewLog(id);
    }

    /**
     * 写入播放记录
     *
     * @param campaignVideoSignPlayLog 播放记录
     */
    @Override
    public void insertCampaignVideoSignPlayLog(CampaignVideoSignPlayLog campaignVideoSignPlayLog) {
        campaignVideoSignDao.insertCampaignVideoSignPlayLog(campaignVideoSignPlayLog);
    }

    /**
     * 写入跳转记录
     *
     * @param campaignVideoSignJumpLog 跳转记录
     */
    @Override
    public void insertCampaignVideoSignJumpLog(CampaignVideoSignJumpLog campaignVideoSignJumpLog) {
        campaignVideoSignDao.insertCampaignVideoSignJumpLog(campaignVideoSignJumpLog);
    }

}
