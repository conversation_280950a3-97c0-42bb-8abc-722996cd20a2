package cn.slashsoft.clinique.dao.mini;


import java.util.ArrayList;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.mini.NotePhotoTag;
import cn.slashsoft.clinique.domain.mini.NoteReadLog;
import cn.slashsoft.clinique.domain.mini.NoteTag;
import cn.slashsoft.clinique.domain.mini.NoteTagSearchLog;
import cn.slashsoft.clinique.domain.mini.Video;
import cn.slashsoft.clinique.domain.mini.VideoLikeLog;
import cn.slashsoft.clinique.domain.mini.VideoPlayLog;
import cn.slashsoft.clinique.domain.mini.VideoShareLog;
import cn.slashsoft.clinique.domain.mini.VideoTag;
import cn.slashsoft.clinique.domain.mini.VideoTagDefine;
import cn.slashsoft.clinique.domain.mini.VideoTagSearchLog;

 /* 视频
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface VideoDao {
	 /**
	 	* 获取top视频标签列表
	 *
	 */
	@Select("SELECT " +
	        "   `id`, `type`,`image`,`title` " +
	        "FROM " +
	        "   `video_tag_define` " +
	        "WHERE " +
	        "   `top`=1 " )
	ArrayList<VideoTagDefine> getTopTagList();
	/**
		 	* 获取热门视频标签列表
		 *
		 */
	@Select("SELECT "
			+ " a.`tag_id` id,b.`title`,b.`type`,b.`image`" + 
		    " FROM `video_tag_search_log`  a " + 
		    " LEFT JOIN `video_tag_define`  b " + 
		    " ON a.`tag_id` = b.`id` " + 
		    " WHERE a.`tag_id` > 1 " + 
		    " AND a.`tag_type` = 2 " + 
		    //" AND a.`tag_id` not in ( #{hasStr} ) " + 
		    " GROUP BY a.`tag_id` " + 
		    " order by count(*) DESC "  
		   // + " limit #{number} "
		    )
	ArrayList<VideoTagDefine> getHotTagList( ); //int number, String hasStr,
	/**
	 * 搜索视频标签
	 *
	 *@param key
	 *@param type
	 */
	@Select("SELECT " +
	        "   `id`, `type`,`image`,`title` " +
	        "FROM " +
	        "   `video_tag_define` " +
	        "WHERE " +
	        "   `title` like concat('%', #{key}, '%') "
	        + " AND `type` = #{type} ")
	ArrayList<VideoTagDefine> getTagList(String key, int type);
	
	/**
	 * 获取标签
	 *
	 *@param tagId
	 */
	@Select("SELECT " +
	        "   * " +
	        "FROM " +
	        "   `video_tag_define` " +
	        "WHERE " +
	        "   `id` = #{tagId} ")
	VideoTagDefine getVideoTagDefine(Long tagId);
    /**
	 	* 保存标签搜索、使用日志
	 *
	 * @param NoteShareLog 
	 */ @Insert("INSERT INTO `video_tag_search_log`(" +
			 "   `tag_id`, " +
             "   `tag_title`, " +
             "   `tag_type`, " +
             "   `customer_id` " +
             ") " +
             "VALUES (" +
             "   #{tagId}, " +
             "   #{tagTitle}, " +
             "   #{tagType}, " +
             "   #{customerId} " +
             ")")
	    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
	int insertTagSearchLog(VideoTagSearchLog log);

   /**
	 * 获取视频列表
	 *
	 *@param customerId
	 */
  @Select("SELECT " +
          "   a.*,"
          + "( SELECT count(`id`) FROM `video_like_log` WHERE `video_id`= a.id ) `likeCount`,  " 
          + "( SELECT count(`id`) FROM `video_like_log` WHERE `video_id`= a.id AND `customer_id`=#{customerId}) `liked`,  " 
          + "( SELECT count(`id`) FROM `video_play_log` WHERE `video_id`= a.id ) `read`  " +
          "FROM " +
          "   `video` a "
          + " WHERE  a.`status`=1 ")
   
   ArrayList<Video> getVideoList(Long customerId);
  /**
 	 * 根据标签获取视频列表
 	 *
 	 *@param customerId
 	 *@parm tagId
 	 */
   @Select("SELECT " +
           "   a.*,"
           + "( SELECT count(`id`) FROM `video_like_log` WHERE `video_id`= a.id ) `likeCount`,  " 
           + "( SELECT count(`id`) FROM `video_like_log` WHERE `video_id`= a.id AND `customer_id`=#{customerId}) `liked`,  " 
           + "( SELECT count(`id`) FROM `video_play_log` WHERE `video_id`= a.id) `read`  " +
           "FROM " +
           "   `video` a WHERE a.`id` in (SELECT `video_id` FROM `video_tag` WHERE `tag_id`=#{tagId} ) "
           + " AND  a.`status`=1 ")    
   ArrayList<Video> getVideoListByTag(Long customerId, Long tagId);


   /**
  	 * 获取用户拔草视频列表
  	 *
  	 *@param customerId
  	 */
    @Select("SELECT " +
            "   a.*  " +
            "FROM " +
            "   `video` a "
            + "WHERE a.`id` in (SELECT `video_id` FROM video_like_log WHERE `customer_id`=#{customerId}) "
            + " AND  a.`status`=1 ")
     
     ArrayList<Video> getCustomerLikeVideoList(Long customerId, int start);
   /**
	 * 获取视频标签列表
	 *
	 *@param videoId
	 */
  @Select("SELECT " +
          "   a.*" +
          "FROM " +
          "   `video_tag` a WHERE a.`video_id`=#{videoId}"
          )
   
   ArrayList<VideoTag> getVideoTagList(Long videoId);
    /**
     * 保存播放视频日志
     *
     * @param VideoReadLog 日志
     */ @Insert("INSERT INTO `video_play_log`(" +
             "   `video_id`, " +
             "   `customer_id`, " +
             "   `type`, " +
             "   `playTime`, " +
             "   `share_customer_id`, " +
             "   `duration` " +
             ") " +
             "VALUES (" +
             "   #{videoId}, " +
             "   #{customerId}, " +
             "   #{type}, " +
             "  #{playTime}," +
             "   #{shareCustomerId}, " +
             "  #{duration}" +
             ")")
     @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insertPlayLog(VideoPlayLog log);    
   /**
    	* 保存拔草视频日志
    *
    * @param VideoLikeLog 
    */ 
    @Insert("INSERT INTO `video_like_log`(" +
   		 "   `video_id`, " +
            "   `customer_id` " +
            ") " +
            "VALUES (" +
            "   #{videoId}, " +
            "   #{customerId} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
   int insertLikeLog(VideoLikeLog log);
    /**
	  	* 删除拔草视频日志
	  *
	  * @param VideoLikeLog 
	  */ 
	  @Insert("DELETE FROM `video_like_log` " +
	          " WHERE  `video_id` =  #{videoId} AND " +
	          "  `customer_id` = #{customerId} " )
	  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
	 int delLikeLog(VideoLikeLog log);
   /**
	 	* 保存分享视频日志
	 *
	 * @param VideoShareLog 
	 */ @Insert("INSERT INTO `video_share_log`(" +
			 "   `video_id`, " +
            "   `share_sign`, " +
            "   `customer_id` " +
            ") " +
            "VALUES (" +
            "   #{videoId}, " +
            "   #{shareSign}, " +
            "   #{customerId} " +
            ")")
	    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
	int insertShareLog(VideoShareLog log);
	 
	   /**
      * 是否存在播放日志
      *
      * @param VideoPlayLog 日志
      */ @Select("SELECT count(`id`) FROM  `video_play_log`" +
              "  WHERE `video_id`= #{videoId} AND " +
              "  `customer_id`= #{customerId} " +
              "")
     int getPlayLogCount(VideoPlayLog log);   
	   /**
	    * 是否存在拔草日志
       *
       * @param VideoLikeLog 日志
       */ @Select("SELECT count(`id`) FROM  `video_like_log`" +
               "  WHERE `video_id`= #{videoId} AND " +
               "  `customer_id`= #{customerId} " +
               "")
	 int getLikeLogCount(VideoLikeLog log);
}
