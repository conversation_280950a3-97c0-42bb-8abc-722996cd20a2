package cn.slashsoft.clinique.service.mini;

import cn.slashsoft.clinique.domain.mini.Customer;
import cn.slashsoft.clinique.domain.mini.CustomerAddress;
import cn.slashsoft.clinique.vo.mini.Info;
import cn.slashsoft.clinique.vo.Result;

import java.util.List;

/**
 * 顾客相关的服务
 *
 * <AUTHOR>
 */
public interface CustomerService {

    /**
     * 获取登陆时需要的信息
     * 登录
     *
     * @param customerId 顾客编号
     * @return 顾客信息
     */
    Customer getCustomerById(long customerId);

    /**
     * 获取顾客注册的手机号码
     *
     * @param customerId 顾客编号
     * @return 顾客信息
     */
    String getCustomerPhoneNumberById(long customerId);

    /**
     * 更新资料
     *
     * @param info 资料
     * @return 处理结果
     */
    Result updateCustomer(Info info);

    /**
     * 获取首选地址
     *
     * @param customerId 顾客编号
     * @return 地址
     */
    CustomerAddress getPreferredAddressByCustomerId(long customerId);

    /**
     * 获取邮寄地址
     *
     * @param customerId 顾客编号
     * @return 邮寄地址
     */
    List<CustomerAddress> getCustomerAddressByCustomerId(long customerId);

    /**
     * 获取邮寄地址
     *
     * @param customerId        顾客编号
     * @param customerAddressId 邮寄地址编号
     * @return 邮寄地址
     */
    CustomerAddress getCustomerAddressById(long customerId, long customerAddressId);

    /**
     * 写入顾客地址
     *
     * @param customerAddress 顾客地址
     */
    void insertAddress(CustomerAddress customerAddress);

    /**
     * 编辑顾客地址
     *
     * @param customerAddress 顾客地址
     */
    void updateAddress(CustomerAddress customerAddress);

    /**
     * 删除顾客地址
     *
     * @param customerAddressId 顾客地址编号
     */
    void delAddress(long customerAddressId);

    /**
     * 写入手机号码
     *
     * @param unionid     开放平台唯一编号
     * @param phoneNumber 手机号码
     */
    void setPhoneNumberByUnionid(String unionid, String phoneNumber);

    /**
     * 写入手机号码
     *
     * @param openid      公众号唯一编号
     * @param phoneNumber 手机号码
     */
    void setPhoneNumberByOpenid(String openid, String phoneNumber);

	Customer getCustomerByOpenId(String openid);
    /**
     * 会员中心获取用户信息
     *
     * @param openid 公众号openid
     */
	Customer getCustomerByOpenIdFromMember(String openid);
    /**
     * 会员中心获取用户信息
     *
     * @param unionid 用户unionid
     */
	Customer getCustomerByUnionIdFromMember(String unionid);


	int updateCustomerActivePoints(int points, Long customerid);

	boolean days3signCheck(String unionid, Long customerId);
}
