package cn.slashsoft.clinique.service.mini.impl;

import java.math.BigDecimal;
import java.util.List;

import org.springframework.stereotype.Service;

import cn.slashsoft.clinique.dao.mini.CounterDao;
import cn.slashsoft.clinique.domain.mini.Counter;
import cn.slashsoft.clinique.domain.mini.CounterLog;
import cn.slashsoft.clinique.service.mini.CounterService;

/**
 * 柜台
 *
 * <AUTHOR>
 */
@Service
public class CounterServiceImpl implements CounterService {

    private final CounterDao counterDao;

    public CounterServiceImpl(CounterDao counterDao) {
        this.counterDao = counterDao;
    }

    /**
     * 跟据定位查找附近的门店
     *
     * @param latitude  经度
     * @param longitude 纬度
     * @return 门店
     */
    @Override
    public List<Counter> getCounter(BigDecimal latitude, BigDecimal longitude) {
        return counterDao.getCounter(latitude, longitude);
    }

    /**
     * 跟据定位查找附近的门店
     *
     * @param latitude  经度
     * @param longitude 纬度
     * @param province  省份
     * @param city      城市
     * @param name      门店名称
     * @return 门店
     */
    @Override
    public List<Counter> getCounterBySearch(BigDecimal latitude, BigDecimal longitude, String province, String city, String name) {
        return counterDao.getCounterBySearch(latitude, longitude, province, city, name);
    }

    /**
     * 写入日志
     *
     * @param counterLog 日志
     */
    @Override
    public void insertCounterLog(CounterLog counterLog) {
        counterDao.insertCounterLog(counterLog);
    }
}
