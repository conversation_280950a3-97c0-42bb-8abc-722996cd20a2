package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.dao.CampaignLaserTalentsDao;
import com.stormcrm.clinique.dao.PointDao;
import com.stormcrm.clinique.domain.CampaignLaserTalents;
import com.stormcrm.clinique.domain.CampaignLaserTalentsImage;
import com.stormcrm.clinique.domain.PointTransaction;
import com.stormcrm.clinique.enums.PointForeignEnum;
import com.stormcrm.clinique.enums.PointTypeEnum;
import com.stormcrm.clinique.service.CampaignLaserTalentsService;
import com.stormcrm.clinique.service.SmotService;
import com.stormcrm.clinique.util.DateUtil;
import com.stormcrm.clinique.util.LongUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 新品体验官活动
 *
 * <AUTHOR>
 */
@Service
public class CampaignLaserTalentsServiceImpl implements CampaignLaserTalentsService {

    @Value("${wechat.subscribe-template.examine-result}")
    private String templateExamineResult;

    private final CampaignLaserTalentsDao campaignLaserTalentsDao;
    private final PointDao pointDao;
    private final SmotService smotService;

    public CampaignLaserTalentsServiceImpl(CampaignLaserTalentsDao campaignLaserTalentsDao, PointDao pointDao, SmotService smotService) {
        this.campaignLaserTalentsDao = campaignLaserTalentsDao;
        this.pointDao = pointDao;
        this.smotService = smotService;
    }

    /**
     * 查询所有活动带分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索
     * @param status        状态
     * @return 活动列表分页
     */
    @Override
    public List<CampaignLaserTalents> getPage(int page, int perpage, String generalSearch, Short status) {
        return campaignLaserTalentsDao.getPage((page - 1) * perpage, perpage, generalSearch, status);
    }

    /**
     * 查询所有活动带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    @Override
    public int getPageCount(String generalSearch, Short status) {
        return campaignLaserTalentsDao.getPageCount(generalSearch, status);
    }

    /**
     * 获取美白达人官信息
     *
     * @param id 编号
     * @return 美白达人官信息
     */
    @Override
    public CampaignLaserTalents getById(long id) {
        return campaignLaserTalentsDao.getById(id);
    }

    /**
     * 获取图片
     *
     * @param id 顾客活动信息编号
     * @return 图片地址
     */
    @Override
    public List<CampaignLaserTalentsImage> getImageById(long id) {
        return campaignLaserTalentsDao.getImageById(id);
    }

    /**
     * 审核通过
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Override
    public int accept(long id) {

        // 读取用户及活动信息
        CampaignLaserTalents campaignLaserTalents = campaignLaserTalentsDao.getById(id);
        if (null == campaignLaserTalents) {
            return 0;
        }

        // 只在审核中和审核未通过才可以审核通过
        if (0 == campaignLaserTalentsDao.accept(id)) {
            return 0;
        }

        // 发放积分
        Date now = new Date();
        PointTransaction pointTransaction = new PointTransaction();
        pointTransaction.setCustomerId(campaignLaserTalents.getCustomerId());
        pointTransaction.setPointTypeId(PointTypeEnum.LASER_TALENTS.getId());
        pointTransaction.setPoints(PointTypeEnum.LASER_TALENTS.getPoints());
        pointTransaction.setRemainingPoints(PointTypeEnum.LASER_TALENTS.getPoints());
        pointTransaction.setStartTime(now);
        pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
        pointTransaction.setForeignId(PointForeignEnum.LASER_TALENTS.getId());
        pointTransaction.setForeignMasterId(id);
        pointTransaction.setForeignDetailId(0L);
        pointDao.insertPointTransaction(pointTransaction);

        String data = "{" +
                "\"thing2\":{\"value\":\"审核通过\"}," +
                "\"thing8\":{\"value\":\"积分及抽奖机会已到帐，现在查看你的晒图！\"}" +
                "}";

        // 发送订阅消息
        smotService.send((short) 5, campaignLaserTalents.getWechatMiniOpenid(), templateExamineResult, "/laser/talents/talents?source=SuccessUploadMot", data);

        return 1;
    }

    /**
     * 批量同意
     *
     * @param ids 自动编号
     * @return 影响的行数
     */
    @Override
    public int acceptBatch(String ids) {

        String[] idArray = ids.split(",");

        List<Long> idList = new ArrayList<>();
        for (String idString : idArray) {
            long id = LongUtil.parseInt(idString);
            if (0 < id) {
                idList.add(id);
            }
        }

        if (0 == idList.size()) {
            return 0;
        }

        List<CampaignLaserTalents> campaignLaserTalentsList = campaignLaserTalentsDao.getByIds(idList);
        if (null == campaignLaserTalentsList || 0 == campaignLaserTalentsList.size()) {
            return 0;
        }

        for (CampaignLaserTalents campaignLaserTalents : campaignLaserTalentsList) {

            // 只在审核中和审核未通过才可以审核通过
            if (0 == campaignLaserTalentsDao.accept(campaignLaserTalents.getId())) {
                continue;
            }

            // 发放积分
            Date now = new Date();
            PointTransaction pointTransaction = new PointTransaction();
            pointTransaction.setCustomerId(campaignLaserTalents.getCustomerId());
            pointTransaction.setPointTypeId(PointTypeEnum.LASER_TALENTS.getId());
            pointTransaction.setPoints(PointTypeEnum.LASER_TALENTS.getPoints());
            pointTransaction.setRemainingPoints(PointTypeEnum.LASER_TALENTS.getPoints());
            pointTransaction.setStartTime(now);
            pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
            pointTransaction.setForeignId(PointForeignEnum.LASER_TALENTS.getId());
            pointTransaction.setForeignMasterId(campaignLaserTalents.getId());
            pointTransaction.setForeignDetailId(0L);
            pointDao.insertPointTransaction(pointTransaction);

            String data = "{" +
                    "\"thing2\":{\"value\":\"审核通过\"}," +
                    "\"thing8\":{\"value\":\"积分及抽奖机会已到帐，现在查看你的晒图！\"}" +
                    "}";

            // 发送订阅消息
            smotService.send((short) 5, campaignLaserTalents.getWechatMiniOpenid(), templateExamineResult, "/laser/talents/talents?source=SuccessUploadMot", data);

        }

        return 1;
    }

    /**
     * 审核失败
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Override
    public int reject(long id) {

        // 读取用户及活动信息
        CampaignLaserTalents campaignLaserTalents = campaignLaserTalentsDao.getById(id);
        if (null == campaignLaserTalents) {
            return 0;
        }

        // 改变状态
        if (0 == campaignLaserTalentsDao.reject(id)) {
            return 0;
        }

        // 重置图片
        campaignLaserTalentsDao.setImageStatus(id);

        String data = "{" +
                "\"thing2\":{\"value\":\"审核失败\"}," +
                "\"thing8\":{\"value\":\"检查截图是否符合审核条件，再重新上传哦！\"}" +
                "}";

        // 发送订阅消息
        smotService.send((short) 6, campaignLaserTalents.getWechatMiniOpenid(), templateExamineResult, "/laser/talents/talents?source=FailedUploadMot", data);

        return 1;
    }

    /**
     * 批量拒绝
     *
     * @param ids 自动编号
     * @return 影响的行数
     */
    @Override
    public int rejectBatch(String ids) {

        String[] idArray = ids.split(",");

        List<Long> idList = new ArrayList<>();
        for (String idString : idArray) {
            long id = LongUtil.parseInt(idString);
            if (0 < id) {
                idList.add(id);
            }
        }

        if (0 == idList.size()) {
            return 0;
        }

        List<CampaignLaserTalents> campaignLaserTalentsList = campaignLaserTalentsDao.getByIds(idList);
        if (null == campaignLaserTalentsList || 0 == campaignLaserTalentsList.size()) {
            return 0;
        }

        for (CampaignLaserTalents campaignLaserTalents : campaignLaserTalentsList) {

            // 改变状态
            if (0 == campaignLaserTalentsDao.reject(campaignLaserTalents.getId())) {
                continue;
            }

            // 重置图片
            campaignLaserTalentsDao.setImageStatus(campaignLaserTalents.getId());

            String data = "{" +
                    "\"thing2\":{\"value\":\"审核失败\"}," +
                    "\"thing8\":{\"value\":\"检查截图是否符合审核条件，再重新上传哦！\"}" +
                    "}";

            // 发送订阅消息
            smotService.send((short) 6, campaignLaserTalents.getWechatMiniOpenid(), templateExamineResult, "/laser/talents/talents?source=FailedUploadMot", data);

        }

        return 1;
    }
}
