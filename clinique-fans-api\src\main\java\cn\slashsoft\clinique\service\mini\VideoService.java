package cn.slashsoft.clinique.service.mini;

import java.util.ArrayList;
import java.util.List;

import cn.slashsoft.clinique.domain.mini.Video;
import cn.slashsoft.clinique.domain.mini.VideoLikeLog;
import cn.slashsoft.clinique.domain.mini.VideoPlayLog;
import cn.slashsoft.clinique.domain.mini.VideoShareLog;
import cn.slashsoft.clinique.domain.mini.VideoTag;
import cn.slashsoft.clinique.domain.mini.VideoTagDefine;
import cn.slashsoft.clinique.domain.mini.VideoTagSearchLog;

/**
 * 视频
 *
 * <AUTHOR>
 */
public interface VideoService {
   
    /**
     * 保存阅读视频日志
     *
     * @param VideoReadLog 日志
     */
   int insertReadLog(VideoPlayLog log, String shareOpenId);    

    /**
     	*拔草视频
     *
     * @param VideoLikeLog 
     */
   int insertLikeLog(VideoLikeLog log);
   /**
	 *删除 拔草视频
	 *
	 *@param videoId
	 *@param customerId
	 */
	int unlikeVideo(VideoLikeLog log);
    /**
	 	* 保存分享视频日志
	 *
	 * @param VideoShareLog 
	 */
   int insertShareLog(VideoShareLog log);
 
  
    /**
	 * 获取用户拔草视频列表
	 *
	 *@param customerId
	 *@param start
	 */
    ArrayList<Video> getCustomerLikeVideoList(Long customerId, int start);
    /**
	 * 获取视频列表
	 *
	 *@param customerId
	 */
    ArrayList<Video> getVideoList(Long customerId, Long tagId);

	List<VideoTagDefine> getTagList(String key, int type);

	int insertTagSearchLog(VideoTagSearchLog log);

	List<VideoTagDefine> getTopTagList();

}
