package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.client.MemberClient;
import com.stormcrm.clinique.dao.*;
import com.stormcrm.clinique.dao.mini.CactiveDao;
import com.stormcrm.clinique.dao.mini.CustomerDao;
import com.stormcrm.clinique.domain.*;
import com.stormcrm.clinique.domain.campaign.CampaignH5;
import com.stormcrm.clinique.enums.CActiveTypeEnum;
import com.stormcrm.clinique.enums.CBForeignEnum;
import com.stormcrm.clinique.service.NoteService;
import com.stormcrm.clinique.service.OutsideService;
import com.stormcrm.clinique.service.SmotService;
import com.stormcrm.clinique.service.campaign.ugc.CampaignH5Service;
import com.stormcrm.clinique.util.DateUtil;
import com.stormcrm.clinique.util.LongUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;


/**
 * 笔记
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class NoteServiceImpl implements NoteService {

    @Value("${wechat.subscribe-template.examine-result}")
    private String templateExamineResult;

    @Value("${wechat.mini.appid}")
    private String appId;

    @Resource
    private NoteDao noteDao;
    @Resource
    private PointDao pointDao;
    @Resource
    private TTestimonyFragmentDao tTestimonyFragmentDao;
    @Resource
    private CactiveDao cactiveDao;
    @Resource
    private CustomerDao customerDao;
    @Resource
    private SmotService smotService;
    @Resource
    private OutsideService outsideService;
    @Resource
    private PointTypeDao pointTypeDao;
    @Resource
    private NotePointLogDao notePointLogDao;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private MemberClient memberClient;
    @Resource
    private CampaignH5Service campaignH5Service;


    /**
     * 删除笔记
     *
     * @param id 笔记id
     */
    public int delNote(long id) {
        return noteDao.delNote(id);
    }

    /**
     * 查询所有活动带分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索
     * @param status        状态
     * @return 活动列表分页
     */
    @Override
    public List<Note> getPage(int page, int perpage, String generalSearch, Short status) {
        return noteDao.getPage((page - 1) * perpage, perpage, generalSearch, status);
    }

    /**
     * 查询所有活动带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    @Override
    public int getPageCount(String generalSearch, Short status) {
        return noteDao.getPageCount(generalSearch, status);
    }

    /**
     * 查询所有 笔记的图片 带分页
     *
     * @param page    页码
     * @param perpage 页面大小
     * @param noteId  笔记id
     * @return 活动列表分页
     */
    @Override
    public List<NotePhoto> getPhotoPage(int page, int perpage, Long noteId) {
        return noteDao.getPhotoPage((page - 1) * perpage, perpage, noteId);
    }

    /**
     * 查询所有 笔记的图片 带分页的记录数
     *
     * @param nodeId 笔记id
     * @return 记录数
     */
    @Override
    public int getPhotoPageCount(Long nodeId) {
        return noteDao.getPhotoPageCount(nodeId);
    }

    /**
     * 获取用户笔记评论列表
     *
     * @param customerId 客户id
     * @param start      起始
     */
    public List<NoteDiscuss> getCustomerDiscussList(Long customerId, int start) {
        return this.noteDao.getCustomerDiscussList(customerId, start);
    }

    /**
     * 获取笔记列表
     *
     * @param start 起始
     * @return List<Note>
     */
    public List<Note> getNoteList(int start) {
        return this.noteDao.getNoteList(start);
    }

    /**
     * 获取用户笔记列表
     *
     * @param customerId 客户id
     * @param start      起始
     */
    public List<Note> getCustomerNoteList(Long customerId, int start) {
        return this.noteDao.getCustomerNoteList(customerId, start);
    }

    /**
     * 获取用户笔记
     *
     * @param noteId 笔记id
     */
    public Note getNote(Long noteId) {
        return this.noteDao.getNote(noteId);
    }

    /**
     * 获取用户笔记 照片
     *
     * @param noteId 笔记id
     */
    public List<NotePhoto> getPhotos(Long noteId) {
        return noteDao.getPhotos(noteId);
    }

    /**
     * 获取用户笔记 标签
     *
     * @param noteId 笔记id
     */
    @Override
    public List<NotePhotoTag> getPhotoTags(Long noteId, Long photoId) {
        return noteDao.getPhotoTags(noteId, photoId);
    }


    /**
     * @param id 照片id
     * @return int
     */
    @Override
    public int deletePhoto(long id) {
        return noteDao.deletePhoto(id);
    }

    /**
     * 获取照片
     *
     * @param id 照片id
     * @return NotePhoto
     */
    @Override
    public NotePhoto getPhoto(long id) {
        return noteDao.getPhoto(id);
    }


    /**
     * 获取笔记评论 分页
     *
     * @param page          起始页
     * @param perpage       分页
     * @param generalSearch 搜索条件
     * @param status        状态
     * @param noteId        笔记id
     * @return 笔记评论列表
     */
    @Override
    public List<NoteDiscuss> getDiscussPage(Integer page, int perpage, String generalSearch, Short status, Long noteId) {
        return noteDao.getDiscussPage((page - 1) * perpage, perpage, generalSearch, status, noteId);
    }

    /**
     * 获取笔记评论 分页数
     *
     * @param generalSearch 搜索条件
     * @param status        状态
     * @param noteId        笔记id
     * @return 总数
     */
    @Override
    public int getDiscussPageCount(String generalSearch, Short status, Long noteId) {
        return noteDao.getDiscussPageCount(generalSearch, status, noteId);
    }

    @Override
    public int acceptDiscuss(long noteDiscussId) {
        // 读取该条评论
        NoteDiscuss noteDiscuss = noteDao.getDiscussById(noteDiscussId);
        if (null == noteDiscuss) {
            return 0;
        }

        // 只在审核中和审核未通过才可以审核通过
        if (0 == noteDao.acceptDiscuss(noteDiscussId)) {
            return 0;
        }

        //发布内容
        CampaignH5 camp = campaignH5Service.getValidCampaign("campaign2408");
        if (!ObjectUtils.isEmpty(camp)){
            this.noteDao.acceptCampDiscuss(noteDiscussId,"campaign2408");
        }

        return 1;
    }

    @Override
    public int acceptDiscussBatch(String noteDiscussIds) {
        String[] idArray = noteDiscussIds.split(",");

        for (String idString : idArray) {
            long id = LongUtil.parseInt(idString);
            if (0 < id) {
                this.acceptDiscuss(id);
            }
        }

        return 1;
    }

    /**
     * 審核不通過
     *
     * @param noteDiscussId
     * @return
     */
    @Override
    public int rejectDiscuss(long noteDiscussId) {
        // 读取该条评论
        NoteDiscuss noteDiscuss = noteDao.getDiscussById(noteDiscussId);
        if (null == noteDiscuss) {
            return 0;
        }

        // 改变状态
        if (0 == noteDao.rejectDiscuss(noteDiscussId)) {
            return 0;
        }

        CampaignH5 camp = campaignH5Service.getValidCampaign("campaign2408");
        if (!ObjectUtils.isEmpty(camp)){
            this.noteDao.rejectCampDiscuss(noteDiscussId,"campaign2408");
        }

//        log.trace("{}{}", noteDiscuss.getNoteId(), noteDiscuss.getId());
        deleteDiscussPoint(noteDiscuss.getNoteId(), noteDiscuss.getId(), noteDiscuss.getCustomerId());
        deleteDiscussActivePoints(noteDiscuss.getNoteId(), noteDiscuss.getId(), noteDiscuss.getCustomerId());

        return 1;
    }

    /**
     * 刪除筆記的評論
     *
     * @param noteDiscussId 筆記評論的Id
     * @return 0 未操作  1 成功
     */
    @Override
    public int deleteDiscuss(long noteDiscussId) {

        // 读取该条评论
        NoteDiscuss noteDiscuss = noteDao.getDiscussById(noteDiscussId);
        if (null == noteDiscuss) {
            return 0;
        }

        // 改变状态
        if (0 == noteDao.deleteDiscuss(noteDiscussId)) {
            return 0;
        }

        //发布内容
        CampaignH5 camp = campaignH5Service.getValidCampaign("campaign2408");
        if (!ObjectUtils.isEmpty(camp)){
            this.noteDao.deleteCamDiscuss(noteDiscussId,"campaign2408");
        }

//        log.trace(" ===== 》 {}{}", noteDiscuss.getNoteId(), noteDiscuss.getId());
        deleteDiscussPoint(noteDiscuss.getNoteId(), noteDiscuss.getId(), noteDiscuss.getCustomerId());
        deleteDiscussActivePoints(noteDiscuss.getNoteId(), noteDiscuss.getId(), noteDiscuss.getCustomerId());
        return 1;
    }

    private void deleteDiscussPoint(long nodeId, long noteDiscussId, long customerId) {

        //  查詢得分規則，根據規則進行處理【暫時不查】

        // 查找對應的積分記錄，如果沒有，標明已經被刪除
        PointTransaction pointTransaction = new PointTransaction();
        pointTransaction.setCustomerId(customerId);
        // CBForeignEnum.NOTE_VIDEO_ACTION 18 // 如果需要做扣減分操作，之後根據這個id去獲取規則
        pointTransaction.setPointTypeId(CBForeignEnum.NOTE_VIDEO_ACTION.getId());
        pointTransaction.setForeignId(CBForeignEnum.NOTE_VIDEO_ACTION.getId());
        pointTransaction.setForeignMasterId(nodeId);
        pointTransaction.setForeignDetailId(noteDiscussId);

        pointTransaction = pointDao.get(pointTransaction);
        // 筆記評論數有最大次數限制，所以有的評論沒有分
        if (pointTransaction != null && null != pointTransaction.getId()) {
//            log.debug("pointTransaction id is ====== > {}", pointTransaction.getId());
            pointDao.deletePointTransactionBy(pointTransaction.getId());
            // 【未要求】 刪除 cactive_transaction 表 評論id 對應的
//            log.debug("update customer point total ====== > {}", pointTransaction.getId());
            // 減少 points_total customer 表  【customer 表會有定時任務每10分鐘自動統計更新，本處不做處理也可以】
//            pointDao.updateRanking();
        }
    }

    /**
     * 删除积分
     *
     * @param customerId customerId
     * @param masterId   nodeId
     * @param detailId   noteDiscussId
     * @return
     */
    private int deleteDiscussActivePoints(
            Long masterId,
            Long detailId,
            Long customerId
    ) {


        com.stormcrm.clinique.domain.mini.PointTransaction activeTransaction = new com.stormcrm.clinique.domain.mini.PointTransaction();

        activeTransaction.setPointTypeId(CActiveTypeEnum.NOTE_DISCUSS.getId());
        activeTransaction.setCustomerId(customerId);
        activeTransaction.setForeignMasterId(masterId);
        activeTransaction.setForeignDetailId(detailId);

        // 查询到记录，执行删除
        Integer activeId = cactiveDao.getIdBy(activeTransaction);
        if (activeId != null && activeId > 0) {
//            log.trace(" ===== cactiveDao.getIdBy ...... {}",activeId);
            int result = cactiveDao.deletePointTransactionBy(activeId);
//            log.trace(" ===== cactiveDao.deletePointTransactionBy ...... {}",result);
            if (result > 0) {
                // 减分
                int resultR = customerDao.minusCustomerActivePoints(CActiveTypeEnum.NOTE_DISCUSS.getPoints(), customerId);
//                log.trace(" ===== customerDao.minusCustomerActivePoints ...... {}",resultR);
            }
            return result;
        }
        return 1;
    }

    @Override
    public int rejectDiscussBatch(String noteDiscussIds) {
        String[] idArray = noteDiscussIds.split(",");

        for (String idString : idArray) {
            long id = LongUtil.parseInt(idString);
            if (0 < id) {
                this.rejectDiscuss(id);
            }
        }

        return 1;
    }

    @Override
    public ArrayList<NoteDiscuss> getDiscussByNoteId(long noteId) {
        return noteDao.getDiscussByNoteId(noteId);
    }

    /**
     * 笔记是否拔草
     *
     * @param noteId 笔记
     */
    public int liked(Long noteId, Long customerId) {
        return 0;

    }

    @Override
    public List<Object> getTagList(Long noteId) {
        return Collections.emptyList();
    }

    /**
     * 审核通过
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Override
    public int accept(long id) {
        return accept(id, 2);
    }

    /**
     * 审核通过 通过后发消息模板 M5rE6Y5zAfAk7OrL2jIJv7LYgrnC5KajjyW4gq-8FNs
     *
     * @param noteId 笔记id
     * @param status 0 未审核 1 审核不通过 2 审核通过 3 加精 4 置顶
     * @return 影响的行数
     */
    @Override
    public int accept(long noteId, int status) {
        // 读取用户及活动信息
        Note note = noteDao.getNoteById(noteId);
        if (null == note) {
            return 0;
        }

        // 只在审核中和审核未通过才可以审核通过( 这个条件未判断 )
        int res = noteDao.acceptByStatus(noteId, status);
        if (0 == res) {
            return 0;
        }

//        //发布内容
//        CampaignH5 camp = campaignH5Service.getValidCampaign("campaign2408");
//        if (!ObjectUtils.isEmpty(camp)){
//            this.noteDao.acceptCampByStatus(noteId,status,"campaign2408");
//        }

        String sCBNotice = "";

        //  根据 status 审核通过加分
        if (status >= 2 && status <= 4) {
            //  改成读 point type 表的方式  沿用 CBTypeEnum.NOTE_PUBLISH 的id 通过这个id 去表里查询具体内容
            sCBNotice = addCBToCustomerByNote(note, CBForeignEnum.NOTE_PUBLISH);
        }

        //  status == 3 加精 再得 50分
        if (status == 3) {
            //  改成读 point type 表的方式
            sCBNotice = addCBToCustomerByNote(note, CBForeignEnum.NOTE_FAVORITE);
        }

        //  检查 该 note 关联的 tag
        List<NotePhotoTag> notePhotoTags = noteDao.getNoteTags(note.getId());
        if (notePhotoTags != null && !notePhotoTags.isEmpty()) {
            // 获取通用加分规则
            List<PointType> normalRules = pointTypeDao.getNormalNoteTagReward();
            for (NotePhotoTag notePhotoTag : notePhotoTags){
                // 改成活动标签获取分值的方式  // 后台控制
                if (normalRules != null && !normalRules.isEmpty()) {
                    normalRules.forEach(rule -> {
                        addCBToCustomerByNoteTagNew(note, notePhotoTag, rule);
                    });
                }
                // 改为后台控制这个分值   不再判断 koc
                if (null != notePhotoTag.getPointTypeId() && notePhotoTag.getPointTypeId() > 0) {
                    PointType specialRule = pointTypeDao.getSpecialNoteTagReward(notePhotoTag.getPointTypeId());
                    addCBToCustomerByNoteTagNew(note, notePhotoTag, specialRule);
                }
            }
        }

        try {
            memberClient.setCfans(note.getUnionid());
        }
        catch (Exception ignored){
        }

        // 原本是得分后追加到订阅消息后面的，但是，有20个字符的字数限制，这个被清掉，以后可能会再用
        sCBNotice = "";

        String data = String.format("{" +
                "\"thing2\":{\"value\":\"C粉圈笔记审核 — 成功\"}," +
                "\"thing8\":{\"value\":\"快去查看你的笔记并分享给你的朋友吧！%s\"}" +
                "}", sCBNotice);
        // 如果没有这个id 会发不了通知
        if (null == note.getWechatMiniOpenid()) {
            log.error("note pass check,but customer WechatMiniOpenid is null. can not send notice");
        } else {
            // 发送订阅消息
            smotService.send((short) 5,
                    note.getWechatMiniOpenid(),
                    templateExamineResult,
                    "/pages/community/noteView/noteView?id=" + noteId,
                    data);
        }

        return 1;
    }

    /**
     * 根据类型，奖励用户
     *
     * @param note       笔记信息
     * @param rewardType 奖励类型
     * @return 结果字符串 文字描述
     */
    private String addCBToCustomerByNote(Note note, CBForeignEnum rewardType) {

        // 结果
        String sCBNotice = "";

        //  检查是否已经加过分了
        List<NotePointLog> pointLogs = notePointLogDao.getPointLog(note.getId(), rewardType.getId());
        if (pointLogs == null || pointLogs.isEmpty()) {

            PointType rule = pointTypeDao.getById(rewardType.getId());

            Date now = new Date();
            // 每月的奖励次数如果设置了，查询是否已经奖励最大次数
            if (overMaxTimeInMonth(note, rule, now)) return "已经到达最大奖励次数！";

            if (overMaxForeverTime(note, rule)) return "到达最大奖励次数！";

            // 发放( C币 )积分
            PointTransaction pointTransaction = new PointTransaction();
            pointTransaction.setCustomerId(note.getCustomerId());
            pointTransaction.setPointTypeId(rule.getId());
            pointTransaction.setPoints(rule.getRewardPoint());
            pointTransaction.setRemainingPoints(rule.getRewardPoint());
            pointTransaction.setStartTime(now);
            pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
            pointTransaction.setForeignId(rule.getId());
            pointTransaction.setForeignMasterId(note.getId());
            pointTransaction.setForeignDetailId(0L);
            pointTransaction.setRemark(rule.getName());

            pointDao.insertPointTransaction(pointTransaction);

            //  记录日志
            NotePointLog notePointLog = new NotePointLog();
            notePointLog.setTargetId(note.getId());
            notePointLog.setNoteId(note.getId());
            notePointLog.setPointType(rule.getId());
            notePointLog.setPoints(rule.getRewardPoint());
            notePointLog.setCustomerId(note.getCustomerId());

            notePointLogDao.insertPointLog(notePointLog);

            sCBNotice = "C币已到账，请记得使用。！";
        }

        return sCBNotice;

    }


    /**
     * 由于开发比活动进行的慢，这个需要补发
     * <p>
     * 查询携带 黄油奖励，在增加奖励规则后执行  执行加分操作
     */
    @Override
    public void appendNoteTagCBByHistory() {
        //  查询携带 奖励 标签 的笔记  没有加分记录
        List<Note> notes = noteDao.getNotesLossAddCBByNoteTag();

        if (notes != null && !notes.isEmpty()) {
            notes.forEach(note -> {

                //  检查 该 note 关联的 tag
                List<NotePhotoTag> notePhotoTags = noteDao.getNoteTags(note.getId());
                if (notePhotoTags != null && !notePhotoTags.isEmpty()) {

                    notePhotoTags.forEach(notePhotoTag -> {

                        // 改为后台控制这个分值   不再判断 koc
                        if (null != notePhotoTag.getPointTypeId() && notePhotoTag.getPointTypeId() > 0) {
//                            log.trace("============> note PhotoTags getPointTypeId: " + notePhotoTag.getPointTypeId());

                            PointType specialRule = pointTypeDao.getSpecialNoteTagReward(notePhotoTag.getPointTypeId());

                            addCBToCustomerByNoteTagNew(note, notePhotoTag, specialRule);
                        }

                    });
                }


            });
        }
    }

    /**
     * 奖励分类中，type 0 为客制化奖励规则，根据标签携带的奖励分值加分。
     * <p>
     * 取 PointType中的规则
     * <p>
     * 前端查询的时候，之前是取  CBForeignEnum  CBTypeEnum 里的文字说明
     *
     * @param note         笔记
     * @param notePhotoTag 笔记标签
     */
    private void addCBToCustomerByNoteTagNew(Note note, NotePhotoTag notePhotoTag, PointType rule) {
        //  检查是否已经加过分了
        List<NotePointLog> pointLogs = notePointLogDao.getPointLogs(note.getId(), notePhotoTag.getTagId(), rule.getId());
        if (pointLogs == null || pointLogs.isEmpty()) {

            // 如果奖励的分值未设置，或者为0 不需要加币
            if ((rule.getRewardPoint() == null) || (rule.getRewardPoint() == 0)) {
                return;
            }
            // 如果奖励起始时间设置了，判断 note 是这个时间 之后发的
            if ((rule.getRewardActiveStart() != null)
                    && DateUtil.earlierThan(note.getCreateTime(), rule.getRewardActiveStart())) {
                return;
            }
            // 判断 rule 是否有起止时间，如果有根据 note 的创建时间判断是否加分
            if ((rule.getRewardActiveEnd() != null)
                    && DateUtil.earlierThan(rule.getRewardActiveEnd(), note.getCreateTime())) {
                return;
            }

            Date now = new Date();
            // 如果是标签，就按照标签的奖励方式控制
            if (Boolean.TRUE.equals(rule.getForNoteTag())) {

                // 每月的奖励次数如果设置了，查询是否已经奖励最大次数
                if (overMaxTimeByTagInMonth(note, notePhotoTag.getTagId(), rule, now)) return;

                //  检查规则中的加分类型，每月最大次数，永久最大次数
                if (overMaxForeverTimeByTag(note, notePhotoTag.getTagId(), rule)) return;

            } else {
                // 每月的奖励次数如果设置了，查询是否已经奖励最大次数
                if (overMaxTimeInMonth(note, rule, now)) return;

                //  检查规则中的加分类型，每月最大次数，永久最大次数
                if (overMaxForeverTime(note, rule)) return;
            }


            //  保存得分 保存得分：互动
            // 发放( C币 )积分
            PointTransaction pointTransaction = new PointTransaction();
            pointTransaction.setCustomerId(note.getCustomerId());
            pointTransaction.setPointTypeId(rule.getId());
            pointTransaction.setPoints(rule.getRewardPoint());
            pointTransaction.setRemainingPoints(rule.getRewardPoint());
            pointTransaction.setStartTime(now);
            // 积分过期时间
            pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
            pointTransaction.setForeignId(rule.getId());
            pointTransaction.setForeignMasterId(notePhotoTag.getTagId());
            pointTransaction.setForeignDetailId(notePhotoTag.getId());
            pointTransaction.setRemark(rule.getName());
            pointDao.insertPointTransaction(pointTransaction);

            //  记录日志
            NotePointLog notePointLog = new NotePointLog();
            notePointLog.setTargetId(notePhotoTag.getTagId());
            notePointLog.setNoteId(note.getId());
            notePointLog.setTagId(notePhotoTag.getTagId());
            notePointLog.setPointType(rule.getId());
            notePointLog.setPoints(rule.getRewardPoint());
            notePointLog.setCustomerId(note.getCustomerId());
            notePointLogDao.insertPointLog(notePointLog);
        }
    }

    private boolean overMaxTimeByTagInMonth(Note note, Long tagId, PointType rule, Date now) {
        if (rule.getRewardMax() != null && rule.getRewardMax() > 0) {
            Integer rewardTimesInMonth = notePointLogDao.getMaxTimesForTagBy(note.getCustomerId(), rule.getId(), DateUtil.parseString(now), tagId);
            return rewardTimesInMonth != null && rewardTimesInMonth >= rule.getRewardMax();
        }
        return false;
    }

    private boolean overMaxForeverTimeByTag(Note note, Long tagId, PointType rule) {
        if (rule.getRewardMaxForever() != null && rule.getRewardMaxForever() > 0) {
            Integer rewardTimes = notePointLogDao.getMaxTimesForTagWithoutDate(note.getCustomerId(), rule.getId(), tagId);
            return rewardTimes != null && rewardTimes >= rule.getRewardMaxForever();
        }
        return false;
    }

    private boolean overMaxTimeInMonth(Note note, PointType rule, Date now) {
        if (rule.getRewardMax() != null && rule.getRewardMax() > 0) {
            Integer rewardTimesInMonth = notePointLogDao.getMaxTimesBy(note.getCustomerId(), rule.getId(), DateUtil.parseString(now));
            return rewardTimesInMonth != null && rewardTimesInMonth >= rule.getRewardMax();
        }
        return false;
    }

    private boolean overMaxForeverTime(Note note, PointType rule) {
        if (rule.getRewardMaxForever() != null && rule.getRewardMaxForever() > 0) {
            Integer rewardTimes = notePointLogDao.getMaxTimesWithoutDate(note.getCustomerId(), rule.getId());
            return rewardTimes != null && rewardTimes >= rule.getRewardMaxForever();
        }
        return false;
    }

    /**
     * 批量同意
     *
     * @param ids 自动编号
     * @return 影响的行数
     */
    @Override
    public int acceptBatch(String ids) {
        return acceptBatch(ids, 2);

    }

    /**
     * 批量同意审核通过
     *
     * @param ids    自动编号
     * @param status 0 未审核 1 审核不通过 2 审核通过 3 加精 4 置顶
     * @return 影响的行数
     */
    @Override
    public int acceptBatch(String ids, int status) {
        String[] idArray = ids.split(",");

        for (String idString : idArray) {
            long id = LongUtil.parseInt(idString);
            if (0 < id) {
                accept(id, status);
            }
        }

        return 1;
    }

    /**
     * 审核失败   审核失败后发消息模板 M5rE6Y5zAfAk7OrL2jIJv7LYgrnC5KajjyW4gq-8FNs
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Override
    public int reject(long id) {

        // 读取用户及活动信息
        Note note = noteDao.getNoteById(id);
        if (null == note) {
            return 0;
        }

        // 改变状态
        if (0 == noteDao.reject(id)) {
            return 0;
        }

        //发布内容
        CampaignH5 camp = campaignH5Service.getValidCampaign("campaign2408");
        if (!ObjectUtils.isEmpty(camp)){
            this.noteDao.rejectCamp(id,"campaign2408");
        }

        String data = "{" +
                "\"thing2\":{\"value\":\"C粉圈笔记审核 — 失败\"}," +
                "\"thing8\":{\"value\":\"请检查笔记是否符合社区规范再重新上传哦！\"}" +
                "}";

        if (null == note.getWechatMiniOpenid()) {
            log.error("note pass check,but customer WechatMiniOpenid is null. can not send notice");
        } else {
            // 发送订阅消息
            smotService.send((short) 6,
                    note.getWechatMiniOpenid(),
                    templateExamineResult,
                    "/pages/community/noteView/noteView?id=" + id,
                    data);

//            outsideService.templateSendWithMp((short) 6, note.getWechatOfficialOpenid(), templateExamineResult,
//                    data, "wxc0099da8e25e2948", "/pages/community/noteView/noteView?id=" + id);
//            outsideService.templateSendWithMp((short) 6, note.getWechatOfficialOpenid(), templateExamineResult,
//                    data, appId, "/pages/community/noteView/noteView?id=" + id);

//            outsideService.templateSendWithMp((short) 6,
//                    note.getWechatMiniOpenid(),
//                    templateExamineResult,
//                    data, appId, "/pages/community/noteView/noteView?id=" + id);
        }

        return 1;
    }

    /**
     * 批量拒绝
     *
     * @param ids 自动编号
     * @return 影响的行数
     */
    @Override
    public int rejectBatch(String ids) {

        String[] idArray = ids.split(",");

        for (String idString : idArray) {
            long id = LongUtil.parseInt(idString);
            if (0 < id) {
                this.reject(id);
            }
        }
        return 1;
    }


    @Override
    public void insertNote(Note note) {

    }


    @Override
    public void insertDiscuss(NoteDiscuss noteDiscuss) {

    }

    @Override
    public void delNote(Note note) {

    }


    @Override
    public void insertReadLog(NoteReadLog log) {

    }


    @Override
    public void insertLikeLog(NoteLikeLog log) {

    }


    @Override
    public void insertShareLog(NoteShareLog log) {

    }


    @Override
    public ArrayList<NoteDiscuss> getCustomerDiscussList(int customerId, int start) {
        return null;
    }


    @Override
    public ArrayList<Note> getCustomerNoteList(int customerId, int start) {
        return null;
    }

    /**
     * @param noteId 笔记id
     */
    @Override
    public ArrayList<NoteTag> getTagListByNoteId(Long noteId) {
        return noteDao.getTagListByNoteId(noteId);
    }
}
