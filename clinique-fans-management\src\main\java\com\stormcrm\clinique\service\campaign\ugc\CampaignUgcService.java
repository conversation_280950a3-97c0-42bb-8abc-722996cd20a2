package com.stormcrm.clinique.service.campaign.ugc;

import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgc;
import com.stormcrm.clinique.vo.Result;

import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
public interface CampaignUgcService {

    /**
     * 查询所有
     *
     * @return 列表
     */
    List<CampaignUgc> getAll();

    /**
     * 查询所有记录数
     *
     * @return 记录数
     */
    int getAllCount();

    /**
     * 查询信息
     *
     * @param id 自动编号
     * @return 信息
     */
    CampaignUgc getById(long id);

    /**
     * 保存
     *
     * @param campaignUgc 信息
     * @return 影响的行数
     */
    Result save(CampaignUgc campaignUgc);

    /**
     * 更新
     *
     * @param campaignUgc 信息
     * @return 影响的行数
     */
    Result update(CampaignUgc campaignUgc);

    /**
     * 上架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    int upper(long id);

    /**
     * 下架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    int lower(long id);

    /**
     * 逻辑删除
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    int del(long id);

}
