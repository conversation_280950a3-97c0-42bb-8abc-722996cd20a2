package cn.slashsoft.clinique.dao.campaign;

import cn.slashsoft.clinique.domain.campaign.*;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * MS 达人榜
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignMsTalentsDao {

    /**
     * 获取达人榜信息
     *
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `status` " +
            "FROM " +
            "   `campaign_ms_talents` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    CampaignMsTalents getCampaignMsTalents(long customerId);

    /**
     * 获取达人榜信息, 和上传图片的数量
     *
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `alert`," +
            "   `status`," +
            "   `upload_time`," +
            "   `examine_time`," +
            "   (" +
            "       SELECT " +
            "           COUNT(`id`) " +
            "       FROM " +
            "           `campaign_ms_talents_image` " +
            "       WHERE " +
            "           `talents_id`=`campaign_ms_talents`.`id` " +
            "           AND `status`=1" +
            "   ) `image_count` " +
            "FROM " +
            "   `campaign_ms_talents` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    CampaignMsTalents getCampaignMsTalentsWithImageCount(long customerId);

    /**
     * 获取所有图片
     *
     * @param campaignMsTalentsId 顾客活动信息编号
     * @return 图片地址
     */
    @Select("SELECT " +
            "   `image_url`," +
            "   `status` " +
            "FROM " +
            "   `campaign_ms_talents_image` " +
            "WHERE " +
            "   `talents_id`=#{talentsId} " +
            "ORDER BY `id` DESC")
    List<CampaignMsTalentsImage> getCampaignMsTalentsImageList(long campaignMsTalentsId);

    /**
     * 获取当前TOP信息
     *
     * @return TOP信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `stage` " +
            "FROM " +
            "   `campaign_ms_talents_top` " +
            "WHERE " +
            "   `status`=1 " +
            "   AND NOW()>=`start_time` AND NOW()<=`end_time`")
    CampaignMsTalentsTop getCampaignMsTalentsTop();

    /**
     * 获取入选TOP的名单
     *
     * @param campaignMsTalentsTopId TOP信息编号
     * @return 名单
     */
    @Select("SELECT " +
            "   `t`.`id` `talents_detail_id`," +
            "   `t`.`examine_time`, " +
            "   `w`.`nick_name`, " +
            "   `w`.`avatar_url` " +
            "FROM " +
            "   `campaign_ms_talents_top_detail` `d` " +
            "       INNER JOIN " +
            "   `campaign_ms_talents` `t` " +
            "       ON `d`.`talents_id`=`t`.`id` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `t`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "   `d`.`talents_top_id`=#{talentsTopId} AND `d`.`status`=1 " +
            "ORDER BY " +
            "   `d`.`sort`")
    List<CampaignMsTalentsTopDetail> getCampaignMsTalentsTopDetailList(long campaignMsTalentsTopId);

    /**
     * 获取入选TOP的图片
     *
     * @param campaignMsTalentsTopId TOP信息编号
     * @return 图片
     */
    @Select("SELECT " +
            "   `i`.`id`, " +
            "   `i`.`talents_id` `talents_detail_id`, " +
            "   `i`.`image_url` " +
            "FROM " +
            "   `campaign_ms_talents_top_detail` `d` " +
            "       INNER JOIN " +
            "   `campaign_ms_talents_image` `i` " +
            "       ON `d`.`talents_id`=`i`.`talents_id` " +
            "WHERE " +
            "   `d`.`talents_top_id`=#{talentsTopId}" +
            "   AND `d`.`status`=1 AND `i`.`status`=1")
    List<CampaignMsTalentsTopDetailImage> getCampaignMsTalentsTopDetailImageList(long campaignMsTalentsTopId);

    /**
     * 写入达人榜信息
     *
     * @param campaignMsTalents 达人榜信息
     */
    @Insert("INSERT INTO `campaign_ms_talents`(" +
            "   `customer_id`, " +
            "   `follow`, " +
            "   `follow_source`, " +
            "   `follow_first_time`, " +
            "   `follow_last_time`, " +
            "   `follow_cancel_time`, " +
            "   `bind`, " +
            "   `bind_time`, " +
            "   `status` " +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{follow}," +
            "   #{followSource}," +
            "   #{followFirstTime}," +
            "   #{followLastTime}," +
            "   #{followCancelTime}," +
            "   #{bind}," +
            "   #{bindTime}," +
            "   #{status}" +
            ")")
    void insertCampaignMsTalents(CampaignMsTalents campaignMsTalents);

    /**
     * 变改状态
     *
     * @param customerId     顾客编号
     * @param originalStatus 原始状态
     * @param newStatus      新状态
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ms_talents` " +
            "SET " +
            "   `status`=#{newStatus}, " +
            "   `alert`=1 " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "   AND `status`=#{originalStatus}")
    int updateCampaignMsTalents(long customerId, short originalStatus, short newStatus);

    /**
     * 更新提醒状态
     *
     * @param customerId 顾客编号
     */
    @Update("UPDATE " +
            "   `campaign_ms_talents` " +
            "SET " +
            "   `alert`=0 " +
            "WHERE " +
            "   `customer_id`=#{customerId}")
    void updateCampaignMsTalentsAlert(long customerId);

    /**
     * 变改状态
     *
     * @param customerId     顾客编号
     * @param originalStatus 原始状态
     * @param newStatus      新状态
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ms_talents` " +
            "SET " +
            "   `status`=#{newStatus}," +
            "   `alert`=1, " +
            "   `upload_time`=NOW() " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "   AND `status`=#{originalStatus}")
    int updateCampaignMsTalentsWithUploadTime(long customerId, short originalStatus, short newStatus);

    /**
     * 设置图片无效
     *
     * @param campaignMsTalentsId 顾客活动信息编号
     */
    @Update("UPDATE " +
            "   `campaign_ms_talents_image` " +
            "SET " +
            "   `status`=0 " +
            "WHERE " +
            "   `talents_id`=#{talentsId}")
    void updateCampaignMsTalentsImageStatus(long campaignMsTalentsId);

    /**
     * 写入图片信息
     *
     * @param campaignMsTalentsImage 图片信息
     */
    @Insert("INSERT INTO `campaign_ms_talents_image`(" +
            "   `talents_id`, " +
            "   `image_url`" +
            ") " +
            "VALUES (" +
            "   #{talentsId}, " +
            "   #{imageUrl}" +
            ")")
    void insertCampaignMsTalentsImage(CampaignMsTalentsImage campaignMsTalentsImage);

    /**
     * 写入活动操作日志
     *
     * @param campaignMsTalentsLog 操作日志
     */
    @Insert("INSERT INTO `campaign_ms_talents_log`(" +
            "   `customer_id`, " +
            "   `status`, " +
            "   `content`" +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{status}, " +
            "   #{content}" +
            ")")
    void insertCampaignMsTalentsLog(CampaignMsTalentsLog campaignMsTalentsLog);

    /**
     * 更新肤质
     *
     * @param customerId 顾客编号
     * @param skinType   肤质
     */
    @Update("UPDATE " +
            "   `campaign_ms_talents` " +
            "SET " +
            "   `skin_type`=#{skinType} " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    void setCampaignMsTalentsSkinType(long customerId, short skinType);

    /**
     * 写入访问日志
     *
     * @param campaignMsTalentsViewLog 日志
     */
    @Insert("INSERT INTO `campaign_ms_talents_view_log`(" +
            "   `customer_id`," +
            "   `source`," +
            "   `page`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{source}," +
            "   #{page}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertCampaignMsTalentsViewLog(CampaignMsTalentsViewLog campaignMsTalentsViewLog);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Update("UPDATE " +
            "   `campaign_ms_talents_view_log` " +
            "SET " +
            "   `end_time`=NOW()," +
            "   `diff_second`=TimeStampDiff(SECOND,`start_time`,NOW()) " +
            "WHERE " +
            "   `id`=#{id}")
    void setCampaignMsTalentsViewLog(long id);

    /**
     * 写入达人榜访问记录
     *
     * @param campaignMsTalentsTopDetailLog 达人榜访问记录
     */
    @Insert("INSERT INTO `campaign_ms_talents_top_detail_log`(" +
            "   `customer_id`, " +
            "   `stage`, " +
            "   `talents_top_detail_id`" +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{stage}, " +
            "   #{talentsTopDetailId}" +
            ")")
    void insertCampaignMsTalentsTopDetailLog(CampaignMsTalentsTopDetailLog campaignMsTalentsTopDetailLog);

    /**
     * 写入达人榜图片访问记录
     *
     * @param campaignMsTalentsTopDetailImageLog 达人榜图片访问记录
     */
    @Insert("INSERT INTO `campaign_ms_talents_top_detail_image_log`(" +
            "   `customer_id`, " +
            "   `stage`, " +
            "   `talents_top_detail_id`, " +
            "   `talents_image_id`, " +
            "   `type` " +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{stage}, " +
            "   #{talentsTopDetailId}, " +
            "   #{talentsImageId}, " +
            "   #{type} " +
            ")")
    void insertCampaignMsTalentsTopDetailImageLog(CampaignMsTalentsTopDetailImageLog campaignMsTalentsTopDetailImageLog);

}
