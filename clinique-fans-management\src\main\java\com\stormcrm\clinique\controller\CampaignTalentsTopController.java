package com.stormcrm.clinique.controller;

import com.stormcrm.clinique.domain.CampaignTalentsTop;
import com.stormcrm.clinique.service.CampaignTalentsTopService;
import com.stormcrm.clinique.util.DateUtil;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.util.VerifyUtil;
import com.stormcrm.clinique.vo.Result;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 新品体验官活动TOP5
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("campaign-talents-top")
public class CampaignTalentsTopController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    private final CampaignTalentsTopService campaignTalentsTopService;

    public CampaignTalentsTopController(CampaignTalentsTopService campaignTalentsTopService) {
        this.campaignTalentsTopService = campaignTalentsTopService;
    }

    /**
     * 页面模版
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_TALENTS_TOP')")
    @GetMapping("")
    public String index(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/campaign-talents-top/index";
    }

    /**
     * 查询所有
     *
     * @return 列表
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_TALENTS_TOP')")
    @PostMapping("get-all")
    @ResponseBody
    public String getAll() {

        List<CampaignTalentsTop> campaignTalentsTopList = campaignTalentsTopService.getAll();

        JSONObject mata = new JSONObject();
        mata.put("page", 1);
        mata.put("pages", 1);
        mata.put("perpage", -1);
        mata.put("total", campaignTalentsTopList.size());
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("mata", mata);
        result.put("data", campaignTalentsTopList);

        return JSONObject.toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }

    /**
     * 增加模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_TALENTS_TOP')")
    @RequestMapping("add")
    public String add(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/campaign-talents-top/add";
    }

    /**
     * 新增
     *
     * @param stage     期数
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_TALENTS_TOP')")
    @PostMapping(value = "add-submit", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String addSubmit(
            @RequestParam(value = "form-stage", required = false) Short stage,
            @RequestParam(value = "form-start-time", required = false) String startTime,
            @RequestParam(value = "form-end-time", required = false) String endTime
    ) {

        // 验证
        if (null == stage) {
            return ResultUtil.verifyFailToJson("form-stage", "这是必填字段");
        }

        // 验证
        if (!VerifyUtil.required(startTime)) {
            return ResultUtil.verifyFailToJson("form-start-time", "这是必填字段");
        }

        // 验证
        if (!VerifyUtil.required(endTime)) {
            return ResultUtil.verifyFailToJson("form-end-time", "这是必填字段");
        }

        Date startTimeDate = DateUtil.valueOf(startTime);
        if(null == startTimeDate){
            return ResultUtil.verifyFailToJson("form-start-time", "日期格式错误");
        }

        Date endTimeDate = DateUtil.valueOf(endTime);
        if(null == endTimeDate){
            return ResultUtil.verifyFailToJson("form-end-time", "日期格式错误");
        }

        // 生成对象
        CampaignTalentsTop campaignTalentsTop = new CampaignTalentsTop();
        campaignTalentsTop.setStage(stage);
        campaignTalentsTop.setStartTime(startTimeDate);
        campaignTalentsTop.setEndTime(endTimeDate);

        // 传到Service服务中保存
        Result result = campaignTalentsTopService.save(campaignTalentsTop);

        // 返回绍果给前端
        return JSONObject.toJSONString(result);
    }

    /**
     * 编辑模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_TALENTS_TOP')")
    @GetMapping("edit/{id}")
    public String edit(@PathVariable long id, Model model) {
        model.addAttribute("staticDomain", staticDomain);
        // 读数据库
        CampaignTalentsTop campaignTalentsTop = campaignTalentsTopService.getById(id);
        if (null == campaignTalentsTop) {
            return "m/fans/common/empty";
        }
        model.addAttribute("id", id);
        model.addAttribute("campaignTalentsTop", campaignTalentsTop);
        return "m/fans/campaign-talents-top/edit";
    }

    /**
     * 编辑
     *
     * @param id        编号
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_TALENTS_TOP')")
    @PostMapping(value = "edit-submit/{id}", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String editSubmit(
            @PathVariable("id") long id,
            @RequestParam(value = "form-start-time", required = false) String startTime,
            @RequestParam(value = "form-end-time", required = false) String endTime
    ) {

        // 验证
        if (!VerifyUtil.required(startTime)) {
            return ResultUtil.verifyFailToJson("form-start-time", "这是必填字段");
        }

        // 验证
        if (!VerifyUtil.required(endTime)) {
            return ResultUtil.verifyFailToJson("form-end-time", "这是必填字段");
        }

        Date startTimeDate = DateUtil.valueOf(startTime);
        if(null == startTimeDate){
            return ResultUtil.verifyFailToJson("form-start-time", "日期格式错误");
        }

        Date endTimeDate = DateUtil.valueOf(endTime);
        if(null == endTimeDate){
            return ResultUtil.verifyFailToJson("form-end-time", "日期格式错误");
        }

        // 生成对象
        CampaignTalentsTop campaignTalentsTop = new CampaignTalentsTop();
        campaignTalentsTop.setId(id);
        campaignTalentsTop.setStartTime(startTimeDate);
        campaignTalentsTop.setEndTime(endTimeDate);

        // 传到Service服务中保存
        Result result = campaignTalentsTopService.update(campaignTalentsTop);

        // 返回绍果给前端
        return JSONObject.toJSONString(result);
    }

}
