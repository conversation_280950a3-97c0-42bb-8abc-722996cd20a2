package cn.slashsoft.clinique.controller.mini;

import cn.slashsoft.clinique.domain.mini.Customer;
import cn.slashsoft.clinique.domain.mini.CustomerAddress;
import cn.slashsoft.clinique.domain.mini.PointTransaction;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.CactiveService;
import cn.slashsoft.clinique.service.mini.CustomerService;
import cn.slashsoft.clinique.service.mini.PointService;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.IntegerUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.mini.*;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Date;
import java.util.List;

/**
 * 个人中心
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/mine")
public class MineController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;

    @Resource
    private HttpServletRequest request;

    @Resource
    private HttpSession session;

    @Resource
    private PointService pointService;

    @Resource
    private CactiveService cactiveService;

    @Resource
    private CustomerService customerService;

    @Resource
    private WechatService wechatService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 模拟登陆
     *
     * @return 执行结果
     */
    @GetMapping("/getuser")
    public long getuser() {
        long customerId = (long) request.getAttribute("customerId");
        return customerId;
    }

    /**
     * 获取个人中心页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/get-data")
    public String getMineData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        int rankingStatus = IntegerUtil.parseInt(stringRedisTemplate.opsForValue().get("configRankingStatus"));

        MineVo mineVo = new MineVo();

        mineVo.setRankingStatus(rankingStatus);
        // 排名期间
        // if(0 < rankingStatus){
            mineVo.setPointTotal(pointService.getPointTotal(customerId));
        // }

        mineVo.setRemainingPointTotal(pointService.getRemainingPointTotal(customerId));
        mineVo.setExpiredPointTotal(pointService.getExpiredPointTotal(customerId));
        mineVo.setExpiredData(DateUtil.parseDayString(DateUtil.getLastDayOfMonth(new Date())));

        Wechat wechat = wechatService.getWechatInfoByCustomerId(customerId);
        mineVo.setNickName(wechat.getNickName());
        mineVo.setAvatarUrl(wechat.getAvatarUrl());
        
        int all = this.cactiveService.getCustomerAll(customerId);
        mineVo.setCactivePoints(all<0?0:all);

        return ResultUtil.customer(ResultEnum.SUCCESS, mineVo);

    }

    /**
     * 获取积分明细页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/get-point-detail-data")
    public String getPointDetailData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        List<PointTransaction> pointTransactionList = pointService.getPointTransaction(customerId);

        PointDetailVo pointDetailVo = new PointDetailVo();
        pointDetailVo.setPointTransactionList(pointTransactionList);

        return ResultUtil.customer(ResultEnum.SUCCESS, pointDetailVo);

    }

    /**
     * 获取编辑资料页页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/get-info-data")
    public String getInfoData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        MineInfoVo mineInfoVo = new MineInfoVo();

        Wechat wechat = wechatService.getWechatInfoByCustomerId(customerId);
        mineInfoVo.setNickName(wechat.getNickName());
        mineInfoVo.setAvatarUrl(wechat.getAvatarUrl());

        Customer customer = customerService.getCustomerById(customerId);
        mineInfoVo.setPhoneNumber(customer.getPhoneNumber());
        mineInfoVo.setName(customer.getName());
        mineInfoVo.setBirthday(customer.getBirthday());
        mineInfoVo.setBirthdayUpdateTimes(customer.getBirthdayUpdateTimes());
        mineInfoVo.setXiaohongshuAccount(customer.getXiaohongshuAccount());
        mineInfoVo.setAgreeCorpCommunicate(null != customer.getAgreeCorpCommunicate() && customer.getAgreeCorpCommunicate());
        mineInfoVo.setAgreeBrandCommunicate(null != customer.getAgreeBrandCommunicate() && customer.getAgreeBrandCommunicate());

        CustomerAddress customerAddress = customerService.getPreferredAddressByCustomerId(customerId);
        if (null != customerAddress) {
            mineInfoVo.setAddress(customerAddress.getProvince() + " " + customerAddress.getCity() + " " + customerAddress.getDistrict());
        } else {
            mineInfoVo.setAddress("");
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, mineInfoVo);

    }

    /**
     * 获取编辑资料页页面首选地址数据
     *
     * @return 页面数据
     */
    @PostMapping("/get-info-preferred-address-data")
    public String getInfoPreferredAddressData(){

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        CustomerAddress customerAddress = customerService.getPreferredAddressByCustomerId(customerId);
        MineInfoVo mineInfoVo = new MineInfoVo();

        if (null != customerAddress) {
            mineInfoVo.setAddress(customerAddress.getProvince() + " " + customerAddress.getCity() + " " + customerAddress.getDistrict());
        } else {
            mineInfoVo.setAddress("");
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, mineInfoVo);
    }

    /**
     * 更新个人资料
     *
     * @return 处理结果
     */
    @PostMapping("/set-info")
    public String setMineInfo(
            @RequestBody Info info
    ) {
        // 获取缓存中的顾客编号
        String unionid = (String) session.getAttribute("unionid");
        long customerId = (long) request.getAttribute("customerId");
        info.setUnionid(unionid);
        info.setCustomerId(customerId);

        customerService.updateCustomer(info);

        return ResultUtil.customer(ResultEnum.SUCCESS);

    }

    /**
     * 获取邮寄地址列表页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/get-address-data")
    public String getAddressData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        List<CustomerAddress> customerAddressList = customerService.getCustomerAddressByCustomerId(customerId);

        AddressVo addressVo = new AddressVo();
        addressVo.setCustomerAddressList(customerAddressList);

        return ResultUtil.customer(ResultEnum.SUCCESS, addressVo);

    }

    /**
     * 获取邮寄地址增加页页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/get-address-add-data")
    public String getAddressAddData(){

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        String phoneNumber = customerService.getCustomerPhoneNumberById(customerId);

        AddressAddVo addressAddVo = new AddressAddVo();

        addressAddVo.setPhoneNumber(phoneNumber);

        return ResultUtil.customer(ResultEnum.SUCCESS, addressAddVo);

    }

    /**
     * 获取邮寄地址编辑页页面数据
     *
     * @param customerAddressId 邮寄地址编号
     * @return 页面数据
     */
    @PostMapping("/get-address-edit-data/{id}")
    public String getAddressEditData(@PathVariable("id") long customerAddressId) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        CustomerAddress customerAddress = customerService.getCustomerAddressById(customerId, customerAddressId);

        AddressEditVo addressEditVo = new AddressEditVo();
        addressEditVo.setCustomerAddress(customerAddress);

        return ResultUtil.customer(ResultEnum.SUCCESS, addressEditVo);

    }

    /**
     * 新增邮寄地址
     *
     * @return 处理结果
     */
    @PostMapping("/address-add")
    public String addressAdd(
            @RequestBody CustomerAddress customerAddress
    ) {

        // 验证

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        customerAddress.setCustomerId(customerId);

        customerService.insertAddress(customerAddress);

        return ResultUtil.customer(ResultEnum.SUCCESS);

    }

    /**
     * 编辑邮寄地址
     *
     * @return 处理结果
     */
    @PostMapping("/address-update")
    public String addressUpdate(
            @RequestBody CustomerAddress customerAddress
    ) {
        // 验证

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        CustomerAddress customerAddressById = customerService.getCustomerAddressById(customerId, customerAddress.getId());
        if(customerAddressById==null){
            return ResultUtil.customer(ResultEnum.FAILED,"编辑失败");
        }


        customerAddress.setCustomerId(customerId);

        customerService.updateAddress(customerAddress);

        return ResultUtil.customer(ResultEnum.SUCCESS);

    }

    /**
     * 删除邮寄地址
     *
     * @return 处理结果
     */
    @PostMapping("/address-del/{id}")
    public String addressDel(
            @PathVariable("id") long customerAddressId
    ) {
        long customerId = (long) request.getAttribute("customerId");

        CustomerAddress customerAddressById = customerService.getCustomerAddressById(customerId, customerAddressId);
        if(customerAddressById==null){
            return ResultUtil.customer(ResultEnum.FAILED,"删除失败");
        }

        customerService.delAddress(customerAddressId);

        return ResultUtil.customer(ResultEnum.SUCCESS);

    }

}
