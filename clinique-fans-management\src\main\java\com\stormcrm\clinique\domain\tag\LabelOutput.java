package com.stormcrm.clinique.domain.tag;

import lombok.Data;
/**
 * 
 *
 * <AUTHOR>
 */

@Data
public class LabelOutput {
	//id
	int id;
	//标题
	String name;
	//标题
	String desc;
	//产出类型 1count 2avg 3sum
	int type;
	
	String table;
	
	String column ;

	public String getSql() {
		String sql = " SELECT ";
		String out_name = "total" ;
		
		switch(type) {
			case 1:
				sql += " COUNT( " + column + ") " + out_name + " ";
				break;
			case 2:
				sql += " AVG( " + column + ") " + out_name + " ";
				break;
			case 3:
				sql += " SUM( " + column + ") " + out_name + " ";
				break;
		}
		
		return sql + " FROM " + table + " ";
	}
	
}
