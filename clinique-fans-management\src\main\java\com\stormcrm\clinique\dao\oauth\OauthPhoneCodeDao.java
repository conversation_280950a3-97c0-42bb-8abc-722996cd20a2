package com.stormcrm.clinique.dao.oauth;

import com.stormcrm.clinique.oauth.domain.OauthPhoneCode;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
public interface OauthPhoneCodeDao {

    /**
     * 获取管理员 手机号 对应的 code
     *
     * @param number 手机号
     * @param second 过期秒数
     * @return 管理员信息
     */
    @Select("SELECT " +
            "   `id`, " +
            "   `code`, " +
            "   `session_id`, " +
            "   `phone_number`, " +
            "   `times` " +
            "FROM " +
            "   `oauth_phone_code` " +
            "WHERE " +
            "   `times`>0 AND " +
            // 不需要  手机号加密
            "   `phone_number`=#{number} AND" +
            "   (UNIX_TIMESTAMP(CURRENT_TIMESTAMP()) - UNIX_TIMESTAMP(create_time) ) < #{second}  " +
            "LIMIT 1")
    OauthPhoneCode getPhoneValidCode(String number, Long second);


    @Update(" UPDATE `oauth_phone_code` SET  "
            + "  `times` = `times` - 1 "
            + " WHERE `id` = #{id} ")
    int countDownTimes(OauthPhoneCode code);

    /**
     * 管理员登录用的 手机验证码
     *
     * @param code 手机验证码
     */
    @Insert("INSERT INTO `oauth_phone_code`(" +
            "   `code`, " +
            "   `session_id`, " +
            "   `phone_number`, " +
            "   `times`" +
            ") " +
            "VALUES (" +
            "   #{code}," +
            "   #{sessionId}," +
            // 不需要  手机号加密
            "   #{phoneNumber}," +
            "   #{times}" +
            ")")
    void savePhoneCode(OauthPhoneCode code);
}
