package cn.slashsoft.clinique.dao.mini;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.mini.CactiveRank;
import cn.slashsoft.clinique.domain.mini.PointTransaction;
import cn.slashsoft.clinique.domain.mini.PointTransactionDeduct;

/**
 * 积分相关的数据库操作
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CactiveDao {

    /**
     * 读取某类型本月加CB次数
     * @param customerId 顾客编号
     * @return 加CB次数
     */
    @Select("SELECT " +
            "   count(`id`) " +
            "FROM " +
            "   `cactive_transaction` " +
            "WHERE " +
            "   `customer_id` = #{customerId} " +
            "   AND `status` = 1"
            + " AND `cactive_type_id`= #{type}"
            + " AND  MONTH(`create_time`) = MONTH(now()) "
    )
    int getCustomerMonthPointCountByType( int type,Long customerId);
    /**
     * 读取内容是否已加CB
     * @param customerId 顾客编号
     * @return 加CB次数
     */
    @Select("SELECT " +
            "   count(`id`) " +
            "FROM " +
            "   `cactive_transaction` " +
            "WHERE " +
            "   `customer_id` = #{customerId} " +
            "   AND `status` = 1"
            + " AND `cactive_type_id`= #{type}"
            + " AND  MONTH(`create_time`) = MONTH(now()) "
            + " AND `foreign_master_id`=#{detailId} "
    )
    int getCustomerPointCountByDetailId(short type, Long customerId, Long detailId);
    /**
     * 读取内容是否已加CB

     * @return 加CB次数
     */
    @Select("SELECT " +
            "   count(`id`) " +
            "FROM " +
            "   `cactive_transaction` " +
            "WHERE " +
            "   `customer_id` = #{customerId} " +
            "   AND `status` = 1"
            + " AND `cactive_type_id`= #{pointTypeId} "
            + " AND `foreign_master_id`=#{foreignMasterId} "
            + " AND `remark`=#{remark} "
    )
    int has(PointTransaction pt);
    
    /**
     * 读取排名
     * @param length 顾客编号
     */
    @Select("SELECT a.id, a.`cactive_points` `points`, " +
            "   `w`.`nick_name` `ownerName`, " +
            "   `w`.`avatar_url` `avatarUrl`,  " 
            + " k.`customer_id` `koc_v` "
    		+ " FROM customer a " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`id`=`w`.`customer_id` " 
            + " LEFT JOIN `customer_special_koc_v` k "
            + " ON k.`customer_id` = `a`.`id` "
            //+ " where a.`cactive_points` > 0 "
    		+ " ORDER BY `points`  desc "
    		+ " limit #{length} ")
    List<CactiveRank> getRank(int length);

   
 /*
     * 写入积分变动表
     *
     * @param pointTransaction 积分变动表
     */
    @Insert("INSERT INTO `cactive_transaction`(" +
            "   `customer_id`, " +
            "   `cactive_type_id`, " +
            "   `points`, " +
            "   `remaining_points`, " +
            "   `remark`, " +
            "   `start_time`, " +
            "   `expired_time`, " +
            "   `foreign_id`, " +
            "   `foreign_master_id`, " +
            "   `foreign_detail_id`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{pointTypeId}," +
            "   #{points}," +
            "   #{remainingPoints}," +
            "   #{remark}," +
            "   #{startTime}," +
            "   #{expiredTime}," +
            "   #{foreignId}," +
            "   #{foreignMasterId}," +
            "   #{foreignDetailId}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertPointTransaction(PointTransaction pointTransaction);

    /**
     * 写入积分扣除表
     *
     * @param pointTransactionDeduct 积分扣除
     */
    @Insert("INSERT INTO `cactive_transaction_deduct`(" +
            "   `cactive_transaction_id`, " +
            "   `deduct_cactive_transaction_id`, " +
            "   `points`" +
            ") " +
            "VALUES (" +
            "   #{pointTransactionId}," +
            "   #{deductPointTransactionId}," +
            "   #{points}" +
            ")"
    )
    void insertPointTransactionDeduct(PointTransactionDeduct pointTransactionDeduct);
    
    /*
     * 获取用户c粉值排名 去掉已删除用户
     * @param count
     */

    @Select("SELECT * FROM `cactive_transaction`  "
    		+ " WHERE `customer_id` = #{id} and  `cactive_type_id`= #{id2}  ")
	List<PointTransaction> getCactiveBy(Long id, short id2);
	/*
	 * 获取参与用户
	 */
	@Select("select count(DISTINCT customer_id) from cactive_transaction ")
	int getAllCount();

 

}
