package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.Mot;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * 模版消息
 * <AUTHOR>
 */
@Repository
@Mapper
public interface MotDao {

    /**
     * 写入模版消息
     * @param mot 模版消息
     */
    @Insert("INSERT INTO `mot`(" +
            "   `wechat_official_openid`, " +
            "   `type`, " +
            "   `param`, " +
            "   `result`, " +
            "   `status`" +
            ") " +
            "VALUES (" +
            "   #{wechatOfficialOpenid}, " +
            "   #{type}, " +
            "   #{param}, " +
            "   #{result}, " +
            "   #{status}" +
            ")")
    void insertMot(Mot mot);

}
