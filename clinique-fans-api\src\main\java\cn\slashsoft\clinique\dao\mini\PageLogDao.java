package cn.slashsoft.clinique.dao.mini;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.mini.PageLog;

/**
 * 页面打开记录
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface PageLogDao {


    /**
     * 写入页面打开记录
     *
     * @param log 页面打开记录
     */
    @Insert("insert into `page_log` ( " +
            "   `wechat_mini_openid`, " +
            "   `source`, " +
            "   `share_mini_openid`, " +
            "   `page` " +
            ") " +
            "VALUES ( " +
            "   #{wechatMiniOpenid}, " +
            "   #{source}, " +
            "   #{shareMiniOpenid}, " +
            "   #{page} " +
            ")")
    void insertPageLog(PageLog log);
  
}
