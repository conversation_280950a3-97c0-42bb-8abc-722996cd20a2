package cn.slashsoft.clinique.service.mini.impl;

import cn.slashsoft.clinique.dao.mini.TTestimonyFragmentTypeDao;
import cn.slashsoft.clinique.domain.mini.TTestimonyFragmentType;
import cn.slashsoft.clinique.service.mini.TTestimonyFragmentTypeService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
* <p>
    * 记录获得碎片信息
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
@Service
public class TTestimonyFragmentTypeServiceImpl implements TTestimonyFragmentTypeService {

    @Resource
    private TTestimonyFragmentTypeDao tTestimonyFragmentTypeDao;

    @Override
    public void insertTTestimonyFragmentType(TTestimonyFragmentType tTestimonyFragmentType) {
        tTestimonyFragmentTypeDao.insertTTestimonyFragmentType(tTestimonyFragmentType);
    }

    @Override
    public void updateTTestimonyFragmentType(TTestimonyFragmentType tTestimonyFragmentType) {
        tTestimonyFragmentTypeDao.updateTTestimonyFragmentType(tTestimonyFragmentType);
    }

    @Override
    public TTestimonyFragmentType getTTestimonyFragmentType(Long id) {
        return tTestimonyFragmentTypeDao.getTTestimonyFragmentType(id);
    }
}
