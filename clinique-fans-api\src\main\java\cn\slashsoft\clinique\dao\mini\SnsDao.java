package cn.slashsoft.clinique.dao.mini;

import cn.slashsoft.clinique.domain.mini.Sns;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * 短信
 * <AUTHOR>
 */
@Repository
@Mapper
public interface SnsDao {

    /**
     * 写入短信发送记录
     * @param sns 短信发送记录
     */
    @Insert("INSERT INTO `sns`(" +
            "   `sns_type_id`, " +
            "   `customer_id`, " +
            "   `phone_number`, " +
            "   `message`, " +
            "   `param`, " +
            "   `response_code`" +
            ") " +
            "VALUES (" +
            "   #{snsTypeId}," +
            "   #{customerId}," +
            "   #{phoneNumber}," +
            "   #{message}," +
            "   #{param}," +
            "   #{responseCode}" +
            ")")
    void insertSns(Sns sns);

}
