package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignChallenge72;
import cn.slashsoft.clinique.domain.campaign.CampaignChallenge72CityConfig;
import cn.slashsoft.clinique.domain.campaign.CampaignViewLog;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.campaign.CampaignChallenge72Service;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.campaign.CampaignChallenge72Vo;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * 挑战72小时
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class CampaignChallenge72Controller {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final CampaignChallenge72Service campaignChallenge72Service;
    private final StringRedisTemplate stringRedisTemplate;

    public CampaignChallenge72Controller(CampaignChallenge72Service campaignChallenge72Service, StringRedisTemplate stringRedisTemplate) {
        this.campaignChallenge72Service = campaignChallenge72Service;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 配置活动信息
     *
     * @param verification 验证
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 处理结果
     * https://clinique.stormcrm.com/api/campaign-config/c520/ON/20-15-04-32-29/2051/2020-04-28-00-00-00/2020-05-05-23-59-59
     */
    @GetMapping("/campaign-config/challenge72/{verification}/{startTime}/{endTime}")
    public String setConfig(
            @PathVariable("verification") String verification,
            @PathVariable("startTime") String startTime,
            @PathVariable("endTime") String endTime
    ) {
        if (DateUtil.parseVerification(new Date()).equals(verification)) {
            stringRedisTemplate.opsForValue().set("campaignChallenge72StartTime", DateUtil.parseString(DateUtil.valueOf(startTime, "yyyy-MM-dd-HH-mm-ss")));
            stringRedisTemplate.opsForValue().set("campaignChallenge72EndTime", DateUtil.parseString(DateUtil.valueOf(endTime, "yyyy-MM-dd-HH-mm-ss")));
            return "success";
        }
        return "fail";
    }


    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-challenge72/get-data/{source}")
    public String getData(
            @PathVariable("source") String source
    ) {

        CampaignChallenge72Vo campaignChallenge72Vo = new CampaignChallenge72Vo();

        campaignChallenge72Vo.setStartTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignChallenge72StartTime")));
        campaignChallenge72Vo.setEndTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignChallenge72EndTime")));

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        // 获取缓存中的顾客编号
        String openid = (String) request.getAttribute("miniOpenid");

        CampaignChallenge72 campaignChallenge72 = campaignChallenge72Service.getCampaignChallenge72(customerId, source);

        campaignChallenge72Vo.setFirstRound(campaignChallenge72.getFirstRound());
        campaignChallenge72Vo.setSecondRound(campaignChallenge72.getSecondRound());
        campaignChallenge72Vo.setThirdRound(campaignChallenge72.getThirdRound());
        campaignChallenge72Vo.setResult(campaignChallenge72.getResult());
        campaignChallenge72Vo.setResultExperience(campaignChallenge72.getResultExperience());
        campaignChallenge72Vo.setResultShare(campaignChallenge72.getResultShare());

        campaignChallenge72Vo.setInviteDone(campaignChallenge72.getInviteDone());
        campaignChallenge72Vo.setAvatarUrl(campaignChallenge72Service.getAvatarUrl(customerId));
        campaignChallenge72Vo.setInviteeAvatarUrl(campaignChallenge72Service.getInviteeAvatarUrl(openid));

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignChallenge72Vo);

    }

    /**
     * 设置第一轮完成
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-challenge72/set-first-round")
    public String setFirstRound() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        campaignChallenge72Service.setCampaignChallenge72FirstRound(customerId);
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 设置第二轮完成
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-challenge72/set-second-round")
    public String setSecondRound() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        campaignChallenge72Service.setCampaignChallenge72SecondRound(customerId);
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 设置第三轮完成
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-challenge72/set-third-round")
    public String setThirdRound() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        campaignChallenge72Service.setCampaignChallenge72ThirdRound(customerId);
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 摇一摇
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-challenge72/get-city")
    public String getCity() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        CampaignChallenge72CityConfig campaignChallenge72CityConfig = campaignChallenge72Service.getCampaignChallenge72City(customerId);
        if(null == campaignChallenge72CityConfig){
            return ResultUtil.customer(ResultEnum.FAILED);
        }
        return ResultUtil.customer(ResultEnum.SUCCESS, campaignChallenge72CityConfig);
    }

    /**
     * 选择心得大师
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-challenge72/set-result-experience")
    public String setResultExperience(){
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        campaignChallenge72Service.setResultExperience(customerId);
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 选择心得大师
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-challenge72/set-result-share")
    public String setResultShare(){
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        campaignChallenge72Service.setResultShare(customerId);
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 写入访问开始日志
     *
     * @param source 来源
     * @return 成功
     */
    @PostMapping("/campaign-challenge72/start-view-log/{source}/{page}")
    public String setStartViewLog(
            @PathVariable("source") String source,
            @PathVariable("page") String page
    ) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        CampaignViewLog campaignViewLog = new CampaignViewLog();
        campaignViewLog.setCustomerId(customerId);
        campaignViewLog.setSource(source);
        campaignViewLog.setPage(page);

        long id = campaignChallenge72Service.insertCampaignChallenge72ViewLog(campaignViewLog);

        return ResultUtil.customer(ResultEnum.SUCCESS, id);

    }

    /**
     * 写入访问结束日志
     *
     * @param id 来源
     * @return 成功
     */
    @PostMapping("/campaign-challenge72/end-view-log/{id}")
    public String setEndViewLog(
            @PathVariable("id") long id
    ) {

        campaignChallenge72Service.setCampaignChallenge72ViewLog(id);
        return ResultUtil.customer(ResultEnum.SUCCESS);

    }


}
