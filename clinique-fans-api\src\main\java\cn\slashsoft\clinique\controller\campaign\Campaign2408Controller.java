package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignH5;
import cn.slashsoft.clinique.domain.campaign.lottery2408.CampaignTrackingDTO;
import cn.slashsoft.clinique.domain.campaign.lottery2408.CampaignUserLotteryAwardLogs;
import cn.slashsoft.clinique.domain.campaign.lottery2408.IndexVo;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.campaign.Campaign2408Service;
import cn.slashsoft.clinique.service.campaign.CampaignH5Service;
import cn.slashsoft.clinique.util.ResultUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

/**
 * campaign H5
 *
 * <AUTHOR>
 * 
 */
@RestController
@RequestMapping("/api/campaign/2408")
public class Campaign2408Controller {

    @Resource
    private CampaignH5Service campaignH5Service;
    @Resource
    private Campaign2408Service campaign2408Service;
    @Resource
    private HttpServletRequest request;

	//引导页
    @GetMapping("/get/info/{campaignFlag}")
    public String index(
        @PathVariable("campaignFlag") String campaignFlag
    ){
        long customerId = (long) request.getAttribute("customerId");
        CampaignH5 camp = campaignH5Service.getCampaign(campaignFlag);
        //获取抽奖奖品
        CampaignUserLotteryAwardLogs info = campaign2408Service.getUserLotteryInfo(customerId, campaignFlag);
        IndexVo res = new IndexVo();
        res.setCamp(camp);
        res.setInfo(info);
        return ResultUtil.customer(ResultEnum.SUCCESS, res);
	}

    @GetMapping("/get/award/{campaignFlag}")
    public String getAward(
        @PathVariable("campaignFlag") String campaignFlag
    ){
        //抽奖
        long customerId = (long) request.getAttribute("customerId");
        return campaign2408Service.getAward(customerId,campaignFlag);
    }

    @GetMapping("/get/ranking/{campaignFlag}/{rankType}")
    public String ranking(
        @PathVariable("campaignFlag") String campaignFlag,
        @PathVariable("rankType") String rankType
    ){
        CampaignH5 camp = campaignH5Service.getCampaign(campaignFlag);
        long customerId = (long) request.getAttribute("customerId");
        HashMap res =  campaign2408Service.getActivityRanking(customerId,camp,rankType);
        return ResultUtil.customer(ResultEnum.SUCCESS, res);
    }

    @PostMapping("/set/tracking")
    public String setTracking(
        @RequestBody CampaignTrackingDTO param
    ){
        Integer res =  campaign2408Service.setTracking(param);
        if (res <= 0){
            ResultUtil.customer(ResultEnum.FAILED, "请求失败");
        }
        return ResultUtil.customer(ResultEnum.SUCCESS, res);
    }

}
