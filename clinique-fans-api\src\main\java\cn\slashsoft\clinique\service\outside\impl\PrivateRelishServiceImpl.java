package cn.slashsoft.clinique.service.outside.impl;

import java.util.Collections;
import java.util.List;
import java.util.logging.Logger;

import org.springframework.stereotype.Service;

import cn.slashsoft.clinique.dao.official.CustomerServiceDao;
import cn.slashsoft.clinique.dao.official.PrivateRelishlDao;
import cn.slashsoft.clinique.domain.official.CustomerServiceStaff;
import cn.slashsoft.clinique.domain.official.PrivateRelish;
import cn.slashsoft.clinique.domain.official.PrivateRelishCustomer;
import cn.slashsoft.clinique.service.outside.PrivateRelishService;

/**
 * 外部接口
 *
 * <AUTHOR>
 */
@Service
public class PrivateRelishServiceImpl implements PrivateRelishService {
    private final Logger logger = Logger.getLogger(PrivateRelishServiceImpl.class.getName());

    private final PrivateRelishlDao prDao;
    private final CustomerServiceDao csDao;

    public PrivateRelishServiceImpl(
    		PrivateRelishlDao prDao,
    		CustomerServiceDao csDao) {
        this.prDao = prDao;
        this.csDao = csDao;
    }


	@Override
	public CustomerServiceStaff addPrivateRelish(String phone , String storeCode) {
		PrivateRelish pr = this.prDao.getByPhone(phone);
		if(pr == null) {
			pr = new PrivateRelish();
			pr.setPhoneNumber(phone);
			pr.setStoreCode(storeCode);
			
			List<CustomerServiceStaff>  all = this.csDao.getByStoreCode(storeCode);
			if(all.size() > 0) {
				Collections.shuffle(all);
				CustomerServiceStaff staff = all.get(0);
				pr.setStaffCode(staff.getTitle());
				this.prDao.addPrivateRelish(pr);
				return staff;
			}
			return null;
		}else {
			return this.csDao.getByCode(pr.getStaffCode());
		}
	}

	@Override
	public boolean  canSee(String officialOpenId) {
		PrivateRelishCustomer pr = this.prDao.getByOpenId(officialOpenId);
		return pr == null?true:false;
	}


	@Override
	public String joined(String phone) {
		PrivateRelish pr = this.prDao.getByPhone(phone);
		if(pr == null) {
			return "no";
		}
		return pr.getStaffCode();
	}


	@Override
	public boolean koccanSee(String customerId) {
		Integer all = this.prDao.getKocByCustomerId(customerId);
		return all >0?true:false;
	}


}
