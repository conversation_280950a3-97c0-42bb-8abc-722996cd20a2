package cn.slashsoft.clinique.domain.campaign.ugc;

import lombok.Data;

import java.util.Date;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Data
public class KocDetail {

    private Long id;
    private Long campaignId;
    private Long customerId;

    private Boolean follow;
    private String followSource;
    private Date followFirstTime;
    private Date followLastTime;
    private Date followCancelTime;
    private Boolean bind;
    private Date bindTime;
    
    private String title;

    private Boolean alert;
    private Short status;
    private Integer imageCount;
    private Date uploadTime;
    private Date examineTime;

}
