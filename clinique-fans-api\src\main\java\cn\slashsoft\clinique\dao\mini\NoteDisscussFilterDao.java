package cn.slashsoft.clinique.dao.mini;


import java.util.ArrayList;
import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.mini.CIndexBanner;
import cn.slashsoft.clinique.domain.mini.Note;
import cn.slashsoft.clinique.domain.mini.NoteDiscuss;
import cn.slashsoft.clinique.domain.mini.NoteDiscussMy;
import cn.slashsoft.clinique.domain.mini.NoteLikeLog;
import cn.slashsoft.clinique.domain.mini.NotePhoto;
import cn.slashsoft.clinique.domain.mini.NotePhotoTag;
import cn.slashsoft.clinique.domain.mini.NoteReadLog;
import cn.slashsoft.clinique.domain.mini.NoteShareLog;
import cn.slashsoft.clinique.domain.mini.NoteTag;
import cn.slashsoft.clinique.domain.mini.NoteTagAddressLog;
import cn.slashsoft.clinique.domain.mini.NoteTagSearchLog;

 /* 笔记
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface NoteDisscussFilterDao {
   
    /**
	 *
	 */
	 @Select("SELECT " +
	            "   `keyword` " +
	            "FROM " +
	            "   `note_discuss_filters` ")
	 List<String> getFilters();
    /**
	 *
	 */
	 @Select("SELECT " +
	            "   `keyword` " +
	            "FROM " +
	            "   `note_discuss_full_filters` ")
	 List<String> getFullFilters();
	   
}
