package cn.slashsoft.clinique.service.outside.impl;

import cn.slashsoft.clinique.service.outside.AliyunOssService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.RandomUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import lombok.extern.java.Log;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.Date;

/**
 * 阿里云OSS服务
 *
 * <AUTHOR>
 */
@Log
@Service
public class AliyunOssServiceImpl implements AliyunOssService {

    @Value("${wechat.oss.url}")
    private String urlOss;

    @Value("${wechat.oss.end-point}")
    private String endpoint;

    @Value("${wechat.oss.access-key-id}")
    private String accessKeyId;

    @Value("${wechat.oss.access-key-secret}")
    private String accessKeySecret;

    @Value("${wechat.oss.bucket}")
    private String bucket;

    /**
     * 上传
     *
     * @param inputStream 输入流
     * @param path        路径
     * @return OSS文件地址
     */
    @Override
    public String uploadImage(InputStream inputStream, String path) {

        try {

            Date now = new Date();
            String fileName = path + DateUtil.parseString(now, "yyyyMMdd") + "/" + DateUtil.parseString(now, "HHmmssSSSS") + "-" + RandomUtil.getNumber(4) + ".jpg";

            OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            ossClient.putObject(bucket, fileName, inputStream);

            return urlOss + "/" + fileName;

        } catch (Exception e) {
            log.info("上传到阿里云OSS出错：" + e.getMessage());
        }

        return null;
    }

}
