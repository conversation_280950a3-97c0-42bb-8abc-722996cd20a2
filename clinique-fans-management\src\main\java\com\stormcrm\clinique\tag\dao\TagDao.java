package com.stormcrm.clinique.tag.dao;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import com.stormcrm.clinique.domain.tag.Tag;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
public interface TagDao {

    @Insert("INSERT INTO customer_label ("
    		+ "`unionid`,"
    		+ "`category_id`,"
    		+ "`label_id`,"
    		+ "`status`,"
    		+ "`remark`,"
    		+ "`create_time`,"
    		+ "`update_time`"
    		+ ") VALUES ("
    		+ "#{unionid},"
    		+ "#{categoryId}`,"
    		+ "#{labelId},"
    		+ "#{status},"
    		+ "#{remark},"
    		+ "now(),"
    		+ "now()"
    		+ ") ")
	int addLabel(Tag tag);

    @Update("UPDATE customer_label  SET "
    		+ "`label_id` = #{labelId},"
    		+ "`status` = #{status},"
    		+ "`remark` = #{remark},"
    		+ "`update_time`= now()"
    		
    		+ " WHERE id = #{id} ")
	int updateLabel(Tag tag);
    
    @Select("select count(id) from customer_label where unionid=#{unoinid} and category_id=#{categoryId}")
    int checkLabel(Tag tag);
    

    @Select("select * from customer_label where unionid=#{unoinid} and category_id=#{categoryId} limit 1")
    Tag getLabel(String unionid, int categoryId);
}
