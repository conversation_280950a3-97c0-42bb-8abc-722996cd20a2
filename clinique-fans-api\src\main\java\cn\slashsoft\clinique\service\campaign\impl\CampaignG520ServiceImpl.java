package cn.slashsoft.clinique.service.campaign.impl;

import cn.slashsoft.clinique.dao.mini.WechatDao;
import cn.slashsoft.clinique.dao.campaign.CampaignG520Dao;
import cn.slashsoft.clinique.domain.mini.Follow;
import cn.slashsoft.clinique.domain.campaign.CampaignG520;
import cn.slashsoft.clinique.domain.campaign.CampaignViewLog;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.service.mini.SmotService;
import cn.slashsoft.clinique.service.campaign.CampaignG520Service;
import cn.slashsoft.clinique.util.StringUtil;
import org.springframework.stereotype.Service;

/**
 * 520活动
 *
 * <AUTHOR>
 */
@Service
public class CampaignG520ServiceImpl implements CampaignG520Service {

    private final CampaignG520Dao campaignG520Dao;
    private final WechatDao wechatDao;
    private final SmotService smotService;
    private final OutsideService outsideService;

    public CampaignG520ServiceImpl(CampaignG520Dao campaignG520Dao, SmotService smotService, WechatDao wechatDao, OutsideService outsideService) {
        this.campaignG520Dao = campaignG520Dao;
        this.smotService = smotService;
        this.wechatDao = wechatDao;
        this.outsideService = outsideService;
    }

    /**
     * 获取520活动信息
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @param openid     小程序唯一编号
     * @return 520活动信息
     */
    @Override
    public CampaignG520 getCampaignG520(String openid, long customerId, String source) {

        CampaignG520 campaignG520 = campaignG520Dao.getCampaignG520(customerId);
        if (null == campaignG520) {
            campaignG520 = new CampaignG520();
            campaignG520.setCustomerId(customerId);
            campaignG520.setSource(source);

            String officialOpenid = wechatDao.getOfficialOpenidByCustomerId(customerId);
            if (!StringUtil.isNullOrEmpty(officialOpenid)){
                // 获取是否关注
                Follow follow = outsideService.isFollow(officialOpenid);
                // 未获取到是否关注，即为AccessToken过期
                if (null != follow) {
                    campaignG520.setFollow(follow.getFollow());
                    campaignG520.setFollowSource(follow.getFollowSource());
                    campaignG520.setFollowFirstTime(follow.getFollowFirstTime());
                    campaignG520.setFollowLastTime(follow.getFollowLastTime());
                    campaignG520.setFollowCancelTime(follow.getFollowCancelTime());
                    campaignG520.setBind(follow.getBind());
                    campaignG520.setBindTime(follow.getBindTime());
                }
            }

            // 写入活动信息
            campaignG520Dao.insertCampaignG520(campaignG520);

            // 重新读取活动信息
            campaignG520 = campaignG520Dao.getCampaignG520(customerId);

            // 发送订阅消息
            smotService.receiveCampaignG520(openid, campaignG520.getNickName());
        }

        return campaignG520;
    }

    /**
     * 设置定位信息
     *
     * @param customerId        顾客编号
     * @param locationAuthorize 是否授权
     */
    @Override
    public void setCampaignG520LocationAuthorize(long customerId, boolean locationAuthorize) {
        campaignG520Dao.setCampaignG520LocationAuthorize(customerId, locationAuthorize);
    }

    /**
     * 写入访问开始日志
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @param page       页面
     * @return 访问日志编号
     */
    @Override
    public long insertCampaignViewLog(long customerId, String source, String page) {
        CampaignViewLog campaignViewLog = new CampaignViewLog();
        campaignViewLog.setCustomerId(customerId);
        campaignViewLog.setSource(source);
        campaignViewLog.setPage(page);
        campaignG520Dao.insertCampaignViewLog(campaignViewLog);
        return campaignViewLog.getId();
    }

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Override
    public void setCampaignViewLog(long id) {
        campaignG520Dao.setCampaignViewLog(id);
    }

}
