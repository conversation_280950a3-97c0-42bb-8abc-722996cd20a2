package cn.slashsoft.clinique.dao.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignG520;
import cn.slashsoft.clinique.domain.campaign.CampaignViewLog;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

/**
 * 520活动
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignG520Dao {

    /**
     * 获取520活动信息
     *
     * @param customerId 顾客编号
     * @return 520活动信息
     */
    @Select("SELECT " +
            "   `k`.`id`," +
            "    decryptPhone(`c`.`phone_number`) phone_number," +
            "   `w`.`avatar_url`," +
            "   `w`.`nick_name`," +
            "   `k`.`location_authorize`," +
            "   `k`.`create_time` " +
            "FROM " +
            "   `campaign_g520` `k` " +
            "       INNER JOIN " +
            "   `customer` `c` " +
            "       ON `k`.`customer_id`=`c`.`id` " +
            "       INNER JOIN " +
            "   `wechat` `w`" +
            "       ON `k`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "   `k`.`customer_id`=#{customerId} " +
            "LIMIT 1")
    CampaignG520 getCampaignG520(long customerId);

    /**
     * 写入520活动信息
     *
     * @param campaignG520 520活动信息
     */
    @Insert("INSERT INTO `campaign_g520`(" +
            "   `customer_id`," +
            "   `follow`," +
            "   `follow_source`," +
            "   `follow_first_time`," +
            "   `follow_last_time`," +
            "   `follow_cancel_time`," +
            "   `bind`," +
            "   `bind_time`," +
            "   `source`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{follow}," +
            "   #{followSource}," +
            "   #{followFirstTime}," +
            "   #{followLastTime}," +
            "   #{followCancelTime}," +
            "   #{bind}," +
            "   #{bindTime}," +
            "   #{source}" +
            ")")
    void insertCampaignG520(CampaignG520 campaignG520);

    /**
     * 设置定位信息
     *
     * @param customerId        顾客编号
     * @param locationAuthorize 是否授权
     */
    @Update("UPDATE " +
            "   `campaign_g520` " +
            "SET " +
            "   `location_authorize`=#{locationAuthorize} " +
            "WHERE " +
            "   `customer_id`=#{customerId}")
     void setCampaignG520LocationAuthorize(@Param("customerId") long customerId, @Param("locationAuthorize") boolean locationAuthorize);

    /**
     * 写入访问日志
     *
     * @param campaignViewLog 日志
     */
    @Insert("INSERT INTO `campaign_g520_view_log`(" +
            "   `customer_id`," +
            "   `source`," +
            "   `page`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{source}," +
            "   #{page}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertCampaignViewLog(CampaignViewLog campaignViewLog);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Update("UPDATE " +
            "   `campaign_g520_view_log` " +
            "SET " +
            "   `end_time`=NOW()," +
            "   `diff_second`=TimeStampDiff(SECOND,`start_time`,NOW()) " +
            "WHERE " +
            "   `id`=#{id}")
    void setCampaignViewLog(long id);

}
