package cn.slashsoft.clinique.service.campaign.impl;

import cn.slashsoft.clinique.dao.campaign.CampaignEbciTmallClubDao;
import cn.slashsoft.clinique.domain.campaign.*;
import cn.slashsoft.clinique.service.campaign.CampaignEbciTmallClubService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

/**
 * 302美白镭射瓶
 *
 * <AUTHOR>
 */
@Service
public class CampaignEbciTmallClubServiceImpl implements CampaignEbciTmallClubService {

    private final CampaignEbciTmallClubDao campaignEbciTmallClubDao;

    public CampaignEbciTmallClubServiceImpl(CampaignEbciTmallClubDao campaignEbciTmallClubDao) {
        this.campaignEbciTmallClubDao = campaignEbciTmallClubDao;
    }

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    @Override
    public CampaignEbciDetail getDetail(String phoneNumber) {
        return campaignEbciTmallClubDao.getDetail(phoneNumber);
    }

    /**
     * 申领
     *
     * @param campaignEbciDetail 资料
     * @return 0:成功，1：手机号码已经领过了，2：库存不足, 3： 其他错误
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int submit(CampaignEbciDetail campaignEbciDetail) {
        // 扣库存
    	if(campaignEbciTmallClubDao.getDetail(campaignEbciDetail.getPhoneNumber()) != null) {
    		return 1;
    	}

        // 写入记录
        try {
            campaignEbciTmallClubDao.insetDetail(campaignEbciDetail);
            return 0;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return 3;
        }

    }

}
