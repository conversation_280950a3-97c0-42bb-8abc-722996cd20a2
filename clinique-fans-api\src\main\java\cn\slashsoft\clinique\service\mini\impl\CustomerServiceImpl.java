package cn.slashsoft.clinique.service.mini.impl;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import cn.slashsoft.clinique.domain.mini.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.slashsoft.clinique.dao.mini.CustomerDao;
import cn.slashsoft.clinique.dao.mini.Day3TaskDao;
import cn.slashsoft.clinique.dao.mini.PointDao;
import cn.slashsoft.clinique.enums.PointForeignEnum;
import cn.slashsoft.clinique.enums.PointTypeEnum;
import cn.slashsoft.clinique.service.mini.CustomerService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.HttpUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.Result;
import cn.slashsoft.clinique.vo.mini.Info;

/**
 * 顾客相关的服务
 *
 * <AUTHOR>
 */
@Service
public class CustomerServiceImpl implements CustomerService {

    @Value("${member.get-customer-by-openid}")
    private String getCustomerByOpenid;
    
    private final CustomerDao customerDao;
    private final PointDao pointDao;
    
    @Resource
    private Day3TaskDao day3taskDao;

    public CustomerServiceImpl(CustomerDao customerDao, PointDao pointDao) {
        this.customerDao = customerDao;
        this.pointDao = pointDao;
    }

    /**
     * 获取登陆时需要的信息
     * 登录
     *
     * @param customerId 顾客编号
     * @return 顾客信息
     */
    @Override
    public Customer getCustomerById(long customerId) {
        return customerDao.getCustomerById(customerId);
    }

    /**
     * 获取顾客注册的手机号码
     *
     * @param customerId 顾客编号
     * @return 顾客信息
     */
    @Override
    public String getCustomerPhoneNumberById(long customerId) {
        return customerDao.getCustomerPhoneNumberById(customerId);
    }

    /**
     * 更新资料
     *
     * @param info 资料
     * @return 处理结果
     */
    @Override
    public Result updateCustomer(Info info) {

        // 读取用户信息
        Customer customer = customerDao.getCustomerForUpdate(info.getCustomerId());
//        // 更新用户信息
//        if (null == customer.getBirthday()) {
//            // 未填写生日的，不累加生日更新次数
//            customerDao.updateCustomerWithBirthday(info);
//        } else if (0 == customer.getBirthdayUpdateTimes() && !DateUtil.isSameDay(info.getBirthday(), customer.getBirthday())) {
//            // 已填过生日的，并且生日更新次数为零的，可再次更新生日，并累计1次
//            customerDao.updateCustomerWithBirthdayAndTimes(info);
//        } else {
//            // 已填过生日，更新次数大于零的，不更新生日
//            customerDao.updateCustomerWithoutBirthday(info);
//        }
        // 已填过生日，更新次数大于零的，不更新生日
        customerDao.updateCustomerWithoutBirthday(info);
        // 更新头像昵称
        customerDao.updateWechatNickNameAndAvatarUrl(info);

        // 是否首次更新
        if (!customer.getUpdateReward() && 0 < customerDao.updateCustomerUpdateRewardById(info.getCustomerId())) {
            // 发放积分
            Date now = new Date();
            PointTransaction pointTransaction = new PointTransaction();
            pointTransaction.setCustomerId(info.getCustomerId());
            pointTransaction.setPointTypeId(PointTypeEnum.INFO.getId());
            pointTransaction.setPoints(PointTypeEnum.INFO.getPoints());
            pointTransaction.setRemainingPoints(PointTypeEnum.INFO.getPoints());
            pointTransaction.setStartTime(now);
            pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
            pointTransaction.setForeignId(PointForeignEnum.INFO.getId());
            pointTransaction.setForeignMasterId(0L);
            pointTransaction.setForeignDetailId(0L);

            pointTransaction.setRemark(PointTypeEnum.INFO.getName());
            pointDao.insertPointTransaction(pointTransaction);
        }

        // 保存授权记录
        if (null != info.getAgreeBrandCommunicate() && null != info.getAgreeCorpCommunicate()) {
            CustomerClause customerClause = customerDao.getLastCustomerClause(info.getUnionid());
            if (null == customerClause) {
                customerDao.saveCustomerClause(info.getUnionid(), info.getAgreeBrandCommunicate(), info.getAgreeCorpCommunicate(), "1.2");
            } else {
                if (!Objects.equals(customerClause.getAgreeBrandCommunicate(), info.getAgreeBrandCommunicate()) || !Objects.equals(customerClause.getAgreeCorpCommunicate(), info.getAgreeCorpCommunicate())) {
                    customerDao.saveCustomerClause(info.getUnionid(), info.getAgreeBrandCommunicate(), info.getAgreeCorpCommunicate(), "1.2");
                }
            }
        }

        return ResultUtil.customer(1, "成功");
    }

    /**
     * 获取首选地址
     *
     * @param customerId 顾客编号
     * @return 地址
     */
    @Override
    public CustomerAddress getPreferredAddressByCustomerId(long customerId) {
        return customerDao.getPreferredAddressByCustomerId(customerId);
    }

    /**
     * 获取邮寄地址
     *
     * @param customerId 顾客编号
     * @return 邮寄地址
     */
    @Override
    public List<CustomerAddress> getCustomerAddressByCustomerId(long customerId) {
        return customerDao.getCustomerAddressByCustomerId(customerId);
    }

    /**
     * 获取邮寄地址
     *
     * @param customerId        顾客编号
     * @param customerAddressId 邮寄地址编号
     * @return 邮寄地址
     */
    @Override
    public CustomerAddress getCustomerAddressById(long customerId, long customerAddressId) {
        return customerDao.getCustomerAddressById(customerId, customerAddressId);
    }

    /**
     * 写入顾客地址
     *
     * @param customerAddress 顾客地址
     */
    @Override
    public void insertAddress(CustomerAddress customerAddress) {

        if (customerAddress.getPreferred()) {
            customerDao.updatePreferredByCustomerId(customerAddress.getCustomerId());
        }

        customerDao.insertCustomerAddress(customerAddress);

    }

    /**
     * 编辑顾客地址
     *
     * @param customerAddress 顾客地址
     */
    @Override
    public void updateAddress(CustomerAddress customerAddress) {

        if (customerAddress.getPreferred()) {
            customerDao.updatePreferredByCustomerId(customerAddress.getCustomerId());
        }

        customerDao.updateCustomerAddress(customerAddress);

    }

    /**
     * 删除顾客地址
     *
     * @param customerAddressId 顾客地址编号
     */
    @Override
    public void delAddress(long customerAddressId) {

        customerDao.updateCustomerAddressStatus(customerAddressId);

    }

    /**
     * 写入手机号码
     *
     * @param unionid     开放平台唯一编号
     * @param phoneNumber 手机号码
     */
    @Override
    public void setPhoneNumberByUnionid(String unionid, String phoneNumber) {
        try {
            customerDao.setPhoneNumberByUnionid(unionid, phoneNumber);
        }
        catch (Exception ignored){}
    }

    /**
     * 写入手机号码
     *
     * @param openid      公众号唯一编号
     * @param phoneNumber 手机号码
     */
    @Override
    public void setPhoneNumberByOpenid(String openid, String phoneNumber) {
        try {
            customerDao.setPhoneNumberByOfficialOpenid(openid, phoneNumber);
        }
        catch (Exception ignored){}
    }

	@Override
	public Customer getCustomerByOpenId(String openid) {
        return customerDao.getCustomerByOfficialId(openid);
	}

	@Override
	public Customer getCustomerByOpenIdFromMember(String openid) {
		
		try {
			String data = HttpUtil.get(this.getCustomerByOpenid + openid);
            JSONObject result = (JSONObject) JSON.parse(data);

            if (result.containsKey("data") && !result.getString("data").equals("null")) {
                JSONObject member = result.getJSONObject("data");
 	           System.out.println(member.toJSONString());
                Customer customer = new Customer();
                customer.setName(member.getString("name"));
                customer.setPhoneNumber(member.getString("phoneNumber"));
                
                return customer;
            }

        } catch (Exception ignored) {
        	System.out.println(ignored.getStackTrace());
        }

		return null;
	}

	@Override
	public Customer getCustomerByUnionIdFromMember(String unionid) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public int updateCustomerActivePoints(int points, Long customerid) {

		return customerDao.updateCustomerActivePoints(points, customerid);
	}

	@Override
	public boolean days3signCheck(String unionid, Long customerId) {
		Customer cus = customerDao.getDay7CustomerById(customerId);
		if(cus != null) {
			if(day3taskDao.enabledByPhone(cus.getPhoneNumber().substring(0, 7)) > 0) {

				return true;
			}
		}
		return false;
	}
}
