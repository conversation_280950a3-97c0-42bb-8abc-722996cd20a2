package cn.slashsoft.clinique.dao.mini;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.mini.TTestimonyAmount;

@Repository
@Mapper
public interface TTestimonyAmountDao {

    @Insert("INSERT INTO `t_testimony_amount`(" +
        "   `id`, " +
        "   `customer_id`, " +
        "   `like_count`, " +
        "   `create_time`, " +
        "   `update_time` " +
        ") " +
        "VALUES (" +
        "   #{id}, " +
        "   #{customerId}, " +
        "   #{likeCount}, " +
        "   #{createTime}, " +
        "   #{updateTime} " +
        ")")
    void insertTTestimonyAmount(TTestimonyAmount tTestimonyAmount);

    @Update("UPDATE " +
        "   `t_testimony_amount` " +
        "SET " +
        "   `id` = #{id}, " +
        "   `customer_id` = #{customerId}, " +
        "   `like_count` = #{likeCount}, " +
        "   `create_time` = #{createTime}, " +
        "   `update_time` = #{updateTime} " +
        "WHERE " +
        "   `id`=#{id} ")
    void updateTTestimonyAmount(TTestimonyAmount tTestimonyAmount);

    /**
     * 获取点赞记录统计
     * @param id
     * @return
     */
    @Select("SELECT " +
        "   `id`, " +
        "   `customer_id`, " +
        "   `like_count`, " +
        "   `create_time`, " +
        "   `update_time` " +
        "FROM " +
        "   `t_testimony_amount` " +
        "WHERE " +
        "   `id`=#{id} " +
        "LIMIT 1 ")
    TTestimonyAmount getTTestimonyAmount(Long id);

    /**
     * 更加customerId 获取记录
     * @param customerId
     * @return
     */
    @Select("SELECT " +
            "   `id`, " +
            "   `customer_id`, " +
            "   `like_count`, " +
            "   `create_time`, " +
            "   `update_time` " +
            "FROM " +
            "   `t_testimony_amount` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1 ")
    TTestimonyAmount getTTestimonyAmountByCustomerId(Long customerId);

    /**
     * 获取点赞数量
     * @param customerId
     * @return
     */
    @Select("SELECT " +
            "   `like_count` " +
            "FROM " +
            "   `t_testimony_amount` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1 ")
    Integer getTTestimonyLikeCountByCustomerId(Long customerId);

    /**
     * 获取点赞排名
     * @param customerId
     * @return
     */
    @Select("select t.rank \n" +
            "from ( \n" +
            "\tselect customer_id,(@ranknum:=@ranknum+1) as rank  \n" +
            "\tfrom t_testimony_amount t, (select (@ranknum :=0) ) b \n" +
            "\torder by like_count desc )t \n" +
            "WHERE " +
            "   `customer_id`=#{customerId} ")
    Integer getTTestimonyLikeRankByCustomerId(Long customerId);

    /**
     * 更新点赞数量
     * @param customerId
     */
    @Update("UPDATE " +
            "   `t_testimony_amount` " +
            "SET " +
            "   `like_count` = `like_count` + 1, " +
            "   `update_time` = now()  " +
            "WHERE " +
            "   `customer_id`=#{customerId} ")
    void updateTTestimonyAmountByCustomerId(Long customerId);



}
