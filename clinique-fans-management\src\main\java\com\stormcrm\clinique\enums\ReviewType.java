package com.stormcrm.clinique.enums;
import lombok.Getter;

public enum ReviewType {

    NONE(0, "","未审核"),
    DENY(1, "","拒绝"),
    PASS(2, "NOTE_PUBLISH","审核通过"),
    BEST(3, "NOTE_FAVORITE","加精"),
    TOP(4, "","置顶")
    ;

    @Getter
    private int status;

    @Getter
    private String pointType;

    @Getter
    private String message;

    ReviewType(int status,String pointType, String message) {
        this.status = status;
        this.pointType = pointType;
        this.message = message;
    }
}
