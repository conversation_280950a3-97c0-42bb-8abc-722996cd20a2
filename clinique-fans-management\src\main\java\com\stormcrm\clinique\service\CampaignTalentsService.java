package com.stormcrm.clinique.service;

import com.stormcrm.clinique.domain.CampaignTalents;
import com.stormcrm.clinique.domain.CampaignTalentsImage;

import java.util.List;

/**
 * 新品体验官活动
 *
 * <AUTHOR>
 */
public interface CampaignTalentsService {

    /**
     * 查询所有活动带分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索
     * @param status        状态
     * @return 活动列表分页
     */
    List<CampaignTalents> getPage(
            int page,
            int perpage,
            String generalSearch,
            Short status
    );

    /**
     * 查询所有活动带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    int getPageCount(
            String generalSearch,
            Short status
    );

    /**
     * 获取美白达人官信息
     *
     * @param id 编号
     * @return 美白达人官信息
     */
    CampaignTalents getById(long id);

    /**
     * 获取图片
     *
     * @param id 顾客活动信息编号
     * @return 图片地址
     */
    List<CampaignTalentsImage> getImageById(long id);

    /**
     * 审核通过
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    int accept(long id);

    /**
     * 批量同意
     *
     * @param ids 自动编号
     * @return 影响的行数
     */
    int acceptBatch(String ids);

    /**
     * 拒绝
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    int reject(long id);

    /**
     * 批量拒绝
     *
     * @param ids 自动编号
     * @return 影响的行数
     */
    int rejectBatch(String ids);

}
