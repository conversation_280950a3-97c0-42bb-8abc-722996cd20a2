package com.stormcrm.clinique.controller;

import com.stormcrm.clinique.domain.VideoTagDefine;
import com.stormcrm.clinique.service.OutsideService;
import com.stormcrm.clinique.service.VideoTagService;

import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.util.VerifyUtil;
import com.stormcrm.clinique.vo.Result;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

import static com.alibaba.fastjson.JSONObject.*;

/**
 * 笔记
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("video/tag")
@Slf4j
public class VideoTagController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    public static final String VALID_MUST_HAVE_VAR = "这是必填字段";
    public static final String OSS_VIDEO_IMAGE_FOLDER_NAME = "video";

    private final VideoTagService videoTagService;
    private final OutsideService outsideService;

    public VideoTagController(
            VideoTagService videoTagService,
            OutsideService outsideService
    ) {
        this.videoTagService = videoTagService;
        this.outsideService = outsideService;
    }

    /**
     * 视频列表
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('VIDEO_TAG_INDEX')")
    @RequestMapping("index")
    public String index(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/video-tag/index";
    }

    /**
     * 查询所有-分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索关键字
     * @param type          状态
     * @return 列表
     */
    @PreAuthorize("hasAuthority('VIDEO_TAG_INDEX')")
    @PostMapping("get-page")
    @ResponseBody
    public String getPage(
            @RequestParam(value = "pagination[page]", required = false) Integer page,
            @RequestParam(value = "pagination[perpage]", required = false) Integer perpage,
            @RequestParam(value = "query[generalSearch]", required = false) String generalSearch,
            @RequestParam(value = "query[type]", required = false) Short type
    ) {

        if (null == page) {
            page = 1;
        }

        if (null == perpage) {
            perpage = 10;
        }

        List<VideoTagDefine> videoTagDefines = videoTagService.getPage(page, 500, generalSearch, type);
        int count = videoTagService.getPageCount(generalSearch, type);

        JSONObject mata = new JSONObject();
        mata.put("page", page);
        mata.put("pages", (count + perpage - 1) / perpage);
        mata.put("perpage", perpage);
        mata.put("total", count);
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("mata", mata);
        result.put("data", videoTagDefines);

        return toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }

    /**
     * 增加模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('VIDEO_TAG_ADD')")
    @RequestMapping("add")
    public String add(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/video-tag/add";
    }

    /**
     * 新增
     *
     * @param title 名称
     * @param type  产品  话题
     * @param image 图片
     * @param top   置顶
     * @return 结果
     */
    @PreAuthorize("hasAuthority('VIDEO_TAG_ADD')")
    @PostMapping(value = "add-submit", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String addSubmit(
            @RequestParam(value = "form-title", required = false) String title,
            @RequestParam(value = "form-image", required = false) MultipartFile image,
            @RequestParam(value = "form-type", required = false) Integer type,
            @RequestParam(value = "form-top", required = false) Integer top
    ) {

        // 验证
        if (!VerifyUtil.required(title)) {
            return ResultUtil.verifyFailToJson("form-title", VALID_MUST_HAVE_VAR);
        }

        // 验证
        if (null == image || image.isEmpty()) {
            return ResultUtil.verifyFailToJson("form-image", VALID_MUST_HAVE_VAR);
        }

        // 生成对象
        VideoTagDefine videoTagDefine = new VideoTagDefine();
        videoTagDefine.setTitle(title);
        videoTagDefine.setImage(outsideService.uploadImageOss(image, OSS_VIDEO_IMAGE_FOLDER_NAME));
        videoTagDefine.setTop(top);
        videoTagDefine.setType(type);
        // 传到Service服务中保存
        videoTagService.insert(videoTagDefine);
        Result result = ResultUtil.success("添加成功!");
        // 返回绍果给前端
        return toJSONString(result);
    }

    /**
     * 逻辑删除
     *
     * @param id 胸章的自动编号
     * @return 影响的行数
     */
    @PreAuthorize("hasAuthority('VIDEO_TAG_DEL')")
    @GetMapping("del/{id}")
    @ResponseBody
    public String del(@PathVariable("id") Long id) {

        if (0 == videoTagService.delete(id)) {
            return ResultUtil.failToJson();
        }

        return ResultUtil.successToJson();

    }

    /**
     * 编辑模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('VIDEO_TAG_EDIT')")
    @GetMapping("edit/{id}")
    public String edit(@PathVariable Long id, Model model) {
        model.addAttribute("staticDomain", staticDomain);
        // 读数据库
        VideoTagDefine videoTagDefine = videoTagService.get(id);
        if (null == videoTagDefine) {
            return "m/fans/common/empty";
        }

        model.addAttribute("id", id);
        model.addAttribute("videoTagDefine", videoTagDefine);

        return "m/fans/video-tag/edit";
    }

    /**
     * 编辑
     *
     * @param id    编号
     * @param title 名称
     * @return 结果
     */
    @PreAuthorize("hasAuthority('VIDEO_TAG_EDIT')")
    @PostMapping(value = "edit-submit/{id}", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String editSubmit(
            @PathVariable("id") Long id,
            @RequestParam(value = "form-title", required = false) String title,
            @RequestParam(value = "form-image", required = false) MultipartFile image,
            @RequestParam(value = "form-type", required = false) Integer type,
            @RequestParam(value = "form-top", required = false) Integer top
    ) {

        // 验证
        if (!VerifyUtil.required(title)) {
            return ResultUtil.verifyFailToJson("form-title", VALID_MUST_HAVE_VAR);
        }

        // 生成对象
        VideoTagDefine videoTagDefine = new VideoTagDefine();
        videoTagDefine.setId(id);
        videoTagDefine.setTitle(title);
        if (null != image && !image.isEmpty()) {
            videoTagDefine.setImage(outsideService.uploadImageOss(image, OSS_VIDEO_IMAGE_FOLDER_NAME));
        }
        videoTagDefine.setTop(top);
        videoTagDefine.setType(type);

        // 传到Service服务中保存
        videoTagService.update(videoTagDefine);
        Result result = ResultUtil.success("编辑成功!");
        // 返回绍果给前端
        return toJSONString(result);
    }

    /**
     * 查看图片上的全部的标签 或者 直接全查出来
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('VIDEO_EDIT')")
    @RequestMapping("all")
    @ResponseBody
    public String all() {
        ArrayList<VideoTagDefine> tags = videoTagService.getTagDefineList();

        JSONObject result = new JSONObject();
        result.put("data", tags);

        return toJSONStringWithDateFormat(
                result,
                "yyyy-MM-dd HH:mm:ss",
                SerializerFeature.WriteDateUseDateFormat
        );
    }
}
