package cn.slashsoft.clinique.api.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import cn.slashsoft.clinique.api.service.OpenApiService;
import cn.slashsoft.clinique.dao.api.OpenApiDao;
import cn.slashsoft.clinique.domain.api.CouponApiLog;
import cn.slashsoft.clinique.util.EncryptUtil;

/**
 * 卡券
 *
 * <AUTHOR>
 */
@Service
public class OpenApiServiceImpl implements OpenApiService {

	private HashMap<String,String> sourceSecreat ;
	
	@Resource
	private OpenApiDao openApiDao;
	
	@Override
	public void saveApiLog(CouponApiLog log) {
		openApiDao.addApiLog(log);
		
	}

	@Override
	public String checkAndGetSource(HashMap<String, String> params) {
		sourceSecreat = new HashMap<String,String> ();
		//商城小程序
		sourceSecreat.put("miniapp-store", "Dg#546Rrtre$45Gtuk8fT54jDrt653df45h%67$6GtFKM56y");
			
        if (params != null) {
    		String sign = params.get("sign");
        	params.remove("sign");   
	        StringBuilder content = new StringBuilder();
	        List<String> keys = new ArrayList<String>(params.keySet());
	        Collections.sort(keys);

	        for (int i = 0; i < keys.size(); i++) {
	            String key = keys.get(i);
	            String value = params.get(key);
	            content.append(i == 0 ? "" : "&").append(key).append("=").append(value);
	        }

	        for(String source : this.sourceSecreat.keySet()) {
	 	         System.out.println(source);
	 	         System.out.println("-----------------------"+content.toString());
	 	         System.out.println("-----------------------"+this.sourceSecreat.get(source)+content.toString()+this.sourceSecreat.get(source));
	        	 String sss = EncryptUtil.md5Encode(this.sourceSecreat.get(source)+content.toString()+this.sourceSecreat.get(source));
	 	         System.out.println("-----------------------"+this.sourceSecreat.get(source)+ sss);	 	       
	 	         System.out.println("-----------------------"+EncryptUtil.md5Encode(this.sourceSecreat.get(source) + sss));
	 	        if(EncryptUtil.md5Encode(this.sourceSecreat.get(source) + sss).equals(sign)){
	 	    		return source;	        	
	 	        }
	        }	       
	   
        }
        return "";
	}

}
