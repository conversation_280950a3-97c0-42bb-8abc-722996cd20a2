package cn.slashsoft.clinique.service.mini.impl;

import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.logging.Logger;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;

import org.springframework.stereotype.Service;

import cn.slashsoft.clinique.dao.mini.CustomerDao;
import cn.slashsoft.clinique.dao.mini.SmotDao;
import cn.slashsoft.clinique.dao.mini.SnsDao;
import cn.slashsoft.clinique.dao.mini.StoreDao;
import cn.slashsoft.clinique.domain.campaign.CampaignH5Detail;
import cn.slashsoft.clinique.domain.mini.BSmotTemplate;
import cn.slashsoft.clinique.domain.mini.Sns;
import cn.slashsoft.clinique.domain.mini.StoreAreaPlace;
import cn.slashsoft.clinique.service.mini.SnsService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.HttpUtil;
import cn.slashsoft.clinique.util.RandomUtil;
import cn.slashsoft.clinique.util.StringUtil;

/**
 * 验证码
 *
 * <AUTHOR>
 */
@Service
public class SnsServiceImpl implements SnsService {

    private final Logger logger = Logger.getLogger(SnsServiceImpl.class.getName());

    private final String url = "http://43.240.124.37:3308/sms/mt?command=MT_REQUEST&spid=CL00J1&sppassword=61423e&da=PHONE_NUMBER&dc=8&sm=MESSAGE";

    @Resource
    private HttpSession session;

    private final CustomerDao customerDao;
    private final StoreDao storeDao;
    private final SnsDao snsDao;
    
    @Resource
    private SmotDao smotDao;

    public SnsServiceImpl(CustomerDao customerDao, StoreDao storeDao, SnsDao snsDao) {
        this.customerDao = customerDao;
        this.storeDao = storeDao;
        this.snsDao = snsDao;
    }

    /**
     * 发送短信验证码
     *
     * @param phoneNumber 手机号码
     */
    @Override
    public int sendVerifyCode(String phoneNumber) {

        try{

            logger.info("发送验证码：生成随机数");

            String verifyCode = RandomUtil.getVerifyCode();

            logger.info("发送验证码：写入SESSION");

            session.setAttribute("verifyPhoneNumber", phoneNumber);
            session.setAttribute("verifyCode", verifyCode);
            session.setAttribute("verifyTime", DateUtil.parseString(DateUtil.addMinute(new Date(), 10)));

            logger.info("发送验证码：写入SESSION完成");

            // 短信内容
            String message = "您的验证码是：" + verifyCode + "，10分钟有效，如非本人操作请忽略";

            logger.info("发送验证码：调用接口");
            // 发送内容
            String sendUrl = url.replace("PHONE_NUMBER", phoneNumber).replace("MESSAGE", encodeHexStr(message.getBytes(StandardCharsets.UTF_16BE)));
            // 发送
            String response = HttpUtil.get(sendUrl);

            logger.info("发送验证码：调用验证码返回，" + response);

            // 返回值
            String code;
            // 返回异常
            if (StringUtil.isNullOrEmpty(response) || 3 > response.length()) {
                code = "999";
            } else {
                code = StringUtil.right(response, 3);
            }

            logger.info("发送验证码：保存发送记录");

            // 保存日志
            Sns sns = new Sns();
            sns.setSnsTypeId((short) 3);
            sns.setCustomerId(null);
            sns.setPhoneNumber(phoneNumber);
            sns.setMessage(message);
            sns.setParam(sendUrl);
            sns.setResponseCode(code);
            snsDao.insertSns(sns);

            logger.info("发送验证码：保存成功");

            if("000".equals(code)){
                return 0;
            }

        } catch (Exception ignored) {}

        return 1;
    }

    /**
     * 到店取货类订单兑换成功发送短信验证码
     *
     * @param customerId  顾客编号
     * @param areaPlaceId 门店编号
     */
    @Override
    public void exchangePickupSend(long customerId, Long areaPlaceId) {
        try {
            // 短信内容
            String message = "您已成功兑换C粉圈会员好礼，我们正快马加鞭将礼物送往您的城市，请您耐心等待。礼物将于15个工作日内到达STORE_AREA_NAME专柜，届时请凭到货通知短信至该柜台领取哦！回TD退订";
            // 获取手机号码
            String phoneNumber = customerDao.getCustomerPhoneNumberById(customerId);
            // 获取门店名称
            StoreAreaPlace storeAreaPlace = storeDao.getStoreAreaPlace(areaPlaceId);
            // 替换门店名称
            message = message.replace("STORE_AREA_NAME", storeAreaPlace.getName());
            // 发送内容
            String sendUrl = url.replace("PHONE_NUMBER", phoneNumber).replace("MESSAGE", encodeHexStr(message.getBytes(StandardCharsets.UTF_16BE)));
            // 发送
            String response = HttpUtil.get(sendUrl);
            // 返回值
            String code;
            // 返回异常
            if (StringUtil.isNullOrEmpty(response) || 3 > response.length()) {
                code = "999";
            } else {
                code = StringUtil.right(response, 3);
            }
            // 保存日志
            Sns sns = new Sns();
            sns.setSnsTypeId((short) 1);
            sns.setCustomerId(customerId);
            sns.setPhoneNumber(phoneNumber);
            sns.setMessage(message);
            sns.setParam(sendUrl);
            sns.setResponseCode(code);
            snsDao.insertSns(sns);

        } catch (Exception ignored) {}
    }

    /**
     * 邮寄类订单兑换成功发送短信验证码
     *
     * @param customerId 顾客编号
     */
    @Override
    public void exchangeExpress(long customerId) {
        try {
            // 短信内容
            String message = "您已成功兑换C粉圈会员好礼，我们将于15个工作日内将礼品寄出，届时请注意查收哦！回TD退订";
            // 获取手机号码
            String phoneNumber = customerDao.getCustomerPhoneNumberById(customerId);
            // 发送内容
            String sendUrl = url.replace("PHONE_NUMBER", phoneNumber).replace("MESSAGE", encodeHexStr(message.getBytes(StandardCharsets.UTF_16BE)));
            // 发送
            String response = HttpUtil.get(sendUrl);
            // 返回值
            String code;
            // 返回异常
            if (StringUtil.isNullOrEmpty(response) || 3 > response.length()) {
                code = "999";
            } else {
                code = StringUtil.right(response, 3);
            }
            // 保存日志
            Sns sns = new Sns();
            sns.setSnsTypeId((short) 2);
            sns.setCustomerId(customerId);
            sns.setPhoneNumber(phoneNumber);
            sns.setMessage(message);
            sns.setParam(sendUrl);
            sns.setResponseCode(code);
            snsDao.insertSns(sns);

        } catch (Exception ignored) {}
    }

    /**
     * 黄油变粉安瓶级保湿补光申领成功短信
     *
     * @param phoneNumber 手机号码
     * @param store       门店
     */
    @Override
    public void toneup(String phoneNumber, String store) {
        try{
            // 短信内容
            String message = "恭喜您获得全新倩碧粉黄油体验装1份，请凭此短信于2020年3月8日前，前往#" + store + "#领取。每人限领一份，数量有限，赠完即止。关注Clinique倩碧微信公众号，领取更多福利！";
            // 发送内容
            String sendUrl = url.replace("PHONE_NUMBER", phoneNumber).replace("MESSAGE", encodeHexStr(message.getBytes(StandardCharsets.UTF_16BE)));
            // 发送
            String response = HttpUtil.get(sendUrl);
            // 返回值
            String code;
            // 返回异常
            if (StringUtil.isNullOrEmpty(response) || 3 > response.length()) {
                code = "999";
            } else {
                code = StringUtil.right(response, 3);
            }
            // 保存日志
            Sns sns = new Sns();
            sns.setSnsTypeId((short) 3);
            sns.setCustomerId(null);
            sns.setPhoneNumber(phoneNumber);
            sns.setMessage(message);
            sns.setParam(sendUrl);
            sns.setResponseCode(code);
            snsDao.insertSns(sns);
        } catch (Exception ignored) {}
    }

    /**
     * 302美白镭射瓶微信端申请成功短信
     *
     * @param phoneNumber 手机号码
     * @param store       门店
     */
    @Override
    public void ebci(String phoneNumber, String store) {
        try{
            // 短信内容
            String message = "恭喜您获得倩碧302美白[镭射瓶]体验装一份！请于2020年5月17日前，前往#" + store + "#柜台，扫码完成领取。每人限领一份，数量有限，赠完即止。";
            // 发送内容
            String sendUrl = url.replace("PHONE_NUMBER", phoneNumber).replace("MESSAGE", encodeHexStr(message.getBytes(StandardCharsets.UTF_16BE)));
            // 发送
            String response = HttpUtil.get(sendUrl);
            // 返回值
            String code;
            // 返回异常
            if (StringUtil.isNullOrEmpty(response) || 3 > response.length()) {
                code = "999";
            } else {
                code = StringUtil.right(response, 3);
            }
            // 保存日志
            Sns sns = new Sns();
            sns.setSnsTypeId((short) 3);
            sns.setCustomerId(null);
            sns.setPhoneNumber(phoneNumber);
            sns.setMessage(message);
            sns.setParam(sendUrl);
            sns.setResponseCode(code);
            snsDao.insertSns(sns);
        } catch (Exception ignored) {}
    }

    /**
     * 302美白镭射瓶非微信端申请成功短信
     *
     * @param phoneNumber 手机号码
     * @param store       门店
     */
    @Override
    public void ebciExternal(String phoneNumber, String store) {
        try{
            // 短信内容
            String message = "您已成功申领倩碧302美白镭射瓶3日体验礼，请凭此短信于2021年3月31日前，至"+store+"倩碧柜台领取。每人限领一份，数量有限，先到先得赠完即止。"
            		+ "\n\n立即点击http://rcs1.cn/1087Gnn，绑定会员中心，享有更多福利！";
            // 发送内容
            String sendUrl = url.replace("PHONE_NUMBER", phoneNumber).replace("MESSAGE", encodeHexStr(message.getBytes(StandardCharsets.UTF_16BE)));
            // 发送
            String response = HttpUtil.get(sendUrl);
            // 返回值
            String code;
            // 返回异常
            if (StringUtil.isNullOrEmpty(response) || 3 > response.length()) {
                code = "999";
            } else {
                code = StringUtil.right(response, 3);
            }
            // 保存日志
            Sns sns = new Sns();
            sns.setSnsTypeId((short) 3);
            sns.setCustomerId(null);
            sns.setPhoneNumber(phoneNumber);
            sns.setMessage(message);
            sns.setParam(sendUrl);
            sns.setResponseCode(code);
            snsDao.insertSns(sns);
        } catch (Exception ignored) {}
    }
    /**
     * 全新302美白粉底非微信端申请成功短信
     *
     * @param phoneNumber 手机号码
     * @param store       门店
     */
    @Override
    public void ebcfExternal(String phoneNumber, String store) {
        try{
            // 短信内容
            String message = 
            "您已成功申领倩碧全新302美白粉底2日体验礼，请凭此短信于2021年5月31日前，至"+store+"倩碧柜台领取。每人限领一份，数量有限，先到先得赠完即止。\r\n"
            + "\r\n立即点击rcs1.cn/1052rW5，绑定会员中心，享有更多福利！";
            // 发送内容
            String sendUrl = url.replace("PHONE_NUMBER", phoneNumber).replace("MESSAGE", encodeHexStr(message.getBytes(StandardCharsets.UTF_16BE)));
            // 发送
            String response = HttpUtil.get(sendUrl);
            // 返回值
            String code;
            // 返回异常
            if (StringUtil.isNullOrEmpty(response) || 3 > response.length()) {
                code = "999";
            } else {
                code = StringUtil.right(response, 3);
            }
            // 保存日志
            Sns sns = new Sns();
            //TODO
            sns.setSnsTypeId((short) 4);
            sns.setCustomerId(null);
            sns.setPhoneNumber(phoneNumber);
            sns.setMessage(message);
            sns.setParam(sendUrl);
            sns.setResponseCode(code);
            snsDao.insertSns(sns);
        } catch (Exception ignored) {}
    }

    /**
     * 字节转Hex
     *
     * @param src 字节码
     * @return Hex字符串
     */
    private String encodeHexStr(byte[] src) {
        String strHex = "";
        StringBuilder sb = new StringBuilder("");
        for (byte b : src) {
            strHex = Integer.toHexString(b & 0xFF);
            sb.append((strHex.length() == 1) ? "0" + strHex : strHex);
        }
        return sb.toString().trim();
    }

    /**
     * 302美白镭射瓶微信端申请成功短信
     *
     * @param phoneNumber 手机号码
     * @param store       门店
     */
    @Override
    public void ms(String phoneNumber, String store) {
        try{
            // 短信内容
            String message = "恭喜您获得倩碧「三日体验装」1份！请于2020年6月15日前，前往#" + store + "#领取。每人限领一份，数量有限，赠完即止。请关注Clinique倩碧微信公众号，领取更多福利！";
            // 发送内容
            String sendUrl = url.replace("PHONE_NUMBER", phoneNumber).replace("MESSAGE", encodeHexStr(message.getBytes(StandardCharsets.UTF_16BE)));
            // 发送
            String response = HttpUtil.get(sendUrl);
            // 返回值
            String code;
            // 返回异常
            if (StringUtil.isNullOrEmpty(response) || 3 > response.length()) {
                code = "999";
            } else {
                code = StringUtil.right(response, 3);
            }
            // 保存日志
            Sns sns = new Sns();
            sns.setSnsTypeId((short) 3);
            sns.setCustomerId(null);
            sns.setPhoneNumber(phoneNumber);
            sns.setMessage(message);
            sns.setParam(sendUrl);
            sns.setResponseCode(code);
            snsDao.insertSns(sns);
        } catch (Exception ignored) {}
    }

    /**
     * 302美白镭射瓶非微信端申请成功短信
     *
     * @param phoneNumber 手机号码
     * @param store       门店
     */
    @Override
    public void msExternal(String phoneNumber, String store) {
        try{
            // 短信内容
            String message = "恭喜您获得倩碧「三日体验装」1份！请于2020年6月15日前，前往#" + store + "#领取。每人限领一份，数量有限，赠完即止。请关注Clinique倩碧微信公众号，领取更多福利！";
            // 发送内容
            String sendUrl = url.replace("PHONE_NUMBER", phoneNumber).replace("MESSAGE", encodeHexStr(message.getBytes(StandardCharsets.UTF_16BE)));
            // 发送
            String response = HttpUtil.get(sendUrl);
            // 返回值
            String code;
            // 返回异常
            if (StringUtil.isNullOrEmpty(response) || 3 > response.length()) {
                code = "999";
            } else {
                code = StringUtil.right(response, 3);
            }
            // 保存日志
            Sns sns = new Sns();
            sns.setSnsTypeId((short) 3);
            sns.setCustomerId(null);
            sns.setPhoneNumber(phoneNumber);
            sns.setMessage(message);
            sns.setParam(sendUrl);
            sns.setResponseCode(code);
            snsDao.insertSns(sns);
        } catch (Exception ignored) {}
    }

	@Override
	public void sendMessage(String phoneNumber, CampaignH5Detail campaignH5Detail, Long snsId) {
		 try{
			 BSmotTemplate template = smotDao.getSmot(snsId);
            // 短信内容
            String message = template.getTemplateContent();
        	logger.info("申领短信 模板： "+message);
            String[] all = message.split("\\{");
            message = "";
            for(String line : all) {
            	int end = line.indexOf("}");
            	if(end > 0) {
            		String property = line.substring(0, end);
            		Class<?> cla = campaignH5Detail.getClass();
            		Field field = cla.getDeclaredField(property);
            		field.setAccessible(true);
            		line = field.get(campaignH5Detail) + line.substring(end+1) ;
            	}
            	message += line;
            }
        	logger.info("申领短信： "+message);
            // 发送内容
            String sendUrl = url.replace("PHONE_NUMBER", phoneNumber).replace("MESSAGE", encodeHexStr(message.getBytes(StandardCharsets.UTF_16BE)));
            // 发送
            String response = HttpUtil.get(sendUrl);
            // 返回值
            String code;
        	logger.info("申领短信结果： "+ response);
            // 返回异常
            if (StringUtil.isNullOrEmpty(response) || 3 > response.length()) {
                code = "999";
            } else {
                code = StringUtil.right(response, 3);
            }
            // 保存日志
            Sns sns = new Sns();
            sns.setSnsTypeId((short) 3);
            sns.setCustomerId(null);
            sns.setPhoneNumber(phoneNumber);
            sns.setMessage(message);
            sns.setParam(sendUrl);
            sns.setResponseCode(code);
            snsDao.insertSns(sns);
        } catch (Exception ignored) {
        	logger.info(ignored.getMessage());
        }		
	}
}
