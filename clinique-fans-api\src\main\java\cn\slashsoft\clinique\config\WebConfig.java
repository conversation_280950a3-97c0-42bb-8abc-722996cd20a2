package cn.slashsoft.clinique.config;

import cn.slashsoft.clinique.interceptor.AuthInterceptor;
import cn.slashsoft.clinique.interceptor.AuthProInterceptor;
import cn.slashsoft.clinique.interceptor.LoginInterceptor;
import cn.slashsoft.clinique.interceptor.RegisterInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 配置拦截器
 *
 * <AUTHOR>
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    private final LoginInterceptor loginInterceptor;
    private final RegisterInterceptor registerInterceptor;
    private final AuthInterceptor authInterceptor;
    private final AuthProInterceptor authProInterceptor;

    public WebConfig(LoginInterceptor loginInterceptor, RegisterInterceptor registerInterceptor, AuthInterceptor authInterceptor, AuthProInterceptor authProInterceptor) {
        this.loginInterceptor = loginInterceptor;
        this.registerInterceptor = registerInterceptor;
        this.authInterceptor = authInterceptor;
        this.authProInterceptor = authProInterceptor;
    }

    /**
     * 解决中文乱码
     */
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(new StringHttpMessageConverter(StandardCharsets.UTF_8));
    }

    /**
     * 跨域
     *
     * @param registry 跨域
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowedHeaders("*")
                .allowedMethods("*")
                .allowCredentials(true);
    }

    /**
     * 拦截器页面配置
     *
     * @param registry 注册
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 登录拦截: 必须已登录才能访问
        registry.addInterceptor(loginInterceptor)
                // 拦截小程序所有页面
                .addPathPatterns("/api/**")
                // 小程序外部接品
                .excludePathPatterns("/open-api/**")
                // 小程序登录页面不拦截
                .excludePathPatterns("/api/login")
                // 小程序登录页面不拦截
                .excludePathPatterns("/api/check")
                // 小程序配置更新不拦截
                .excludePathPatterns("/api/updateConfig")
                // 小程序活动配置
                .excludePathPatterns("/api/campaign-config/**")
                // 小程序页面打开记录不拦截
                .excludePathPatterns("/api/into-page/**")
                // 未登陆的日志
                .excludePathPatterns("/api/set-nologin-log")
        ;

        // 注册拦截: 必须已注册才能访问
        registry.addInterceptor(registerInterceptor)
                // 拦截小程序所有页面
                .addPathPatterns("/api/**")
                // 小程序外部接品
                .excludePathPatterns("/open-api/**")
                // 小程序登录页面不拦截
                .excludePathPatterns("/api/login")
                // 小程序登录页面不拦截
                .excludePathPatterns("/api/check")
                // 小程序页面打开记录不拦截
                .excludePathPatterns("/api/into-page/**")
                // 小程序配置更新不拦截
                .excludePathPatterns("/api/updateConfig")
                // 小程序活动配置
                .excludePathPatterns("/api/campaign-config/**")
                // 是否关注
                .excludePathPatterns("/api/is-friend")
                // 小程序授权页面不拦截
                .excludePathPatterns("/api/authorize")
                // 小程序是否注册不拦截
                .excludePathPatterns("/api/get-is-register")
                // 小程序解密手机号码不拦截
                .excludePathPatterns("/api/get-phone-number")
                // 小程序注册页面不拦截
                .excludePathPatterns("/api/register")
                // 小程序上传头像不拦截
                .excludePathPatterns("/api/upload-avatar")
                // 小程序打开日志不拦截
                .excludePathPatterns("/api/set-originate/*")
                // 小程序动作日志不拦截
                .excludePathPatterns("/api/set-action/*")

                // 未登陆的日志
                .excludePathPatterns("/api/set-nologin-log")
                // 隐私检查
                .excludePathPatterns("/api/private-relish/check")
                // koc检查
                .excludePathPatterns("/api/kocbeta/check")
        ;

        // 授权拦截: 必须已授权才能访问 - 静默授权
        registry.addInterceptor(authInterceptor)
                // 拦截公众号所有页面
                .addPathPatterns("/official/**")
                .addPathPatterns("/wechat/**")
                .addPathPatterns("/mini/**")
                // 小程序页面打开记录不拦截
                .excludePathPatterns("/api/into-page/**")
                // 公众号开发者服务器
                .excludePathPatterns("/official/server")
                // 公众号JSSDK
                .excludePathPatterns("/official/getJssdk")
                // 公众号授权页面不拦截
                .excludePathPatterns("/official/auth")
                .excludePathPatterns("/official/auth-pro")
                // 绑定美容顾客活动
                .excludePathPatterns("/official/campaign-wechat-work-binding/**")
        ;

        // 授权拦截: 必须已授权才能访问 - 主动授权
        registry.addInterceptor(authProInterceptor)
                // 绑定美容顾客活动
                .addPathPatterns("/official/campaign-wechat-work-binding/**")
        ;
    }

}
