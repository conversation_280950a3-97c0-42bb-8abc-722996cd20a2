package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.Campaign;
import com.stormcrm.clinique.util.StringUtil;
import org.apache.ibatis.jdbc.SQL;

/**
 * 商城礼品管理
 *
 * <AUTHOR>
 */
public class CampaignProvider {

    /**
     * 查询所有礼品-分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 礼品列表
     */
    public String getPage(int pageIndex, int pageSize, String generalSearch, Boolean status) {

        return new SQL() {{

            SELECT("`id`,`name`,`link_url`,`status`");
            FROM("`campaign`");
            WHERE("`recovery`=0");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`name` LIKE '%${generalSearch}%'");
            }
            if (null != status) {
                AND();
                WHERE("`status`=#{status}");
            }
            ORDER_BY("`id` DESC LIMIT #{pageIndex},#{pageSize}");

        }}.toString();

    }

    /**
     * 查询所有礼品带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    public String getPageCount(String generalSearch, Boolean status) {

        return new SQL() {{

            SELECT("COUNT(*)");
            FROM("`campaign`");
            WHERE("`recovery`=0");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`name` LIKE '%${generalSearch}%'");
            }
            if (null != status) {
                AND();
                WHERE("`status`=#{status}");
            }

        }}.toString();

    }

    /**
     * 更新
     *
     * @param campaign 信息
     */
    public String update(Campaign campaign) {

        return new SQL() {{

            UPDATE("`campaign`");

            SET("`name`=#{name}");
            if (!StringUtil.isNullOrEmpty(campaign.getImageUrl())) {
                SET("`image_url`=#{imageUrl}");
            }
            SET("`link_url`=#{linkUrl}");

            WHERE("`id`=#{id}");

        }}.toString();

    }

}
