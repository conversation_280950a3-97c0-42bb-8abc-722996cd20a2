package cn.slashsoft.clinique.util;

/**
 * 布尔工具类
 *
 * <AUTHOR>
 */
public class BooleanUtil {

    /**
     * 长整型转布尔型
     *
     * @param l 长整型
     * @return 为空或0返回false, 其他返回true
     */
    public static boolean valueOf(Long l) {
        return !(null == l || 0 == l);
    }

    /**
     * 字符型转布尔型
     *
     * @param s 字符型
     * @return 为空或0返回false, 其他返回true
     */
    public static boolean valueOf(String s){
        return s != null && !s.isEmpty() && Boolean.valueOf(s);
    }

}
