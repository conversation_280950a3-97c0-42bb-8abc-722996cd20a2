package cn.slashsoft.clinique.service.mini;

import cn.slashsoft.clinique.domain.mini.Counter;
import cn.slashsoft.clinique.domain.mini.CounterLog;

import java.math.BigDecimal;
import java.util.List;

/**
 * 柜台
 *
 * <AUTHOR>
 */
public interface CounterService {

    /**
     * 跟据定位查找附近的门店
     *
     * @param latitude  经度
     * @param longitude 纬度
     * @return 门店
     */
    List<Counter> getCounter(
            BigDecimal latitude,
            BigDecimal longitude
    );

    /**
     * 跟据定位查找附近的门店
     *
     * @param latitude  经度
     * @param longitude 纬度
     * @param province  省份
     * @param city      城市
     * @param name      门店名称
     * @return 门店
     */
    List<Counter> getCounterBySearch(
            BigDecimal latitude,
            BigDecimal longitude,
            String province,
            String city,
            String name
    );

    /**
     * 写入日志
     * @param counterLog 日志
     */
    void insertCounterLog(CounterLog counterLog);

}
