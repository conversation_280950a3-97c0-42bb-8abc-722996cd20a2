package cn.slashsoft.clinique.domain.mini;

import java.util.Date;

import lombok.Data;

/**
 * 活动页面返回数据
 * <AUTHOR>
 */
@Data
public class NoteDiscussMy {
	private Long id;
	private Long noteId;
	private String title;
	private String ownerName;
	private String avatarUrl;
	private String coverPhoto;
	private String content;
	private int status;
	private Date updateTime;
    /* 给用户加一个小绿色V */
    private Integer kocV;
}
