package com.stormcrm.clinique.domain;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class Note{

	private Long id;

	private Object content;

	private Date createTime;
	private Long customerId;

	/**
	 * status 0 未审核 1 审核不通过 2 审核通过 3 加精 4 置顶
	 */
	private int status;

	private Object title;

	private Date updateTime;
	
	private String coverPhoto;
	private String ownerName;
	private String avatarUrl;
	private Long likeCount;
	private Long readCount;
	private Integer discussCount;
	private int liked;
	private int self;

	private String unionid;
	private String wechatMiniOpenid;
	private String wechatOfficialOpenid;
	private List<NotePhoto> photos;
	private ArrayList<NoteDiscuss> discuss;
	
	public String toJson() {
		return "";
	}

}