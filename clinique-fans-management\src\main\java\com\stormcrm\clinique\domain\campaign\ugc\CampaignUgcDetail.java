package com.stormcrm.clinique.domain.campaign.ugc;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Data
public class CampaignUgcDetail {

    private Long id;
    private Long campaignId;
    private String wechatMiniOpenid;
    private Long customerId;
    private String nickName;
    private String avatarUrl;
    private Short status;
    private Date uploadTime;
    private Date examineTime;
    private Date createTime;
    
    private String title;

    private float point;

    private CampaignUgcDetailPoint pointChoose;

    private List<CampaignUgcImage> imageList;

}
