package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.BSmotCampaign;
import com.stormcrm.clinique.domain.BSmotLog;
import com.stormcrm.clinique.domain.BSmotTemplate;
import com.stormcrm.clinique.domain.BSmotTestCustomer;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * 发送模版消息 四个表的处理
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface BSmotDao {    
    @Select(" SELECT * FROM `smot_template` ")
    List<BSmotTemplate> getTemplates();

    @Select(" SELECT * FROM `smot_template` where id=#{id} limit 1 ")
    BSmotTemplate getTemplateById(Long id);    

    @Select(" SELECT * FROM `smot_test_customer` ")
    List<BSmotTestCustomer> getTestCustomers();

    @Select(" SELECT * FROM `smot_test_customer` where id=#{id} limit 1 ")
    BSmotTestCustomer getTestCustomerById(Long id);
    
    @Select(" SELECT * FROM `smot_test_customer` where type <=#{type}  ")
    List<BSmotTestCustomer> getTestCustomerByType(Short type);
    
    @Insert(" INSERT INTO `smot_campaign` "
    		+ " ( `desc`, `source`,  `template_id`, `miniapp_id`, `page_path`, `param` )"
    		+ " VALUES " 
    		+ " (#{desc}, #{source},  #{templateId}, #{miniappId}, #{pagePath}, #{param} )")
    int addBSmotCampaign(BSmotCampaign sc);
    
    @Update(" UPDATE `smot_campaign` SET  "
    		+ "  `desc` = #{desc}, "
    		+ " `source` = #{source},  "
    		+ " `template_id` =  #{templateId}, "
    		+ " `miniapp_id` = #{miniappId}, "
    		+ " `page_path` = #{pagePath} , "
    		+ " `param` =  #{param}"
    		+ " WHERE `id` = #{id} ")
    int updateBSmotCampaign(BSmotCampaign sc);

    @Select(" SELECT a.*, "
    		+ " (SELECT count(*)  FROM `smot_log` b WHERE b.campaign_id=a.id  ) `create_count`, "
    		+ " (SELECT count(*)  FROM `smot_log` b where b.campaign_id=a.id AND  b.`status` = 1) `send_count`, "
    		+ " (SELECT count(*)  FROM `smot_log` b where b.campaign_id=a.id AND  b.`result_code` != 0) `error_count` " 
    		+ " FROM `smot_campaign` a")
    List<BSmotCampaign> getCampaigns();

    
    @Select(" SELECT a.*, "
    		+ " (SELECT count(*)  FROM `smot_log`  ) `create_count`, "
    		+ " (SELECT count(*)  FROM `smot_log` where  b.`status` = 1) `send_count`, "
    		+ " (SELECT count(*)  FROM `smot_log` where  b.`result_code` != 0) `error_count` " 
    		+ " FROM `smot_campaign` a "
    		+ " LEFT JOIN `smot_log` b ON  b.campaign_id=a.id "
    		+ " where a.id=#{id} limit 1 ")
    BSmotCampaign getCampaignById(Long id);
     
    @Insert(" INSERT INTO `smot_log` "
    		+ " ( `campaign_id` ,`phone_number`,`wechat_official_openid`,`param`, `result`, `result_code`,`status`,`create_time` )"
    		+ " VALUES " 
    		+ " (#{campaignId}, #{phoneNumber},  #{wechatOfficialOpenid}, #{param}, #{result}, #{resultCode},0, now() )")
    int addBSmotLog(BSmotLog sc);
    
    @Update(" UPDATE `smot_log` SET  "
    		+ " `result` = #{result}, "
    		+ " `result_code` = #{resultCode},"
    		+ " `status` = #{status},"
    		+ " `update_time` = now() "    		
    		+ " WHERE `id` = #{id} ")
    int updateBSmotLog(BSmotLog sc);
  

    @Select(" SELECT a.*,b.page_path, c.template_code,c.type,b.miniapp_id "
    		+ " FROM `smot_log` a "
    		+ " LEFT JOIN `smot_campaign` b on b.`id`=a.`campaign_id` "
    		+ " LEFT JOIN `smot_template` c on c.`id`=b.`template_id` "
    		+ " WHERE campaign_id=#{campaignId} AND a.status=0 ")
    List<BSmotLog> getLogsByCampaign(Long campaignId);

    @Select(" SELECT a.*,b.page_path, c.template_code,c.type,b.miniapp_id "
    		+ " FROM `smot_log` a"
    		+ " LEFT JOIN `smot_campaign` b on b.`id`=a.`campaign_id` "
    		+ " LEFT JOIN `smot_template` c on c.`id`=b.`template_id` "
    		+ " WHERE campaign_id=#{campaignId} limit 2 ")
    List<BSmotLog> getTestLogsByCampaign(Long campaignId);

    @Select(" SELECT a.*,b.page_path, c.template_code,c.type,b.miniapp_id   FROM `smot_log` a "
    		+ " LEFT JOIN `smot_campaign` b on b.`id`=a.`campaign_id` "
    		+ " LEFT JOIN `smot_template` c on c.`id`=b.`template_id` "
    		+ " where id=#{id} limit 1 ")
    BSmotLog getLogById(Long id);
    
}
