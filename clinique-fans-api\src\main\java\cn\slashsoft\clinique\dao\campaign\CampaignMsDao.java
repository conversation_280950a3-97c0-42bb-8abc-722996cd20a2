package cn.slashsoft.clinique.dao.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignMsDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignMsLog;
import cn.slashsoft.clinique.domain.campaign.CampaignMsStock;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * 水磁场申领
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignMsDao {

    /**
     * 保存日志
     *
     * @param campaignMsLog 日志
     */
    @Insert("INSERT INTO `campaign_ms_log`(" +
            "   `type`," +
            "   `unique_id`, " +
            "   `source`," +
            "   `page`" +
            ") " +
            "VALUES (" +
            "   #{type}, " +
            "   #{uniqueId}, " +
            "   #{source}," +
            "   #{page}" +
            ")")
    void insertLog(CampaignMsLog campaignMsLog);

    /**
     * 获取是否已经审领
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    @Select("SELECT " +
            "   COUNT(*) " +
            "FROM " +
            "   `campaign_ms_detail` " +
            "WHERE " +
            "   `phone_number`=#{phoneNumber} " +
            "LIMIT 1")
    int hasDetail(String phoneNumber);

    /**
     * 获取是否已经审领
     *
     * @param openid 公众号唯一编号
     * @return 数量
     */
    @Select("SELECT " +
            "   COUNT(*) " +
            "FROM " +
            "   `campaign_ms_detail` " +
            "WHERE " +
            "   `unique_id`=#{openid} " +
            "LIMIT 1")
    int hasDetailByOpenid(String openid);

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    @Select("SELECT " +
            "   `id`, `name`, `phone_number`, `city`, `store`, `follow` " +
            "FROM " +
            "   `campaign_ms_detail` " +
            "WHERE " +
            "   `phone_number`=#{phoneNumber} " +
            "LIMIT 1")
    CampaignMsDetail getDetail(String phoneNumber);

    /**
     * 获取审领信息
     *
     * @param openid 公众号唯一编号
     * @return 审领信息
     */
    @Select("SELECT " +
            "   `id`, `name`, `phone_number`, `city`, `store`, `follow`, `receive` " +
            "FROM " +
            "   `campaign_ms_detail` " +
            "WHERE " +
            "   `unique_id`=#{openid} " +
            "LIMIT 1")
    CampaignMsDetail getDetailByOpenid(String openid);

    /**
     * 获取手机号码
     *
     * @param unionid 开放平台唯一编号
     * @return 手机号码
     */
    @Select("SELECT " +
            "   `c`.`phone_number` " +
            "FROM " +
            "   `wechat` `w` " +
            "       INNER JOIN " +
            "   `customer` `c` " +
            "       ON `w`.`customer_id`=`c`.`id` " +
            "WHERE " +
            "   `w`.`unionid`=#{unionid}")
    String getPhoneNumberByUnionid(String unionid);

    /**
     * 获取手机号码
     *
     * @param openid 公众号唯一编号
     * @return 手机号码
     */
    @Select("SELECT " +
            "   `c`.`phone_number` " +
            "FROM " +
            "   `wechat` `w` " +
            "       INNER JOIN " +
            "   `customer` `c` " +
            "       ON `w`.`customer_id`=`c`.`id` " +
            "WHERE " +
            "   `w`.`wechat_official_openid`=#{openid}")
    String getPhoneNumberByOpenid(String openid);

    /**
     * 扣库存
     *
     * @param campaignMsDetail 申领信息
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ms_stock` " +
            "SET " +
            "   `stock`=`stock`-1, " +
            "   `total`=`total`+1 " +
            "WHERE " +
            "   `city`=#{city} " +
            "   AND `store`=#{store} " +
            "   AND `stock`>0")
    int updateStock(CampaignMsDetail campaignMsDetail);

    /**
     * 定入申领信息
     *
     * @param campaignMsDetail 申领信息
     */
    @Insert("INSERT INTO `campaign_ms_detail`(" +
            "   `type`, " +
            "   `unique_id`, " +
            "   `name`, " +
            "   `phone_number`, " +
            "   `city`, " +
            "   `store`, " +
            "   `source` " +
            ") " +
            "VALUES (" +
            "   #{type}, " +
            "   #{uniqueId}, " +
            "   #{name}, " +
            "   #{phoneNumber}, " +
            "   #{city}, " +
            "   #{store}, " +
            "   #{source} " +
            ")")
    void insetDetail(CampaignMsDetail campaignMsDetail);

    /**
     * 更新是否关注
     *
     * @param campaignMsDetail 申领信息
     */
    @Update("UPDATE " +
            "   `campaign_ms_detail` " +
            "SET " +
            "   `follow`=#{follow}," +
            "   `follow_source`=#{followSource}," +
            "   `follow_first_time`=#{followFirstTime}," +
            "   `follow_last_time`=#{followLastTime}," +
            "   `follow_cancel_time`=#{followCancelTime}," +
            "   `bind`=#{bind}," +
            "   `bind_time`=#{bindTime} " +
            "WHERE " +
            "   `id`=#{id}")
    void setFollow(CampaignMsDetail campaignMsDetail);

    /**
     * 跟据场景值获取门店
     *
     * @param scene 场景值
     * @return 门店
     */
    @Select("SELECT " +
            "   `city`,`store`,`store_id` " +
            "FROM " +
            "   `campaign_ms_stock` " +
            "WHERE " +
            "   `scene`=#{scene} " +
            "LIMIT 1")
    CampaignMsStock getByScene(String scene);

    /**
     * 更新核销信息
     *
     * @param campaignMsDetail 核销信息
     */
    @Update("UPDATE " +
            "   `campaign_ms_detail` " +
            "SET " +
            "   `receive`=1, " +
            "   `receive_city`=#{receiveCity}, " +
            "   `receive_store`=#{receiveStore}, " +
            "   `receive_store_id`=#{receiveStoreId}, " +
            "   `receive_time`=NOW(), " +
            "   `scene`=#{scene}," +
            "   `cdp`=0 " +
            "WHERE " +
            "   `id`=#{id} ")
    void setReceive(CampaignMsDetail campaignMsDetail);

}
