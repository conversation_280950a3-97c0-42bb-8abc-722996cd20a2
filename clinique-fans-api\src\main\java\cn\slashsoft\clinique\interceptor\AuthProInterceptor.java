package cn.slashsoft.clinique.interceptor;

import cn.slashsoft.clinique.util.ServerUtil;
import cn.slashsoft.clinique.util.StringUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;

/**
 * 公众号授权拦截器
 *
 * <AUTHOR>
 */
@Component
public class AuthProInterceptor implements HandlerInterceptor {

    @Value("${wechat.official.authorize-pro}")
    private String urlAuthorize;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        // 读取Session中的openid
        String officialOpenid = (String) request.getSession().getAttribute("officialOpenid");

        if (StringUtil.isNullOrEmpty(officialOpenid)) {
            request.getSession().setAttribute("officialOauthPath", ServerUtil.getFullUrl(request, "/fans"));
            String redirectUri = URLEncoder.encode(ServerUtil.getDomain(request) + "/fans/official/auth-pro" + ServerUtil.getQuery(request), "UTF-8");
            // 跳转至授权页面
            response.sendRedirect(urlAuthorize + redirectUri);
            return false;
        }

        request.setAttribute("unionid", request.getSession().getAttribute("unionid"));
        request.setAttribute("nickName", request.getSession().getAttribute("nickName"));
        request.setAttribute("avatarUrl", request.getSession().getAttribute("avatarUrl"));
        request.setAttribute("officialOpenid", officialOpenid);
        return true;

    }

}
