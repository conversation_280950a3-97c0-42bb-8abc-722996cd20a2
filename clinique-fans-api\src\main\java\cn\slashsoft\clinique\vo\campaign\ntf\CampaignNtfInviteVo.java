package cn.slashsoft.clinique.vo.campaign.ntf;

import cn.slashsoft.clinique.domain.campaign.ntf.CampaignNtf;
import cn.slashsoft.clinique.domain.campaign.ntf.CampaignNtfRecord;
import cn.slashsoft.clinique.domain.mini.Wechat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * NTF
 *
 * <AUTHOR>
 */
@Data
public class CampaignNtfInviteVo {

    private Date startTime;
    private Date endTime;

    private String nickName;
    private String avatarUrl;

    private CampaignNtf campaignNtf;
    private List<String> inviteeAvatarUrlList;

}
