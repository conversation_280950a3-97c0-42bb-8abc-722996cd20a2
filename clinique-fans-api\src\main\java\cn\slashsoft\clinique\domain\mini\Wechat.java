package cn.slashsoft.clinique.domain.mini;

import lombok.Data;

import java.util.Date;

/**
 * 与微信开放平台、微信公众号、微信小程序相关的表
 *
 * <AUTHOR>
 */
@Data
public class Wechat {

    private Long id;

    private Long customerId;
    private String phoneNumber;
    /**
     * 开放平台唯一编号
     */
    private String unionid;
    /**
     * 公众号唯一编号
     */
    private String wechatOfficialOpenid;
    /**
     * 小程序唯一编号
     */
    private String wechatMiniOpenid;

    private String nickName;
    private String avatarUrl;
    private String gender;
    private String country;
    private String province;
    private String city;
    private String language;
    /**
     * 场景值时间，从公众号文章进来后发放最后发放积分的时间
     */
    private Date sceneRewardLastTime;

    private String platform;

    private Boolean status;

    /* 给用户加一个小绿色V */
    private Integer kocV;

    private Date updateTime;
    private Date createTime;

}
