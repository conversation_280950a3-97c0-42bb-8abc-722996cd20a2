package cn.slashsoft.clinique.controller.mini;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.slashsoft.clinique.domain.mini.Action;
import cn.slashsoft.clinique.domain.mini.PageLog;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.ActionService;
import cn.slashsoft.clinique.service.mini.PageLogService;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.util.ResultUtil;
import io.netty.util.internal.StringUtil;

/**
 * 动作
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class ActionController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;
    
    @Resource
    private WechatService wechatService;
    
    @Resource
    private PageLogService pagelogService;

    private final ActionService actionService;

    public ActionController(ActionService actionService) {
        this.actionService = actionService;
    }

    /**
     * 写入动作
     *
     * @param type 类型
     * @return 成功
     */
    @PostMapping("/set-action/{type}")
    public String setAction(
            @PathVariable("type") short type
    ) {
        // 获取缓存中的顾客编号
        String openid = (String) request.getAttribute("miniOpenid");

        Action action = new Action();
        action.setWechatOfficialOpenid(openid);
        action.setType(type);
        actionService.insertAction(action);

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }
    /**
     * 写入页面打开记录
     *
     * @param 页面打开记录
     * @return 成功
     */
    @PostMapping("/into-page/{page}/{source}/{jscode}")
    public String setPageLog(
            @PathVariable("page") String page,
            @PathVariable("source") String source,
            @RequestParam(value = "share",required=false) String shareMiniOpenId,
            @RequestParam(value = "openid",required=false) String ownerOpenid,
            @PathVariable("jscode") String jscode
    ) {
        // 获取缓存中的顾客编号
        String openid = null;
 
    	if(!StringUtil.isNullOrEmpty(ownerOpenid)) {
    		openid = ownerOpenid;
    	}else if(jscode.equals("nocode")) {
    		openid =(String) request.getAttribute("miniOpenid");
    	}else {
    		openid = wechatService.getMiniOpenidByJsCode(jscode);
    	}
    	

    	if(null == openid) {
    		return ResultUtil.customer(ResultEnum.FAILED);
    	}
        PageLog log = new PageLog();
        log.setWechatMiniOpenid(openid);
        log.setPage(page);
        log.setSource(source);
        log.setShareMiniOpenid(shareMiniOpenId);
        
        pagelogService.insertPageLog(log);

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }
}
