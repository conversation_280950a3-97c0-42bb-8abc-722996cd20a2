package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.service.campaign.CampaignD11Service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 双十一活动  老客户用码签到
 */
@Controller
@RequestMapping("/d11")
public class CampaignDoubleElevenController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    private final CampaignD11Service campaignD11Service;

    public CampaignDoubleElevenController(CampaignD11Service campaignD11Service) {
        this.campaignD11Service = campaignD11Service;
    }


    /**
     * 签到页
     */
    @GetMapping("/index")
    public String index(Model model) {
        model.addAttribute("serviceName", "external");
            model.addAttribute("staticDomain", staticDomain);
        return "w/fans/campaign/d11/index";
    }


    /**
     * 根据码查姓名
     */
    @GetMapping("/queryName/{code}")
    @ResponseBody
    public String queryName(@PathVariable("code") String code) {
        return campaignD11Service.getCustomerNameByCode(code);
    }

    /**
     * 根据码查 签到
     */
    @GetMapping("/sign/{code}")
    @ResponseBody
    public String sign(@PathVariable("code") String code) {
        return campaignD11Service.signIn();
    }
}
