package cn.slashsoft.cliniqueapi;

import cn.slashsoft.clinique.util.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.junit.Test;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.AlgorithmParameters;
import java.security.Security;
import java.util.Arrays;
import java.util.Base64;

public class WechatTests {

    @Test
    public void code2Unionid() {

        String appId = "wxc1c6c6a5f83f13ff";
        String appSecret = "33cfff5b1d45ae20ab8eb6d57c35874b";
        String urlCode2Session = "https://api.weixin.qq.com/sns/jscode2session?appid=APPID&secret=SECRET&js_code=JSCODE&grant_type=authorization_code";

        String code = "043rMrZA1VKJsc0a00WA1l4pZA1rMrZm";

        try {
            // 第一步： 拼接接口地址
            // 替换APPID
            // 第二步：调接口获得openid
            // 调用微信小程序登录接口, 解析JSON
            JSONObject json = (JSONObject) JSON.parse(HttpUtil.get(urlCode2Session.replace("APPID", appId).replace("SECRET", appSecret).replace("JSCODE", code)));
            System.out.println(json.toJSONString());

        } catch (Exception e) {
            e.printStackTrace();
        }

    }




    /**
     * 解密微信敏感数据
     *
     * @param iv            向量
     * @param encryptedData 加密数据
     * @param sessionKey    会话KEY
     * @return 数据
     */
    private JSONObject decrypt(String iv, String encryptedData, String sessionKey) {
        try {
            Base64.Decoder decoder = Base64.getDecoder();
            byte[] dataByte = decoder.decode(encryptedData);
            byte[] keyByte = decoder.decode(sessionKey);
            byte[] ivByte = decoder.decode(iv);
            int base = 16;
            if (keyByte.length % base != 0) {
                int groups = keyByte.length / base + 1;
                byte[] temp = new byte[groups * base];
                Arrays.fill(temp, (byte) 0);
                System.arraycopy(keyByte, 0, temp, 0, keyByte.length);
                keyByte = temp;
            }
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
            SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
            AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
            parameters.init(new IvParameterSpec(ivByte));
            cipher.init(Cipher.DECRYPT_MODE, spec, parameters);
            byte[] resultByte = cipher.doFinal(dataByte);
            if (null != resultByte && resultByte.length > 0) {
                String result = new String(resultByte, StandardCharsets.UTF_8);
                return JSONObject.parseObject(result);
            }
        } catch (Exception e) {
            System.out.println("数据解密失败，" + e.getMessage());
        }
        return null;
    }

}
