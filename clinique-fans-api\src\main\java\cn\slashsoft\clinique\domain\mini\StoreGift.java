package cn.slashsoft.clinique.domain.mini;

import java.util.Date;

import lombok.Data;

/**
 * 兑换礼品表
 *
 * <AUTHOR>
 * Berg
 */
@Data
public class StoreGift {

    private Long id;
    private Short storeGiftTypeId;
    private String message;
    private String name;
    private String imageUrl;
    private String description;
    private Integer points;
    private Integer stocks;
    private Integer exchangeTotal;
    private Boolean status;
    private Boolean recovery;
    
    private Date startTime;
    private Date endTime;

    private Long couponId;
    

    //Berg  折扣
    private String halfDesc;
    private Integer halfPrice;
    private Integer wholePoints;
    
    

}
