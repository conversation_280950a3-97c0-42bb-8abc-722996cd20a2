package com.stormcrm.clinique.controller.campaign.ugc;

import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgc;
import com.stormcrm.clinique.service.OutsideService;
import com.stormcrm.clinique.service.campaign.ugc.CampaignUgcService;
import com.stormcrm.clinique.util.DateUtil;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.util.VerifyUtil;
import com.stormcrm.clinique.vo.Result;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("campaign-ugc")
public class CampaignUgcController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;

    private final CampaignUgcService campaignUgcService;
    private final OutsideService outsideService;

    public CampaignUgcController(CampaignUgcService campaignUgcService, OutsideService outsideService) {
        this.campaignUgcService = campaignUgcService;
        this.outsideService = outsideService;
    }

    /**
     * 页面模版
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC')")
    @GetMapping("")
    public String index(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/campaign-ugc/index";
    }

    /**
     * 查询所有
     *
     * @return 列表
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC')")
    @PostMapping("get-all")
    @ResponseBody
    public String getAll() {

        List<CampaignUgc> campaignUgcList = campaignUgcService.getAll();

        JSONObject mata = new JSONObject();
        mata.put("page", 1);
        mata.put("pages", 1);
        mata.put("perpage", -1);
        mata.put("total", campaignUgcList.size());
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("mata", mata);
        result.put("data", campaignUgcList);

        return JSONObject.toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }

    /**
     * 增加模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_ADD')")
    @RequestMapping("add")
    public String add(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/campaign-ugc/add";
    }

    /**
     * 新增
     *
     * @return 结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_ADD')")
    @PostMapping(value = "add-submit", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String addSubmit(
            @RequestParam(value = "form-name", required = false) String name,
            @RequestParam(value = "form-startTime", required = false) String startTime,
            @RequestParam(value = "form-endTime", required = false) String endTime,
            @RequestParam(value = "form-indexBannerImage", required = false) MultipartFile indexBannerImage,
            @RequestParam(value = "form-uploadBannerImage", required = false) MultipartFile uploadBannerImage,
            @RequestParam(value = "form-ruleImage", required = false) MultipartFile ruleImage,
            @RequestParam(value = "form-failImage", required = false) MultipartFile failImage,
            @RequestParam(value = "form-shareTitle", required = false) String shareTitle,
            @RequestParam(value = "form-shareImage", required = false) MultipartFile shareImage
    ) {

        // 验证
        if (!VerifyUtil.required(name)) {
            return ResultUtil.verifyFailToJson("form-name", "这是必填字段");
        }
        if (!VerifyUtil.required(startTime)) {
            return ResultUtil.verifyFailToJson("form-startTime", "这是必填字段");
        }
        if (!VerifyUtil.required(endTime)) {
            return ResultUtil.verifyFailToJson("form-endTime", "这是必填字段");
        }
        if (null == indexBannerImage || indexBannerImage.isEmpty()) {
            return ResultUtil.verifyFailToJson("form-indexBannerImage", "这是必填字段");
        }
        if (null == uploadBannerImage || uploadBannerImage.isEmpty()) {
            return ResultUtil.verifyFailToJson("form-uploadBannerImage", "这是必填字段");
        }
        if (null == ruleImage || ruleImage.isEmpty()) {
            return ResultUtil.verifyFailToJson("form-ruleImage", "这是必填字段");
        }
        if (null == failImage || failImage.isEmpty()) {
            return ResultUtil.verifyFailToJson("form-failImage", "这是必填字段");
        }
        if (!VerifyUtil.required(shareTitle)) {
            return ResultUtil.verifyFailToJson("form-shareTitle", "这是必填字段");
        }
        if (null == shareImage || shareImage.isEmpty()) {
            return ResultUtil.verifyFailToJson("form-shareImage", "这是必填字段");
        }

        // 生成对象
        CampaignUgc campaignUgc = new CampaignUgc();
        campaignUgc.setName(name);
        campaignUgc.setStartTime(DateUtil.valueOf(startTime));
        campaignUgc.setEndTime(DateUtil.valueOf(endTime));
        campaignUgc.setIndexBannerImageUrl(outsideService.uploadImageOss(indexBannerImage, "w/fans/mini/campaign/ugc/image"));
        campaignUgc.setUploadBannerImageUrl(outsideService.uploadImageOss(uploadBannerImage, "w/fans/mini/campaign/ugc/image"));
        campaignUgc.setRuleImageUrl(outsideService.uploadImageOss(ruleImage, "w/fans/mini/campaign/ugc/image"));
        campaignUgc.setFailImageUrl(outsideService.uploadImageOss(failImage, "w/fans/mini/campaign/ugc/image"));
        campaignUgc.setShareTitle(shareTitle);
        campaignUgc.setShareImageUrl(outsideService.uploadImageOss(shareImage, "w/fans/mini/campaign/ugc/image"));

        // 传到Service服务中保存
        Result result = campaignUgcService.save(campaignUgc);

        // 返回绍果给前端
        return JSONObject.toJSONString(result);
    }

    /**
     * 编辑模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_EDIT')")
    @GetMapping("edit/{id}")
    public String edit(@PathVariable long id, Model model) {
        model.addAttribute("staticDomain", staticDomain);
        // 读数据库
        CampaignUgc campaignUgc = campaignUgcService.getById(id);
        if (null == campaignUgc) {
            return "m/fans/common/empty";
        }
        model.addAttribute("id", id);
        model.addAttribute("campaignUgc", campaignUgc);
        return "m/fans/campaign-ugc/edit";
    }

    /**
     * 编辑
     *
     * @param id        编号
     * @return 结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_EDIT')")
    @PostMapping(value = "edit-submit/{id}", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String editSubmit(
            @PathVariable("id") long id,
            @RequestParam(value = "form-name", required = false) String name,
            @RequestParam(value = "form-startTime", required = false) String startTime,
            @RequestParam(value = "form-endTime", required = false) String endTime,
            @RequestParam(value = "form-indexBannerImage", required = false) MultipartFile indexBannerImage,
            @RequestParam(value = "form-uploadBannerImage", required = false) MultipartFile uploadBannerImage,
            @RequestParam(value = "form-ruleImage", required = false) MultipartFile ruleImage,
            @RequestParam(value = "form-failImage", required = false) MultipartFile failImage,
            @RequestParam(value = "form-shareTitle", required = false) String shareTitle,
            @RequestParam(value = "form-shareImage", required = false) MultipartFile shareImage
    ) {

        // 验证
        if (!VerifyUtil.required(name)) {
            return ResultUtil.verifyFailToJson("form-name", "这是必填字段");
        }
        if (!VerifyUtil.required(startTime)) {
            return ResultUtil.verifyFailToJson("form-startTime", "这是必填字段");
        }
        if (!VerifyUtil.required(endTime)) {
            return ResultUtil.verifyFailToJson("form-endTime", "这是必填字段");
        }
        if (!VerifyUtil.required(shareTitle)) {
            return ResultUtil.verifyFailToJson("form-shareTitle", "这是必填字段");
        }

        // 生成对象
        CampaignUgc campaignUgc = new CampaignUgc();
        campaignUgc.setId(id);
        campaignUgc.setName(name);
        campaignUgc.setStartTime(DateUtil.valueOf(startTime));
        campaignUgc.setEndTime(DateUtil.valueOf(endTime));
        if (null != indexBannerImage && !indexBannerImage.isEmpty()) {
            campaignUgc.setIndexBannerImageUrl(outsideService.uploadImageOss(indexBannerImage, "w/fans/mini/campaign/ugc/image"));
        }
        if (null != uploadBannerImage && !uploadBannerImage.isEmpty()) {
            campaignUgc.setUploadBannerImageUrl(outsideService.uploadImageOss(uploadBannerImage, "w/fans/mini/campaign/ugc/image"));
        }
        if (null != ruleImage && !ruleImage.isEmpty()) {
            campaignUgc.setRuleImageUrl(outsideService.uploadImageOss(ruleImage, "w/fans/mini/campaign/ugc/image"));
        }
        if (null != failImage && !failImage.isEmpty()) {
            campaignUgc.setFailImageUrl(outsideService.uploadImageOss(failImage, "w/fans/mini/campaign/ugc/image"));
        }
        campaignUgc.setShareTitle(shareTitle);
        if (null != shareImage && !shareImage.isEmpty()) {
            campaignUgc.setShareImageUrl(outsideService.uploadImageOss(shareImage, "w/fans/mini/campaign/ugc/image"));
        }
        // 传到Service服务中保存
        Result result = campaignUgcService.update(campaignUgc);

        // 返回绍果给前端
        return JSONObject.toJSONString(result);
    }

    /**
     * 上架
     *
     * @param id 胸章的自动编号
     * @return 影响的行数
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_UPPER')")
    @GetMapping("upper/{id}")
    @ResponseBody
    public String upper(@PathVariable("id") long id) {

        if (0 == campaignUgcService.upper(id)) {
            return ResultUtil.failToJson();
        }

        return ResultUtil.successToJson();
    }

    /**
     * 下架
     *
     * @param id 胸章的自动编号
     * @return 影响的行数
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_LOWER')")
    @GetMapping("lower/{id}")
    @ResponseBody
    public String lower(@PathVariable("id") long id) {

        if (0 == campaignUgcService.lower(id)) {
            return ResultUtil.failToJson();
        }

        return ResultUtil.successToJson();

    }

    /**
     * 逻辑删除
     *
     * @param id 胸章的自动编号
     * @return 影响的行数
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_DEL')")
    @GetMapping("del/{id}")
    @ResponseBody
    public String del(@PathVariable("id") long id) {

        if (0 == campaignUgcService.del(id)) {
            return ResultUtil.failToJson();
        }

        return ResultUtil.successToJson();

    }

}
