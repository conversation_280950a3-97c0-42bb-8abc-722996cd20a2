package cn.slashsoft.clinique.dao.official;

import cn.slashsoft.clinique.domain.mini.Wechat;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * 公众号相关的操作
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface OfficialDao {

    /**
     * 获取用户信息
     *
     * @param unionid 开放平台唯一编号
     * @return 用户信息
     */
    @Select("SELECT " +
            "   `w`.`id`, " +
            "   `w`.`unionid`, " +
            "   `w`.`nick_name`, " +
            "   `c`.`phone_number` " +
            "FROM " +
            "   `customer` `c` " +
            "       inner join " +
            "   `wechat` `w` " +
            "       on `w`.`customer_id`=`c`.`id` " +
            "WHERE " +
            "   `w`.`unionid`=#{unionid} " +
            "LIMIT 1")
    Wechat getByUnionid(String unionid);

    /**
     * 写入公众号唯一编号
     *
     * @param wechat 微信信息
     */
    @Update("UPDATE " +
            "   `wechat` " +
            "SET " +
            "   `wechat_official_openid`=#{wechatOfficialOpenid} " +
            "WHERE " +
            "   `id`=#{id}")
    void setOfficialOpenid(Wechat wechat);


}
