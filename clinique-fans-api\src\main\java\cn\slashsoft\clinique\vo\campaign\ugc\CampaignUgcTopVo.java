package cn.slashsoft.clinique.vo.campaign.ugc;

import cn.slashsoft.clinique.domain.campaign.CampaignLaserTalentsTopDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignLaserTalentsTopDetailImage;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTopDetail;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTopDetailImage;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Data
public class CampaignUgcTopVo {

    private Date startTime;
    private Date endTime;

    private String shareTitle;
    private String shareImageUrl;
    
    private String title;

    private Short stage;
    private List<KocTopDetail> topDetailList;
    private List<KocTopDetailImage> topDetailImageList;


}
