package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.util.StringUtil;
import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 */
public class NoteProvider {

    /**
     * 查询所有礼品-分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 礼品列表
     */
    public String getPage(int pageIndex, int pageSize, String generalSearch, Short status) {

        return new SQL() {{
//`t`.`content`,
            SELECT("`t`.`id`,`t`.`customer_id`," +
                    "`w`.`nick_name` as ownerName,`w`.`avatar_url`," +
                    " ( SELECT count(d.`id`) from `note_discuss` d WHERE d.`note_id`= t.id ) 	`discussCount`, " +
                    "`t`.`status`,`t`.`title`,`t`.`createTime`");
            FROM("`note` `t` LEFT JOIN `wechat` `w` ON `t`.`customer_id`=`w`.`customer_id`");
            WHERE("1=1");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`t`.`title` LIKE '%${generalSearch}%'");
            }
            if (null != status && 0 <= status) {
                AND();
                WHERE("`t`.`status`=#{status}");
            }
            ORDER_BY("`t`.`id` DESC LIMIT #{pageIndex},#{pageSize}");

        }}.toString();

    }

    /**
     * 查询所有礼品带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    public String getPageCount(String generalSearch, Short status) {

        return new SQL() {{

            SELECT("COUNT(*)");
            FROM("`note` `t` ");
            WHERE("1=1");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`t`.`title` LIKE '%${generalSearch}%'");
            }
            if (null != status && 0 <= status) {
                AND();
                WHERE("`t`.`status`=#{status}");
            }

        }}.toString();

    }


    /**
     * 查询所有礼品-分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param top           状态
     * @return 礼品列表
     */
    public String getTagPage(int pageIndex, int pageSize, String generalSearch, Short top) {

        return new SQL() {{

            SELECT("`t`.* ");
            FROM("`note_tag` `t` ");
            WHERE("1=1");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`t`.`title` LIKE '%${generalSearch}%'");
            }
            if (null != top && 0 < top) {
                AND();
                WHERE("`t`.`top`=#{top}");
            }
            ORDER_BY("`t`.`id` DESC LIMIT #{pageIndex},#{pageSize}");

        }}.toString();

    }

    /**
     * 查询所有礼品带分页的记录数
     *
     * @param generalSearch 搜索
     * @param top           状态
     * @return 记录数
     */
    public String getTagPageCount(String generalSearch, Short top) {

        return new SQL() {{

            SELECT("COUNT(*)");
            FROM("`note_tag` `t` ");
            WHERE("1=1");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`t`.`title` LIKE '%${generalSearch}%'");
            }
            if (null != top && 0 < top) {
                AND();
                WHERE("`t`.`top`=#{top}");
            }

        }}.toString();

    }


    /**
     * 查询所有礼品-分页
     *
     * @param pageIndex 页面索引
     * @param pageSize  页面大小
     * @param noteId    状态
     * @return 礼品列表
     */
    public String getPhotoPage(int pageIndex, int pageSize, long noteId) {

        return new SQL() {{

            SELECT("`t`.* ");
            FROM("`note_photo` `t` ");
            WHERE("1=1");
//            if (!StringUtil.isNullOrEmpty(generalSearch)) {
//                AND();
//                WHERE("`t`.`title` LIKE '%${generalSearch}%'");
//            }
            if (0 < noteId) {
                AND();
                WHERE("`t`.`note_id`=#{noteId}");
            }
            ORDER_BY("`t`.`id` DESC LIMIT #{pageIndex},#{pageSize}");

        }}.toString();

    }

    /**
     * 查询所有礼品带分页的记录数
     *
     * @param noteId 状态
     * @return 记录数
     */
    public String getPhotoPageCount(long noteId) {

        return new SQL() {{

            SELECT("COUNT(*)");
            FROM("`note_photo` `t` ");
            WHERE("1=1");
//            if (!StringUtil.isNullOrEmpty(generalSearch)) {
//                AND();
//                WHERE("`t`.`title` LIKE '%${generalSearch}%'");
//            }
            if (0 < noteId) {
                AND();
                WHERE("`t`.`note_id`=#{noteId}");
            }

        }}.toString();

    }


    /**
     * 查询 笔记评论-分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 礼品列表
     */
    public String getDiscussPage(int pageIndex, int pageSize, String generalSearch, Short status, Long noteId) {

        return new SQL() {{

            SELECT("`t`.`id`,`t`.`customer_id`,`w`.`nick_name` as ownerName,`w`.`avatar_url`," +
                    "`t`.`status`,`t`.`createTime`,`t`.`content`,`t`.`updateTime`,`t`.`note_id`,`n`.`title` as  noteTitle");
            FROM("`note_discuss` `t` LEFT JOIN `wechat` `w` ON `t`.`customer_id`=`w`.`customer_id`  left join note n on t.note_id=n.id    ");
            WHERE("1=1");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`t`.`content` LIKE '%${generalSearch}%'");
            }
            if (null != status && 0 <= status) {
                AND();
                WHERE("`t`.`status`=#{status}");
            }
            if (null != noteId && 0 < noteId) {
                AND();
                WHERE("`t`.`note_id`=#{noteId}");
            }
            ORDER_BY("`t`.`id` DESC LIMIT #{pageIndex},#{pageSize}");

        }}.toString();

    }

    /**
     * 查询 笔记-评论 带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    public String getDiscussPageCount(String generalSearch, Short status, Long noteId) {

        return new SQL() {{

            SELECT("COUNT(*)");
            FROM("`note_discuss` `t` ");
            WHERE("1=1");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`t`.`content` LIKE '%${generalSearch}%'");
            }
            if (null != status && 0 <= status) {
                AND();
                WHERE("`t`.`status`=#{status}");
            }
            if (null != noteId && 0 < noteId) {
                AND();
                WHERE("`t`.`note_id`=#{noteId}");
            }
        }}.toString();

    }
}
