package cn.slashsoft.clinique.service.campaign.impl;

import cn.slashsoft.clinique.dao.mini.PointDao;
import cn.slashsoft.clinique.dao.campaign.CampaignEbciSignDao;
import cn.slashsoft.clinique.domain.mini.PointTransaction;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciSign;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciSignDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciSignLog;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciSignViewLog;
import cn.slashsoft.clinique.enums.PointForeignEnum;
import cn.slashsoft.clinique.enums.PointTypeEnum;
import cn.slashsoft.clinique.service.mini.MotService;
import cn.slashsoft.clinique.service.campaign.CampaignEbciSignService;
import cn.slashsoft.clinique.util.DateUtil;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * EBCI 7日打卡活动
 *
 * <AUTHOR>
 */
@Service
public class CampaignEbciSignServiceImpl implements CampaignEbciSignService {

    private final CampaignEbciSignDao campaignEbciSignDao;
    private final PointDao pointDao;
    private final MotService motService;

    public CampaignEbciSignServiceImpl(CampaignEbciSignDao campaignEbciSignDao, PointDao pointDao, MotService motService) {
        this.campaignEbciSignDao = campaignEbciSignDao;
        this.pointDao = pointDao;
        this.motService = motService;
    }

    /**
     * 获取签到信息
     *
     * @param customerId 顾客编号
     * @return 签到信息
     */
    @Override
    public CampaignEbciSign getCampaignEbciSign(long customerId) {

        CampaignEbciSign campaignEbciSign = campaignEbciSignDao.getCampaignEbciSign(customerId);

        if(null == campaignEbciSign){
            campaignEbciSign = new CampaignEbciSign();
            campaignEbciSign.setCustomerId(customerId);
            campaignEbciSign.setApply(false);
            campaignEbciSign.setLevel((short) 0);
            campaignEbciSign.setInviting(false);
            campaignEbciSign.setInviteTotal((short) 0);
            campaignEbciSignDao.insertCampaignEbciSign(campaignEbciSign);
        }

        return campaignEbciSign;
    }

    /**
     * 获取签到日志
     *
     * @param customerId 顾客编号
     * @return 签到日志
     */
    @Override
    public List<CampaignEbciSignLog> getCampaignEbciSignLog(long customerId) {
        return campaignEbciSignDao.getCampaignEbciSignLog(customerId);
    }

    /**
     * 签到
     *
     * @param customerId 顾客编号
     * @return 签到次数
     */
    @Override
    public Short sign(long customerId) {

        CampaignEbciSign campaignEbciSign = campaignEbciSignDao.getCampaignEbciSign(customerId);
        if (null == campaignEbciSign
                // 已经申领
                || campaignEbciSign.getApply()
                // 已经签完
                || 7 <= campaignEbciSign.getLevel()
                // 邀请中，耒邀请到人
                || (campaignEbciSign.getInviting() && 0 == campaignEbciSign.getInviteTotal())
                // 非邀请中，非首次签到，签到时间不是昨天
                || (!campaignEbciSign.getInviting() && 0 < campaignEbciSign.getLevel() && !DateUtil.isYesterday(campaignEbciSign.getLevelTime()))
        ){
            return 0;
        }

        campaignEbciSign.setCustomerId(customerId);

        if(0 == campaignEbciSignDao.sign(campaignEbciSign)){
            return 0;
        }

        Short level = (short) (campaignEbciSign.getLevel() + 1);

        // 写入日志
        CampaignEbciSignDetail campaignEbciSignDetail = new CampaignEbciSignDetail();
        campaignEbciSignDetail.setCustomerId(customerId);
        campaignEbciSignDetail.setRounds(campaignEbciSign.getRounds());
        campaignEbciSignDetail.setLevel(level);
        campaignEbciSignDao.insertCampaignEbciSignDetail(campaignEbciSignDetail);

        // 第5次发放MOT
        if (5 == level){
            // 发送MOT
            motService.ebciSign5(customerId);
        }
        // 第7次发放积分
        else if (7 == level){
            // 发放积分
            Date now = new Date();
            PointTransaction pointTransaction = new PointTransaction();
            pointTransaction.setCustomerId(customerId);
            pointTransaction.setPointTypeId(PointTypeEnum.EBCI_SIGN.getId());
            pointTransaction.setPoints(PointTypeEnum.EBCI_SIGN.getPoints());
            pointTransaction.setRemainingPoints(PointTypeEnum.EBCI_SIGN.getPoints());
            pointTransaction.setStartTime(now);
            pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
            pointTransaction.setForeignId(PointForeignEnum.EBCI_SIGN.getId());
            pointTransaction.setForeignMasterId(0L);
            pointTransaction.setForeignDetailId(0L);
            pointDao.insertPointTransaction(pointTransaction);

            CampaignEbciSignLog campaignEbciSignLog = new CampaignEbciSignLog();
            campaignEbciSignLog.setType((short) 6);
            campaignEbciSignLog.setCustomerId(customerId);
            campaignEbciSignLog.setContent("完成全部 7 次打卡，获得 100 积分");
            campaignEbciSignDao.insertCampaignEbciSignLog(campaignEbciSignLog);

            // 发送MOT
            motService.ebciSign(customerId);
        }
        else{
            CampaignEbciSignLog campaignEbciSignLog = new CampaignEbciSignLog();
            campaignEbciSignLog.setType((short) 2);
            campaignEbciSignLog.setCustomerId(customerId);
            campaignEbciSignLog.setContent("完成 1 次打卡");
            campaignEbciSignDao.insertCampaignEbciSignLog(campaignEbciSignLog);
        }

        return level;
    }

    /**
     * 重置
     *
     * @param customerId 顾客编号
     * @return 是否成功
     */
    @Override
    public boolean reset(long customerId) {
        if (0 < campaignEbciSignDao.reset(customerId)){
            CampaignEbciSignLog campaignEbciSignLog = new CampaignEbciSignLog();
            campaignEbciSignLog.setType((short) 3);
            campaignEbciSignLog.setCustomerId(customerId);
            campaignEbciSignLog.setContent("选择【重新开始】");
            campaignEbciSignDao.insertCampaignEbciSignLog(campaignEbciSignLog);

            return true;
        }
        return false;
    }

    /**
     * 邀请
     *
     * @param customerId 顾客编号
     * @return 是否成功
     */
    @Override
    public boolean inviting(long customerId) {
        if (0 < campaignEbciSignDao.inviting(customerId)){
            CampaignEbciSignLog campaignEbciSignLog = new CampaignEbciSignLog();
            campaignEbciSignLog.setType((short) 4);
            campaignEbciSignLog.setCustomerId(customerId);
            campaignEbciSignLog.setContent("选择【邀请好友】，开启本次邀请");
            campaignEbciSignDao.insertCampaignEbciSignLog(campaignEbciSignLog);

            return true;
        }
        return false;
    }

    /**
     * 邀请
     *
     * @param customerId 顾客编号
     * @return 是否成功
     */
    @Override
    public boolean invited(long customerId) {
        if (0 < campaignEbciSignDao.invited(customerId)){
            CampaignEbciSignLog campaignEbciSignLog = new CampaignEbciSignLog();
            campaignEbciSignLog.setType((short) 5);
            campaignEbciSignLog.setCustomerId(customerId);
            campaignEbciSignLog.setContent("回来继续打卡啦");
            campaignEbciSignDao.insertCampaignEbciSignLog(campaignEbciSignLog);

            return true;
        }
        return false;
    }

    /**
     * 申领
     *
     * @param customerId 顾客编号
     * @return 是否成功
     */
    @Override
    public boolean apply(long customerId) {
        return 0 < campaignEbciSignDao.apply(customerId);
    }

    /**
     * 写入访问开始日志
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @return 访问日志编号
     */
    @Override
    public long insertCampaignEbciSignViewLog(long customerId, String source) {
        CampaignEbciSignViewLog campaignEbciSignViewLog = new CampaignEbciSignViewLog();
        campaignEbciSignViewLog.setCustomerId(customerId);
        campaignEbciSignViewLog.setSource(source);
        campaignEbciSignDao.insertCampaignEbciSignViewLog(campaignEbciSignViewLog);

        return campaignEbciSignViewLog.getId();
    }

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Override
    public void setCampaignEbciSignViewLog(long id) {
        campaignEbciSignDao.setCampaignEbciSignViewLog(id);
    }

}
