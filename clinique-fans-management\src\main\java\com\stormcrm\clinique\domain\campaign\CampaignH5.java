package com.stormcrm.clinique.domain.campaign;

import lombok.Data;

import java.util.Date;

/**
 * 申领活动
 * <AUTHOR>
 */
@Data
public class CampaignH5 {

    private Long id;
    private Long couponCampaignId;
    private Short type;
    private String name;
    //用在url
    private String code;
    //引导页背景图
    private String lauchBg;
    //申领页背景图
    private String applyBg;
    private String rule;
    
    private String appId;
    private String linkUrl;
    
    private Boolean status;
    
    private Date startTime;
    private Date endTime;

    private Long snsId;
    private Long motId;
}
