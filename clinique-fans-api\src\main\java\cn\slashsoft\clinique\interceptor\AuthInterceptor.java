package cn.slashsoft.clinique.interceptor;

import cn.slashsoft.clinique.util.ServerUtil;
import cn.slashsoft.clinique.util.StringUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.net.URLEncoder;
import java.util.Enumeration;

/**
 * 公众号授权拦截器
 *
 * <AUTHOR>
 */
@Component
public class AuthInterceptor implements HandlerInterceptor {

    @Value("${wechat.official.authorize}")
    private String urlAuthorize;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
    	
  		
        // 读取Session中的openid
        String openid = (String) request.getSession().getAttribute("officialOpenid");

        if (StringUtil.isNullOrEmpty(openid)) {
            request.getSession().setAttribute("officialOauthPath", ServerUtil.getFullUrl(request, "/fans"));
            String redirectUri = URLEncoder.encode(ServerUtil.getDomain(request) + "/fans/official/auth" + ServerUtil.getQuery(request), "UTF-8");
            // 跳转至授权页面
            response.sendRedirect(urlAuthorize + redirectUri);
            return false;
        }

        request.setAttribute("officialOpenid", openid);
        return true;

    }

}
