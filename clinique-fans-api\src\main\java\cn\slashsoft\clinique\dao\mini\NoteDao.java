package cn.slashsoft.clinique.dao.mini;


import cn.slashsoft.clinique.domain.mini.*;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * 笔记
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface NoteDao {

    /**
     * 获取点赞排名，从高到底
     *
     * @param idList 标签编号
     * @param size  数量
     * @return 排名列表
     */
    @Select({"<script>" +
            "SELECT " +
            "   `n`.`id`, " +
            "   `w`.`nick_name` `name`, " +
            "   `w`.`avatar_url`, " +
            "   `n`.`like_count` " +
            "FROM " +
            "   (" +
            "   SELECT " +
            "       `id`, " +
            "       `customer_id`, " +
            "       MAX(`like_count`) `like_count` " +
            "   FROM `note` `n` " +
            "   WHERE <![CDATA[`customer_id`<>677575 AND `n`.`status` in (2,3,4) and `n`.`like_count`>0]]> " +
            "       <foreach collection='idList' item='id'><![CDATA[and (select count(`id`) from `note_photo_tag` where `note_id`=`n`.`id` and `tag_id`=#{id})>0]]></foreach>" +
            "   GROUP BY `n`.`customer_id` " +
            "   ORDER BY `n`.`like_count` DESC " +
            "   LIMIT #{size} " +
            ") `n` " +
            "INNER JOIN `wechat` `w` ON `n`.`customer_id`=`w`.`customer_id`" +
            "</script>"})
    List<NoteRanking> getRankingList(@Param("idList") List<Integer> idList, @Param("size") int size);

    /**
     * 获取点赞排名的缓存
     *
     * @param tagIds 标签编号
     * @param size  数量
     * @return 排名列表
     */
    @Select("SELECT " +
            "   `w`.`nick_name` `name`," +
            "   `w`.`avatar_url`," +
            "   `r`.`like_count` " +
            "FROM " +
            "   `note_tag_ranking` `r` " +
            "   INNER JOIN `wechat` `w` ON `r`.`customer_id`=`w`.`customer_id` " +
            "WHERE `r`.`tag_id`=#{tagIds} AND `r`.`status`=1  " +
            "ORDER BY `r`.`like_count` DESC " +
            "LIMIT #{size}")
    List<NoteRanking> getRankingListFromCache(String tagIds, int size);

    /**
     * 保存笔记
     *
     * @param note
     */
    @Insert("INSERT INTO `note`(" +
            "   `customer_id`, " +
            "   `title`, " +
            "   `content`, " +
            "   `status`, " +
            "   `createTime` " +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{title}, " +
            "   #{content}, " +
            "   #{status}, " +
            "   now() " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    Long insertNote(Note note);

    /**
     * 保存笔记
     *
     * @param note
     */
    @Insert("INSERT INTO `campaign_2408_note`(" +
            "   `campaign_flag`, " +
            "   `note_id`, " +
            "   `customer_id`, " +
            "   `title`, " +
            "   `content`, " +
            "   `status`, " +
            "   `createTime` " +
            ") " +
            "VALUES (" +
            "   #{campaignFlag}, " +
            "   #{noteid}, " +
            "   #{customerId}, " +
            "   #{title}, " +
            "   #{content}, " +
            "   #{status}, " +
            "   now() " +
            ")")
    Long insertCamNote(Long customerId,Object title,Object content,Long noteid,int status,String campaignFlag);


    /**
     * 更新笔记
     *
     * @param note
     */
    @Update("UPDATE `note` SET " +
            "   `title` = #{title}, " +
            "   `content` = #{content}, `status`=0 " +
            " WHERE `id` = #{id} ")
    Long updateNote(Note note);

    /**
     * 删除笔记图片
     *
     * @param id
     */
    @Delete("DELETE FROM `note_photo`" +
            " WHERE " +
            " `note_id`=#{Id} "
    )
    void delPhotosByNote(Long id);

    /**
     * 删除笔记tag
     *
     * @param id
     */
    @Delete("DELETE FROM `note_photo_tag`" +
            " WHERE " +
            " `note_id`=#{Id} "
    )
    void delPhotoTagsByNote(Long id);

    /**
     * 删除笔记
     *
     * @param noteId,customerId
     */
    @Delete("DELETE FROM `note`" +
            " WHERE " +
            " `Id`=#{noteId} AND `customer_id` = #{customerId}"
    )
    int delNote(Long noteId, Long customerId);

    /**
     * 删除笔记
     *
     * @param noteId,customerId
     */
    @Delete("DELETE FROM `campaign_2408_note`" +
            " WHERE " +
            " `note_id`=#{noteId} AND `customer_id` = #{customerId} and campaign_flag = #{campaignFlag}  "
    )
    int delCamDiscussg(Long noteId, Long customerId,String campaignFlag);


    /**
     * 保存笔记图片
     *
     * @param photo
     */
    @Insert("INSERT INTO `note_photo` (" +
            "   `note_id`, " +
            "   `photo`, " +
            "   `status` " +
            " ) " +
            "VALUES (" +
            "   #{noteId}, " +
            "   #{photo}, " +
            "   #{status} " +
            " )")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    Long insertNotePhoto(NotePhoto photo);

    /**
     * 保存图片标签
     *
     * @param phototag
     */
    @Insert("INSERT INTO `note_photo_tag`(" +
            "   `note_id`, " +
            "   `photo_id`, " +
            "   `tag_id`, " +
            "   `tag_title`, " +
            "   `tag_type`, " +
            "   `position`, " +
            "   `status` " +
            ") " +
            "VALUES (" +
            "   #{noteId}, " +
            "   #{photoId}, " +
            "   #{tagId}, " +
            "   #{title}, " +
            "   #{type}, " +
            "   #{position}, " +
            "   #{status} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    Long insertNotePhotoTag(NotePhotoTag phototag);

    /**
     * 保存笔记评论
     *
     * @param noteDiscuss
     */
    @Insert("INSERT INTO `note_discuss`(" +
            "   `note_id`, " +
            "   `customer_id`, " +
            "   `content`, " +
            "   `status` " +
            ") " +
            "VALUES (" +
            "   #{noteId}, " +
            "   #{customerId}, " +
            "   #{content}, " +
            "   #{status} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insertDiscuss(NoteDiscuss noteDiscuss);

    /**
     * 保存笔记评论
     *
     * @param noteDiscuss
     */
    @Insert("INSERT INTO `campaign_2408_note_discuss`(" +
            "   `campaign_flag`, " +
            "   `note_id`, " +
            "   `discuss_id`, " +
            "   `customer_id`, " +
            "   `content`, " +
            "   `status` " +
            ") " +
            "VALUES (" +
            "   #{campaignFlag}, " +
            "   #{noteId}, " +
            "   #{discussId}, " +
            "   #{customerId}, " +
            "   #{content}, " +
            "   #{status} " +
            ")")
    int insCamDiscussg(Long noteId,Long discussId, Long customerId,String content,Integer status,String campaignFlag);

    /**
     * 保存阅读笔记日志
     *
     * @param log 日志
     */
    @Insert("INSERT INTO `note_read_log`(" +
            "   `note_id`, " +
            "   `share_customer_id`, " +
            "   `customer_id` " +
            ") " +
            "VALUES (" +
            "   #{noteId}, " +
            "   #{shareCustomerId}, " +
            "   #{customerId} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insertReadLog(NoteReadLog log);

    /**
     * 保存拔草笔记日志
     *
     * @param log
     */
    @Insert("INSERT INTO `note_like_log`(" +
            "   `note_id`, " +
            "   `customer_id` " +
            ") " +
            "VALUES (" +
            "   #{noteId}, " +
            "   #{customerId} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insertLikeLog(NoteLikeLog log);

    /**
     * 保存拔草笔记日志
     *
     * @param log
     */
    @Insert("INSERT INTO `campaign_2408_note_like_log`(" +
            "   `campaign_flag`, " +
            "   `note_id`, " +
            "   `customer_id` " +
            ") " +
            "VALUES (" +
            "   #{campaignFlag}, " +
            "   #{noteId}, " +
            "   #{customerId} " +
            ")")
    int insCamLikeLog(Long noteId,Long customerId,String campaignFlag);

    /**
     * 删除拔草笔记日志
     *
     * @param log
     */
    @Insert("DELETE FROM `note_like_log` " +
            " WHERE  `note_id` =  #{noteId} AND " +
            "  `customer_id` = #{customerId} ")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int         delLikeLog(NoteLikeLog log);

    /**
     * 删除拔草笔记日志
     *
     * @param log
     */
    @Insert("DELETE FROM `campaign_2408_note_like_log` " +
            " WHERE  `note_id` =  #{noteId} AND  `campaign_flag` =  #{campaignFlag} AND  " +
            "  `customer_id` = #{customerId} ")
    int delCamLikeLog(Long noteId,Long customerId,String campaignFlag);


    /**
     * 保存分享笔记日志
     *
     * @param log
     */
    @Insert("INSERT INTO `note_share_log`(" +
            "   `note_id`, " +
            "   `share_sign`, " +
            "   `customer_id` " +
            ") " +
            "VALUES (" +
            "   #{noteId}, " +
            "   #{shareSign}, " +
            "   #{customerId} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insertShareLog(NoteShareLog log);

    /**
     * 保存标签搜索、使用日志
     *
     * @param log
     */
    @Insert("INSERT INTO `note_tag_search_log`(" +
            "   `tag_id`, " +
            "   `tag_title`, " +
            "   `tag_type`, " +
            "   `customer_id` " +
            ") " +
            "VALUES (" +
            "   #{tagId}, " +
            "   #{tagTitle}, " +
            "   #{tagType}, " +
            "   #{customerId} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insertTagSearchLog(NoteTagSearchLog log);

    /**
     * 保存位置、地址日志
     *
     * @param log
     */
    @Insert("INSERT INTO `note_tag_address_log`(" +
            "   `latitude`, " +
            "   `longitude`, " +
            "   `address`, " +
            "   `customer_id` " +
            ") " +
            "VALUES (" +
            "   #{latitude}, " +
            "   #{longitude}, " +
            "   #{address}, " +
            "   #{customerId} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insertTagAddressLog(NoteTagAddressLog log);

    /**
     * 笔记是否拔草
     *
     * @param noteId,customerId
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `note_like` " +
            "WHERE " +
            "   `customer_id`=#{customerId} AND " +
            "   `note_id`=#{noteId} " +
            "LIMIT 1")
    NoteLikeLog liked(Long noteId, Long customerId);

    /**
     * 获取top笔记标签列表
     */
    @Select("SELECT " +
            "   `id`, `type`,`image`,`title` " +
            "FROM " +
            "   `note_tag` " +
            "WHERE " +
            "   `status`=1 " +
            "   AND `top`=1 ")
    ArrayList<NoteTag> getTopTagList();

    /**
     * 获取top笔记标签列表
     */
    @Select("SELECT " +
            "   `id`, `type`,`image`,`title` " +
            "FROM " +
            "   `note_tag` " +
            "WHERE " +
            "   `status`=1 " +
            "   AND `top_index`=1 ")
    ArrayList<NoteTag> getIndexTopTagList();

    /**
     * 获取top笔记标签列表
     */
    @Select("SELECT " +
            "   `id`, `type`,`image`,`title` " +
            "FROM " +
            "   `note_tag` " +
            "WHERE " +
            "   `status`=1 " +
            "   AND `top_search`=1 ")
    ArrayList<NoteTag> getSearchTopTagList();

    /**
     * 获取热门笔记标签列表
     */
    @Select("SELECT "
            + " a.`tag_id` id,b.`title`,b.`type`,b.`image`" +
            " FROM `note_tag_search_log`  a " +
            " LEFT JOIN `note_tag`  b " +
            " ON a.`tag_id` = b.`id` " +
            " WHERE a.`tag_id` > 1 " +
            "   AND `b`.`status`=1 " +
            // " AND a.`tag_type` = #{type} " +
            //" AND a.`tag_id` not in ( #{hasStr} ) " +
            " GROUP BY a.`tag_id` " +
            " order by count(*) DESC "
            // + " limit #{number} "
    )
    ArrayList<NoteTag> getHotTagList(int type); //int number, String hasStr,

    /**
     * 搜索笔记标签
     *
     * @param key
     * @param type
     */
    @Select("SELECT " +
            "   `id`, `type`,`image`,`title` " +
            "FROM " +
            "   `note_tag` " +
            "WHERE " +
            "   `status`=1 " +
            "   AND `title` like concat('%', #{key}, '%') "
            + " AND `type` = #{type}")
    ArrayList<NoteTag> getTagList(String key, int type);

    /**
     * 获取标签
     *
     * @param tagId
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `note_tag` " +
            "WHERE " +
            "   `id` = #{tagId} ")
    NotePhotoTag getNoteTag(Long tagId);

    /**
     * 获取标签分组列表
     *
     * @return 分组列表
     */
    @Select("SELECT `id`,`title` FROM `note_tag_group` WHERE `status`=1 ORDER BY `sort` DESC")
    List<NoteTagGroup> getNoteTagGroupList();

    /**
     * 获取笔记详情
     *
     * @param id,customerId
     */
    @Select("SELECT " +
            "   a.*, b.`photo` `coverPhoto`, " +
            "   `w`.`nick_name` `ownerName`, " +
            " k.`customer_id` `koc_v`, " +
            "   `w`.`avatar_url` `avatarUrl` , " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id ) 	`likeCount`, " +
            " ( `a`.`customer_id` = #{customerId} ) 	`self`, " +
            " ( SELECT count(`id`) from `note_read_log` WHERE `note_id`= a.id ) 	`readCount`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id AND `customer_id`=#{customerId} ) 	`liked` " +
            "FROM " +
            "   `note` a "
            + " LEFT JOIN "
            + "`note_photo` b "
            + " ON a.id=b.note_id " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`customer_id`=`w`.`customer_id` "
            + " LEFT JOIN `customer_special_koc_v` k "
            + " ON k.`customer_id` = `a`.`customer_id` " +
            "WHERE " +
            "   a.`id`=#{id} AND ( a.`status`>1 OR a.`customer_id` =#{customerId} ) " +
            "LIMIT 1")
    Note getNote(Long id, Long customerId);

    /**
     * 获取笔记状态
     *
     * @param id
     */
    @Select("SELECT " +
            "   a.status " +
            "FROM " +
            "   `note` a " +
            "WHERE " +
            "   a.`id`=#{id}  " +
            "LIMIT 1")
    Note noteStatus(Long id);

    /**
     * 获取笔记图片
     *
     * @param noteId
     */

    @Select("SELECT " +
            "  `photo`,`id` " +
            "FROM " +
            "   `note_photo` " +
            "WHERE " +
            "   `note_id`=#{noteId} ")
    ArrayList<NotePhoto> getNotePhotos(Long noteId);

    /**
     * 获取笔记图片tag
     *
     * @param photoId
     */

    @Select("SELECT " +
            "  a.`position`, " +
            " a.`tag_title` `title`,a.`tag_type` `type`, a.`tag_id` `tag_id`  " +
            "FROM " +
            "   `note_photo_tag` a " +
            // " left JOIN `note_tag` b " +
            // " ON a.tag_id = b.id " +
            "WHERE " +
            "   a.`photo_id`=#{photoId} ")
    ArrayList<NotePhotoTag> getNotePhotoTags(Long photoId);

    /**
     * 获取笔记数目
     *
     * @param noteId
     */

    @Select("SELECT " +
            "   a.*, " +
            "   `w`.`nick_name` `ownerName`, " +
            "   `w`.`avatar_url` `avatarUrl`  " +
            "FROM " +
            "   `note_discuss` a " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "   `note_id`=#{noteId} ")
    ArrayList<NoteDiscuss> getNoteDiscuss(Long noteId);

    /**
     * 获取笔记数目
     *
     * @param tagId
     */

    @Select("SELECT " +
            "   count(*) " +
            "FROM " +
            "   `note` " +
            "WHERE " +
            "   `status` > 1 AND id in "
            + " ( "
            + " SELECT `note_id` FROM `note_photo_tag` WHERE tag_id = #{tagId} "
            + ") ")
    int getNoteCountByTag(Long tagId);

    @Select("SELECT " +
            "  count(*) " +
            "FROM " +
            "   `note` " +
            "WHERE " +
            "   `status` > 1 ")
    int getNoteCount();

    /**
     * 获取笔记数目
     *
     * @param groupId 组编号
     * @return 笔记数目
     */
    @Select("SELECT " +
            "   COUNT(`note_id`) " +
            "FROM " +
            "   `note_tag_group_tag` `g` INNER JOIN `note_photo_tag` `p` ON `g`.`tag_id`=`p`.`tag_id` " +
            "WHERE " +
            "   `g`.`group_id`=#{groupId}")
    int getNoteCountByGroup(long groupId);

    /**
     * 获取笔记列表
     *
     * @param tagId,customerId,start,pageSize
     */
    @Select("SELECT " +
            "   a.title,a.id,a.status, b.`photo` `coverPhoto`, " +
            "   `w`.`nick_name` `ownerName`, " +
            "   `w`.`avatar_url` `avatarUrl` , " +
            " k.`customer_id` `koc_v`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id ) `likeCount`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id AND `customer_id`=#{customerId} ) 	`liked` " +
            "FROM " +
            "   `note` a "
            + " LEFT JOIN "
            + "`note_photo` b "
            + " ON a.id=b.note_id " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`customer_id`=`w`.`customer_id` "
            + " LEFT JOIN `customer_special_koc_v` k "
            + " ON k.`customer_id` = `a`.`customer_id` " +
            "WHERE " +
            "   a.`status` > 1 AND a.id in "
            + " ( "
            + " SELECT `note_id` FROM `note_photo_tag` WHERE tag_id = #{tagId} "
            + " ) "
            + " GROUP BY a.id ORDER BY a.`status` DESC, `likeCount` DESC  " +
            "LIMIT #{start}, #{pageSize}")
    ArrayList<Note> getNoteListByTag(Long tagId, Long customerId, int start, int pageSize);

    /**
     * 获取笔记列表
     *
     * @param tagId,customerId,start,pageSize
     */
    @Select("SELECT " +
            "   a.title,a.id,a.status, b.`photo` `coverPhoto`, " +
            "   `w`.`nick_name` `ownerName`, " +
            "   `w`.`avatar_url` `avatarUrl` , " +
            " k.`customer_id` `koc_v`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id ) `likeCount`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id AND `customer_id`=#{customerId} ) 	`liked` " +
            "FROM " +
            "   `note` a "
            + " LEFT JOIN "
            + "`note_photo` b "
            + " ON a.id=b.note_id " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`customer_id`=`w`.`customer_id` "
            + " LEFT JOIN `customer_special_koc_v` k "
            + " ON k.`customer_id` = `a`.`customer_id` " +
            "WHERE " +
            "   a.`status` > 1 AND a.id in "
            + " ( "
            + " SELECT `note_id` FROM `note_photo_tag` WHERE tag_id = #{tagId} "
            + " ) "
            + " GROUP BY a.id ORDER BY a.`status` DESC, a.`createTime` DESC  " +
            "LIMIT #{start}, #{pageSize}")
    ArrayList<Note> getNewNoteListByTag(Long tagId, Long customerId, int start, int pageSize);

    @Select("SELECT " +
            "   a.title,a.id,a.status, b.photo coverPhoto, " +
            "   `w`.`nick_name` ownerName, " +
            "   `w`.`avatar_url` avatarUrl , " +
            " k.`customer_id` `koc_v`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id ) 	`likeCount`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id AND `customer_id`=#{customerId} ) 	`liked` " +
            "FROM " +
            "   `note` a "
            + " LEFT JOIN "
            + "`note_photo` b "
            + " ON a.id=b.note_id " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`customer_id`=`w`.`customer_id` "
            + " LEFT JOIN `customer_special_koc_v` k "
            + " ON k.`customer_id` = `a`.`customer_id` " +
            // "WHERE " +
            // "   a.`status` > 1 "
            "WHERE " +
            "   a.`status` =2 AND a.`customer_id` not  in "
            + " ( "
            + " SELECT `customer_id` FROM `customer_random_note_always` "
            + " ) "
            + " GROUP BY a.id  ORDER BY a.`status` DESC, `likeCount` DESC " +
            "LIMIT #{start}, #{pageSize}")
    ArrayList<Note> getNoteList(Long customerId, int start, int pageSize);


    /**
     * 获取笔记列表
     *
     * @param tagId,customerId,start,pageSize
     */
    @Select("SELECT " +
            "   a.title,a.id,a.status, b.`photo` `coverPhoto`, " +
            "   `w`.`nick_name` `ownerName`, " +
            "   `w`.`avatar_url` `avatarUrl` , " +
            " k.`customer_id` `koc_v`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id ) `likeCount`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id AND `customer_id`=#{customerId} ) 	`liked` " +
            "FROM " +
            "   `note` a "
            + " LEFT JOIN "
            + "`note_photo` b "
            + " ON a.id=b.note_id " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`customer_id`=`w`.`customer_id` "
            + " LEFT JOIN `customer_special_koc_v` k "
            + " ON k.`customer_id` = `a`.`customer_id` " +
            "WHERE " +
            "   a.`status` > 1 AND a.id in "
            + " ( "
            + " SELECT `p`.`note_id` FROM `note_photo_tag` `p` inner join `note_tag_group_tag` `g` on `p`.`tag_id`=`g`.`tag_id` WHERE `g`.`group_id`=#{groupId} "
            + " ) "
            + " GROUP BY a.id ORDER BY `likeCount` DESC  " +
            "LIMIT #{start}, #{pageSize}")
    ArrayList<Note> getHotNoteListByGroup(Long groupId, Long customerId, int start, int pageSize);

    /**
     * 获取笔记列表
     *
     * @param tagId,customerId,start,pageSize
     */
    @Select("SELECT " +
            "   a.title,a.id,a.status, b.`photo` `coverPhoto`, " +
            "   `w`.`nick_name` `ownerName`, " +
            "   `w`.`avatar_url` `avatarUrl` , " +
            " k.`customer_id` `koc_v`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id ) `likeCount`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id AND `customer_id`=#{customerId} ) 	`liked` " +
            "FROM " +
            "   `note` a "
            + " LEFT JOIN "
            + "`note_photo` b "
            + " ON a.id=b.note_id " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`customer_id`=`w`.`customer_id` "
            + " LEFT JOIN `customer_special_koc_v` k "
            + " ON k.`customer_id` = `a`.`customer_id` " +
            "WHERE " +
            "   a.`status` > 1 AND a.id in "
            + " ( "
            + " SELECT `p`.`note_id` FROM `note_photo_tag` `p` inner join `note_tag_group_tag` `g` on `p`.`tag_id`=`g`.`tag_id` WHERE `g`.`group_id`=#{groupId} "
            + " ) "
            + " GROUP BY a.id ORDER BY a.`createTime` DESC " +
            "LIMIT #{start}, #{pageSize}")
    ArrayList<Note> getNewNoteListByGroup(Long groupId, Long customerId, int start, int pageSize);

    @Select("SELECT " +
            "   a.title,a.id,a.status, b.photo coverPhoto, " +
            "   `w`.`nick_name` ownerName, " +
            "   `w`.`avatar_url` avatarUrl , " +
            " k.`customer_id` `koc_v`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id ) 	`likeCount`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id AND `customer_id`=#{customerId} ) 	`liked` " +
            "FROM " +
            "   `note` a "
            + " LEFT JOIN "
            + "`note_photo` b "
            + " ON a.id=b.note_id " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`customer_id`=`w`.`customer_id` "
            + " LEFT JOIN `customer_special_koc_v` k "
            + " ON k.`customer_id` = `a`.`customer_id` " +
            // "WHERE " +
            // "   a.`status` > 1 "
            "WHERE " +
            "   a.`status` =2 AND a.`customer_id` not  in "
            + " ( "
            + " SELECT `customer_id` FROM `customer_random_note_always` "
            + " ) "
            + " GROUP BY a.id  ORDER BY a.`status` DESC, a.`createTime` DESC " +
            "LIMIT #{start}, #{pageSize}")
    ArrayList<Note> getNoteListByNew(Long customerId, int start, int pageSize);

    @Select("SELECT " +
            "   a.title,a.id,a.status, b.photo coverPhoto, " +
            "   `w`.`nick_name` ownerName, " +
            "   `w`.`avatar_url` avatarUrl , " +
            " k.`customer_id` `koc_v`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id ) 	`likeCount`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id AND `customer_id`=#{customerId} ) 	`liked` " +
            "FROM " +
            "   `note` a "
            + " LEFT JOIN "
            + "`note_photo` b "
            + " ON a.id=b.note_id " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`customer_id`=`w`.`customer_id` "
            + " LEFT JOIN `customer_special_koc_v` k "
            + " ON k.`customer_id` = `a`.`customer_id` " +
            "WHERE " +
            //"   a.`status` > 2 "
            "   a.`status` =4 "
            + " GROUP BY a.id  ORDER BY a.`status` DESC, `likeCount` DESC ")
    ArrayList<Note> getNoteListTop(Long customerId);

    @Select("SELECT " +
            "   a.title,a.id,a.status, b.photo coverPhoto, " +
            "   `w`.`nick_name` ownerName, " +
            "   `w`.`avatar_url` avatarUrl , " +
            " k.`customer_id` `koc_v`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id ) 	`likeCount`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id AND `customer_id`=#{customerId} ) 	`liked` " +
            "FROM " +
            "   `note` a "
            + " LEFT JOIN "
            + "`note_photo` b "
            + " ON a.id=b.note_id " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`customer_id`=`w`.`customer_id` "
            + " LEFT JOIN `customer_special_koc_v` k "
            + " ON k.`customer_id` = `a`.`customer_id` " +
            "WHERE " +
            "   a.`status` =3  "
            + " GROUP BY a.id  ORDER BY a.`status` DESC, a.`createTime` DESC " +
            "LIMIT #{page}, 1")
    ArrayList<Note> getNoteListThree(Long customerId, int page);

    @Select("SELECT " +
            "   a.title,a.id,a.status, b.photo coverPhoto, " +
            "   `w`.`nick_name` ownerName, " +
            "   `w`.`avatar_url` avatarUrl , " +
            " k.`customer_id` `koc_v`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id ) 	`likeCount`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id AND `customer_id`=#{customerId} ) 	`liked` " +
            "FROM " +
            "   `note` a "
            + " LEFT JOIN "
            + "`note_photo` b "
            + " ON a.id=b.note_id " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`customer_id`=`w`.`customer_id` "
            + " LEFT JOIN `customer_special_koc_v` k "
            + " ON k.`customer_id` = `a`.`customer_id` " +
            "WHERE " +
            "   a.`status` =2 AND a.`customer_id` not  in "
            + " ( "
            + " SELECT `customer_id` FROM `customer_random_note_always` "
            + " ) "
            + " GROUP BY a.id  ORDER BY a.`status` DESC, a.`createTime` DESC " +
            "LIMIT #{start}, #{pageSize}")
    ArrayList<Note> getNoteListNormal(Long customerId, int start, int pageSize);

    @Select("SELECT " +
            "  count(*) " +
            "FROM " +
            "   `note` " +
            "WHERE " +
            "   `status` =2  AND customer_id not in "
            + " ( "
            + " SELECT `customer_id` FROM `customer_random_note_always` "
            + " ) ")
    int getNoteListNormalCount();

    @Select("SELECT " +
            "   a.title,a.id,a.status, b.photo coverPhoto, " +
            "   `w`.`nick_name` ownerName, " +
            "   `w`.`avatar_url` avatarUrl , " +
            " k.`customer_id` `koc_v`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id ) 	`likeCount`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id AND `customer_id`=#{customerId} ) 	`liked` " +
            "FROM " +
            "   `note` a "
            + " LEFT JOIN "
            + "`note_photo` b "
            + " ON a.id=b.note_id " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`customer_id`=`w`.`customer_id` "
            + " LEFT JOIN `customer_special_koc_v` k "
            + " ON k.`customer_id` = `a`.`customer_id` " +
            "WHERE " +
            "   a.`status` =2  AND a.`customer_id`  in "
            + " ( "
            + " SELECT `customer_id` FROM `customer_random_note_always` "
            + " ) "
            + " GROUP BY a.id  ORDER BY a.`status` DESC, a.`createTime` " +
            "LIMIT #{start}, #{pageSize}")
    ArrayList<Note> getNoteListSpecial(Long customerId, int start, int pageSize);

    /**
     * 获取用户笔记列表
     *
     * @param customerId
     * @param start
     */
    @Select("SELECT " +
            "   a.title,a.id,a.status, b.photo coverPhoto, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id ) as	`likeCount`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id AND `customer_id`=#{customerId} ) as `liked` " +
            "FROM " +
            "   `note` a "
            + " LEFT JOIN "
            + "`note_photo` b "
            + " ON a.id=b.note_id "
            + "WHERE " +
            "   a.`customer_id`=#{customerId} "
            + " GROUP BY a.id  ORDER BY a.`createTime` DESC  ")
    ArrayList<Note> getCustomerNoteList(Long customerId, int start);

    /**
     * 获取用户拔草列表
     *
     * @param customerId
     * @param start
     */
    @Select("SELECT " +
            "   a.title,a.id,a.status, b.photo coverPhoto, " +
            "   `w`.`nick_name` ownerName, " +
            "   `w`.`avatar_url` avatarUrl , " +
            " k.`customer_id` `koc_v`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id ) 	`likeCount`, " +
            " ( SELECT count(`id`) from `note_like_log` WHERE `note_id`= a.id AND `customer_id`=#{customerId} ) 	`liked` " +
            "FROM " +
            "   `note` a "
            + " LEFT JOIN "
            + "`note_photo` b "
            + " ON a.id=b.note_id " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`customer_id`=`w`.`customer_id` "
            + " LEFT JOIN `customer_special_koc_v` k "
            + " ON k.`customer_id` = `a`.`customer_id` " +
            "WHERE " +
            "   a.`status` > 1 AND " +
            "   a.`id` in  ( SELECT `note_id` FROM `note_like_log` WHERE `customer_id` = #{customerId} ) "
            + " GROUP BY a.id  ORDER BY a.`status` DESC, `likeCount` DESC  ")
    ArrayList<Note> getCustomerLikeNoteList(Long customerId, int start);

    /**
     * 获取用户笔记评论列表
     *
     * @param customerId
     */
    @Select("SELECT " +
            "   a.`content`,a.`updateTime`,a.`id`,a.`status`, "
            + " b.`id` `noteId`, "
            + " b.`title` `title`, "
            + " c.`photo` `coverPhoto`, " +
            "   `w`.`nick_name` ownerName, " +
            "   `w`.`avatar_url` avatarUrl ,  "
            + " k.`customer_id` `koc_v` " +
            "FROM " +
            "   `note_discuss` a "
            + " JOIN `note` b "
            + " ON a.`note_id`=b.`id` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `b`.`customer_id`=`w`.`customer_id` "
            + " LEFT JOIN `customer_special_koc_v` k "
            + " ON k.`customer_id` = `a`.`customer_id` "
            + " LEFT JOIN "
            + "`note_photo` c "
            + " ON b.id=c.note_id " +
            "WHERE " +
            "   a.`customer_id`=#{customerId}  GROUP BY a.`id`  ORDER BY a.`updateTime` DESC ")
    ArrayList<NoteDiscussMy> getCustomerDiscussList(Long customerId);

    /**
     * 获取笔记评论列表
     *
     * @param noteId
     * @param start
     * @param pageSize
     */
    @Select("SELECT " +
            "   a.*, " +
            "   `w`.`nick_name` ownerName, " +
            "   `w`.`avatar_url` avatarUrl  ,  "
            + " k.`customer_id` `koc_v` " +
            "FROM " +
            "   `note_discuss` a " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`customer_id`=`w`.`customer_id` "
            + " LEFT JOIN `customer_special_koc_v` k "
            + " ON k.`customer_id` = `a`.`customer_id` " +

            "WHERE " +
            "   a.`note_id`=#{noteId} and a.`status`=1 ORDER BY a.`updateTime` DESC " +
            "LIMIT #{start}, #{pageSize}")
    ArrayList<NoteDiscuss> getNoteDiscussList(Long noteId, int start, int pageSize);

    /**
     * 获取笔记评论数目
     *
     * @param noteId
     */
    @Select("SELECT " +
            "   count(*) " +
            "FROM " +
            "   `note_discuss` a " +
            "WHERE " +
            "   a.`note_id`=#{noteId}  and a.`status`=1 ")
    int getNoteDiscussCount(Long noteId);


    /**
     * 是否存在阅读笔记日志
     *
     * @param log 日志
     */
    @Select("SELECT count(`id`) FROM  `note_read_log`" +
            "  WHERE `note_id`= #{noteId} AND " +
            "  `customer_id`= #{customerId} " +
            "")
    int getReadLogCount(NoteReadLog log);

    /**
     * 是否存在拔草笔记日志
     *
     * @param log 日志
     */
    @Select("SELECT count(`id`) FROM  `note_like_log`" +
            "  WHERE `note_id`= #{noteId} AND " +
            "  `customer_id`= #{customerId} " +
            "")
    int getLikeLogCount(NoteLikeLog log);

    /**
     * 是否存在笔记评论
     *
     * @param log 日志
     */
    @Select("SELECT count(`id`) FROM  `note_discuss`" +
            "  WHERE `note_id`= #{noteId} AND " +
            "  `customer_id`= #{customerId} " +
            "")
    int getDiscussCount(NoteDiscuss log);

    /**
     * 是否存在分享笔记日志
     *
     * @param log 日志
     */
    @Select("SELECT count(`id`) FROM  `note_share_log`" +
            "  WHERE `note_id`= #{noteId} AND " +
            "  `customer_id`= #{customerId} " +
            "")
    int getShareLogCount(NoteShareLog log);

    /**
     * 获取c粉社区首页Banner
     */
    @Select("SELECT * FROM  `cindex_banner`" +
            "  WHERE `status`= 1 and id not in (  123456792 , 123456793,123456799) order by `create_time` desc ")
    List<CIndexBanner> getCindexBanner();

    /**
     * 获取c粉社区首页Banner
     */
    @Select("SELECT * FROM  `cindex_banner`" +
            "  WHERE id=#{id} AND `status`=1 limit 1 ")
    CIndexBanner getCindexBannerById(Long id);

    @Select("SELECT count(*) FROM  `note_like_log`" +
            "  WHERE customer_id=#{cutomerId} AND to_days(createTime)=to_days(now()) ")
    int getTodayLikeCount(Long cutomerId);

    @Select("SELECT count(*) FROM  `note_discuss`" +
            "  WHERE customer_id=#{cutomerId} AND to_days(updateTime)=to_days(now()) ")
    int getTodayDiscussCount(Long cutomerId);

    /**
     * 读取每月限定发放次数
     *
     * @param customerId 顾客编号
     * @return 限定发放次数
     */
    @Select("SELECT " +
            "	`id`,`customer_id`,`count`,`last_month` " +
            "FROM " +
            "	`note_point_give_limit` " +
            "WHERE " +
            "	`customer_id`=#{customerId} " +
            "LIMIT 1")
    NotePointGiveLimit getNotePointGiveLimit(Long customerId);

    /**
     * 写入每月限定发放次数
     *
     * @param notePointGiveLimit 限定发放次数
     */
    @Insert("INSERT INTO `note_point_give_limit`(" +
            "   `customer_id`," +
            "   `count`," +
            "   `last_month`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{count}," +
            "   #{lastMonth}" +
            ")")
    void insertNotePointGiveLimit(NotePointGiveLimit notePointGiveLimit);

    /**
     * 更新每月限定发放次数
     *
     * @param notePointGiveLimit 限定发放次数
     */
    @Update("UPDATE " +
            "	`note_point_give_limit` " +
            "SET " +
            "   `count`=#{count}," +
            "   `last_month`=#{lastMonth} " +
            "WHERE " +
            "	`customer_id`=#{customerId} " +
            "LIMIT 1")
    void updateNotePointGiveLimit(NotePointGiveLimit notePointGiveLimit);

    /**
     * 更新喜欢记录
     *
     * @param id 帖子编号
     */
    @Update("UPDATE " +
            "	`note` " +
            "SET " +
            "	`like_count`=(SELECT COUNT(`id`) FROM `note_like_log` WHERE `note_id`=`note`.`id`) " +
            "WHERE " +
            "	`id`=#{id}")
    void updateNoteLikeCount(long id);

    @Select("SELECT * FROM `note` WHERE id =#{id}  and customer_id =#{customerId} ")
    Note getNoteById(Long id, long customerId);
}
