package com.stormcrm.clinique.dao.campaign.ugc;

import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgc;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignUgcDao {

    /**
     * 查询所有
     *
     * @return 列表
     */
    @Select("SELECT " +
            "   `id`," +
            "   `name`," +
            "   `start_time`, " +
            "   `end_time`, " +
            "   (select count(*) from `koc_detail` where `campaign_id`=`koc`.`id`) `detail_total`, " +
            "   (select count(*) from `koc_top` where `campaign_id`=`koc`.`id`) `top_total`, " +
            "   `status`, " +
            "   `update_time`, " +
            "   `create_time` " +
            "FROM " +
            "   `koc` " +
            "WHERE " +
            "   `recovery`=0 " +
            "ORDER BY `id` DESC")
    List<CampaignUgc> getAll();

    /**
     * 查询所有活动列表的记录数
     *
     * @return 记录数
     */
    @Select("SELECT " +
            "   COUNT(*) " +
            "FROM " +
            "   `koc` " +
            "WHERE " +
            "   `recovery`=0")
    int getAllCount();

    /**
     * 查询
     *
     * @param id 自动编号
     * @return 信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `name`," +
            "   `start_time`, " +
            "   `end_time`, " +
            "   `index_banner_image_url`," +
            "   `upload_banner_image_url`," +
            "   `rule_image_url`," +
            "   `fail_image_url`," +
            "   `share_title`," +
            "   `share_image_url` " +
            "FROM " +
            "   `koc` " +
            "WHERE " +
            "   `id`=#{id} AND " +
            "   `recovery`=0 " +
            "LIMIT 1"
    )
    CampaignUgc getById(long id);

    /**
     * 保存
     *
     * @param campaignUgc 信息
     */
    @Insert("INSERT INTO `koc`(" +
            "   `name`," +
            "   `start_time`, " +
            "   `end_time`, " +
            "   `index_banner_image_url`," +
            "   `upload_banner_image_url`," +
            "   `rule_image_url`," +
            "   `fail_image_url`," +
            "   `share_title`," +
            "   `share_image_url` " +
            ") " +
            "VALUES (" +
            "   #{name}, " +
            "   #{startTime}, " +
            "   #{endTime}, " +
            "   #{indexBannerImageUrl}, " +
            "   #{uploadBannerImageUrl}, " +
            "   #{ruleImageUrl}, " +
            "   #{failImageUrl}, " +
            "   #{shareTitle}, " +
            "   #{shareImageUrl} " +
            ")")
    void save(CampaignUgc campaignUgc);

    /**
     * 更新
     *
     * @param campaignUgc 信息
     */
    @Update("<script>" +
            "UPDATE " +
            "   `koc` " +
            "SET " +
            "   `name`=#{name}," +
            "   `start_time`=#{startTime}," +
            "   `end_time`=#{endTime}," +
            "   <if test='indexBannerImageUrl!=null'>`index_banner_image_url`=#{indexBannerImageUrl},</if>" +
            "   <if test='uploadBannerImageUrl!=null'>`upload_banner_image_url`=#{uploadBannerImageUrl},</if>" +
            "   <if test='ruleImageUrl!=null'>`rule_image_url`=#{ruleImageUrl},</if>" +
            "   <if test='failImageUrl!=null'>`fail_image_url`=#{failImageUrl},</if>" +
            "   <if test='shareImageUrl!=null'>`share_image_url`=#{shareImageUrl},</if>" +
            "   `share_title`=#{shareTitle} " +
            "WHERE " +
            "   `id`=#{id}" +
            "</script>"
    )
    void update(CampaignUgc campaignUgc);

    /**
     * 上架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Update("UPDATE " +
            "   `koc` " +
            "SET " +
            "   `status`=1 " +
            "WHERE " +
            "   `id`=#{id}"
    )
    int upper(long id);

    /**
     * 下架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Update("UPDATE " +
            "   `koc` " +
            "SET " +
            "   `status`=0 " +
            "WHERE " +
            "   `id`=#{id}"
    )
    int lower(long id);

    /**
     * 逻辑删除
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Update("UPDATE " +
            "   `koc` " +
            "SET " +
            "   `recovery`=1 " +
            "WHERE " +
            "   `id`=#{id}"
    )
    int del(long id);

}
