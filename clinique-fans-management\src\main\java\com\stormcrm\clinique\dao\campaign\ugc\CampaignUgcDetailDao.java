package com.stormcrm.clinique.dao.campaign.ugc;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgcDetail;
import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgcDetailPoint;
import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgcImage;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignUgcDetailDao {

    /**
     * 查询所有活动-分页
     *
     * @param campaignId    顾客编号
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 活动列表
     */
    @SelectProvider(type = CampaignUgcDetailProvider.class, method = "getPage")
    List<CampaignUgcDetail> getPage(
            @Param("campaignId") long campaignId,
            @Param("pageIndex") int pageIndex,
            @Param("pageSize") int pageSize,
            @Param("generalSearch") String generalSearch,
            @Param("status") Short status
    );

    /**
     * 查询所有活动带分页的记录数
     *
     * @param campaignId    顾客编号
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    @SelectProvider(type = CampaignUgcDetailProvider.class, method = "getPageCount")
    int getPageCount(
            @Param("campaignId") long campaignId,
            @Param("generalSearch") String generalSearch,
            @Param("status") Short status
    );

    /**
     * 获取美白达人官信息
     *
     * @param id 编号
     * @return 美白达人官信息
     */
    @Select("SELECT " +
            "   `t`.`id`," +
            "   `t`.`title`," +
            "   `t`.`campaign_id`," +
            "   `w`.`wechat_mini_openid`, " +
            "   `w`.`nick_name`, " +
            "   `w`.`avatar_url`, " +
            "   `t`.`customer_id`," +
            "   `t`.`status` " +
            "FROM " +
            "   `koc_detail` `t` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `t`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "   `t`.`id`=#{id} " +
            "LIMIT 1")
    CampaignUgcDetail getById(long id);

    /**
     * 获取美白达人官信息
     *
     * @param idList 编号
     * @return 美白达人官信息
     */
    @Select({"<script>" +
            "SELECT ",
            "   `t`.`id`,",
            "   `t`.`title`," +
            "   `t`.`campaign_id`,",
            "   `w`.`wechat_mini_openid`, ",
            "   `w`.`nick_name`, ",
            "   `w`.`avatar_url`, ",
            "   `t`.`customer_id`,",
            "   `t`.`status` ",
            "FROM ",
            "   `koc_detail` `t` ",
            "       INNER JOIN ",
            "   `wechat` `w` ",
            "       ON `t`.`customer_id`=`w`.`customer_id` ",
            "WHERE ",
            "   `t`.`id` IN <foreach collection='idList' item='id' open='(' separator=',' close=')'>#{id}</foreach>" +
                    "</script>"})
    List<CampaignUgcDetail> getByIds(@Param("idList") List<Long> idList);

    /**
     * 获取图片
     *
     * @param id 顾客活动信息编号
     * @return 图片地址
     */
    @Select("SELECT " +
            "   `image_url` " +
            "FROM " +
            "   `koc_image` " +
            "WHERE " +
            "   `detail_id`=#{id} and `status`= 1")
    List<CampaignUgcImage> getImageById(long id);

    /**
     * 审核成功
     *
     * @param id 编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `koc_detail` " +
            "SET " +
            "   `alert`=1," +
            "   `status`=4," +
            "   `examine_time`=NOW() " +
            "WHERE " +
            "   `id`=#{id} " +
            "   AND (`status`=2 OR `status`=3)")
    int accept(long id);

    /**
     * 审核失败
     *
     * @param id 编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `koc_detail` " +
            "SET " +
            "   `alert`=1," +
            "   `status`=3," +
            "   `examine_time`=NOW() " +
            "WHERE " +
            "   `id`=#{Id} " +
            "   AND `status`=2")
    int reject(long id);

    /**
     * 停用所有图片
     *
     * @param id 编号
     */
    @Update("UPDATE " +
            "   `koc_image` " +
            "SET " +
            "   `status`=0 " +
            "WHERE " +
            "   `detail_id`=#{id}")
    void setImageStatus(long id);

    @Select("select * from koc_detail_point where detail_id = #{detailId}")
    CampaignUgcDetailPoint getDetailPoint(Long detailId);
    
    @Insert("insert into koc_detail_point"
    		+ " (`count`,`use`,`percent`,`copy`,`point`,`detail_id`,`create_time`) "
    		+ " VALUES "
    		+ " (#{count},#{use},#{percent},#{copy},#{point},#{detailId},now()) "    	)
    int addDetailPoint(CampaignUgcDetailPoint point);
    

    @Update("update koc_detail_point set `count`=#{count}"
    		+ ",`use`=#{use},`percent`=#{percent},`copy`=#{copy},`point`=#{point}"
    		+ " where `detail_id` = #{detailId}")
    int updateDetailPoint(CampaignUgcDetailPoint point);

}
