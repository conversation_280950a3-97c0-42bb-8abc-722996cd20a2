package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Laser Focus 达人榜
 *
 * <AUTHOR>
 */
public interface CampaignLaserTalentsService {

    /**
     * 获取达人榜信息
     *
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    CampaignLaserTalents getCampaignLaserTalentsAlert(long customerId);

    /**
     * 获取达人榜信息
     *
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    CampaignLaserTalents getCampaignLaserTalents(long customerId);

    /**
     * 更新提醒状态
     *
     * @param customerId 顾客编号
     */
    void updateCampaignLaserTalentsAlert(long customerId);

    /**
     * 获取图片
     *
     * @param talentsId 顾客活动信息编号
     * @return 图片地址
     */
    List<CampaignLaserTalentsImage> getCampaignLaserTalentsImageList(long talentsId);

    /**
     * 获取当前TOP信息
     *
     * @return TOP信息
     */
    CampaignLaserTalentsTop getCampaignLaserTalentsTop();

    /**
     * 获取入选TOP的名单
     *
     * @param talentsTopId TOP信息编号
     * @return 名单
     */
    List<CampaignLaserTalentsTopDetail> getCampaignLaserTalentsTopDetailList(long talentsTopId);

    /**
     * 获取入选TOP的图片
     *
     * @param talentsTopId TOP信息编号
     * @return 图片
     */
    List<CampaignLaserTalentsTopDetailImage> getCampaignLaserTalentsTopDetailImageList(long talentsTopId);

    /**
     * 保存图片
     *
     * @param customerId 顾客编号
     * @param file       要保存的图片
     * @return 处理结果
     */
    boolean upload(long customerId, MultipartFile file);

    /**
     * 更新肤质
     *
     * @param customerId 顾客编号
     * @param skinType   肤质
     */
    void setCampaignLaserTalentsSkinType(long customerId, short skinType);

    /**
     * 写入访问开始日志
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @param page       页面
     * @return 访问日志编号
     */
    long insertCampaignLaserTalentsViewLog(long customerId, String source, String page);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    void setCampaignLaserTalentsViewLog(long id);

    /**
     * 写入达人榜访问记录
     *
     * @param campaignLaserTalentsTopDetailLog 达人榜访问记录
     */
    void insertCampaignLaserTalentsTopDetailLog(CampaignLaserTalentsTopDetailLog campaignLaserTalentsTopDetailLog);

    /**
     * 写入达人榜图片访问记录
     *
     * @param campaignLaserTalentsTopDetailImageLog 达人榜图片访问记录
     */
    void insertCampaignLaserTalentsTopDetailImageLog(CampaignLaserTalentsTopDetailImageLog campaignLaserTalentsTopDetailImageLog);


}
