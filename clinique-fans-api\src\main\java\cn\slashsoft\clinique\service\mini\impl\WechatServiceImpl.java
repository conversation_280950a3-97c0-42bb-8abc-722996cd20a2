package cn.slashsoft.clinique.service.mini.impl;

import cn.slashsoft.clinique.dao.mini.*;
import cn.slashsoft.clinique.dao.official.WechatFriendDao;
import cn.slashsoft.clinique.domain.cdp.Member;
import cn.slashsoft.clinique.domain.mini.*;
import cn.slashsoft.clinique.domain.official.WechatFriend;
import cn.slashsoft.clinique.enums.CActiveTypeEnum;
import cn.slashsoft.clinique.enums.PointForeignEnum;
import cn.slashsoft.clinique.enums.PointTypeEnum;
import cn.slashsoft.clinique.service.mini.CactiveService;
import cn.slashsoft.clinique.service.mini.TaskService;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.service.outside.OutsideCdpService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.HttpUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.vo.Result;
import cn.slashsoft.clinique.vo.mini.Authorize;
import cn.slashsoft.clinique.vo.mini.PhoneNumber;
import cn.slashsoft.clinique.vo.mini.Register;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpSession;
import java.nio.charset.StandardCharsets;
import java.security.AlgorithmParameters;
import java.security.Security;
import java.util.*;
import java.util.logging.Logger;

/**
 * 微信公众号、小程序相关的接口
 *
 * <AUTHOR>
 */
@Service
public class WechatServiceImpl implements WechatService {

    private final Logger logger = Logger.getLogger(WechatServiceImpl.class.getName());

    @Value("${wechat.mini.appid}")
    private String appId;

    @Value("${wechat.mini.appsecret}")
    private String appSecret;

    @Value("${wechat.mini.url.code-2-session}")
    private String urlCode2Session;

    @Resource
    private HttpSession session;

    @Resource
    private CactiveService cactiveService;

    @Resource
    private CactiveDao activeDao;

    @Resource
    private ActionDao actionDao;

    @Resource
    private TaskService taskService;

    @Resource
    private WechatFriendDao wechatFriendDao;

    @Resource
    private CustomerDao customerDao;

    @Resource
    private WechatDao wechatDao;

    @Resource
    private MemberDao memberDao;

    @Resource
    private PointDao pointDao;

    @Resource
    private ConfigDao configDao;

    @Resource
    private OutsideCdpService outsideCdpService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public String getMiniOpenidByJsCode(String code) {
        String openid = null;
        try {
            JSONObject json = (JSONObject) JSON.parse(
                    HttpUtil.get(
                            urlCode2Session.replace("APPID", appId)
                                    .replace("SECRET", appSecret)
                                    .replace("JSCODE", code)));

            logger.info("03 - 调用成功：" + json.toJSONString());

            openid = json.getString("openid");
        } catch (Exception e) {

        }
        return openid;
    }

    /**
     * 调用小程序服务端接口，用JS Code换取Openid和Unionid
     * 登录
     *
     * @param code  用户登录凭证
     * @param scene 景响值
     * @return 结果
     */
    @Override
    public Result code2Unionid(String code, String scene) {

        try {
            logger.info("02 - 调用小程序Code2Session接口");
            // 第一步： 拼接接口地址
            // 替换APPID
            // 第二步：调接口获得openid
            // 调用微信小程序登录接口, 解析JSON
            String result = HttpUtil.get(urlCode2Session.replace("APPID", appId).replace("SECRET", appSecret).replace("JSCODE", code));
            JSONObject json = (JSONObject) JSON.parse(result);

            logger.info("03 - 调用成功：" + json.toJSONString());

            String unionid = json.getString("unionid");
            String miniOpenid = json.getString("openid");

            logger.info("04 - 开始写入SESSION");
            // 缓存
            session.setAttribute("openid", miniOpenid);
            session.setAttribute("miniOpenid", miniOpenid);
            session.setAttribute("unionid", unionid);
            session.setAttribute("sessionKey", json.getString("session_key"));

            logger.info("05 - 写入SESSION成功");

            // 返回微信信息
            Wechat wechat = checkCdp(unionid, miniOpenid);

            // 公众号推文场景值1058
            if ("1058".equals(scene) && !wechat.getCustomerId().equals(0L)) {
                Date now = new Date();
                if (null == wechat.getSceneRewardLastTime() ||
                        DateUtil.earlierThan(DateUtil.addDay(wechat.getSceneRewardLastTime(), 7), now)) {

                    // 发放积分
                    PointTransaction pointTransaction = new PointTransaction();
                    pointTransaction.setCustomerId(wechat.getCustomerId());
                    pointTransaction.setPointTypeId(PointTypeEnum.LINK.getId());
                    pointTransaction.setPoints(PointTypeEnum.LINK.getPoints());
                    pointTransaction.setRemainingPoints(PointTypeEnum.LINK.getPoints());
                    pointTransaction.setStartTime(now);
                    pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
                    pointTransaction.setForeignId(PointForeignEnum.LINK.getId());
                    pointTransaction.setForeignMasterId(0L);
                    //由login id改成customer id 2021.2.25 berg
                    pointTransaction.setForeignDetailId(wechat.getCustomerId());
                    pointDao.insertPointTransaction(pointTransaction);

                    // 更新公众号文章场景值最后发放积分时间
                    memberDao.updateMemberSceneLastTime(unionid);
                }
            }

            // 返回
            return ResultUtil.customer(1, "成功", wechat);

        } catch (Exception e) {
            return new Result(3, e.getMessage());
        }

    }

    /**
     * 检查更新
     *
     * @param unionid    开放平台唯一编号
     * @param miniOpenid 小程序唯一编号
     * @param scene 景响值
     * @return 是否更新成功
     */
    @Override
    public Wechat checkRegister(String unionid, String miniOpenid, String scene) {
        // 返回微信信息
        Wechat wechat = checkCdp(unionid, miniOpenid);

        // 公众号推文场景值1058
        if ("1058".equals(scene) && !wechat.getCustomerId().equals(0L)) {
            Date now = new Date();
            if (null == wechat.getSceneRewardLastTime() ||
                    DateUtil.earlierThan(DateUtil.addDay(wechat.getSceneRewardLastTime(), 7), now)) {

                // 发放积分
                PointTransaction pointTransaction = new PointTransaction();
                pointTransaction.setCustomerId(wechat.getCustomerId());
                pointTransaction.setPointTypeId(PointTypeEnum.LINK.getId());
                pointTransaction.setPoints(PointTypeEnum.LINK.getPoints());
                pointTransaction.setRemainingPoints(PointTypeEnum.LINK.getPoints());
                pointTransaction.setStartTime(now);
                pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
                pointTransaction.setForeignId(PointForeignEnum.LINK.getId());
                pointTransaction.setForeignMasterId(0L);
                //由login id改成customer id 2021.2.25 berg
                pointTransaction.setForeignDetailId(wechat.getCustomerId());
                pointDao.insertPointTransaction(pointTransaction);

                // 更新公众号文章场景值最后发放积分时间
                memberDao.updateMemberSceneLastTime(unionid);
            }
        }

        // 返回
        return wechat;
    }

    /**
     * 去CDP检查是否已注册
     *
     * @param unionid    开放平台唯一编号
     * @param miniOpenid 小程序唯一编号
     * @return
     */
    private Wechat checkCdp(String unionid, String miniOpenid) {
        Wechat wechat;
        // 去CDP查找会员信息
        Member member = outsideCdpService.getCdpCustomerByUnionid(unionid, miniOpenid);
        if (null != member) {
            // 查找是否存在新的会员信息
            wechat = memberDao.getMemberByUnionid(unionid);
            // 不存在则创建
            if (null == wechat) {
                // 查找是否已有Customer
                Customer customer = memberDao.getCustomerByUnionid(unionid);
                // 创建会员
                if (null == customer) {
                    customer = new Customer();
                    customer.setUnionid(unionid);
                    customer.setName(member.getName());
                    customer.setPhoneNumber(member.getPhoneNumber());
                    customer.setBirthday(DateUtil.valueOfDay(member.getBirthday()));
                    customer.setAgreeBrandCommunicate(true);
                    customer.setAgreeCorpCommunicate(true);
                    customer.setCampaign("");
                    customer.setSource("");
                    memberDao.insertCustomer(customer);
                }

                // 创建旧微信记录
                Wechat oldWechat = memberDao.getWechatByUnionid(unionid);
                if (null == oldWechat) {
                    oldWechat = new Wechat();
                    oldWechat.setCustomerId(customer.getId());
                    oldWechat.setUnionid(unionid);
                    oldWechat.setWechatOfficialOpenid(member.getOfficialOpenid());
                    oldWechat.setWechatMiniOpenid(miniOpenid);
                    oldWechat.setNickName(member.getName());
                    oldWechat.setAvatarUrl("");
                    oldWechat.setGender("");
                    oldWechat.setCountry("");
                    oldWechat.setProvince("");
                    oldWechat.setCity("");
                    oldWechat.setLanguage("");
                    memberDao.insertWechat(oldWechat);
                }

                member.setCustomerId(customer.getId());
                member.setCampaign("");
                member.setSource("");
                memberDao.insertMember(member);

                // 发放临时表中的积分
                pointDao.insertPointTransactionFromTemp(unionid);
                pointDao.updatePointTransactionTemp(unionid);

                wechat = new Wechat();
                wechat.setUnionid(unionid);
                wechat.setWechatMiniOpenid(miniOpenid);
                wechat.setPhoneNumber(member.getPhoneNumber());
                wechat.setCustomerId(customer.getId());
                wechat.setKocV(0);
            }
            // 已存在则更新
            else {
                memberDao.updateMember(member);
                memberDao.updateCustomer(member);
            }
        } else {
            wechat = new Wechat();
            wechat.setUnionid(unionid);
            wechat.setWechatMiniOpenid(miniOpenid);
            wechat.setPhoneNumber("");
            wechat.setCustomerId(0L);
            wechat.setKocV(0);
        }

        return wechat;
    }

    /**
     * 获取是微信信息
     * 首页
     *
     * @param openid 小程序唯一编号
     * @return 微信信息
     */
    @Override
    public Wechat getWechatByOpenid(String openid) {
        return wechatDao.getWechatByOpenId(openid);
    }

    /**
     * 更新微信基本信息
     *
     * @param authorize 微信小程序传入的授权信息
     * @return 结果: 1,成功; 2，控糖密码不存在或已失效; 3,控糖密码已被绑定; 4,顾客已绑定; 5,异常
     */
    @Override
    public Result setWechat(Authorize authorize) {
        // 解密用户信息
        JSONObject userInfoDecrypt = decrypt(
                authorize.getUserInfoIv(),
                authorize.getUserInfoEncryptedData(),
                authorize.getSessionKey());
        if (null == userInfoDecrypt) {
            return ResultUtil.customer(2, "用户数据解密失败");
        }

        Wechat wechat = new Wechat();
        wechat.setUnionid(userInfoDecrypt.getString("unionId"));
        wechat.setWechatMiniOpenid(userInfoDecrypt.getString("openId"));
        wechat.setNickName(userInfoDecrypt.getString("nickName"));
        wechat.setAvatarUrl(userInfoDecrypt.getString("avatarUrl"));
        wechat.setGender(userInfoDecrypt.getString("gender"));
        wechat.setCountry(userInfoDecrypt.getString("country"));
        wechat.setProvince(userInfoDecrypt.getString("province"));
        wechat.setCity(userInfoDecrypt.getString("city"));
        wechat.setLanguage(userInfoDecrypt.getString("language"));
        wechatDao.updateWechat(wechat);

        // 返回
        return ResultUtil.customer(1, "成功", wechat);
    }

    /**
     * 授权
     *
     * @param authorize 微信小程序传入的授权信息
     * @return 结果: 1,成功; 2,解密失败
     */
    @Override
    public Result authorize(Authorize authorize) {

        // 解密用户信息
        JSONObject userInfoDecrypt = decrypt(
                authorize.getUserInfoIv(),
                authorize.getUserInfoEncryptedData(),
                authorize.getSessionKey());
        if (null == userInfoDecrypt) {
            return ResultUtil.customer(2, "用户数据解密失败");
        }

        WechatAuthorize wechatAuthorize = new WechatAuthorize();
        wechatAuthorize.setUnionid(authorize.getUnionid());
        wechatAuthorize.setWechatMiniOpenid(authorize.getMiniOpenId());
        wechatAuthorize.setNickName(userInfoDecrypt.getString("nickName"));
        wechatAuthorize.setAvatarUrl(userInfoDecrypt.getString("avatarUrl"));
        wechatAuthorize.setGender(userInfoDecrypt.getString("gender"));
        wechatAuthorize.setCountry(userInfoDecrypt.getString("country"));
        wechatAuthorize.setProvince(userInfoDecrypt.getString("province"));
        wechatAuthorize.setCity(userInfoDecrypt.getString("city"));
        wechatAuthorize.setLanguage(userInfoDecrypt.getString("language"));
        wechatDao.insertWechatAuthorize(wechatAuthorize);

        // 返回
        return ResultUtil.customer(1, "成功", wechatAuthorize);
    }

    /**
     * 解密手机号码
     *
     * @param phoneNumber 手机号码
     * @return 手机号码
     */
    @Override
    public Result getPhoneNumber(PhoneNumber phoneNumber) {

        // 解密手机号码
        JSONObject phoneNumberDecrypt = decrypt(
                phoneNumber.getIv(),
                phoneNumber.getEncryptedData(),
                phoneNumber.getSessionKey());
        if (null == phoneNumberDecrypt) {
            return new Result(2, "手机号码解密失败");
        }

        // 返回
        return ResultUtil.customer(1, "成功", phoneNumberDecrypt.getString("phoneNumber"));

    }

    /**
     * 解密手机号码
     *
     * @param phoneNumber 手机号码
     * @return 手机号码
     */
    @Override
    public Result setPhoneNumber(PhoneNumber phoneNumber) {
        // 解密手机号码
        JSONObject phoneNumberDecrypt = decrypt(
                phoneNumber.getIv(),
                phoneNumber.getEncryptedData(),
                phoneNumber.getSessionKey());
        if (null == phoneNumberDecrypt) {
            return new Result(2, "手机号码解密失败");
        }

        customerDao.setPhoneNumberByMiniOpenid(
                phoneNumber.getOpenid(),
                phoneNumberDecrypt.getString("phoneNumber"));

        // 返回
        return ResultUtil.customer(1, "成功");
    }

    /**
     * 注册信息提交
     * 注册
     *
     * @param register 注册字段
     * @return 结果: 1,成功; 2，手机号码解密失败; 3,用户数据解密失败; 4,手机号码已被注册; 5:开放平台或小程序的唯一编号冲突
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result register(Register register, String miniOpenId) {

        // 解密手机号码
        JSONObject phoneNumberDecrypt = decrypt(
                register.getPhoneNumberIv(),
                register.getPhoneNumberEncryptedData(),
                register.getSessionKey());
        if (null == phoneNumberDecrypt) {
            return new Result(2, "手机号码解密失败");
        }

        // 解密用户信息
        // JSONObject userInfoDecrypt = decrypt(register.getUserInfoIv(),
        // register.getUserInfoEncryptedData(), register.getSessionKey());
        // if (null == userInfoDecrypt) {
        //     return ResultUtil.customer(3, "用户数据解密失败");
        // }

        // 写入顾客信息
        Customer customer = new Customer();
        Wechat wechat = wechatDao.getWechatInfoFromAuthorize(miniOpenId);
        if (wechat == null) {
            return ResultUtil.customer(3, "用户数据解密失败");
        }
        customer.setPhoneNumber(phoneNumberDecrypt.getString("phoneNumber"));
        customer.setName(wechat.getNickName());
        customer.setAgreeBrandCommunicate(register.getAgreeBrandCommunicate());
        customer.setAgreeCorpCommunicate(register.getAgreeCorpCommunicate());
        customer.setCampaign(register.getCampaign());
        customer.setSource(register.getSource());
        try {
            if (0 == customerDao.insertCustomer(customer)) {
                throw new Exception("手机号码已存在");
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResultUtil.customer(4, "手机号码已存在");
        }

        // 写入微信信息
        wechat.setCustomerId(customer.getId());
        wechat.setUnionid(register.getUnionid());

        wechat.setPlatform("C");

        try {
            if (0 == wechatDao.insertWechat(wechat)) {
                throw new Exception("开放平台或小程序的唯一编号冲突");
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResultUtil.customer(5, "开放平台或小程序的唯一编号冲突");
        }

        // 保存同意注册条款记录
        customerDao.saveCustomerClauseRule(register.getUnionid(), "1.2");

        // 保存授权记录
        if (null != register.getAgreeBrandCommunicate() && null != register.getAgreeCorpCommunicate()) {
            CustomerClause customerClause = customerDao.getLastCustomerClause(register.getUnionid());
            if (null == customerClause) {
                customerDao.saveCustomerClause(register.getUnionid(), register.getAgreeBrandCommunicate(), register.getAgreeCorpCommunicate(), "1.2");
            } else {
                if (!Objects.equals(customerClause.getAgreeBrandCommunicate(), register.getAgreeBrandCommunicate()) || !Objects.equals(customerClause.getAgreeCorpCommunicate(), register.getAgreeCorpCommunicate())) {
                    customerDao.saveCustomerClause(register.getUnionid(), register.getAgreeBrandCommunicate(), register.getAgreeCorpCommunicate(), "1.2");
                }
            }
        }

        this.registerAndShareActions(
                customer.getId(),
                wechat.getUnionid(),
                register.getOpenid(),
                wechat.getWechatMiniOpenid(),
                register.getCampaign());

        // 返回
        return ResultUtil.customer(1, "成功", wechat);

    }

    /**
     * 注册
     *
     * @param wechatAuthorize 注册字段
     * @return 结果: 1,成功; 4 手机号码已存在, 5，开放平台或小程序的唯一编号冲突
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result registerWithOutside(WechatAuthorize wechatAuthorize) {

        // 写入顾客信息
        Customer customer = new Customer();
        customer.setPhoneNumber(wechatAuthorize.getPhoneNumber());
        customer.setName(wechatAuthorize.getNickName());
        customer.setCampaign(wechatAuthorize.getCampaign());
        customer.setSource(wechatAuthorize.getSource());
        customer.setAgreeBrandCommunicate(false);
        customer.setAgreeCorpCommunicate(false);
        try {
            if (0 == customerDao.insertCustomer(customer)) {
                throw new Exception("手机号码已存在");
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResultUtil.customer(4, "手机号码已存在");
        }

        // 写入微信信息
        Wechat wechat = new Wechat();
        wechat.setCustomerId(customer.getId());
        wechat.setUnionid(wechatAuthorize.getUnionid());
        wechat.setWechatOfficialOpenid(wechatAuthorize.getWechatOfficialOpenid());
        wechat.setWechatMiniOpenid(wechatAuthorize.getWechatMiniOpenid());
        wechat.setNickName(wechatAuthorize.getNickName());
        wechat.setAvatarUrl(wechatAuthorize.getAvatarUrl());
        wechat.setGender(wechatAuthorize.getGender());
        wechat.setCountry(wechatAuthorize.getCountry());
        wechat.setProvince(wechatAuthorize.getProvince());
        wechat.setCity(wechatAuthorize.getCity());
        wechat.setLanguage(wechatAuthorize.getLanguage());
        wechat.setPlatform("OFFICIAL");

        try {
            if (0 == wechatDao.insertWechat(wechat)) {
                throw new Exception("开放平台或小程序的唯一编号冲突");
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResultUtil.customer(5, "开放平台或小程序的唯一编号冲突");
        }

        this.registerAndShareActions(
                customer.getId(),
                wechatAuthorize.getUnionid(),
                wechatAuthorize.getOpenid(),
                wechat.getWechatMiniOpenid(),
                wechatAuthorize.getCampaign());

        // 返回
        return ResultUtil.customer(1, "成功", wechat);
    }

    private void registerAndShareActions(
            long customerId,
            String unionid,
            String shareOpenid,
            String customerMiniOpenid,
            String campaign) {

        // 发放绑定积分
        Date now = new Date();
        PointTransaction pointTransaction = new PointTransaction();
        pointTransaction.setCustomerId(customerId);
        pointTransaction.setPointTypeId(PointTypeEnum.BINDING.getId());
        pointTransaction.setPoints(PointTypeEnum.BINDING.getPoints());
        pointTransaction.setRemainingPoints(PointTypeEnum.BINDING.getPoints());
        pointTransaction.setStartTime(now);
        pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
        pointTransaction.setForeignId(PointForeignEnum.BINDING.getId());
        pointTransaction.setRemark(PointForeignEnum.BINDING.getName());
        pointTransaction.setForeignMasterId(0L);
        pointTransaction.setForeignDetailId(0L);
        pointDao.insertPointTransaction(pointTransaction);

        // 发放临时积分
        pointDao.insertPointTransactionFromTemp(unionid);
        pointDao.updatePointTransactionTemp(unionid);

        // 写入分享者信息
        if (!StringUtil.isNullOrEmpty(shareOpenid) && !StringUtil.isNullOrEmpty(customerMiniOpenid)) {
            String openid = shareOpenid;
            WechatShareRegister wechatShareRegister = new WechatShareRegister();
            wechatShareRegister.setSharerWechatMiniOpenid(openid);
            wechatShareRegister.setVisitorWechatMiniOpenid(customerMiniOpenid);
            wechatDao.insertWechatShareRegister(wechatShareRegister);
            int shareRegisterId = 0;
            if (wechatShareRegister.getId() != null) {
                shareRegisterId = wechatShareRegister.getId().intValue();
            }

            Customer shareCustomer = customerDao.getCustomerByMiniOpenId(openid);
            if (null != shareCustomer) {
                //分享注册，完成三日打卡第三日任务
                int taskCount = actionDao.getDay3TaskActionCount(openid);
                if (taskCount == 6) {
                    //增加任务日志
                    Action action = new Action();
                    action.setWechatOfficialOpenid(openid);
                    action.setType((short) 60);
                    actionDao.insertAction(action);
                    taskService.day3TaskFinish(shareCustomer.getId());
                } else if (taskCount == 7) {
                    taskService.day3TaskFinish(shareCustomer.getId());
                }

                //
                List<PointTransaction> readactiveTransaction = activeDao.getCactiveBy(
                        shareCustomer.getId(),
                        CActiveTypeEnum.SHARE_READ.getId());
                Long addId = 0L;
                for (PointTransaction rat : readactiveTransaction) {
                    addId = rat.getId();
                    cactiveService.addPoints(
                            shareCustomer.getId(),
                            CActiveTypeEnum.SHARE_READ_BIND,
                            Long.valueOf(shareRegisterId),
                            addId,
                            "分享给好友，好友阅读笔记/观看视频且绑定C粉圈");
                    break;
                }
            }

            // 注册成功的邀请关系写在这个地方

        }

    }

    /**
     * 解密微信敏感数据
     *
     * @param iv            向量
     * @param encryptedData 加密数据
     * @param sessionKey    会话KEY
     * @return 数据
     */
    private JSONObject decrypt(String iv, String encryptedData, String sessionKey) {
        try {
            Base64.Decoder decoder = Base64.getDecoder();
            byte[] dataByte = decoder.decode(encryptedData);
            byte[] keyByte = decoder.decode(sessionKey);
            byte[] ivByte = decoder.decode(iv);
            int base = 16;
            if (keyByte.length % base != 0) {
                int groups = keyByte.length / base + 1;
                byte[] temp = new byte[groups * base];
                Arrays.fill(temp, (byte) 0);
                System.arraycopy(keyByte, 0, temp, 0, keyByte.length);
                keyByte = temp;
            }
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
            SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
            AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
            parameters.init(new IvParameterSpec(ivByte));
            cipher.init(Cipher.DECRYPT_MODE, spec, parameters);
            byte[] resultByte = cipher.doFinal(dataByte);
            if (null != resultByte && resultByte.length > 0) {
                String result = new String(resultByte, StandardCharsets.UTF_8);
                return JSONObject.parseObject(result);
            }
        } catch (Exception e) {
            System.out.println("数据解密失败，" + e.getMessage());
        }
        return null;
    }

    /**
     * 跟据顾客编号查找昵称和头像
     * 个人中心
     * 编辑资料
     *
     * @param customerId 顾客编号
     * @return 昵称和头像
     */
    @Override
    public Wechat getWechatInfoByCustomerId(long customerId) {
        return wechatDao.getWechatInfoByCustomerId(customerId);
    }

    /**
     * 跟据开放平台的唯一编号查找
     * 登录
     *
     * @param unionid 开放平台的唯一编号
     * @return 微信信息
     */
    @Override
    public Wechat getWechatByUnionId(String unionid) {
        return wechatDao.getWechatByUnionId(unionid);
    }

    /**
     * 写入公众号唯一编号
     *
     * @param unionid              开放平台编号
     * @param wechatOfficialOpenid 公众号唯一编号
     */
    @Override
    public void setOfficialOpenid(String unionid, String wechatOfficialOpenid) {
        wechatDao.setOfficialOpenid(unionid, wechatOfficialOpenid);
    }

    /**
     * 写小程序首页的来源
     *
     * @param source 来源
     * @param openid 微信小程序唯一编号
     */
    @Override
    public void setOriginate(String source, String openid) {
        Originate originate = new Originate();
        originate.setSource(source);
        originate.setWechatMiniOpenid(openid);
        wechatDao.insertOriginate(originate);
    }

    @Override
    public void setNoLoginLog(String scene, String openid) {
        logger.info("setNoLoginLog --- 01 - 开始保存登陆信息");
        // 保存登陆信息
        Login login = new Login();
        login.setWechatMiniOpenid(openid);
        login.setScene(scene);
        wechatDao.insertLogin(login);
    }

    /**
     * 发放场景值福利
     *
     * @param scene  场景值
     * @param openid 微信小程序唯一编号
     */
    @Override
    public void setSceneReward(String scene, String openid) {

        if (!"1058".equals(scene)) {
            return;
        }

        // 读取用户信息
        Wechat wechat = wechatDao.getWechatByOpenId(openid);

        if (null == wechat || wechat.getCustomerId().equals(0L)) {
            return;
        }

        Date now = new Date();

        if (null != wechat.getSceneRewardLastTime()
                && DateUtil.laterThan(DateUtil.addDay(wechat.getSceneRewardLastTime(), 7), now)) {
            return;
        }

        if (0 == wechatDao.updateSceneLastTimeCheckLastTime(wechat)) {
            return;
        }

        // 发放积分
        PointTransaction pointTransaction = new PointTransaction();
        pointTransaction.setCustomerId(wechat.getCustomerId());
        pointTransaction.setPointTypeId(PointTypeEnum.LINK.getId());
        pointTransaction.setPoints(PointTypeEnum.LINK.getPoints());
        pointTransaction.setRemainingPoints(PointTypeEnum.LINK.getPoints());
        pointTransaction.setStartTime(now);
        pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
        pointTransaction.setForeignId(PointForeignEnum.LINK.getId());
        pointTransaction.setForeignMasterId(0L);
        pointTransaction.setForeignDetailId(0L);
        pointDao.insertPointTransaction(pointTransaction);

    }

    /**
     * 写入日志
     *
     * @param wechatRegisterStepLog 日志
     */
    @Override
    public void setRegisterStepLog(WechatRegisterStepLog wechatRegisterStepLog) {
        try {
            if (0 == wechatDao.updateRegisterStepLog(wechatRegisterStepLog)) {
                wechatDao.insertRegisterStepLog(wechatRegisterStepLog);
            }
        } catch (Exception ignored) {
        }
    }

    @Override
    public WechatFriend getWechatFriend(String unionid) {
        return wechatFriendDao.getByUnionid(unionid);
    }

    @Override
    public WechatFriend getWechatFriendByOpenid(String openid) {
        return wechatFriendDao.getByOpenid(openid);
    }

    @Override
    public void addWechatFriend(WechatFriend wf) {
        wechatFriendDao.addWechatFriend(wf);
    }

    @Override
    public void updateWechatFriend(WechatFriend wf) {
        wechatFriendDao.updateWechatFriend(wf);

    }

    @Override
    public void saveEvent(String string) {
        wechatFriendDao.saveEvent(string);

    }

    @Override
    public Wechat getWechatByOfficialOpenid(String openid) {
        return wechatDao.getWechatByOfficialOpenId(openid);
    }

    /**
     * 更新小程序文案配置
     */
    @Override
    public void updateConfig() {
        List<Config> configList = configDao.getConfig();
        for (Config config : configList) {
            stringRedisTemplate.opsForValue().set(config.getPlaceholder(), config.getDescription());
        }
    }
}
