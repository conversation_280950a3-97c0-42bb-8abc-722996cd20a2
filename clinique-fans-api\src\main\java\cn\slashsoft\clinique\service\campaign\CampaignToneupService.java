package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignToneupDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignToneupLog;

/**
 * 黄油变粉安瓶级保湿补光申领
 *
 * <AUTHOR>
 */
public interface CampaignToneupService {

    /**
     * 保存日志
     *
     * @param campaignToneupLog 日志
     */
    void insertLog(CampaignToneupLog campaignToneupLog);

    /**
     * 获取手机号码
     *
     * @param unionid 开放平台唯一编号
     * @return 手机号码
     */
    String getPhoneNumberByUnionid(String unionid);

    /**
     * 获取是否已经审领
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    int hasDetail(String phoneNumber);

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    CampaignToneupDetail getDetail(String phoneNumber);

    /**
     * 申领
     *
     * @param campaignToneupDetail 资料
     * @return 0:成功，1，帐户已经领过了，2：手机号码已经领过了，3：库存不足
     */
    int submit(CampaignToneupDetail campaignToneupDetail);

    /**
     * 更新是否关注
     *
     * @param phoneNumber 手机号码
     * @param isFollow    是否关注
     */
    void setFollow(String phoneNumber, boolean isFollow);
}
