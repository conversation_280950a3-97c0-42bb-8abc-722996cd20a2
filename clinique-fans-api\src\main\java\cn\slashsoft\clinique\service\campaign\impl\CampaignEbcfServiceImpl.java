package cn.slashsoft.clinique.service.campaign.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import cn.slashsoft.clinique.dao.campaign.CampaignEbcfDao;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciCity;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciLog;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciStock;
import cn.slashsoft.clinique.service.campaign.CampaignEbcfService;

/**
 * 全新302美白粉底
 *
 * <AUTHOR>
 */
@Service
public class CampaignEbcfServiceImpl implements CampaignEbcfService {
	@Resource
    private CampaignEbcfDao campaignEbcfDao;



    /**
     * 保存日志
     *
     * @param campaignEbciLog 日志
     */
    @Override
    public void insertLog(CampaignEbciLog campaignEbciLog) {
    	campaignEbcfDao.insertLog(campaignEbciLog);
    }

    /**
     * 获取手机号码
     *
     * @param unionid 开放平台唯一编号
     * @return 手机号码
     */
    @Override
    public String getPhoneNumberByUnionid(String unionid) {
        return campaignEbcfDao.getPhoneNumberByUnionid(unionid);
    }

    /**
     * 获取手机号码
     *
     * @param openid 公众号唯一编号
     * @return 手机号码
     */
    @Override
    public String getPhoneNumberByOpenid(String openid) {
        return campaignEbcfDao.getPhoneNumberByOpenid(openid);
    }

    /**
     * 获取是否已经审领
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    @Override
    public int hasDetail(String phoneNumber) {
        return campaignEbcfDao.hasDetail(phoneNumber);
    }

    /**
     * 获取是否已经审领
     *
     * @param openid 公众号唯一编号
     * @return 数量
     */
    @Override
    public int hasDetailByOpenid(String openid) {
        return campaignEbcfDao.hasDetailByOpenid(openid);
    }

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    @Override
    public CampaignEbciDetail getDetail(String phoneNumber) {
        return campaignEbcfDao.getDetail(phoneNumber);
    }

    /**
     * 申领
     *
     * @param campaignEbciDetail 资料
     * @return 0:成功，1：手机号码已经领过了，2：库存不足, 3： 其他错误
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int submit(CampaignEbciDetail campaignEbciDetail) {
        // 扣库存
        //if (0 == campaignEbciDao.updateStock(campaignEbciDetail)) {
        //    return 2;
        //}

    	if(campaignEbcfDao.getDetail(campaignEbciDetail.getPhoneNumber()) != null) {
    		return 1;
    	}

    	//CampaignEbciStock store = campaignEbcfDao.getByStoreName(campaignEbciDetail);
        //if (null == store) {
            //Tmall 线下活动
       // } else if(!store.getStatus()) {
    	//	return 2;
    	//}

        // 写入记录
        try {
        	campaignEbcfDao.insetDetail(campaignEbciDetail);
            return 0;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return 3;
        }

    }
   

    /**
     * 更新是否关注
     *
     * @param campaignEbciDetail 申领信息
     */
    @Override
    public void setFollow(CampaignEbciDetail campaignEbciDetail) {
    	campaignEbcfDao.setFollow(campaignEbciDetail);
    }

    /**
     * 获取审领信息
     *
     * @param openid 公众号唯一编号
     * @return 审领信息
     */
    @Override
    public CampaignEbciDetail getDetailByOpenid(String openid) {
        return campaignEbcfDao.getDetailByOpenid(openid);
    }

    /**
     * 根据场景值获取门店
     *
     * @param scene 场景值
     * @return 门店
     */
    @Override
    public CampaignEbciStock getByScene(String scene) {
        return campaignEbcfDao.getByScene(scene);
    }

    /**
     * 根据门店名称获取门店状态
     *
     * @param scene 场景值
     * @return 门店
     */
    @Override
    public CampaignEbciStock getByStoreName(CampaignEbciDetail campaignEbciDetail) {
        return campaignEbcfDao.getByStoreName(campaignEbciDetail);
    }
    /**
     * 更新核销信息
     *
     * @param campaignEbciDetail 核销信息
     */
    @Override
    public void setReceive(CampaignEbciDetail campaignEbciDetail) {
    	campaignEbcfDao.setReceive(campaignEbciDetail);
    }


	@Override
	public CampaignEbciCity getEbciCity(String city, long campaign) {		
		return campaignEbcfDao.getCity(city, campaign);
	}
}
