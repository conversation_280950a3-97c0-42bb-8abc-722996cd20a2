package cn.slashsoft.clinique.controller.mini;

import java.util.List;
import java.util.logging.Logger;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.slashsoft.clinique.dao.mini.KocDao;
import cn.slashsoft.clinique.domain.campaign.ugc.KocWinner;
import cn.slashsoft.clinique.domain.mini.Note;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.KocService;
import cn.slashsoft.clinique.service.mini.NoteService;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.mini.KocTopVo;

/**
 * KOC
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/koc")
public class KocController {

	private final Logger logger = Logger.getLogger(KocController.class.getName());
	
    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
	@Resource
	private HttpServletRequest request;

	private final KocDao kocDao;
	private final StringRedisTemplate stringRedisTemplate;
    private final KocService kocService;

    @Resource
    NoteService noteService;

	public KocController(KocDao kocDao,
			KocService kocService,
			StringRedisTemplate stringRedisTemplate) {
		this.stringRedisTemplate = stringRedisTemplate;
		this.kocDao = kocDao;
	    this.kocService = kocService;
	}
	 /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/get-list")
    public String getKocList() {       

		// 获取缓存中的顾客编号
		Long customerId = 0L ;
		if(request.getAttribute("customerId") != null) {
			customerId =(Long) request.getAttribute("customerId");
		}
		
		if(customerId.intValue() == 0) {
        	return ResultUtil.customer(ResultEnum.FAILED);
        }
        return ResultUtil.customer(ResultEnum.SUCCESS, kocDao.getCampaignUgcList());

    }
    /**
     * 修改标题
     *
     * @param campaignId 活动编号
     * @param file 图片
     * @param index 索引
     * @return 处理结果
     */
    @PostMapping("/set-title/{campaignId}")
    public String setTitle(
            @PathVariable("campaignId") long campaignId,
            @RequestParam("title") String title
    ) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        Note note = noteService.getKocByCampaign(campaignId, customerId) ;
        logger.info("set-title: "+ note.getId() +" " + customerId + " " + title);
        noteService.updateKocTitle(note.getId(), title, customerId) ;

        return ResultUtil.customer(ResultEnum.SUCCESS,  1);

    }
	 /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/mine/get-list")
    public String getMineKocList() {       

		// 获取缓存中的顾客编号
		Long customerId = 0L ;
		if(request.getAttribute("customerId") != null) {
			customerId =(Long) request.getAttribute("customerId");
		}
		
		if(customerId.intValue() == 0) {
        	return ResultUtil.customer(ResultEnum.FAILED);
        }
        return ResultUtil.customer(ResultEnum.SUCCESS, kocDao.getMineCampaignUgcList(customerId));

    }
    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/top/get-data/{id}")
    public String getKocTopData(
			@PathVariable("id") Long id) {       

		// 获取缓存中的顾客编号
		Long customerId = 0L ;
		if(request.getAttribute("customerId") != null) {
			customerId =(Long) request.getAttribute("customerId");
		}
		
		if(customerId.intValue() == 0) {
        	return ResultUtil.customer(ResultEnum.FAILED);
        }
        KocTopVo kocTopVo = new KocTopVo();
        kocTopVo.setStage((short)1);
        kocTopVo.setKocTopDetailList(kocService.getKocTopDetailList(id));
        kocTopVo.setKocTopDetailImageList(kocService.getKocTopDetailImageList(id));

        return ResultUtil.customer(ResultEnum.SUCCESS, kocTopVo);

    }
	/**
	 * 获取中奖者列表
	 *
	 * @return 中奖者列表
	 */
	@PostMapping("/winner/get-data/{id}")
	public String getWinnerList(
			@PathVariable("id") Long id) {
		
		// 获取缓存中的顾客编号
		Long customerId = 0L ;
		if(request.getAttribute("customerId") != null) {
			customerId =(Long) request.getAttribute("customerId");
		}
		
		if(customerId.intValue() == 0) {
        	return ResultUtil.customer(ResultEnum.FAILED);
        }
		List<KocWinner> ak = this.kocService.getWinnerList(id);
	

		return ResultUtil.customer(ResultEnum.SUCCESS, ak);
	}

	
}
