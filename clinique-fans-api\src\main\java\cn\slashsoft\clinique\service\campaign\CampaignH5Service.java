package cn.slashsoft.clinique.service.campaign;

import java.util.HashMap;
import java.util.List;

import cn.slashsoft.clinique.domain.campaign.CampaignH5;
import cn.slashsoft.clinique.domain.campaign.CampaignH5AD;
import cn.slashsoft.clinique.domain.campaign.CampaignH5City;
import cn.slashsoft.clinique.domain.campaign.CampaignH5Detail;
import cn.slashsoft.clinique.domain.campaign.CampaignH5Log;
import cn.slashsoft.clinique.domain.campaign.CampaignH5Stock;

/**
 * 302美白镭射瓶
 *
 * <AUTHOR>
 */
public interface CampaignH5Service {

    /**
     * 保存日志
     *
     * @param CampaignH5Log 日志
     */
    void insertLog(CampaignH5Log CampaignH5Log);

    /**
     * 获取手机号码
     *
     * @param unionid 开放平台唯一编号
     * @return 手机号码
     */
    String getPhoneNumberByUnionid(String unionid);

    /**
     * 获取手机号码
     *
     * @param openid 公众号唯一编号
     * @return 手机号码
     */
    String getPhoneNumberByOpenid(String openid);

    /**
     * 获取是否已经审领
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    int hasDetail(String phoneNumber, Long campaignId);

    /**
     * 获取是否已经审领
     *
     * @param openid 公众号唯一编号
     * @return 数量
     */
    int hasDetailByOpenid(String openid, Long campaignId);

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    CampaignH5Detail getDetail(String phoneNumber, Long campaignId);

    /**
     * 申领
     *
     * @param CampaignH5Detail 资料
     * @return 0:成功，1，帐户已经领过了，2：手机号码已经领过了，3：库存不足
     */
    int submit(CampaignH5Detail CampaignH5Detail);

    /**
     * 申领
     *
     * @param CampaignH5Detail 资料
     * @return 0:成功，1，帐户已经领过了，2：手机号码已经领过了，3：库存不足
     */
    int tmpsubmit(CampaignH5Detail CampaignH5Detail);

    /**
     * 更新是否关注
     *
     * @param CampaignH5Detail 申领信息
     */
    void setFollow(CampaignH5Detail CampaignH5Detail);

    /**
     * 获取审领信息
     * @param openid 公众号唯一编号
     * @return 审领信息
     */
    CampaignH5Detail getDetailByOpenid(String openid, Long campaignId);

    CampaignH5 getCampaign(String campaignCode);
    CampaignH5 getValidCampaign(String campaignCode);

    /**
     * 跟据场景值获取门店
     *
     * @param scene 场景值
     * @return 门店
     */
    CampaignH5Stock getByScene(String scene, Long campaignId); /**
     * 根据门店名称获取门店状态
    *
    * @param scene 场景值
    * @return 门店
    */
   public CampaignH5Stock getByStoreName(CampaignH5Detail CampaignH5Detail);
    /**
     * 写入ad信息
     *
     * @param CampaignH5AD ad
     * @return ad
     */
    void addAD(CampaignH5AD CampaignH5AD);
    /**
     * 跟据手机号获取ad
     *
     * @param phoneNumber 手机号
     * @return ad
     */
    CampaignH5AD getADByPhoneNumber(String phoneNumber, Long campaignId);
    /**
     * 更新ad信息
     *
     * @param CampaignH5AD ad
     * @return ad
     */
    void setAD(CampaignH5AD CampaignH5AD);

    /**
     * 更新核销信息
     *
     * @param CampaignH5Detail 核销信息
     */
    void setReceive(CampaignH5Detail CampaignH5Detail);

    HashMap<String, Object> getCampaignCityList( long campaign);
    CampaignH5City getCampaignCity(String city, long campaign);
    
     
}
