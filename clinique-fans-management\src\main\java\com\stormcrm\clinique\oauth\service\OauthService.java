package com.stormcrm.clinique.oauth.service;

import java.util.ArrayList;
import java.util.List;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.stormcrm.clinique.dao.oauth.OauthPermissionDao;
import com.stormcrm.clinique.dao.oauth.OauthUserDao;
import com.stormcrm.clinique.oauth.domain.OauthPermission;
import com.stormcrm.clinique.oauth.domain.OauthUser;

/**
 * <AUTHOR>
 */
@Service
public class OauthService implements UserDetailsService {

    private final OauthPermissionDao oauthPermissionDao;
    private final OauthUserDao oauthUserDao;

    public OauthService(OauthPermissionDao oauthPermissionDao, OauthUserDao oauthUserDao) {
        this.oauthPermissionDao = oauthPermissionDao;
        this.oauthUserDao = oauthUserDao;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {

        // 跟据用户名查找用户信息
        OauthUser oauthUser = oauthUserDao.getByUsername(username);

        // 未找到用户，抛出用户名未找到异常
        if (null == oauthUser) {
            throw new UsernameNotFoundException(username);
        }

        // 权限集合
        List<GrantedAuthority> grantedAuthorities = new ArrayList<>();
        List<OauthPermission> oauthPermissions = oauthPermissionDao.getByUserId(oauthUser.getId());
        oauthPermissions.forEach(oauthPermission -> {
            GrantedAuthority grantedAuthority = new SimpleGrantedAuthority(oauthPermission.getMark());
            grantedAuthorities.add(grantedAuthority);
        });

        // 返回
        return new User(username, oauthUser.getPassword(), oauthUser.getStatus(), true, true, !oauthUser.getLocked(), grantedAuthorities);
    }

}
