package cn.slashsoft.clinique.service.mini.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import cn.slashsoft.clinique.dao.mini.RankingDao;
import cn.slashsoft.clinique.dao.mini.StoreDao;
import cn.slashsoft.clinique.domain.mini.Customer;
import cn.slashsoft.clinique.domain.mini.RankingGift;
import cn.slashsoft.clinique.domain.mini.RankingLog;
import cn.slashsoft.clinique.domain.mini.StoreExchange;
import cn.slashsoft.clinique.domain.mini.StoreExchangeExpress;
import cn.slashsoft.clinique.domain.mini.StoreExchangePickup;
import cn.slashsoft.clinique.enums.StoreExchangeLogisticsEnum;
import cn.slashsoft.clinique.enums.StoreExchangeTypeEnum;
import cn.slashsoft.clinique.service.mini.RankingService;
import cn.slashsoft.clinique.util.RandomUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.Result;
import cn.slashsoft.clinique.vo.mini.Exchange;

/**
 * 积分排名
 * <AUTHOR>
 */
@Service
public class RankingServiceImpl implements RankingService {

    private final RankingDao rankingDao;
    private final StoreDao storeDao;

    public RankingServiceImpl(RankingDao rankingDao, StoreDao storeDao) {
        this.rankingDao = rankingDao;
        this.storeDao = storeDao;
    }

    /**
     * 获取我的排名
     * 排名页
     *
     * @param customerId 顾客编号
     * @return 顾客信息
     */
    @Override
    public Customer getCustomerRankingById(long customerId) {
        return rankingDao.getCustomerRankingById(customerId);
    }

    /**
     * 获取排名前5的顾客
     *
     * @return 顾客信息
     */
    @Override
    public List<Customer> getCustomerRankingTop() {
        return rankingDao.getCustomerRankingTop();
    }

    /**
     * 获取有效的商城礼品列表，按更新时间倒序排列
     * 积分兑礼
     *
     * @param customerId 顾客编号
     * @return 商城礼品列表
     */
    @Override
    public List<RankingGift> getRankingGiftList(long customerId) {
        return rankingDao.getRankingGiftList(customerId);
    }

    /**
     * 跟据礼品编号获取礼品资料，和已兑礼数量
     *
     * @param id         礼品编号
     * @param customerId 顾客编号
     * @return 礼品资料
     */
    @Override
    public RankingGift getRankingGiftById(long id, long customerId) {
        return rankingDao.getRankingGiftById(id, customerId);
    }

    /**
     * 立即兑换
     *
     * @param exchange              兑换信息
     * @param storeExchangeTypeEnum 兑换类型
     * @return 处理结果
     */
    @Override
    public Result exchange(Exchange exchange, StoreExchangeTypeEnum storeExchangeTypeEnum) {

        // 读取兑换礼口的详情
        RankingGift rankingGift = rankingDao.getRankingGiftForExchangeById(exchange.getId(), exchange.getCustomerId());

        // 礼品不存在或已下架
        if (null == rankingGift) {
            return ResultUtil.customer(2, "礼品不存在或已下架");
        }

        // 同一礼品兑换不能超过1次
        if (0 < rankingGift.getExchangeTotal()){
            return ResultUtil.customer(3, "您已经兑换过了");
        }

        Customer customer = rankingDao.getCustomerRankingById(exchange.getCustomerId());
        if(customer.getRankingExchange()){
            return ResultUtil.customer(3, "您已经兑换过了");
        }

        Long ranking = customer.getRanking();
        if(rankingGift.getRankingMin() > ranking || ranking > rankingGift.getRankingMax()){
            return ResultUtil.customer(3, "您没有获得此礼品的兑换资格");
        }

        if(0==rankingDao.updateCustomerRankingExchangeById(exchange.getCustomerId())){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResultUtil.customer(5, "系统繁忙，请稍后再试");
        }

        // 写入兑换表
        StoreExchange storeExchange = new StoreExchange();
        storeExchange.setExchangeOrder(RandomUtil.getOrderNumber());
        storeExchange.setCustomerId(exchange.getCustomerId());
        storeExchange.setStoreGiftId(exchange.getId());
        storeExchange.setStoreExchangeTypeId(storeExchangeTypeEnum.getId());
        switch (storeExchangeTypeEnum){
            case PICKUP:
                storeExchange.setStoreExchangeLogisticsId(StoreExchangeLogisticsEnum.WAITING_RECEIVE.getId());
                break;
            case EXPRESS:
                storeExchange.setStoreExchangeLogisticsId(StoreExchangeLogisticsEnum.WAITING_EXPRESS.getId());
                break;
            default:
                break;
        }
        storeExchange.setPoints(0);
        storeDao.insertStoreExchange(storeExchange);

        switch (storeExchangeTypeEnum){
            case PICKUP:
                // 门店自提
                StoreExchangePickup storeExchangePickup = new StoreExchangePickup();
                storeExchangePickup.setStoreExchangeId(storeExchange.getId());
                storeExchangePickup.setStoreAreaId(exchange.getAreaId());
                storeExchangePickup.setStoreAreaPlaceId(exchange.getAreaPlaceId());
                storeDao.insertStoreExchangePickup(storeExchangePickup);
                break;
            case EXPRESS:
                // 快递
                StoreExchangeExpress storeExchangeExpress = new StoreExchangeExpress();
                storeExchangeExpress.setStoreExchangeId(storeExchange.getId());
                storeExchangeExpress.setCustomerAddressId(exchange.getCustomerAddressId());
                storeDao.insertStoreExchangeExpress(storeExchangeExpress);
                break;
            default:
                break;
        }

        return ResultUtil.customer(1, "兑换成功");

    }

    /**
     * 写入日志
     *
     * @param rankingLog 日志
     */
    @Override
    public void setRankingLog(RankingLog rankingLog) {
        rankingDao.insertRankingLog(rankingLog);
    }
}
