package cn.slashsoft.clinique.util;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class MapUtil {

    /**
     * 是否包含键
     * @param map 集合
     * @param key 键名
     * @return 是否
     */
    public static boolean hasKey(Map<String, Object> map, String key) {
        return map.containsKey(key);
    }

    /**
     * 获取字符型字符串
     * @param map 集合
     * @param key 键名
     * @param defaultValue 为空时的默认值
     * @return 值
     */
    public static String getString(Map<String, Object> map, String key, String defaultValue) {
        if (null != map && map.containsKey(key)) {
            return String.valueOf(map.get(key));
        }
        return defaultValue;
    }

    /**
     * 获取字符型字符串
     * @param map 集合
     * @param key 键名
     * @return 值
     */
    public static String getString(Map<String, Object> map, String key) {
        if (null != map && map.containsKey(key)) {
            return String.valueOf(map.get(key));
        }
        return null;
    }

    /**
     * 获取整型字符串
     * @param map 集合
     * @param key 键名
     * @return 值
     */
    public static Integer getInteger(Map<String, Object> map, String key) {
        if (null != map && map.containsKey(key)) {
            return Integer.valueOf(getString(map, key));
        }
        return null;
    }

    /**
     * 获取整型字符串
     * @param map 集合
     * @param key 键名
     * @return 值
     */
    public static int getInt(Map<String, Object> map, String key) {
        if (null != map && map.containsKey(key)) {
            return Integer.parseInt(getString(map, key));
        }
        return 0;
    }

    /**
     * 获取短数字型字符串
     * @param map 集合
     * @param key 键名
     * @return 值
     */
    public static Short getShort(Map<String, Object> map, String key) {
        if (null != map && map.containsKey(key)) {
            return Short.valueOf(getString(map, key));
        }
        return null;
    }

}
