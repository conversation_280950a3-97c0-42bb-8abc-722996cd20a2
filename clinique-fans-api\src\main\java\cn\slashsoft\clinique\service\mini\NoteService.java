package cn.slashsoft.clinique.service.mini;

import cn.slashsoft.clinique.domain.mini.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 笔记
 *
 * <AUTHOR>
 */
public interface NoteService {

    /**
     * 获取点赞排名，从高到底
     *
     * @param tagIds 标签编号
     * @param size 数量
     * @return 排名列表
     */
    List<NoteRanking> getRankingList(String tagIds, int size);

    /**
     * 获取点赞排名的缓存
     *
     * @param tagIds 标签编号
     * @param size 数量
     * @return 排名列表
     */
    List<NoteRanking> getRankingListFromCache(String tagIds, int size);

    /**
     * 保存笔记
     *
     * @param Note
     * @return 影响条数
     */
    Long insertNote(Note note);
    void insertCamNote(Long customerId,Object title,Object content,Long id,Integer status);

    /**
     * 更新笔记
     *
     * @param Note
     * @return 影响条数
     */
    Long updateNote(Note note);

    /**
     * 保存图片
     *
     * @param NotePhoto
     * @return
     */
    Long insertPhoto(NotePhoto notephoto);

    /**
     * 保存图片标签
     *
     * @param NotePhoto
     * @return
     */
    void insertNotePhotoTag(NotePhotoTag tag);

    /**
     * 保存笔记评论
     *
     * @param NoteDiscussBody
     */
    int insertDiscuss(NoteDiscuss noteDiscuss);

    List<String> filterDiscuss(NoteDiscuss noteDiscuss);

    /**
     * 删除笔记
     *
     * @param Note
     */
    int delNote(Long noteId, Long customerId);

    /**
     * 保存阅读笔记日志
     *
     * @param shareOpenId
     * @param NoteReadLog 日志
     */
    int insertReadLog(NoteReadLog log, String shareOpenId);

    /**
     * 拔草笔记
     *
     * @param NoteLikeLog
     */
    int insertLikeLog(NoteLikeLog log);

    /**
     * 删除 拔草笔记
     *
     * @param noteId
     * @param customerId
     */
    int unlikeNote(NoteLikeLog log);

    /**
     * 保存分享笔记日志
     *
     * @param NoteShareLog
     */
    int insertShareLog(NoteShareLog log);

    /**
     * 笔记是否拔草
     *
     * @param NoteLikeLog
     */
    int liked(Long noteId, Long customerId);

    /**
     * 搜索笔记标签
     *
     * @param key
     * @param type
     */
    ArrayList<NoteTag> getTagList(String key, int type);

    /**
     * 搜索笔记标签
     *
     * @param key
     * @param type
     */
    ArrayList<NoteTag> getTopTagList();

    List<NoteTag> getIndexTopTagList(int type);

    List<NoteTag> getSearchTopTagList();

    /**
     * 获取标签分组列表
     * @return 分组列表
     */
    List<NoteTagGroup> getNoteTagGroupList();

    /**
     * 保存标签搜索、使用日志
     *
     * @param NoteTagSearchLog 日志
     */
    int insertTagSearchLog(NoteTagSearchLog log);

    /**
     * 保存位置信息日志
     *
     * @param NoteAddressLog 日志
     */
    int insertTagAddressLog(NoteTagAddressLog log);

    /**
     * 获取用户笔记评论列表
     *
     * @param customerId
     */
    ArrayList<NoteDiscussMy> getCustomerDiscussList(Long customerId);

    /**
     * 获取笔记评论列表
     *
     * @param customerId
     */
    ArrayList<NoteDiscuss> getNoteDiscussList(Long noteId, int page, int pageSize);

    /**
     * 获取笔记评论数目表
     *
     * @param noteId
     */
    int getNoteDiscussCount(Long noteId);


    /**
     * 获取笔记列表
     *
     * @param tagId
     * @param start
     * @param pageSize
     */
    ArrayList<Note> getNoteListByGroup(Long tagId, Long customerId, int start, int pageSize, String sortType, int sign);

    /**
     * 获取笔记列表
     *
     * @param tagId
     * @param start
     * @param pageSize
     */
    ArrayList<Note> getNoteList(Long tagId, Long customerId, int start, int pageSize, String sortType, int sign);

    /**
     * 获取笔记数目
     *
     * @param tagId
     */
    int getNoteCount(Long tagId);

    /**
     * 获取笔记数目
     * @param groupId 组编号
     * @return 笔记数目
     */
    int getNoteCountByGroup(long groupId);

    /**
     * 获取用户笔记列表
     *
     * @param customerId
     * @param start
     */
    ArrayList<Note> getCustomerNoteList(Long customerId, int start);

    /**
     * 获取用户拔草笔记列表
     *
     * @param customerId
     * @param start
     */
    ArrayList<Note> getCustomerLikeNoteList(Long customerId, int start);

    /*
     * 获取用户笔记
     *
     *@param noteId
     */
    Note getNote(Long id, Long CustomerId);

    void delPhotosByNote(Long id);

    void delPhotoTagsByNote(Long id);

    List<CIndexBanner> GetCindexBanner();

    CIndexBanner GetCindexBannerById(Long id);

    List<Note> getKocList(Long customerId, int page, int pageSize);

    int getKocCount();

    Note getKoc(Long id, Long customerId);

    int unlikeKoc(NoteLikeLog log);

    int insertKocLikeLog(NoteLikeLog log);

    void updateKocTitle(Long detailId, String title, Long customerId);

    Note getKocByCampaign(long campaignId, long customerId);

    Note getNoteById(Long id, long customerId);
}
