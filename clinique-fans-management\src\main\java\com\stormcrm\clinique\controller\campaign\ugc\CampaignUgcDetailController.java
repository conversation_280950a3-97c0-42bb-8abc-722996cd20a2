package com.stormcrm.clinique.controller.campaign.ugc;

import com.stormcrm.clinique.domain.CampaignMsTalents;
import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgc;
import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgcDetail;
import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgcDetailPoint;
import com.stormcrm.clinique.service.CampaignMsTalentsService;
import com.stormcrm.clinique.service.campaign.ugc.CampaignUgcDetailService;
import com.stormcrm.clinique.service.campaign.ugc.CampaignUgcService;
import com.stormcrm.clinique.util.ResultUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("campaign-ugc-detail")
public class CampaignUgcDetailController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;

    private final CampaignUgcService campaignUgcService;
    private final CampaignUgcDetailService campaignUgcDetailService;

    public CampaignUgcDetailController(CampaignUgcService campaignUgcService, CampaignUgcDetailService campaignUgcDetailService) {
        this.campaignUgcService = campaignUgcService;
        this.campaignUgcDetailService = campaignUgcDetailService;
    }

    /**
     * 页面模版
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_DETAIL')")
    @GetMapping("{campaignId}")
    public String index(
            @PathVariable("campaignId") long campaignId,
            Model model
    ) {
        model.addAttribute("staticDomain", staticDomain);

        CampaignUgc campaignUgc = campaignUgcService.getById(campaignId);
        if(null == campaignUgc){
            return "m/fans/common/empty";
        }

        model.addAttribute("campaignId", campaignId);
        model.addAttribute("campaignUgc", campaignUgc);
        return "m/fans/campaign-ugc-detail/index";
    }

    /**
     * 查询所有-分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 列表
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_DETAIL')")
    @PostMapping("get-page/{campaignId}")
    @ResponseBody
    public String getPage(
            @PathVariable("campaignId") long campaignId,
            @RequestParam(value = "pagination[page]", required = false) Integer page,
            @RequestParam(value = "pagination[perpage]", required = false) Integer perpage,
            @RequestParam(value = "query[generalSearch]", required = false) String generalSearch,
            @RequestParam(value = "query[status]", required = false) Short status
    ) {

        if (null == page) {
            page = 1;
        }

        if (null == perpage) {
            perpage = 10;
        }

        List<CampaignUgcDetail> campaignUgcDetailList = campaignUgcDetailService.getPage(campaignId, page, 500, generalSearch, status);
        int count = campaignUgcDetailService.getPageCount(campaignId, generalSearch, status);

        JSONObject mata = new JSONObject();
        mata.put("page", page);
        mata.put("pages", (count + perpage - 1) / perpage);
        mata.put("perpage", perpage);
        mata.put("total", count);
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("mata", mata);
        result.put("data", campaignUgcDetailList);

        return JSONObject.toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }

    /**
     * 编辑模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_DETAIL')")
    @GetMapping("edit/{id}")
    public String edit(@PathVariable long id, Model model) {
        model.addAttribute("staticDomain", staticDomain);
        // 读数据库
        CampaignUgcDetail campaignUgcDetail = campaignUgcDetailService.getById(id);
        if (null == campaignUgcDetail) {
            return "m/fans/common/empty";
        }
        CampaignUgcDetailPoint point = campaignUgcDetailService.getCampaignUgcPoint(id);
        if(null != point){
            campaignUgcDetail.setPoint(point.getPoint());
        }
        campaignUgcDetail.setPointChoose(point);
        campaignUgcDetail.setImageList(campaignUgcDetailService.getImageById(id));
        model.addAttribute("id", id);
        model.addAttribute("campaignUgcDetail", campaignUgcDetail);
        return "m/fans/campaign-ugc-detail/edit";
    }

    /**
     * 同意
     *
     * @param id 自动编号
     * @return 处理结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_DETAIL_ACCEPT')")
    @GetMapping("accept/{id}")
    @ResponseBody
    public String accept(@PathVariable("id") long id) {
        if (0 == campaignUgcDetailService.accept(id)) {
            return ResultUtil.failToJson();
        }
        return ResultUtil.successToJson();
    }
    /**
     * 同意
     *
     * @param id 自动编号
     * @return 处理结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_DETAIL_ACCEPT')")
    @GetMapping("save-point/{id}")
    @ResponseBody
    public String savePoint(@PathVariable("id") long id,
                            @RequestParam("count") int count,
                            @RequestParam("percent") int percent,
                            @RequestParam("use") int use,
                            @RequestParam("copy") int copy) {
        CampaignUgcDetailPoint point = new CampaignUgcDetailPoint();
        point.setCount(count);
        point.setDetailId(id);
        point.setUse(use);
        point.setCopy(copy);
        point.setPercent(percent);
        point.setPoint(point.countPoint());
        if (0 == campaignUgcDetailService.savePoint( point)) {
            return ResultUtil.failToJson();
        }
        return ResultUtil.successToJson();
    }
    /**
     * 批量同意
     *
     * @param ids 自动编号
     * @return 处理结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_DETAIL_ACCEPT')")
    @GetMapping("accept-batch/{ids}")
    @ResponseBody
    public String acceptBatch(@PathVariable("ids") String ids) {
        if (0 == campaignUgcDetailService.acceptBatch(ids)) {
            return ResultUtil.failToJson();
        }
        return ResultUtil.successToJson();
    }

    /**
     * 拒绝
     *
     * @param id 自动编号
     * @return 处理结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_DETAIL_REJECT')")
    @GetMapping("reject/{id}")
    @ResponseBody
    public String reject(@PathVariable("id") long id) {
        if (0 == campaignUgcDetailService.reject(id)) {
            return ResultUtil.failToJson();
        }
        return ResultUtil.successToJson();
    }

    /**
     * 批量拒绝
     *
     * @param ids 自动编号
     * @return 处理结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_DETAIL_REJECT')")
    @GetMapping("reject-batch/{ids}")
    @ResponseBody
    public String rejectBatch(@PathVariable("ids") String ids) {
        if (0 == campaignUgcDetailService.rejectBatch(ids)) {
            return ResultUtil.failToJson();
        }
        return ResultUtil.successToJson();
    }

}
