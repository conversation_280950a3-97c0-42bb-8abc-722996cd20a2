package cn.slashsoft.clinique.dao.mini;


import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.mini.SpecialBanner;

 /* 我的特权 banner
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface SpecialBannerDao {
     
       /**
        * 获取我的特权Banner
        *
        */ @Select("SELECT * FROM  `special_banner`" +
                "  WHERE `status`= 1 order by `create_time` desc " )
	List<SpecialBanner> getSpecialBanner(); 
        
        /**
         * 获取我的特权Banner
         *
         */ @Select("SELECT * FROM  `special_banner`" +
                 "  WHERE id=#{id} AND `status`=1 limit 1 " )
         SpecialBanner getSpecialBannerById(Long id);

         /**
          * 获取我的特权Banner
          *
          */ @Select("SELECT * FROM  `special_banner`" +
                  "  WHERE `status`= 3 order by `create_time` desc " )
		List<SpecialBanner> getNormal();
}
