package cn.slashsoft.clinique.dao.campaign;

import cn.slashsoft.clinique.domain.campaign.*;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * EBCI 美白达人官
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignEbciTalentsDao {

    /**
     * 获取美白达人官信息
     *
     * @param customerId 顾客编号
     * @return 美白达人官信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `status` " +
            "FROM " +
            "   `campaign_ebci_talents` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    CampaignEbciTalents getCampaignEbciTalents(long customerId);

    /**
     * 获取美白达人官信息, 和上传图片的数量
     *
     * @param customerId 顾客编号
     * @return 美白达人官信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `alert`," +
            "   `status`," +
            "   `upload_time`," +
            "   `examine_time`," +
            "   (" +
            "       SELECT " +
            "           COUNT(`id`) " +
            "       FROM " +
            "           `campaign_ebci_talents_image` " +
            "       WHERE " +
            "           `campaign_ebci_talents_id`=`campaign_ebci_talents`.`id` " +
            "           AND `status`=1" +
            "   ) `image_count` " +
            "FROM " +
            "   `campaign_ebci_talents` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    CampaignEbciTalents getCampaignEbciTalentsWithImageCount(long customerId);

    /**
     * 获取所有图片
     * @param campaignEbciTalentsId 顾客活动信息编号
     * @return 图片地址
     */
    @Select("SELECT " +
            "   `image_url`," +
            "   `status` " +
            "FROM " +
            "   `campaign_ebci_talents_image` " +
            "WHERE " +
            "   `campaign_ebci_talents_id`=#{campaignEbciTalentsId} " +
            "ORDER BY `id` DESC")
    List<CampaignEbciTalentsImage> getCampaignEbciTalentsImageList(long campaignEbciTalentsId);

    /**
     * 获取当前TOP信息
     * @return TOP信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `stage` " +
            "FROM " +
            "   `campaign_ebci_talents_top` " +
            "WHERE " +
            "   `status`=1 " +
            "   AND NOW()>=`start_time` AND NOW()<=`end_time`")
    CampaignEbciTalentsTop getCampaignEbciTalentsTop();

    /**
     * 获取入选TOP的名单
     * @param campaignEbciTalentsTopId TOP信息编号
     * @return 名单
     */
    @Select("SELECT " +
            "   `t`.`id` `talents_detail_id`," +
            "   `t`.`examine_time`, " +
            "   `w`.`nick_name`, " +
            "   `w`.`avatar_url` " +
            "FROM " +
            "   `campaign_ebci_talents_top_detail` `d` " +
            "       INNER JOIN " +
            "   `campaign_ebci_talents` `t` " +
            "       ON `d`.`campaign_ebci_talents_id`=`t`.`id` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `t`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "   `d`.`campaign_ebci_talents_top_id`=#{campaignEbciTalentsTopId} AND `d`.`status`=1 " +
            "ORDER BY " +
            "   `d`.`sort`")
    List<CampaignEbciTalentsTopDetail> getCampaignEbciTalentsTopDetailList(long campaignEbciTalentsTopId);

    /**
     * 获取入选TOP的图片
     * @param campaignEbciTalentsTopId TOP信息编号
     * @return 图片
     */
    @Select("SELECT " +
            "   `i`.`campaign_ebci_talents_id` `talents_detail_id`, " +
            "   `i`.`image_url` " +
            "FROM " +
            "   `campaign_ebci_talents_top_detail` `d` " +
            "       INNER JOIN " +
            "   `campaign_ebci_talents_image` `i` " +
            "       ON `d`.`campaign_ebci_talents_id`=`i`.`campaign_ebci_talents_id` " +
            "WHERE " +
            "   `d`.`campaign_ebci_talents_top_id`=#{campaignEbciTalentsTopId}" +
            "   AND `d`.`status`=1 AND `i`.`status`=1")
    List<CampaignEbciTalentsTopDetailImage> getCampaignEbciTalentsTopDetailImageList(long campaignEbciTalentsTopId);

    /**
     * 写入美白达人官信息
     *
     * @param campaignEbciTalents 美白达人官信息
     */
    @Insert("INSERT INTO `campaign_ebci_talents`(" +
            "   `customer_id`, " +
            "   `status` " +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{status}" +
            ")")
    void insertCampaignEbciTalents(CampaignEbciTalents campaignEbciTalents);

    /**
     * 变改状态
     *
     * @param customerId     顾客编号
     * @param originalStatus 原始状态
     * @param newStatus      新状态
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ebci_talents` " +
            "SET " +
            "   `status`=#{newStatus} " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "   AND `status`=#{originalStatus}")
    int updateCampaignEbciTalents(long customerId, short originalStatus, short newStatus);

    /**
     * 更新提醒状态
     * @param customerId 顾客编号
     */
    @Update("UPDATE " +
            "   `campaign_ebci_talents` " +
            "SET " +
            "   `alert`=0 " +
            "WHERE " +
            "   `customer_id`=#{customerId}")
    void updateCampaignEbciTalentsAlert(long customerId);

    /**
     * 变改状态
     *
     * @param customerId     顾客编号
     * @param originalStatus 原始状态
     * @param newStatus      新状态
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ebci_talents` " +
            "SET " +
            "   `status`=#{newStatus}," +
            "   `upload_time`=NOW() " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "   AND `status`=#{originalStatus}")
    int updateCampaignEbciTalentsWithUploadTime(long customerId, short originalStatus, short newStatus);

    /**
     * 设置图片无效
     *
     * @param campaignEbciTalentsId 顾客活动信息编号
     */
    @Update("UPDATE " +
            "   `campaign_ebci_talents_image` " +
            "SET " +
            "   `status`=0 " +
            "WHERE " +
            "   `campaign_ebci_talents_id`=#{campaignEbciTalentsId}")
    void updateCampaignEbciTalentsImageStatus(long campaignEbciTalentsId);

    /**
     * 写入图片信息
     * @param campaignEbciTalentsImage 图片信息
     */
    @Insert("INSERT INTO `campaign_ebci_talents_image`(" +
            "   `campaign_ebci_talents_id`, " +
            "   `image_url`" +
            ") " +
            "VALUES (" +
            "   #{campaignEbciTalentsId}, " +
            "   #{imageUrl}" +
            ")")
    void insertCampaignEbciTalentsImage(CampaignEbciTalentsImage campaignEbciTalentsImage);

    /**
     * 写入活动操作日志
     * @param campaignEbciTalentsLog 操作日志
     */
    @Insert("INSERT INTO `campaign_ebci_talents_log`(" +
            "   `customer_id`, " +
            "   `status`, " +
            "   `content`" +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{status}, " +
            "   #{content}" +
            ")")
    void insertCampaignEbciTalentsLog(CampaignEbciTalentsLog campaignEbciTalentsLog);


    /**
     * 写入访问日志
     *
     * @param campaignEbciTalentsViewLog 日志
     */
    @Insert("INSERT INTO `campaign_ebci_talents_view_log`(" +
            "   `customer_id`," +
            "   `source`," +
            "   `page`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{source}," +
            "   #{page}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertCampaignEbciTalentsViewLog(CampaignEbciTalentsViewLog campaignEbciTalentsViewLog);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Update("UPDATE " +
            "   `campaign_ebci_talents_view_log` " +
            "SET " +
            "   `end_time`=NOW()," +
            "   `diff_second`=TimeStampDiff(SECOND,`start_time`,NOW()) " +
            "WHERE " +
            "   `id`=#{id}")
    void setCampaignEbciTalentsViewLog(long id);

}
