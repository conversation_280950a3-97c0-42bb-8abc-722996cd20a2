package cn.slashsoft.clinique.domain.mini;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 记录获得碎片信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TTestimonyFragmentType implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;
    private Integer fragmentType;
    private Integer fragmentQty;
    private Integer fragmentLimit;
    private Date createTime;
    private Date updateTime;

}
