package cn.slashsoft.clinique.controller.mini;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.slashsoft.clinique.domain.mini.CIndexBanner;
import cn.slashsoft.clinique.domain.mini.NoteShareLog;
import cn.slashsoft.clinique.domain.mini.NoteTag;
import cn.slashsoft.clinique.domain.mini.NoteTagSearchLog;
import cn.slashsoft.clinique.domain.mini.Video;
import cn.slashsoft.clinique.domain.mini.VideoLikeLog;
import cn.slashsoft.clinique.domain.mini.VideoPlayLog;
import cn.slashsoft.clinique.domain.mini.VideoShareLog;
import cn.slashsoft.clinique.domain.mini.VideoTag;
import cn.slashsoft.clinique.domain.mini.VideoTagDefine;
import cn.slashsoft.clinique.domain.mini.VideoTagSearchLog;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.CustomerService;
import cn.slashsoft.clinique.service.mini.VideoService;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.mini.CIndexVideoAndBannerListVo;
import cn.slashsoft.clinique.vo.mini.NoteTagVo;
import cn.slashsoft.clinique.vo.mini.TagSearch;
import cn.slashsoft.clinique.vo.mini.VideoTagVo;

/**
 * 活动相关控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class VideoController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
	@Resource
	private HttpServletRequest request;

	private final VideoService videoService;
	private final CustomerService customerService;
	private final StringRedisTemplate stringRedisTemplate;

	public VideoController(VideoService videoService, CustomerService customerService,
			StringRedisTemplate stringRedisTemplate) {
		this.videoService = videoService;
		this.customerService = customerService;
		this.stringRedisTemplate = stringRedisTemplate;
	}

	/**
	 * 获取标签列表
	 *
	 * @request key 搜索关键词
	 * @request type 搜索类型
	 * @request top - yes or no 是否返回置顶，当此参数不为no时，其他两个参数无效
	 *
	 * @return 标签列表
	 */
	@PostMapping("/video/tag/get-data")
	public String getTagList(@RequestBody TagSearch tagSearch) {
		List<VideoTagDefine> videoTagList = null;
		
		// 获取缓存中的顾客编号
		Long customerId = 0L ;
		if(request.getAttribute("customerId") != null) {
			customerId =(Long) request.getAttribute("customerId");
		}
		
		if (tagSearch.getTop().equals("no")) {
			if(customerId.intValue() != 0) {
				VideoTagSearchLog log = new VideoTagSearchLog();
				log.setCustomerId(customerId);
				log.setTagType(tagSearch.getType());
				log.setTagTitle(tagSearch.getKey());
				log.setTagId(0L);
				videoService.insertTagSearchLog(log);
			}
			
			videoTagList = videoService.getTagList(tagSearch.getKey(), tagSearch.getType());
		} else {
			videoTagList = videoService.getTopTagList();
		}
		

		VideoTagVo videoTagVo = new VideoTagVo();
		videoTagVo.setTagList(videoTagList);

		return ResultUtil.customer(ResultEnum.SUCCESS, videoTagVo);
	}

	/**
	 * 获取视频列表
	 *
	 * @request tag 标签id
	 *
	 * @return 视频列表
	 */
	@PostMapping("/video/get-data/{tag}")
	public String getVideoList(@PathVariable("tag") Long tagId) {

		Long customerId = 0L ;
		if(request.getAttribute("customerId") != null) {
			customerId =(Long) request.getAttribute("customerId");
		}
		//if (customerId == 0) {
		//	return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
		//}
		ArrayList<Video>  all = new ArrayList<Video>();
		ArrayList<Video> videos = videoService.getVideoList(customerId, tagId);
		for(Video video: videos) {
			video.setSortTime(video.getUpdateTime().getTime());
			all.add(video);
		}
		return ResultUtil.customer(ResultEnum.SUCCESS, all);
	}

	

	/**
	 * 获取用户拔草视频列表
	 *
	 * @request type 搜索类型
	 *
	 * @return 标签列表
	 */
	@PostMapping("/video/mylike/get-data")
	public String getMyLikeVideoList() {
		// 获取缓存中的顾客编号
		long customerId = (long) request.getAttribute("customerId");
		if (customerId == 0) {
			return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
		}
		List<Video> videoList = videoService.getCustomerLikeVideoList(customerId, 0);

		return ResultUtil.customer(ResultEnum.SUCCESS, videoList);
	}
	/**
	 * 拔草视频
	 *
	 * @request id 视频id
	 *
	 * @return 影响行数
	 */
	@PostMapping("/video/like/{id}")
	public String VideoLike(@PathVariable("id") Long Id) {

		// 获取缓存中的顾客编号
		long customerId = (long) request.getAttribute("customerId");
		if (customerId == 0) {
			return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
		}

		VideoLikeLog log = new VideoLikeLog();
		log.setCustomerId(customerId);
		log.setVideoId(Id);
		return ResultUtil.customer(ResultEnum.SUCCESS, videoService.insertLikeLog(log));
	}

	/**
	 * 播放视频
	 *
	 * @request id 视频id
	 * @request time 视频播放时间
	 *
	 * @return 影响行数
	 */
	@PostMapping("/video/play/{id}/{time}/{all}")
	public String VideoRead(
			@PathVariable("id") Long Id,
			@PathVariable("time") Float playTime,
			@PathVariable("all") Float duration,
			@RequestParam(value = "openid", required = false) String shareOpenId) {

		// 获取缓存中的顾客编号
		long customerId = (long) request.getAttribute("customerId");
		if (customerId == 0) {
			return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
		}

		VideoPlayLog log = new VideoPlayLog();
		log.setCustomerId(customerId);
		log.setVideoId(Id);
		log.setPlayTime(playTime);
		log.setDuration(duration);
		if(playTime == null || playTime.intValue() == 0) {
			log.setType(1);
		}else {
			log.setType(2);
		}
		
		return ResultUtil.customer(ResultEnum.SUCCESS, videoService.insertReadLog(log, shareOpenId));
	}

	/**
	 * 删除拔草
	 *
	 * @request id 视频id
	 *
	 * @return 影响行数
	 */
	@PostMapping("/video/unlike/{id}")
	public String VideoNotLike(@PathVariable("id") Long Id) {

		// 获取缓存中的顾客编号
		long customerId = (long) request.getAttribute("customerId");

		if (customerId == 0) {
			return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
		}

		VideoLikeLog log = new VideoLikeLog();
		log.setCustomerId(customerId);
		log.setVideoId(Id);
		return ResultUtil.customer(ResultEnum.SUCCESS, videoService.unlikeVideo(log));
	}
	/**
	 * 分享笔记日志
	 *
	 * @request id 笔记id
	 *
	 * @return 影响行数
	 */
	@PostMapping("/video/share/{id}")
	public String VideoShare(@PathVariable("id") Long Id) {

		// 获取缓存中的顾客编号
		Long customerId = (Long) request.getAttribute("customerId");

		if (customerId == 0) {
			return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
		}

		VideoShareLog log = new VideoShareLog();
		log.setCustomerId(customerId);
		
		String shareSign = "";
		
		log.setShareSign(shareSign);
		log.setVideoId(Id);
		int count = this.videoService.insertShareLog(log);
		if(count > 0) {
			return ResultUtil.customer(ResultEnum.SUCCESS, shareSign);
		}
		return ResultUtil.customer(ResultEnum.SUCCESS, ResultEnum.FAILED);
	}
	
}
