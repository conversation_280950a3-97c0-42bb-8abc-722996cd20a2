package cn.slashsoft.clinique.domain.mini;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 盲盒店铺库存设置
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TTestimonyProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;
    private String productCode;
    private String productName;
    private Date createTime;
    private Date updateTime;
}
