package cn.slashsoft.clinique.service.mini;

import cn.slashsoft.clinique.domain.mini.SmotAuthorize;

/**
 * 一次性订阅消息服务
 *
 * <AUTHOR>
 */
public interface SmotService {

    /**
     * 51活动发送礼品领取成功一次性消息
     *
     * @param openid     小程序唯一编号
     * @param customerId 顾客编号
     */
    void receiveCampaignC520(String openid, long customerId);

    /**
     * 520活动发送礼品领取成功一次性消息
     *
     * @param openid   小程序唯一编号
     * @param nickName 顾客昵称
     */
    void receiveCampaignG520(String openid, String nickName);

    /**
     * 写入一次性订阅消息授权选择记录
     *
     * @param smotAuthorize 授权选择记录
     */
    void insertSmotAuthorize(SmotAuthorize smotAuthorize);

    /**
     * 获取是否授权
     *
     * @param type   类型
     * @param openid 小程序编号
     * @param choice 是否选择
     * @return 数量
     */
    int getSmotAuthorize(short type, String openid, boolean choice);

    /**
     * 获取是否授权
     *
     * @param type   类型
     * @param openid 小程序编号
     * @return 数量
     */
    int getSmotAuthorize(short type, String openid);

}
