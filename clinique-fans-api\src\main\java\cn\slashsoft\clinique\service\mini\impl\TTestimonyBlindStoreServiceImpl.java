package cn.slashsoft.clinique.service.mini.impl;

import cn.slashsoft.clinique.dao.mini.TTestimonyBlindStoreDao;
import cn.slashsoft.clinique.domain.mini.TTestimonyBlindStore;
import cn.slashsoft.clinique.service.mini.TTestimonyBlindStoreService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
* <p>
    * 盲盒店铺库存设置
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
@Service
public class TTestimonyBlindStoreServiceImpl implements TTestimonyBlindStoreService {

    @Resource
    private TTestimonyBlindStoreDao tTestimonyBlindStoreDao;

    @Override
    public void insertTTestimonyBlindStore(TTestimonyBlindStore tTestimonyBlindStore) {
        tTestimonyBlindStoreDao.insertTTestimonyBlindStore(tTestimonyBlindStore);
    }

    @Override
    public void updateTTestimonyBlindStore(TTestimonyBlindStore tTestimonyBlindStore) {
        tTestimonyBlindStoreDao.updateTTestimonyBlindStore(tTestimonyBlindStore);
    }

    @Override
    public TTestimonyBlindStore getTTestimonyBlindStore(Long id) {
        return tTestimonyBlindStoreDao.getTTestimonyBlindStore(id);
    }

    @Override
    public void subtractTTestimonyBlindStore(Integer storeId, Integer productId) {
        tTestimonyBlindStoreDao.subtractTTestimonyBlindStore(storeId, productId);
    }

    /**
     * 获取指定商品库存
     * @param storeId
     * @param productId
     * @return
     */
    public Integer getTTestimonyBlindStoreAmount(Integer storeId, Integer productId) {
        return tTestimonyBlindStoreDao.getTTestimonyBlindStoreAmount(storeId, productId);
    }
}
