package cn.slashsoft.clinique.service.mini;

import cn.slashsoft.clinique.domain.mini.Customer;
import cn.slashsoft.clinique.domain.mini.RankingGift;
import cn.slashsoft.clinique.domain.mini.RankingLog;
import cn.slashsoft.clinique.enums.StoreExchangeTypeEnum;
import cn.slashsoft.clinique.vo.mini.Exchange;
import cn.slashsoft.clinique.vo.Result;

import java.util.List;

/**
 * 积分排名
 * <AUTHOR>
 */
public interface RankingService {

    /**
     * 获取我的排名
     * 排名页
     *
     * @param customerId 顾客编号
     * @return 顾客信息
     */
    Customer getCustomerRankingById(long customerId);

    /**
     * 获取排名前5的顾客
     * @return 顾客信息
     */
    List<Customer> getCustomerRankingTop();

    /**
     * 获取有效的商城礼品列表，按更新时间倒序排列
     * 积分兑礼
     *
     * @param customerId 顾客编号
     * @return 商城礼品列表
     */
    List<RankingGift> getRankingGiftList(long customerId);

    /**
     * 跟据礼品编号获取礼品资料，和已兑礼数量
     *
     * @param id         礼品编号
     * @param customerId 顾客编号
     * @return 礼品资料
     */
    RankingGift getRankingGiftById(long id, long customerId);

    /**
     * 立即兑换
     *
     * @param exchange              兑换信息
     * @param storeExchangeTypeEnum 兑换类型
     * @return 处理结果
     */
    Result exchange(Exchange exchange, StoreExchangeTypeEnum storeExchangeTypeEnum);

    /**
     * 写入日志
     *
     * @param rankingLog 日志
     */
    void setRankingLog(RankingLog rankingLog);

}
