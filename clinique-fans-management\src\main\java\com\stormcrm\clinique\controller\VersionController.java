package com.stormcrm.clinique.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.stormcrm.clinique.enums.ResultEnum;
import com.stormcrm.clinique.util.ResultUtil;

/**
 * 版本
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class VersionController {	
	/**
     *  Berg 后台版本号
     */
    @GetMapping("/version")
    @ResponseBody
    public String version() {      
    	return ResultUtil.customer(ResultEnum.SUCCESS, "成功","4.3.2");
    }

}
