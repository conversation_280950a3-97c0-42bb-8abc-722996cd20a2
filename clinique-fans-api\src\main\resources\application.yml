server:
  # 端口号
  port: 9121
  servlet:
    session:
      timeout: 172800S

spring:
  application:
    # 服务名称
    name: clinique-fans-api
  servlet:
    multipart:
      max-file-size: 20MB
  profiles:
    active: test

  resources:
    # 静态资源位置
    static-locations: file:static/

  freemarker:
    # 模版位置
    template-loader-path: file:templates/

eureka:
  instance:
    # 实例的名称
    appname: ${spring.application.name}
    # 显示IP地址
    prefer-ip-address: true
    # 在注册中心中显示IP:PORT方式
    instance-id: ${spring.cloud.client.ip-address}:${server.port}

mybatis:
  configuration:
    # 数据库字段与实体类属性名字自动转换
    map-underscore-to-camel-case: true

# 微信接口地址
wechat:
  mini:
    url:
      code-2-session: https://api.weixin.qq.com/sns/jscode2session?appid=APPID&secret=SECRET&js_code=JSCODE&grant_type=authorization_code
      access-token: https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET
      get-unlimited: https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=
      subscribe-message: https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=
      security-msg-sec-check: https://api.weixin.qq.com/wxa/msg_sec_check?access_token=
  official:
    url:
      user-info: https://api.weixin.qq.com/sns/userinfo?access_token=ACCESS_TOKEN&openid=OPENID&lang=zh_CN

