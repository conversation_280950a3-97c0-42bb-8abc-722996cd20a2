package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.domain.StoreGift;
import com.stormcrm.clinique.service.StoreGiftService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.vo.Result;
import com.stormcrm.clinique.dao.StoreGiftDao;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商城礼品管理
 *
 * <AUTHOR>
 */
@Service
public class StoreGiftServiceImpl implements StoreGiftService {

    private final StoreGiftDao storeGiftDao;

    public StoreGiftServiceImpl(StoreGiftDao storeGiftDao) {
        this.storeGiftDao = storeGiftDao;
    }

    /**
     * 查询所有礼品
     *
     * @return 礼品列表
     */
    @Override
    public List<StoreGift> getAll() {
        return storeGiftDao.getAll();
    }

    /**
     * 查询所有礼品列表的记录数
     *
     * @return 记录数
     */
    @Override
    public int getAllCount() {

        return storeGiftDao.getAllCount();
    }

    /**
     * 查询所有礼品带分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索
     * @param status        状态
     * @return 礼品列表分页
     */
    @Override
    public List<StoreGift> getPage(int page, int perpage, String generalSearch, Boolean status) {
        return storeGiftDao.getPage((page - 1) * perpage, perpage, generalSearch, status);
    }

    /**
     * 查询所有礼品带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    @Override
    public int getPageCount(String generalSearch, Boolean status) {
        return storeGiftDao.getPageCount(generalSearch, status);
    }

    /**
     * 查询
     *
     * @param id 自动编号
     * @return 胸章信息
     */
    @Override
    public StoreGift getById(long id) {
        return storeGiftDao.getById(id);
    }

    /**
     * 保存
     *
     * @param storeGift 信息
     * @return 影响的行数
     */
    @Override
    public Result save(StoreGift storeGift) {
        storeGiftDao.save(storeGift);
        return ResultUtil.success("增加成功!");
    }

    /**
     * 更新
     *
     * @param storeGift 信息
     * @return 影响的行数
     */
    @Override
    public Result update(StoreGift storeGift) {
        storeGiftDao.update(storeGift);
        return ResultUtil.success("编辑成功!");
    }

    /**
     * 上架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Override
    public int upper(long id) {
        return storeGiftDao.upper(id);
    }

    /**
     * 下架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Override
    public int lower(long id) {
        return storeGiftDao.lower(id);
    }

    /**
     * 逻辑删除
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Override
    public int del(long id) {
        return storeGiftDao.del(id);
    }

}
