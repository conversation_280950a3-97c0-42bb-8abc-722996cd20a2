package cn.slashsoft.clinique.enums;

import lombok.Getter;

/**
 * C币类别
 *
 * <AUTHOR>
 */
public enum CBForeignEnum {

    // 积分类别
    BINDING          ((short) 1, "绑定会员中心"),
    INFO             ((short) 2, "完善个人资料"),
    COMMENT          ((short) 3, "公众号文章精选评论"),
    LIKE             ((short) 4, "公众号文章点赞Top3评论"),
    LINK             ((short) 5, "公众号文章链接进入小程序"),
    TMALL            ((short) 6, "天猫会员额外赠分"),
    EXCHANGE         ((short) 7, "礼品兑换"),
    EXPIRED          ((short) 8, "积分过期"),
    CNY              ((short) 9, "“鼠你最红”奖励积分"),
    VIDEO_SIGN       ((short) 10, "视频签到活动"),
    EBCI_SIGN        ((short) 11, "EBCI签到活动"),
    TMALL_REGISTER   ((short) 12, "天猫专享积分"),
    EBCI_TALENTS     ((short) 13, "美白新品体验官"),
    MS_TALENTS       ((short) 14, "黄油达人傍"),
    LASER_TALENTS    ((short) 15, "夏日抚纹作战"),
    NOTE_PUBLISH    ((short) 16, "发布个人笔记"),
    NOTE_VIDEO_SHARE    	((short) 17, "分享转发笔记/视频"),
    NOTE_VIDEO_ACTION    	((short) 18, "社群内阅读/评论/收藏"),
    NOTE_ADDTAG    	((short) 19, "参与品牌话题互动"),
    NOTE_FAVORITE    ((short) 20, "个人笔记加精"),
    KOC_JOIN    	((short) 21, "参与KOC争夺战"),
    VIDEO_PLAY   	((short) 22, "观看视频/直播"),
    ZHENGYAN_GET    ((short) 23, "活动加赠-KOC抢鲜玩"),
    NOTE_TAG_41    	((short) 24, "活动加赠-KOC抢鲜玩"),
    WECHAT_WORK_BINDING    ((short) 25, "王牌见面礼")
    ;

    @Getter
    private final short id;

    @Getter
    private final String name;

    CBForeignEnum(short id, String name) {
        this.id = id;
        this.name = name;
    }
}
