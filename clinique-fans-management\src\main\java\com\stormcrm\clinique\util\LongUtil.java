package com.stormcrm.clinique.util;

/**
 * 长整型工具
 * <AUTHOR>
 */
public class LongUtil {

    /**
     * 字符串转长整型
     * @param s 字符串
     * @return 长整型
     */
    public static long parseInt(String s){
        try {
            if(null == s || s.isEmpty()){
                return 0;
            }
            return Long.parseLong(s);
        }
        catch (Exception e){
            return 0;
        }
    }

}
