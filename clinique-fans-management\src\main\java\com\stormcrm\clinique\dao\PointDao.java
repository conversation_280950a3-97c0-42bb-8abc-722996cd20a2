package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.PointTransaction;

import java.util.List;

import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

/**
 * 积分相关的数据库操作
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface PointDao {

    /**
     * 写入积分变动表
     *
     * @param pointTransaction 积分变动表
     */
    @Insert("INSERT INTO `point_transaction`(" +
            "   `customer_id`, " +
            "   `point_type_id`, " +
            "   `points`, " +
            "   `remaining_points`, " +
            "   `remark`, " +
            "   `start_time`, " +
            "   `expired_time`, " +
            "   `foreign_id`, " +
            "   `foreign_master_id`, " +
            "   `foreign_detail_id`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{pointTypeId}," +
            "   #{points}," +
            "   #{remainingPoints}," +
            "   #{remark}," +
            "   #{startTime}," +
            "   #{expiredTime}," +
            "   #{foreignId}," +
            "   #{foreignMasterId}," +
            "   #{foreignDetailId}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertPointTransaction(PointTransaction pointTransaction);


    /**
     * copy from  clinique-fans-schedule/src/main/java/cn/slashsoft/clinique/dao/RankingDao.java
     * <p>
     * 定時服務里定時更新，這邊刪除評論積分后，觸發一次
     * <p>
     * 不同于定時任務的全量，這裡只更新單個客戶的
     * <p>
     * 更新排名
     */
    @Update("UPDATE " +
            "   `customer` `c` " +
            "       INNER JOIN " +
            "   (" +
            "       SELECT" +
            "           @RANK:=@RANK+1 `ranking`," +
            "           `p`.`customer_id`," +
            "           `p`.`points_total`," +
            "           `p`.`id`" +
            "       FROM " +
            "           (" +
            "               SELECT " +
            "                   `customer_id`," +
            "                   SUM(`points`) `points_total`," +
            "                   MAX(`id`) `id`" +
            "               FROM " +
            "                   `point_transaction`" +
            "               WHERE" +
            "                   `status`=1 " +
            "                   AND `points`>0 " +
            "               GROUP BY" +
            "                   `customer_id`" +
            "               ORDER BY" +
            "                   `points_total` DESC, " +
            "                   `id`" +
            "           ) `p`, (SELECT @RANK:=0) `i`" +
            "   ) `p`" +
            "       on `p`.`customer_id` = `c`.`id` " +
            "SET " +
            "   `c`.`ranking`=`p`.`ranking`," +
            "   `c`.`ranking_always`=`p`.`ranking`," +
            "   `c`.`points_total`=`p`.`points_total` "
            // 這個語句更新的是排名，所以不適合加customerId.若只更新某個用戶的話，會導致排名靠前但是分數比較少的情況，或者排名相同但分不對的情況。
//            + "\tWHERE\n" +
//            "\t`c`.`id` = #{customerId} "
    )
    void updateRanking();

    /**
     * 獲取加CB的記錄
     *
     * @return 加CB單條記錄
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `point_transaction` " +
            "WHERE " +
            "   `customer_id` = #{customerId} " +
//            "   AND `status` = 1" +
            " AND `point_type_id`= #{pointTypeId} " +
            " AND `foreign_master_id`=#{foreignMasterId} " +
            " AND `foreign_id`=#{foreignId} " +
            " AND `foreign_detail_id`=#{foreignDetailId} "
    )
    PointTransaction get(PointTransaction pt);

    /**
     * 删除加CB的記錄
     *
     * @param id point_transaction id
     */
    @Delete("DELETE FROM `point_transaction`" +
            " WHERE " +
            " `id`=#{id} "
    )
    int deletePointTransactionBy(long id);

}
