package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.domain.mini.Follow;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciLog;
import cn.slashsoft.clinique.service.mini.CustomerService;
import cn.slashsoft.clinique.service.mini.MotService;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.service.mini.SnsService;
import cn.slashsoft.clinique.service.campaign.CampaignEbciSignApplyService;
import cn.slashsoft.clinique.util.CookieUtil;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.util.VerifyUtil;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * 黄油变粉安瓶级保湿补光申领
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/official")
public class CampaignEbciSignApplyController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    @Resource
    private HttpServletResponse response;

    private final CustomerService customerService;
    private final CampaignEbciSignApplyService campaignEbciSignApplyService;
    private final OutsideService outsideService;
    private final SnsService snsService;
    private final MotService motService;
    private final StringRedisTemplate stringRedisTemplate;

    public CampaignEbciSignApplyController(CampaignEbciSignApplyService campaignEbciSignApplyService, SnsService snsService, OutsideService outsideService, StringRedisTemplate stringRedisTemplate, CustomerService customerService, MotService motService) {
        this.campaignEbciSignApplyService = campaignEbciSignApplyService;
        this.snsService = snsService;
        this.outsideService = outsideService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.customerService = customerService;
        this.motService = motService;
    }

    @GetMapping("/campaign-ebci-sign/{unionid}/{source}.html")
    public String index(
            @PathVariable("source") String source,
            @PathVariable("unionid") String unionid,
            Model model
    ) {

        if (StringUtil.isNullOrEmpty(unionid) || "none".equals(unionid)) {
            return "w/fans/official/ebci-sign/mini/none";
        }

        String openid = (String) request.getAttribute("miniOpenid");

        // 读取是否填写过
        if (0 < campaignEbciSignApplyService.hasDetailByOpenid(openid)) {
            return "redirect:/official/campaign-ebci-sign-result/" + unionid + "/" + source + ".html";
        }

        Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciSignApplyStartTime"));
        Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciSignApplyEndTime"));

        if (DateUtil.laterThanNow(startTime)) {
            model.addAttribute("notStart", true);
        } else {
            model.addAttribute("notStart", false);
        }

        if (DateUtil.earlierThanNow(endTime)) {
            model.addAttribute("isEnd", true);
        } else {
            model.addAttribute("isEnd", false);
        }

        String phoneNumber = campaignEbciSignApplyService.getPhoneNumberByUnionid(unionid);

        // 保存日志
        CampaignEbciLog campaignEbciLog = new CampaignEbciLog();
        campaignEbciLog.setType((short) 1);
        campaignEbciLog.setUniqueId(openid);
        campaignEbciLog.setSource(source);
        campaignEbciLog.setPage("index");
        campaignEbciSignApplyService.insertLog(campaignEbciLog);

        model.addAttribute("unionid", unionid);
        model.addAttribute("phoneNumber", phoneNumber);
        model.addAttribute("serviceName", "official");
        model.addAttribute("source", source);
       
            model.addAttribute("staticDomain", staticDomain);
        return "w/fans/official/ebci-sign/mini/index";
    }

    @GetMapping("/campaign-ebci-sign-verify-code/{phoneNumber}")
    @ResponseBody
    public String getVerifyCode(
            @PathVariable("phoneNumber") String phoneNumber
    ) {
        return "{\"code\":" + snsService.sendVerifyCode(phoneNumber) + "}";
    }

    @PostMapping("/campaign-ebci-sign-submit/{unionid}/{source}.html")
    @ResponseBody
    public String submit(
            @PathVariable("source") String source,
            @PathVariable("unionid") String unionid,
            @RequestParam("formName") String name,
            @RequestParam("formPhoneNumber") String phoneNumber,
            @RequestParam("formVerifyCode") String verifyCode,
            @RequestParam("formCity") String city,
            @RequestParam("formStore") String store
    ) {

        String openid = (String) request.getAttribute("miniOpenid");

        // 判断有没有资格
        if(0 == campaignEbciSignApplyService.hadSigned(openid)){
            return "{\"code\":9,\"message\":\"您还没有完成7日打卡\"}";
        }

        Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciSignApplyStartTime"));
        Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciSignApplyEndTime"));

        if (DateUtil.laterThanNow(startTime)) {
            return "{\"code\":9,\"message\":\"活动未开始\"}";
        }

        if (DateUtil.earlierThanNow(endTime)) {
            return "{\"code\":9,\"message\":\"活动已结束\"}";
        }

        if (!VerifyUtil.required(name)) {
            return "{\"code\":9,\"message\":\"请输入姓名\"}";
        }

        if (!VerifyUtil.required(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.isPhoneNumber(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.required(verifyCode)) {
            return "{\"code\":9,\"message\":\"请输入验证码\"}";
        }

        if (!VerifyUtil.isVerifyCode(verifyCode)) {
            return "{\"code\":9,\"message\":\"验证码格式错误\"}";
        }

        // 验证手机是否一致
        String sessionPhoneNumber = (String) request.getSession().getAttribute("verifyPhoneNumber");
        if (!phoneNumber.equals(sessionPhoneNumber)) {
            return "{\"code\":9,\"message\":\"请重新获取验证码\"}";
        }

        // 验证是否正确
        String sessionVerifyCode = (String) request.getSession().getAttribute("verifyCode");
        if (!verifyCode.equals(sessionVerifyCode)) {
            return "{\"code\":9,\"message\":\"验证码错误\"}";
        }

        // 验证码是否过期
        Date sessionVerifyTime = DateUtil.valueOf((String) request.getSession().getAttribute("verifyTime"));
        if (DateUtil.earlierThanNow(sessionVerifyTime)) {
            return "{\"code\":9,\"message\":\"验证码已过期\"}";
        }

        if (!VerifyUtil.required(city)) {
            return "{\"code\":9,\"message\":\"请选择城市\"}";
        }

        if (!VerifyUtil.required(store)) {
            return "{\"code\":9,\"message\":\"请选择柜台\"}";
        }

        String customerPhoneNumber = campaignEbciSignApplyService.getPhoneNumberByUnionid(unionid);
        if (StringUtil.isNullOrEmpty(customerPhoneNumber)) {
            customerService.setPhoneNumberByUnionid(unionid, phoneNumber);
        }

        // 申领
        CampaignEbciDetail campaignEbciDetail = new CampaignEbciDetail();
        campaignEbciDetail.setType((short) 1);
        campaignEbciDetail.setUniqueId(openid);
        campaignEbciDetail.setName(name);
        campaignEbciDetail.setPhoneNumber(phoneNumber);
        campaignEbciDetail.setCity(city);
        campaignEbciDetail.setStore(store);
        campaignEbciDetail.setSource(source);

        // 0:成功，1：手机号码已经领过了，2：库存不足
        switch (campaignEbciSignApplyService.submit(campaignEbciDetail)) {
            case 0:
                // 申领成功发送验证码
                // snsService.ebci(phoneNumber, store);
                // 申领成功发送MOT
                motService.ebciSignApply(openid, phoneNumber, store);
                CookieUtil.addCookie("phoneNumber", phoneNumber, response);
                return "{\"code\":0}";
            case 1:
                return "{\"code\":1}";
            case 2:
                return "{\"code\":2}";
            default:
                return "{\"code\":3}";
        }

    }

    @GetMapping("/campaign-ebci-sign-result/{unionid}/{source}.html")
    public String result(
            @PathVariable("source") String source,
            @PathVariable("unionid") String unionid,
            Model model
    ) {

        String openid = (String) request.getAttribute("miniOpenid");

        // 读取申领信息
        CampaignEbciDetail campaignEbciDetail = campaignEbciSignApplyService.getDetailByOpenid(openid);
        if (null == campaignEbciDetail) {
            return "redirect:/official/campaign-ebci-sign/" + unionid + "/" + source + ".html";
        }

        // 保存日志
        CampaignEbciLog campaignEbciLog = new CampaignEbciLog();
        campaignEbciLog.setType((short) 1);
        campaignEbciLog.setUniqueId(openid);
        campaignEbciLog.setSource(source);
        campaignEbciLog.setPage("result");
        campaignEbciSignApplyService.insertLog(campaignEbciLog);

        // 获取是否关注
        Follow follow = outsideService.isFollow(openid);

        // 未获取到是否关注，即为AccessToken过期
        if (null != follow) {
            campaignEbciDetail.setFollow(follow.getFollow());
            campaignEbciDetail.setFollowSource(follow.getFollowSource());
            campaignEbciDetail.setFollowFirstTime(follow.getFollowFirstTime());
            campaignEbciDetail.setFollowLastTime(follow.getFollowLastTime());
            campaignEbciDetail.setFollowCancelTime(follow.getFollowCancelTime());
            campaignEbciDetail.setBind(follow.getBind());
            campaignEbciDetail.setBindTime(follow.getBindTime());
            // 保存
            campaignEbciSignApplyService.setFollow(campaignEbciDetail);
        }
       
            model.addAttribute("staticDomain", staticDomain);
        model.addAttribute("campaignEbciDetail", campaignEbciDetail);
        return "w/fans/official/ebci-sign/mini/success";

    }

    @GetMapping("/campaign-ebci-sign-result.html")
    public String result(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "w/fans/official/ebci-sign/mini/result";
    }

}
