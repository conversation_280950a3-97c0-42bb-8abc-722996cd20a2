package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.util.StringUtil;
import org.apache.ibatis.jdbc.SQL;

/**
 * 商城兑礼管理
 *
 * <AUTHOR>
 */
public class StoreExchangeProvider {

    /**
     * 查询所有礼品-分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @return 礼品列表
     */
    public String getPage(int pageIndex, int pageSize, String generalSearch) {

        return new SQL() {{

            SELECT("`id`,`name`,`points`,`stocks`,`exchange_total`,`status`,`update_time`,`create_time`");
            FROM("`store_gift`");
            WHERE("`store_gift_type_id`=1");
            AND();
            WHERE("`recovery`=0");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`name` LIKE '%${generalSearch}%'");
            }
            ORDER_BY("`id` DESC LIMIT #{pageIndex},#{pageSize}");

        }}.toString();

    }

    /**
     * 查询所有礼品带分页的记录数
     *
     * @param generalSearch 搜索
     * @return 记录数
     */
    public String getPageCount(String generalSearch) {

        return new SQL() {{

            SELECT("COUNT(*)");
            FROM("`store_gift`");
            WHERE("`store_gift_type_id`=1");
            AND();
            WHERE("`recovery`=0");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`name` LIKE '%${generalSearch}%'");
            }

        }}.toString();

    }





}
