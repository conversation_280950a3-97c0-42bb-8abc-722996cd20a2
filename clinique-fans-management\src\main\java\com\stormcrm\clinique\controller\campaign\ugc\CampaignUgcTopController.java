package com.stormcrm.clinique.controller.campaign.ugc;

import com.stormcrm.clinique.domain.CampaignMsTalentsTop;
import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgc;
import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgcTop;
import com.stormcrm.clinique.service.CampaignMsTalentsTopService;
import com.stormcrm.clinique.service.campaign.ugc.CampaignUgcService;
import com.stormcrm.clinique.service.campaign.ugc.CampaignUgcTopService;
import com.stormcrm.clinique.util.DateUtil;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.util.VerifyUtil;
import com.stormcrm.clinique.vo.Result;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("campaign-ugc-top")
public class CampaignUgcTopController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;

    private final CampaignUgcService campaignUgcService;
    private final CampaignUgcTopService campaignUgcTopService;

    public CampaignUgcTopController(CampaignUgcTopService campaignUgcTopService, CampaignUgcService campaignUgcService) {
        this.campaignUgcTopService = campaignUgcTopService;
        this.campaignUgcService = campaignUgcService;
    }

    /**
     * 页面模版
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_TOP')")
    @GetMapping("{campaignId}")
    public String index(
            @PathVariable("campaignId") long campaignId,
            Model model
    ) {

        CampaignUgc campaignUgc = campaignUgcService.getById(campaignId);
        if(null == campaignUgc){
            return "m/fans/common/empty";
        }

        model.addAttribute("staticDomain", staticDomain);
        model.addAttribute("campaignId", campaignId);
        model.addAttribute("campaignUgc", campaignUgc);
        return "m/fans/campaign-ugc-top/index";
    }

    /**
     * 查询所有
     *
     * @param campaignId 活动编号
     * @return 列表
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_TOP')")
    @PostMapping("get-all/{campaignId}")
    @ResponseBody
    public String getAll(
            @PathVariable("campaignId") long campaignId
    ) {

        List<CampaignUgcTop> campaignUgcTopList = campaignUgcTopService.getAll(campaignId);

        JSONObject mata = new JSONObject();
        mata.put("page", 1);
        mata.put("pages", 1);
        mata.put("perpage", -1);
        mata.put("total", campaignUgcTopList.size());
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("mata", mata);
        result.put("data", campaignUgcTopList);

        return JSONObject.toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }

    /**
     * 增加模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_TOP')")
    @RequestMapping("add/{campaignId}")
    public String add(
            @PathVariable("campaignId") long campaignId,
            Model model
    ) {
        model.addAttribute("staticDomain", staticDomain);
        model.addAttribute("campaignId", campaignId);
        return "m/fans/campaign-ugc-top/add";
    }

    /**
     * 新增
     *
     * @param stage     期数
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_TOP')")
    @PostMapping(value = "add-submit/{campaignId}", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String addSubmit(
            @PathVariable("campaignId") long campaignId,
            @RequestParam(value = "form-stage", required = false) Short stage,
            @RequestParam(value = "form-start-time", required = false) String startTime,
            @RequestParam(value = "form-end-time", required = false) String endTime
    ) {

        // 验证
        if (null == stage) {
            return ResultUtil.verifyFailToJson("form-stage", "这是必填字段");
        }
        if (!VerifyUtil.required(startTime)) {
            return ResultUtil.verifyFailToJson("form-start-time", "这是必填字段");
        }
        if (!VerifyUtil.required(endTime)) {
            return ResultUtil.verifyFailToJson("form-end-time", "这是必填字段");
        }
        Date startTimeDate = DateUtil.valueOf(startTime);
        if (null == startTimeDate) {
            return ResultUtil.verifyFailToJson("form-start-time", "日期格式错误");
        }
        Date endTimeDate = DateUtil.valueOf(endTime);
        if (null == endTimeDate) {
            return ResultUtil.verifyFailToJson("form-end-time", "日期格式错误");
        }

        // 生成对象
        CampaignUgcTop campaignUgcTop = new CampaignUgcTop();
        campaignUgcTop.setCampaignId(campaignId);
        campaignUgcTop.setStage(stage);
        campaignUgcTop.setStartTime(startTimeDate);
        campaignUgcTop.setEndTime(endTimeDate);

        // 传到Service服务中保存
        Result result = campaignUgcTopService.save(campaignUgcTop);

        // 返回绍果给前端
        return JSONObject.toJSONString(result);
    }

    /**
     * 编辑模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_TOP')")
    @GetMapping("edit/{id}")
    public String edit(@PathVariable long id, Model model) {
        model.addAttribute("staticDomain", staticDomain);
        // 读数据库
        CampaignUgcTop campaignUgcTop = campaignUgcTopService.getById(id);
        if (null == campaignUgcTop) {
            return "m/fans/common/empty";
        }
        model.addAttribute("id", id);
        model.addAttribute("campaignUgcTop", campaignUgcTop);
        return "m/fans/campaign-ugc-top/edit";
    }

    /**
     * 编辑
     *
     * @param id        编号
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_TOP')")
    @PostMapping(value = "edit-submit/{id}", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String editSubmit(
            @PathVariable("id") long id,
            @RequestParam(value = "form-stage", required = false) Short stage,
            @RequestParam(value = "form-start-time", required = false) String startTime,
            @RequestParam(value = "form-end-time", required = false) String endTime
    ) {

        // 验证
        if (null == stage) {
            return ResultUtil.verifyFailToJson("form-stage", "这是必填字段");
        }
        if (!VerifyUtil.required(startTime)) {
            return ResultUtil.verifyFailToJson("form-start-time", "这是必填字段");
        }
        if (!VerifyUtil.required(endTime)) {
            return ResultUtil.verifyFailToJson("form-end-time", "这是必填字段");
        }
        Date startTimeDate = DateUtil.valueOf(startTime);
        if (null == startTimeDate) {
            return ResultUtil.verifyFailToJson("form-start-time", "日期格式错误");
        }
        Date endTimeDate = DateUtil.valueOf(endTime);
        if (null == endTimeDate) {
            return ResultUtil.verifyFailToJson("form-end-time", "日期格式错误");
        }

        // 生成对象
        CampaignUgcTop campaignUgcTop = new CampaignUgcTop();
        campaignUgcTop.setId(id);
        campaignUgcTop.setStage(stage);
        campaignUgcTop.setStartTime(startTimeDate);
        campaignUgcTop.setEndTime(endTimeDate);

        // 传到Service服务中保存
        Result result = campaignUgcTopService.update(campaignUgcTop);

        // 返回绍果给前端
        return JSONObject.toJSONString(result);
    }

}
