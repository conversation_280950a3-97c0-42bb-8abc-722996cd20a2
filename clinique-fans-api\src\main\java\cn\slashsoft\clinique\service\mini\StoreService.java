package cn.slashsoft.clinique.service.mini;

import cn.slashsoft.clinique.domain.mini.*;
import cn.slashsoft.clinique.enums.StoreExchangeTypeEnum;
import cn.slashsoft.clinique.vo.Result;
import cn.slashsoft.clinique.vo.mini.Exchange;

import java.util.List;

/**
 * 商城相关的服务
 *
 * <AUTHOR>
 */
public interface StoreService {

    /**
     * 获取有效的商城礼品列表，按更新时间倒序排列
     *
     * @param customerId 顾客编号
     * @return 商城礼品列表
     */
    List<StoreGift> getStoreGiftList(long customerId);

    /**
     * 读取所有门店区域
     *
     * @return 区域
     */
    List<StoreArea> getStoreAreaList();

    /**
     * 读取所有门店区域提货点
     *
     * @return 提货点
     */
    List<StoreAreaPlace> getStoreAreaPlaceList();

    /**
     * 跟据礼品编号获取礼品资料，和已兑礼数量
     *
     * @param id         礼品编号
     * @param customerId 顾客编号
     * @return 礼品资料
     */
    StoreGift getStoreGiftById(long id, long customerId);

    /**
     * 立即兑换
     *
     * @param unionid 开放平台唯一编号
     * @param exchange              兑换信息
     * @param storeExchangeTypeEnum 兑换类型
     * @return 处理结果
     */
    Result exchange(String unionid, Exchange exchange, StoreExchangeTypeEnum storeExchangeTypeEnum);

    /**
     * 获取兑换记录
     *
     * @param customerId 顾客编号
     * @return 兑换记录
     */
    List<StoreExchange> getStoreExchangeByCustomer(long customerId);

    /**
     * 写入日志
     *
     * @param storeExchangeLog 日志
     */
    void setStoreExchangeLog(StoreExchangeLog storeExchangeLog);

    /**
     * 写入商城列表页访问来源的日志
     *
     * @param source 来源
     * @param openid 微信小程序唯一编号
     */
    void setViewLog(String source, String openid);

}
