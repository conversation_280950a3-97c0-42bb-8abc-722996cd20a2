package com.stormcrm.clinique.dao.oauth;

import com.stormcrm.clinique.oauth.domain.OauthSms;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
public interface OauthSmsDao {

    /**
     * 写入短信发送记录
     *
     * @param sms 短信发送记录
     */
    @Insert("INSERT INTO `oauth_sms`(" +
            "   `phone_number`, " +
            "   `message`, " +
            "   `param`, " +
            "   `response_code`" +
            ") " +
            "VALUES (" +
            // 不需要 手机号加密
            "   #{phoneNumber}," +
            "   #{message}," +
            "   #{param}," +
            "   #{responseCode}" +
            ")")
    void insertSms(OauthSms sms);

}
