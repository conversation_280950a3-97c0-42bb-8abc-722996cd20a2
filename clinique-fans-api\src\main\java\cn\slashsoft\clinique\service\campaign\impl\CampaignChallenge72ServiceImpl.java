package cn.slashsoft.clinique.service.campaign.impl;

import cn.slashsoft.clinique.dao.campaign.CampaignChallenge72Dao;
import cn.slashsoft.clinique.dao.mini.PointDao;
import cn.slashsoft.clinique.domain.campaign.CampaignChallenge72;
import cn.slashsoft.clinique.domain.campaign.CampaignChallenge72CityConfig;
import cn.slashsoft.clinique.domain.campaign.CampaignViewLog;
import cn.slashsoft.clinique.domain.mini.PointTransaction;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.enums.PointForeignEnum;
import cn.slashsoft.clinique.enums.PointTypeEnum;
import cn.slashsoft.clinique.service.campaign.CampaignChallenge72Service;
import cn.slashsoft.clinique.service.mini.MotService;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.RandomUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 挑战72小时
 *
 * <AUTHOR>
 */
@Service
public class CampaignChallenge72ServiceImpl implements CampaignChallenge72Service {

    private final CampaignChallenge72Dao campaignChallenge72Dao;
    private final PointDao pointDao;
    private final MotService motService;
    private final OutsideService outsideService;

    public CampaignChallenge72ServiceImpl(CampaignChallenge72Dao campaignChallenge72Dao, PointDao pointDao, OutsideService outsideService, MotService motService) {
        this.campaignChallenge72Dao = campaignChallenge72Dao;
        this.pointDao = pointDao;
        this.outsideService = outsideService;
        this.motService = motService;
    }

    /**
     * 获取活动信息
     *
     * @param customerId 会员编号
     * @param source     来源
     * @return 活动信息
     */
    @Override
    public CampaignChallenge72 getCampaignChallenge72(long customerId, String source) {

        CampaignChallenge72 campaignChallenge72 = campaignChallenge72Dao.getCampaignChallenge72(customerId);
        if (null == campaignChallenge72) {
            campaignChallenge72 = new CampaignChallenge72();
            campaignChallenge72.setCustomerId(customerId);
            campaignChallenge72.setSource(source);
            campaignChallenge72Dao.insertCampaignChallenge72(campaignChallenge72);
            // 日志
            campaignChallenge72Dao.insertCampaignChallenge72Log(customerId, "首次访问创建活动信息");
        }
        return campaignChallenge72;
    }

    /**
     * 获取头像
     *
     * @param customerId 顾客信息
     * @return 头像
     */
    @Override
    public String getAvatarUrl(long customerId) {
        return campaignChallenge72Dao.getAvatarUrl(customerId);
    }

    /**
     * 获取被邀请者头像
     *
     * @param openid 邀请者openid
     * @return 头像
     */
    @Override
    public List<String> getInviteeAvatarUrl(String openid) {
        return campaignChallenge72Dao.getInviteeAvatarUrl(openid);
    }

    /**
     * 设置第一轮完成
     *
     * @param customerId 顾客编号
     */
    @Override
    public void setCampaignChallenge72FirstRound(long customerId) {
        campaignChallenge72Dao.setCampaignChallenge72FirstRound(customerId);
        // 日志
        campaignChallenge72Dao.insertCampaignChallenge72Log(customerId, "完成第一关");
    }

    /**
     * 设置第二轮完成
     *
     * @param customerId 顾客编号
     */
    @Override
    public void setCampaignChallenge72SecondRound(long customerId) {
        campaignChallenge72Dao.setCampaignChallenge72SecondRound(customerId);
        // 日志
        campaignChallenge72Dao.insertCampaignChallenge72Log(customerId, "完成第二关");
    }

    /**
     * 设置第三轮完成
     *
     * @param customerId 顾客编号
     */
    @Override
    public void setCampaignChallenge72ThirdRound(long customerId) {
        campaignChallenge72Dao.setCampaignChallenge72ThirdRound(customerId);

        // 发放积分
        Date now = new Date();
        PointTransaction pointTransaction = new PointTransaction();
        pointTransaction.setCustomerId(customerId);
        pointTransaction.setPointTypeId(PointTypeEnum.CHALLENGE72.getId());
        pointTransaction.setPoints(PointTypeEnum.CHALLENGE72.getPoints());
        pointTransaction.setRemainingPoints(PointTypeEnum.CHALLENGE72.getPoints());
        pointTransaction.setStartTime(now);
        pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
        pointTransaction.setForeignId(PointForeignEnum.CHALLENGE72.getId());
        pointTransaction.setForeignMasterId(0L);
        pointTransaction.setForeignDetailId(0L);
        pointDao.insertPointTransaction(pointTransaction);

        // 发送MOT
        motService.getPoints(customerId, PointTypeEnum.CHALLENGE72.getPoints(), "恭喜您在72小时保湿挑战活动中闯关成功。您已解锁终极极限挑战，完成任务赢水磁场面霜正装礼、7500份免费体验礼！");
        // 日志
        campaignChallenge72Dao.insertCampaignChallenge72Log(customerId, "完成第三关");
    }

    /**
     * 获取所有城市
     *
     * @param customerId 顾客编号
     * @return 所有城市
     */
    @Override
    public CampaignChallenge72CityConfig getCampaignChallenge72City(long customerId) {

        List<CampaignChallenge72CityConfig> campaignChallenge72CityConfigList = campaignChallenge72Dao.getCampaignChallenge72CityConfig(customerId);

        if (null == campaignChallenge72CityConfigList || 0 == campaignChallenge72CityConfigList.size()) {
            return null;
        }

        int index = RandomUtil.getNumberBetween(0, campaignChallenge72CityConfigList.size());

        CampaignChallenge72CityConfig campaignChallenge72CityConfig = campaignChallenge72CityConfigList.get(index);

        campaignChallenge72Dao.insertCampaignChallenge72City(customerId, campaignChallenge72CityConfig.getId());

        return campaignChallenge72CityConfig;

    }

    /**
     * 选择心得大师
     *
     * @param customerId 顾客编号
     */
    @Override
    public void setResultExperience(long customerId) {
        campaignChallenge72Dao.setResultExperience(customerId);
        // 日志
        campaignChallenge72Dao.insertCampaignChallenge72Log(customerId, "选择心得大咖");
    }

    /**
     * 选择分享大师
     *
     * @param customerId 顾客编号
     */
    @Override
    public void setResultShare(long customerId) {
        campaignChallenge72Dao.setResultShare(customerId);
        // 日志
        campaignChallenge72Dao.insertCampaignChallenge72Log(customerId, "选择分享大师");
    }

    /**
     * 邀请
     *
     * @param inviterMiniOpenid 邀请者
     * @param inviteeMiniOpenid 被邀请者
     */
    @Override
    public void invite(String inviterMiniOpenid, String inviteeMiniOpenid) {
        // 写入表
        campaignChallenge72Dao.insertCampaignChallenge72Invite(inviterMiniOpenid, inviteeMiniOpenid);
        // 邀请人数达到2人并且成功更新活动中邀请状态
        if (2 == campaignChallenge72Dao.getCampaignChallenge72InviteCount(inviterMiniOpenid) && 0 < campaignChallenge72Dao.setCampaignChallenge72InviteDone(inviterMiniOpenid)) {
            // 读取开放平台唯一编号
            Wechat wechat = campaignChallenge72Dao.getUnionidByMiniOpenid(inviterMiniOpenid);
            // 日志
            campaignChallenge72Dao.insertCampaignChallenge72Log(wechat.getCustomerId(), "邀请人数达到2人");

            if(wechat != null && !StringUtil.isNullOrEmpty(wechat.getUnionid())){
                // 发券
                Result result = outsideService.getCoupon(wechat.getUnionid(), "2");
                if(0 == result.getCode()){
                    // 发送领券MOT
                    motService.getCoupon(wechat.getCustomerId(), "倩碧水嫩保湿修护霜5ml", "msi_1112");
                    // 日志
                    campaignChallenge72Dao.insertCampaignChallenge72Log(wechat.getCustomerId(), "领券成功");
                }
            }
        }
    }

    /**
     * 写入访问日志
     *
     * @param campaignViewLog 日志
     * @return 日志编号
     */
    @Override
    public long insertCampaignChallenge72ViewLog(CampaignViewLog campaignViewLog) {
        campaignChallenge72Dao.insertCampaignChallenge72ViewLog(campaignViewLog);
        return campaignViewLog.getId();
    }

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Override
    public void setCampaignChallenge72ViewLog(long id) {
        campaignChallenge72Dao.setCampaignChallenge72ViewLog(id);
    }
}
