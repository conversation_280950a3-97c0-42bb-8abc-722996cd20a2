package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.dao.MessageDao;
import com.stormcrm.clinique.dao.MotDao;
import com.stormcrm.clinique.dao.SmotDao;
import com.stormcrm.clinique.dao.WechatDao;

import com.stormcrm.clinique.domain.*;

import com.stormcrm.clinique.service.OutsideService;
import com.stormcrm.clinique.util.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.Date;
import java.util.logging.Logger;

/**
 * 外部接口
 *
 * <AUTHOR>
 */
@Service
public class OutsideServiceImpl implements OutsideService {

    private final Logger logger = Logger.getLogger(OutsideServiceImpl.class.getName());


    @Value("${wechat.official.access-token}")
    private String urlAccessToken;

    @Value("${wechat.official.template-send}")
    private String urlTemplateSend;

    @Value("${wechat.official.template-send-with-mp}")
    private String urlTemplateSendWithMp;

    @Value("${wechat.official.message-send}")
    private String urlMessageSend;


    @Value("${wechat.mini.url.subscribe-message}")
    private String urlSubscribeMessage;

    @Value("${wechat.mall.member}")
    private String urlMallMember;

    @Value("${wechat.ad.user-actions}")
    private String urlAdUserActions;

    @Value("${wechat.oss.url}")
    private String urlOss;

    @Value("${wechat.oss.end-point}")
    private String endpoint;

    @Value("${wechat.oss.access-key-id}")
    private String accessKeyId;

    @Value("${wechat.oss.access-key-secret}")
    private String accessKeySecret;

    @Value("${wechat.oss.bucket}")
    private String bucket;


    private final WechatDao wechatDao;
    private final MotDao motDao;
    private final MessageDao messageDao;
    private final SmotDao smotDao;

    private final StringRedisTemplate stringRedisTemplate;

    public OutsideServiceImpl(
    		StringRedisTemplate stringRedisTemplate,
    		WechatDao wechatDao,
    		MotDao motDao,
    		MessageDao messageDao,
    		SmotDao smotDao) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.wechatDao = wechatDao;
        this.motDao = motDao;
        this.messageDao = messageDao;
        this.smotDao = smotDao;
    }

   

    /**
     * 获取AccessToken
     */
    @Override
    public void getAccessToken() {

        try {
            JSONObject result = (JSONObject) JSON.parse(HttpUtil.get(urlAccessToken));

            logger.info("获取AccessToken (" + result.toJSONString() + ")");

            if (result.containsKey("IsSuccess") && result.getBooleanValue("IsSuccess")) {
                stringRedisTemplate.opsForValue().set("official-access-token", result.getJSONObject("ResultData").getString("WechatBaseToken"));
            }

        } catch (IOException ignored) {
        }

    }


    /**
     * 发送模版消息
     *
     * @param type       类型
     * @param openid     公众号唯一编号
     * @param templateId 模版编号
     * @param data       数据
     * @return 是否成功
     */
    @Override
    public boolean templateSend(short type, String openid, String templateId, String data) {
        try {
            JSONObject response = JSON.parseObject(HttpUtil.postForm(urlTemplateSend, "openid=" + openid + "&templateID=" + templateId + "&data=" + data));

            String result = "返回结果出错";
            boolean status = false;

            if (null != response && response.getBooleanValue("IsSuccess")) {
                result = response.getJSONObject("ResultData").getString("result");
                JSONObject json = JSON.parseObject(result);
                if (null != json && 0 == json.getIntValue("errcode")) {
                    status = true;
                }
            }

            Mot mot = new Mot();
            mot.setWechatOfficialOpenid(openid);
            mot.setType(type);
            mot.setParam(data);
            mot.setResult(result);
            mot.setStatus(status);
            motDao.insertMot(mot);

            return status;
        } catch (Exception ignored) {
        }

        return false;

    }

    /**
     * 发送模版消息
     *
     * @param type       类型
     * @param openid     公众号唯一编号
     * @param templateId 模版编号
     * @param data       数据
     * @param appKey     小程序App Key
     * @param pagePath   小程序链接地址
     * @return 是否成功
     */
    @Override
    public boolean templateSendWithMp(short type, String openid, String templateId, String data, String appKey, String pagePath) {
        try {

            logger.warning(urlTemplateSendWithMp);

            logger.warning("openid=" + openid + "&templateID=" + templateId + "&data=" + data + "&appkey=" + appKey + "&pagepath=" + pagePath);

            JSONObject response = JSON.parseObject(HttpUtil.postForm(urlTemplateSendWithMp, "openid=" + openid + "&templateID=" + templateId + "&data=" + data + "&appkey=" + appKey + "&pagepath=" + pagePath));

            logger.warning(openid+ ": " +response.toJSONString());

            String result = "返回结果出错";
            boolean status = false;

            if (null != response && response.getBooleanValue("IsSuccess")) {
                result = response.getJSONObject("ResultData").getString("result");
                JSONObject json = JSON.parseObject(result);
                if (null != json && 0 == json.getIntValue("errcode")) {
                    status = true;
                }
            }

            Mot mot = new Mot();
            mot.setWechatOfficialOpenid(openid);
            mot.setType(type);
            mot.setParam("{\"miniprogram\":{\"appid\":\"" + appKey + "\",\"pagepath\":\"" + pagePath + "\"}}" + data);
            mot.setResult(result);
            mot.setStatus(status);
            motDao.insertMot(mot);

            return status;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    /**
     * 发送一次性订阅消息
     *
     * @param type       类型
     * @param openid     小程序唯一编号
     * @param templateId 模版编号
     * @param page       页面，如果为空不跳转
     * @param data       数据
     * @return 是否成功
     */
    @Override
    public boolean subscribeMessageSend(short type, String openid, String templateId, String page, String data) {

        try {

            String accessToken = stringRedisTemplate.opsForValue().get("wechatMiniProgramAccessToken");

            String param = "{" +
                    "\"touser\":\"" + openid + "\"," +
                    "\"template_id\":\"" + templateId + "\"," +
                    (!StringUtil.isNullOrEmpty(page) ? "\"page\":\"" + page + "\"," : "") +
                    "\"data\":" + data +
                    "}";

            String result = HttpUtil.postJson(urlSubscribeMessage + accessToken, param);
            boolean status = false;

            if (!StringUtil.isNullOrEmpty(result)) {
                JSONObject json = JSON.parseObject(result);
                if (null != json && 0 == json.getIntValue("errcode")) {
                    status = true;
                }
            }

            Smot smot = new Smot();
            smot.setWechatMiniOpenid(openid);
            smot.setType(type);
            smot.setParam(data);
            smot.setResult(result);
            smot.setStatus(status);
            smotDao.insertSmot(smot);

            return status;
        } catch (Exception ignored) {
        }

        return false;
    }

    /**
     * 发送客服消息
     *
     * @param type    类型
     * @param openid  公众号唯一编号
     * @param content 发送内容
     * @return 是否成功
     */
    @Override
    public boolean messageSend(short type, String openid, String content) {

        try {

            JSONObject response = JSON.parseObject(HttpUtil.get(urlMessageSend.replace("OPENID", openid).replace("CONTENT", content)));

            String result = "返回结果出错";
            boolean status = false;

            if (null != response && response.getBooleanValue("IsSuccess")) {
                result = response.getJSONObject("ResultData").getString("JsonResult");
                JSONObject json = JSON.parseObject(result);
                if (null != json && 0 == json.getIntValue("errcode")) {
                    status = true;
                }
            }

            Message message = new Message();
            message.setWechatOfficialOpenid(openid);
            message.setType(type);
            message.setContent(content);
            message.setResult(result);
            message.setStatus(status);
            messageDao.insertMessage(message);

            return status;
        } catch (Exception ignored) {
        }

        return false;

    }

    /**
     * 获取系统时间
     *
     * @return 系统时间
     */
    private String getUnixStamp() {
        return String.valueOf(System.currentTimeMillis() / 1000);
    }

    /**
     * 上传图片到OSS上
     *
     * @param file 文件
     * @param root 根目录
     * @return 返回的路径
     */
    @Override
    public String uploadImageOss(MultipartFile file, String root) {

        try {
            OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            InputStream inputStream = file.getInputStream();

            Date now = new Date();
            String originalFileName = file.getOriginalFilename();
            if (StringUtil.isNullOrEmpty(originalFileName)) {
                throw new Exception("上传文件的原始文件名为空");
            }
            String fileName = DateUtil.parseString(now, "yyyyMMdd");
            fileName = root + "/" + fileName + "/" + DateUtil.parseString(now, "HHmmssSSSS") + RandomUtil.getVerifyCode() + originalFileName.substring(originalFileName.lastIndexOf("."));

            ossClient.putObject(bucket, fileName, inputStream);

            return urlOss + fileName;
        } catch (Exception e) {
            logger.info("上传到阿里云OSS出错：" + e.getMessage());
        }

        return null;
    }
}
