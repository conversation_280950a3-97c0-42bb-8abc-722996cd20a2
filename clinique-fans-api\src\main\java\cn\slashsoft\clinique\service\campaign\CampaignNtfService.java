package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignViewLog;
import cn.slashsoft.clinique.domain.campaign.ntf.CampaignNtf;
import cn.slashsoft.clinique.domain.campaign.ntf.CampaignNtfRecord;
import cn.slashsoft.clinique.vo.Result;
import cn.slashsoft.clinique.vo.mini.Location;

import java.util.List;

/**
 * NTF
 *
 * <AUTHOR>
 */
public interface CampaignNtfService {

    /**
     * 获取活动信息
     *
     * @param unionid    开放平台唯一编号
     * @param customerId 顾客编号
     * @return 活动信息
     */
    CampaignNtf getOrCreateCampaignNtf(String unionid, long customerId);

    /**
     * 获取活动信息
     *
     * @param customerId 顾客编号
     * @return 活动信息
     */
    CampaignNtf getCampaignNtf(long customerId);

    /**
     * 获取我的抽奖记录
     *
     * @param customerId 顾客编号
     * @return 我的抽奖记录列表
     */
    List<CampaignNtfRecord> getCampaignNtfRecord(long customerId);

    /**
     * 获取被邀请者头像
     *
     * @param miniOpenid 邀请者openid
     * @return 头像
     */
    List<String> getInviteeAvatarUrl(String miniOpenid);

    /**
     * 开启
     *
     * @param customerId 顾客编号
     * @param location   定位
     * @return 处理结果
     */
    Result back(long customerId, Location location);

    /**
     * 抽奖
     *
     * @param customerId 顾客编号
     * @param location   定位
     * @return 处理结果
     */
    Result draw(long customerId, Location location);

    /**
     * 邀请
     *
     * @param inviterMiniOpenid 邀请者
     * @param inviteeMiniOpenid 被邀请者
     */
    void invite(String inviterMiniOpenid, String inviteeMiniOpenid);

    /**
     * 写入访问日志
     *
     * @param campaignViewLog 日志
     * @return 日志编号
     */
    long insertViewLog(CampaignViewLog campaignViewLog);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    void setViewLog(long id);

    /**
     * 是否是新客
     * @param unionid 开放平台唯一编号
     * @param customerId 顾客编号
     * @return 是否
     */
    boolean isNewCustomer(String unionid, long customerId);

}
