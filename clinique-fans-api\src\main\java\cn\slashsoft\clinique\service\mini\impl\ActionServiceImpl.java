package cn.slashsoft.clinique.service.mini.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import cn.slashsoft.clinique.dao.mini.ActionDao;
import cn.slashsoft.clinique.domain.mini.Action;
import cn.slashsoft.clinique.service.mini.ActionService;

/**
 * 与活动相关
 *
 * <AUTHOR>
 */
@Service
public class ActionServiceImpl implements ActionService {

    private final ActionDao actionDao;

    public ActionServiceImpl(ActionDao actionDao) {
        this.actionDao = actionDao;
    }

    /**
     * 写入动作
     *
     * @param action 动作
     */
    @Override
    public void insertAction(Action action) {
        actionDao.insertAction(action);
    }

    /**
     * 获取三日打卡任务 
     *
     * @param miniopenid
     */
    @Override
    public List<Action> getDay3TaskActions(String miniOpenid) {
        return actionDao.getDay3TaskActions(miniOpenid);
    }
}
