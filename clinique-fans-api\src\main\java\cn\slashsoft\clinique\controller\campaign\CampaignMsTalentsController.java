package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.domain.campaign.*;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.service.campaign.CampaignMsTalentsService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.campaign.CampaignMsTalentsTopVo;
import cn.slashsoft.clinique.vo.campaign.CampaignMsTalentsVo;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * MS 达人榜
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class CampaignMsTalentsController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final CampaignMsTalentsService campaignMsTalentsService;
    private final StringRedisTemplate stringRedisTemplate;
    private final WechatService wechatService;

    public CampaignMsTalentsController(CampaignMsTalentsService campaignMsTalentsService, StringRedisTemplate stringRedisTemplate, WechatService wechatService) {
        this.campaignMsTalentsService = campaignMsTalentsService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.wechatService = wechatService;
    }

    /**
     * 配置活动信息
     *
     * @param verification 验证
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 处理结果
     * https://clinique.stormcrm.com/api/campaign-config/ms-talents/20-23-04-00-28/2020-06-01-00-00-00/2020-06-30-23-59-59
     * http://127.0.0.1/api/campaign-config/ms-talents/20-23-04-00-28/2020-06-01-00-00-00/2020-06-30-23-59-59
     */
    @GetMapping("/campaign-config/ms-talents/{verification}/{startTime}/{endTime}")
    public String setConfig(
            @PathVariable("verification") String verification,
            @PathVariable("startTime") String startTime,
            @PathVariable("endTime") String endTime
    ) {
        if (DateUtil.parseVerification(new Date()).equals(verification)) {
            stringRedisTemplate.opsForValue().set("campaignMsTalentsStartTime", DateUtil.parseString(DateUtil.valueOf(startTime, "yyyy-MM-dd-HH-mm-ss")));
            stringRedisTemplate.opsForValue().set("campaignMsTalentsEndTime", DateUtil.parseString(DateUtil.valueOf(endTime, "yyyy-MM-dd-HH-mm-ss")));
            return "success";
        }
        return "fail";
    }

    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-ms-talents/get-data")
    public String getData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 获取美白达人官活动参与信息
        CampaignMsTalents campaignMsTalents = campaignMsTalentsService.getCampaignMsTalents(customerId);

        CampaignMsTalentsVo campaignMsTalentsVo = new CampaignMsTalentsVo();

        campaignMsTalentsVo.setStartTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignMsTalentsStartTime")));
        campaignMsTalentsVo.setEndTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignMsTalentsEndTime")));

        campaignMsTalentsVo.setAlert(campaignMsTalents.getAlert());
        campaignMsTalentsVo.setStatus(campaignMsTalents.getStatus());

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignMsTalentsVo);

    }

    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-ms-talents/get-alert-data")
    public String getAlertData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 获取美白达人官活动参与信息
        CampaignMsTalents campaignMsTalents = campaignMsTalentsService.getCampaignMsTalentsAlert(customerId);
        if (null == campaignMsTalents){
            return ResultUtil.customer(ResultEnum.SUCCESS);

        }

        CampaignMsTalentsVo campaignMsTalentsVo = new CampaignMsTalentsVo();
        campaignMsTalentsVo.setAlert(campaignMsTalents.getAlert());
        campaignMsTalentsVo.setStatus(campaignMsTalents.getStatus());

        // 首次变更状态时，设置提醒状态
        if (campaignMsTalents.getAlert()) {
            campaignMsTalentsService.updateCampaignMsTalentsAlert(customerId);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignMsTalentsVo);

    }

    /**
     * 图片上传
     *
     * @param file 图片
     * @return 处理结果
     */
    @PostMapping("/campaign-ms-talents/upload")
    public String upload(@RequestParam("file") MultipartFile file, @RequestParam("index") int index) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        if (!campaignMsTalentsService.upload(customerId, file)) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, index);

    }

    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-ms-talents-mine/get-data")
    public String getMineData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 获取美白达人官活动参与信息
        CampaignMsTalents campaignMsTalents = campaignMsTalentsService.getCampaignMsTalents(customerId);

        CampaignMsTalentsVo campaignMsTalentsVo = new CampaignMsTalentsVo();

        campaignMsTalentsVo.setStartTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignMsTalentsStartTime")));
        campaignMsTalentsVo.setEndTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignMsTalentsEndTime")));

        campaignMsTalentsVo.setStatus(campaignMsTalents.getStatus());
        campaignMsTalentsVo.setUploadTime(campaignMsTalents.getUploadTime());
        campaignMsTalentsVo.setExamineTime(campaignMsTalents.getExamineTime());

        List<CampaignMsTalentsImage> campaignMsTalentsImageList = campaignMsTalentsService.getCampaignMsTalentsImageList(campaignMsTalents.getId());
        campaignMsTalentsVo.setTalentsImageList(campaignMsTalentsImageList);

        Wechat wechat = wechatService.getWechatInfoByCustomerId(customerId);
        campaignMsTalentsVo.setNickName(wechat.getNickName());
        campaignMsTalentsVo.setAvatarUrl(wechat.getAvatarUrl());

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignMsTalentsVo);

    }

    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-ms-talents-top/get-data")
    public String getTopData() {

        // 获取美白达人官活动TOP信息
        CampaignMsTalentsTop campaignMsTalentsTop = campaignMsTalentsService.getCampaignMsTalentsTop();
        if (null == campaignMsTalentsTop) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        CampaignMsTalentsTopVo campaignMsTalentsTopVo = new CampaignMsTalentsTopVo();
        campaignMsTalentsTopVo.setStage(campaignMsTalentsTop.getStage());
        campaignMsTalentsTopVo.setTalentsTopDetailList(campaignMsTalentsService.getCampaignMsTalentsTopDetailList(campaignMsTalentsTop.getId()));
        campaignMsTalentsTopVo.setTalentsTopDetailImageList(campaignMsTalentsService.getCampaignMsTalentsTopDetailImageList(campaignMsTalentsTop.getId()));

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignMsTalentsTopVo);

    }

    @PostMapping("/campaign-ms-talents/skin-type/{skinType}")
    public String setCampaignMsTalentsSkinType(
            @PathVariable("skinType") short skinType
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        campaignMsTalentsService.setCampaignMsTalentsSkinType(customerId, skinType);
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 写入访问开始日志
     *
     * @param source 来源
     * @return 成功
     */
    @PostMapping("/campaign-ms-talents/start-view-log/{source}/{page}")
    public String setStartViewLog(
            @PathVariable("source") String source,
            @PathVariable("page") String page
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        long id = campaignMsTalentsService.insertCampaignMsTalentsViewLog(customerId, source, page);

        return ResultUtil.customer(ResultEnum.SUCCESS, id);
    }

    /**
     * 写入访问结束日志
     *
     * @param id 来源
     * @return 成功
     */
    @PostMapping("/campaign-ms-talents/end-view-log/{id}")
    public String setEndViewLog(
            @PathVariable("id") long id
    ) {
        campaignMsTalentsService.setCampaignMsTalentsViewLog(id);
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 写入达人傍访问日志
     *
     * @param stage       达人榜编号
     * @param topDetailId 达人榜明细编号
     * @return 成功
     */
    @PostMapping("/campaign-ms-talents/top-detail-log/{stage}/{topDetailId}")
    public String setTalentsTopDetailLog(
            @PathVariable("stage") short stage,
            @PathVariable("topDetailId") long topDetailId
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        CampaignMsTalentsTopDetailLog campaignMsTalentsTopDetailLog = new CampaignMsTalentsTopDetailLog();
        campaignMsTalentsTopDetailLog.setCustomerId(customerId);
        campaignMsTalentsTopDetailLog.setStage(stage);
        campaignMsTalentsTopDetailLog.setTalentsTopDetailId(topDetailId);
        campaignMsTalentsService.insertCampaignMsTalentsTopDetailLog(campaignMsTalentsTopDetailLog);

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 写入达人傍访问日志
     *
     * @param stage       达人榜编号
     * @param topDetailId 达人榜明细编号
     * @return 成功
     */
    @PostMapping("/campaign-ms-talents/top-detail-image-log/{stage}/{topDetailId}/{imageId}/{type}")
    public String setTalentsTopDetailImageLog(
            @PathVariable("stage") short stage,
            @PathVariable("topDetailId") long topDetailId,
            @PathVariable("imageId") long imageId,
            @PathVariable("type") short type
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        CampaignMsTalentsTopDetailImageLog campaignMsTalentsTopDetailImageLog = new CampaignMsTalentsTopDetailImageLog();
        campaignMsTalentsTopDetailImageLog.setCustomerId(customerId);
        campaignMsTalentsTopDetailImageLog.setStage(stage);
        campaignMsTalentsTopDetailImageLog.setTalentsTopDetailId(topDetailId);
        campaignMsTalentsTopDetailImageLog.setTalentsImageId(imageId);
        campaignMsTalentsTopDetailImageLog.setType(type);
        campaignMsTalentsService.insertCampaignMsTalentsTopDetailImageLog(campaignMsTalentsTopDetailImageLog);

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

}
