package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.util.StringUtil;
import org.apache.ibatis.jdbc.SQL;

/**
 * 积分规则查询
 *
 * <AUTHOR>
 */
public class PointTypesProvider {

    /**
     * 查询所有积分规则-分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 积分类型列表
     */
    public String getPage(int pageIndex, int pageSize, String generalSearch, Short status) {

        return new SQL() {{

            SELECT("`t`.* ");
            FROM("`point_type` `t`");
            WHERE("1=1");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`t`.`name` LIKE '%${generalSearch}%'");
            }
            if (null != status && 0 < status) {
                AND();
                WHERE("`t`.`status`=#{status}");
            }
            ORDER_BY("`t`.`id` DESC LIMIT #{pageIndex},#{pageSize}");

        }}.toString();

    }

    /**
     * 查询所有积分类型列表 带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    public String getPageCount(String generalSearch, Short status) {

        return new SQL() {{

            SELECT("COUNT(*)");
            FROM("`point_type` `t` ");
            WHERE("1=1");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`w`.`name` LIKE '%${generalSearch}%'");
            }
            if (null != status && 0 < status) {
                AND();
                WHERE("`t`.`status`=#{status}");
            }

        }}.toString();

    }

}
