package cn.slashsoft.clinique.dao.mini;

import java.util.ArrayList;
import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.campaign.ugc.Koc;
import cn.slashsoft.clinique.domain.campaign.ugc.KocDetail;
import cn.slashsoft.clinique.domain.campaign.ugc.KocDetailImage;
import cn.slashsoft.clinique.domain.campaign.ugc.KocLog;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTop;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTopDetail;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTopDetailImage;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTopDetailImageLog;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTopDetailLog;
import cn.slashsoft.clinique.domain.campaign.ugc.KocViewLog;
import cn.slashsoft.clinique.domain.campaign.ugc.KocWinner;
import cn.slashsoft.clinique.domain.mini.Note;
import cn.slashsoft.clinique.domain.mini.NoteLikeLog;
import cn.slashsoft.clinique.domain.mini.NotePhoto;

/**
 * 积分相关的数据库操作
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface KocDao {

    /**
     * 读取活动列表信息
     * @return 活动信息
     */
    @Select("SELECT " +
            "   `id`, " +
            "   `start_time` , " +
            "   `end_time`, " +
            "   `list_banner` , " +
            "   `type` , " +
            "   `share_title`  " +
            "FROM " +
            "   `koc` " +
            "WHERE " +
            "    `status`=1 " + 
            " ORDER BY start_time DESC ")
    List<Koc> getCampaignUgcList();

    /**
     * 读取活动列表信息
     * @return 活动信息
     */
    @Select("SELECT " +
            "   `id`, " +
            "   `start_time` , " +
            "   `end_time`, " +
            "   `list_banner` , " +
            "   `type` , " +
            "   `share_title`  " +
            "FROM " +
            "   `koc` " +
            "WHERE " +
            "    `status`=1 AND `id` in ( SELECT a.campaign_id from koc_detail a "
            + "		 join koc_image b on b.detail_id = a.id where a.customer_id=#{customerId} ) " + 
            " ORDER BY start_time DESC ")
    List<Koc> getMineCampaignUgcList(Long customerId);
    /**
	 * 获取获奖详情
	 *
	 */
    @Select("SELECT " +
            "   a.*,  " +
            "   `w`.`nick_name` `ownerName`, " +
            "   `w`.`avatar_url` `avatarUrl`  " +         
            "FROM " +
            "   `koc_winner` a " +
             "       INNER JOIN " +
             "   `wechat` `w` " +
             "       ON `a`.`customer_id`=`w`.`customer_id` " +
             "   WHERE a.`koc_id`=#{id} " +
             " ORDER BY `create_time` DESC, CONVERT(LTRIM(`ownerName`) USING GBK) ASC"
    		)
    ArrayList<KocWinner> getWinnerList(Long id);
   


    /**
     * 获取入选koc TOP的名单
     *
     * @param campaignLaserTalentsTopId TOP信息编号
     * @return 名单
     */
    @Select("SELECT " +
            "   `t`.`id` `talents_detail_id`," +
            "   `t`.`examine_time`, " +
            "   `w`.`nick_name`, " +
            "   `w`.`avatar_url` " +
            "FROM " +
            "   `campaign_laser_talents_top_detail` `d` " +
            "       INNER JOIN " +
            "   `campaign_laser_talents` `t` " +
            "       ON `d`.`talents_id`=`t`.`id` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `t`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "    `d`.`status`=1 " +
            "ORDER BY " +
            "   `d`.`sort`")
    List<KocTopDetail> getLaserTalentsTopDetailList();

    /**
     * 获取入选TOP的名单
     *
     * @param topId TOP信息编号
     * @return 名单
     */
    @Select("SELECT " +
            "   `d`.`detail_id` `talents_detail_id`," +
            "   `t`.`examine_time`, " +
            "   `t`.`title`, " +
            "   `w`.`nick_name`, " +
            "   `w`.`avatar_url` " +
            "FROM " +
            "   `koc_top_detail` `d` " +
            "       INNER JOIN " +
            "   `koc_detail` `t` " +
            "       ON `d`.`detail_id`=`t`.`id` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `t`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "   `d`.`top_id` in ( " +
            " SELECT `id`  from `koc_top` WHERE `campaign_id` = #{campaignId} AND `stage`=#{stage} " 
            + " ) AND `d`.`status`=1 " +
            "ORDER BY " +
            "   `d`.`sort`")
    List<KocTopDetail> getCampaignUgcTopDetailList(Long campaignId, int stage);
    /**
     * 获取入选koc TOP的图片
     *
     * @param campaignLaserTalentsTopId TOP信息编号
     * @return 图片
     */
    @Select("SELECT " +
            "   `i`.`id`, " +
            "   `i`.`talents_id` `talents_detail_id`, " +
            "   `i`.`image_url` " +
            "FROM " +
            "   `campaign_laser_talents_top_detail` `d` " +
            "       INNER JOIN " +
            "   `campaign_laser_talents_image` `i` " +
            "       ON `d`.`talents_id`=`i`.`talents_id` " +
           // "WHERE " +
          //  "   `d`.`talents_top_id` > 1 " +
            "   AND `d`.`status`=1 AND `i`.`status`=1")
    List<KocTopDetailImage> getLaserTalentsTopDetailImageList();

    /**
     * 获取入选TOP的图片
     *
     * @param topId TOP信息编号
     * @return 图片
     */
    @Select("SELECT " +
            "   `i`.`id`, " +
            "   `i`.`detail_id`  `talents_detail_id`, " +
            "   `i`.`image_url` " +
            "FROM " +
            "   `koc_top_detail` `d` " +
            "       INNER JOIN " +
            "   `koc_image` `i` " +
            "       ON `d`.`detail_id`=`i`.`detail_id` " +
            "WHERE " +
            "   `d`.`top_id` in ( " + 
            " SELECT `id`  from `koc_top` WHERE `campaign_id` = #{campaignId} AND `stage`=#{stage} " 
            + " )" +
            "   AND `d`.`status`=1 AND `i`.`status`=1")
    List<KocTopDetailImage> getCampaignUgcTopDetailImageList(Long campaignId, int stage);
    
    @Select(" SELECT  IFNULL(max(`stage`),0) from `koc_top` WHERE `campaign_id` = #{campaignId} ")
    int getUgcNewerStageByCampaign(Long campaignId);

    @Select("SELECT " +
            "   a.title,a.id,a.status, b.image_url coverPhoto, " +
            "   `w`.`nick_name` ownerName, " +
            "   `w`.`avatar_url` avatarUrl , " +
            " k.`customer_id` `koc_v`, " +
            " ( SELECT count(`id`) from `koc_like_log` WHERE `detail_id`= a.id ) 	`likeCount`, "+
            " ( SELECT count(`id`) from `koc_like_log` WHERE `detail_id`= a.id AND `customer_id`=#{customerId} ) 	`liked` "+
            "FROM " +
            "   `koc_detail` a "
            + " LEFT JOIN "
            + "`koc_image` b "
            + " ON a.id=b.detail_id and b.`status`=1"  +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `a`.`customer_id`=`w`.`customer_id` " 
            + " LEFT JOIN `customer_special_koc_v` k "
            + " ON k.`customer_id` = `a`.`customer_id` "
            + "WHERE a.`status` = 4 and a.campaign_id >= 7 " 
            + " GROUP BY a.id  ORDER BY a.`status` DESC, a.`create_time` DESC " +
            "LIMIT #{start}, #{pageSize}")
	List<Note> getKocListForNote(Long customerId, int start, int pageSize);

    @Select(" SELECT  IFNULL(count(*),0) from `koc_detail` WHERE `status` = 4 and campaign_id >= 7 ")
	int getKocCountForNote();

    
    @Select("SELECT " +
            "   a.*, b.`image_url` `coverPhoto`, " +
            "   `w`.`nick_name` `ownerName`, " +
             " k.`customer_id` `koc_v`, " +
            "   `w`.`avatar_url` `avatarUrl` , " +
            " ( SELECT count(`id`) from `koc_like_log` WHERE `detail_id`= a.id ) 	`likeCount`, "+
            " ( `a`.`customer_id` = #{customerId} ) 	`self`, "+       
            " ( SELECT count(`id`) from `koc_like_log` WHERE `detail_id`= a.id AND `customer_id`=#{customerId} ) 	`liked` "+   
            " FROM " +
            "   `koc_detail` a "
            + " LEFT JOIN "
            + " `koc_image` b "
            + " ON a.id = b.detail_id   and b.`status`=1" +
             "       INNER JOIN " +
             "   `wechat` `w` " +
             "       ON `a`.`customer_id`=`w`.`customer_id` " 
             + " LEFT JOIN `customer_special_koc_v` k "
             + " ON k.`customer_id` = `a`.`customer_id` " + 
            "WHERE " +
            "   a.`id`=#{id} AND ( a.`status`>1 OR a.`customer_id` =#{customerId} ) " +
            "LIMIT 1")
	Note getNote(Long id, Long customerId);
    @Select("SELECT " +
            "   a.* " +
            " FROM " +
            "   `koc_detail` a " + 
            "WHERE " +
            "   a.`campaign_id`=#{id} AND  a.`customer_id` =#{customerId}  " +
            "LIMIT 1")
	Note getNoteByCampaign(Long id, Long customerId);
    @Select("SELECT " +
            "  `image_url`  photo,`id` " +
            "FROM " +
            "   `koc_image` " +
            "WHERE " +
            "   `detail_id`=#{noteId} and `status`=1 " )
	ArrayList<NotePhoto> getNotePhotos(Long id);

    @Insert("DELETE FROM `koc_like_log` " +
	          " WHERE  `detail_id` =  #{noteId} AND " +
	          "  `customer_id` = #{customerId} " )
	  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
	int delLikeLog(NoteLikeLog log);
	@Select("SELECT count(`id`) FROM  `koc_like_log`" +
            "  WHERE `detail_id`= #{noteId} AND " +
            "  `customer_id`= #{customerId} " +
            "")
	int getLikeLogCount(NoteLikeLog log);
	
	@Insert("INSERT INTO `koc_like_log`(" +
   		 "   `detail_id`, " +
            "   `customer_id` " +
            ") " +
            "VALUES (" +
            "   #{noteId}, " +
            "   #{customerId} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
	int insertLikeLog(NoteLikeLog log);
	

    @Update("UPDATE " +
            "   `koc_detail` " +
            "SET " +
            "   `title`=#{title} " +
            "WHERE " +
            "   `id`=#{detailId} " +
            "   AND `customer_id`=#{customerId}" )
    void updateTitle(Long detailId, String title, Long customerId);
    
    

    /**
     * 读取活动信息
     * @param campaignId 编号
     * @return 活动信息
     */
    @Select("SELECT " +
            "   `start_time`, " +
            "   `end_time`, " +
            "   `index_banner_image_url`, " +
            "   `upload_banner_image_url`, " +
            "   `rule_image_url`, " +
            "   `fail_image_url`, " +
            "   `share_title`, " +
            "   `share_image_url` " +
            "FROM " +
            "   `koc` " +
            "WHERE " +
            "   `id`=#{id} " +
            "   AND `status`=1 " +
            "LIMIT 1")
    Koc getKoc(long campaignId);
    
    /**
     * 获取达人榜信息
     *
     * @param campaignId 活动编号
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `alert`," +
            "   `status` " +
            "FROM " +
            "   `koc_detail` " +
            "WHERE " +
            "   `campaign_id`=#{campaignId} " +
            "   AND `customer_id`=#{customerId} " +
            "LIMIT 1")
    KocDetail getKocDetailAlert(
            @Param("campaignId") long campaignId,
            @Param("customerId") long customerId
    );

    /**
     * 更新提醒状态
     *
     * @param campaignId 活动编号
     * @param customerId 顾客编号
     */
    @Update("UPDATE " +
            "   `koc_detail` " +
            "SET " +
            "   `alert`=0 " +
            "WHERE " +
            "   `campaign_id`=#{campaignId} " +
            "   AND `customer_id`=#{customerId}")
    void updateKocDetailAlert(
            @Param("campaignId") long campaignId,
            @Param("customerId") long customerId
    );

    /**
     * 获取达人榜信息, 和上传图片的数量
     *
     * @param campaignId 活动编号
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `status`," +
            "   `title`," +
            "   `upload_time`," +
            "   `examine_time`," +
            "   (" +
            "       SELECT " +
            "           COUNT(`id`) " +
            "       FROM " +
            "           `koc_image` " +
            "       WHERE " +
            "           `detail_id`=`koc_detail`.`id` " +
            "           AND `status`=1" +
            "   ) `image_count` " +
            "FROM " +
            "   `koc_detail` " +
            "WHERE " +
            "   `campaign_id`=#{campaignId} " +
            "   AND `customer_id`=#{customerId} " +
            "LIMIT 1")
    KocDetail getKocDetailWithImageCount(
            @Param("campaignId") long campaignId,
            @Param("customerId") long customerId
    );

    /**
     * 写入达人榜信息
     *
     * @param campaignUgcDetail 达人榜信息
     */
    @Insert("INSERT INTO `koc_detail`(" +
            "   `campaign_id`, " +
            "   `customer_id`, " +
            "   `follow`, " +
            "   `follow_source`, " +
            "   `follow_first_time`, " +
            "   `follow_last_time`, " +
            "   `follow_cancel_time`, " +
            "   `bind`, " +
            "   `bind_time`, " +
            "   `status` " +
            ") " +
            "VALUES (" +
            "   #{campaignId}," +
            "   #{customerId}," +
            "   #{follow}," +
            "   #{followSource}," +
            "   #{followFirstTime}," +
            "   #{followLastTime}," +
            "   #{followCancelTime}," +
            "   #{bind}," +
            "   #{bindTime}," +
            "   #{status}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertKocDetail(KocDetail campaignUgcDetail);

    /**
     * 获取所有图片
     *
     * @param detailId 顾客活动信息编号
     * @return 图片地址
     */
    @Select("SELECT " +
            "   `image_url`," +
            "   `status` " +
            "FROM " +
            "   `koc_image` " +
            "WHERE " +
            "   `detail_id`=#{detailId} and `status`=1 " +
            "ORDER BY `id` ")
    List<KocDetailImage> getKocDetailImageList(long detailId);

    /**
     * 获取当前TOP信息
     *
     * @param campaignId 活动编号
     * @return TOP信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `stage` " +
            "   `title`, " +
            "FROM " +
            "   `koc_top` " +
            "WHERE " +
            "   `campaign_id`=#{campaignId} " +
            "   AND `status`=1 " +
            "   AND NOW()>=`start_time` AND NOW()<=`end_time`")
    KocTop getKocTop(long campaignId);

    /**
     * 获取入选TOP的名单
     *
     * @param topId TOP信息编号
     * @return 名单
     */
    @Select("SELECT " +
            "   `d`.`detail_id`," +
            "   `t`.`examine_time`, " +
            "   `t`.`title`, " +
            "   `w`.`nick_name`, " +
            "   `w`.`avatar_url` " +
            "FROM " +
            "   `koc_top_detail` `d` " +
            "       INNER JOIN " +
            "   `koc_detail` `t` " +
            "       ON `d`.`detail_id`=`t`.`id` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `t`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "   `d`.`top_id`=#{topId} AND `d`.`status`=1 " +
            "ORDER BY " +
            "   `d`.`sort`")
    List<KocTopDetail> getKocTopDetailList(long topId);

    /**
     * 获取入选TOP的图片
     *
     * @param topId TOP信息编号
     * @return 图片
     */
    @Select("SELECT " +
            "   `i`.`id`, " +
            "   `i`.`detail_id`, " +
            "   `i`.`image_url` " +
            "FROM " +
            "   `koc_top_detail` `d` " +
            "       INNER JOIN " +
            "   `koc_image` `i` " +
            "       ON `d`.`detail_id`=`i`.`detail_id` " +
            "WHERE " +
            "   `d`.`top_id`=#{topId}" +
            "   AND `d`.`status`=1 AND `i`.`status`=1")
    List<KocTopDetailImage> getKocTopDetailImageList(long topId);

    /**
     * 变改状态
     *
     * @param campaignId 活动编号
     * @param customerId     顾客编号
     * @param originalStatus 原始状态
     * @param newStatus      新状态
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `koc_detail` " +
            "SET " +
            "   `status`=#{newStatus}, " +
            "   `alert`=1 " +
            "WHERE " +
            "   `campaign_id`=#{campaignId} " +
            "   AND `customer_id`=#{customerId} " +
            "   AND `status`=#{originalStatus}")
    int updateKocDetail(
            @Param("campaignId") long campaignId,
            @Param("customerId") long customerId,
            @Param("originalStatus") short originalStatus,
            @Param("newStatus") short newStatus
    );

    /**
     * 变改状态
     *
     * @param campaignId 活动编号
     * @param customerId     顾客编号
     * @param originalStatus 原始状态
     * @param newStatus      新状态
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `koc_detail` " +
            "SET " +
            "   `status`=#{newStatus}," +
            "   `alert`=1, " +
            "   `upload_time`=NOW() " +
            "WHERE " +
            "   `campaign_id`=#{campaignId} " +
            "   AND `customer_id`=#{customerId} " +
            "   AND `status`=#{originalStatus}")
    int updateKocDetailWithUploadTime(
            @Param("campaignId") long campaignId,
            @Param("customerId") long customerId,
            @Param("originalStatus") short originalStatus,
            @Param("newStatus") short newStatus
    );

    /**
     * 设置图片无效
     *
     * @param detailId 顾客活动信息编号
     */
    @Update("UPDATE " +
            "   `koc_image` " +
            "SET " +
            "   `status`=0 " +
            "WHERE " +
            "   `detail_id`=#{detailId}")
    void updateKocDetailImageStatus(long detailId);

    /**
     * 写入图片信息
     *
     * @param campaignUgcImage 图片信息
     */
    @Insert("INSERT INTO `koc_image`(" +
            "   `detail_id`, " +
            "   `image_url`" +
            ") " +
            "VALUES (" +
            "   #{detailId}, " +
            "   #{imageUrl}" +
            ")")
    void insertKocDetailImage(KocDetailImage campaignUgcImage);

    /**
     * 写入活动操作日志
     *
     * @param campaignUgcLog 操作日志
     */
    @Insert("INSERT INTO `koc_log`(" +
            "   `campaign_id`, " +
            "   `customer_id`, " +
            "   `status`, " +
            "   `content`" +
            ") " +
            "VALUES (" +
            "   #{campaignId}, " +
            "   #{customerId}, " +
            "   #{status}, " +
            "   #{content}" +
            ")")
    void insertKocLog(KocLog campaignUgcLog);

    /**
     * 更新肤质
     *
     * @param campaignId 活动编号
     * @param customerId 顾客编号
     * @param skinType   肤质
     */
    @Update("UPDATE " +
            "   `koc_detail` " +
            "SET " +
            "   `skin_type`=#{skinType} " +
            "WHERE " +
            "   `campaign_id`=#{campaignId} " +
            "   AND `customer_id`=#{customerId} " +
            "LIMIT 1")
    void setKocSkinType(
            @Param("campaignId") long campaignId,
            @Param("customerId") long customerId,
            @Param("skinType") short skinType
    );

    /**
     * 写入访问日志
     *
     * @param campaignUgcViewLog 日志
     */
    @Insert("INSERT INTO `koc_view_log`(" +
            "   `campaign_id`," +
            "   `customer_id`," +
            "   `source`," +
            "   `page`" +
            ") " +
            "VALUES (" +
            "   #{campaignId}," +
            "   #{customerId}," +
            "   #{source}," +
            "   #{page}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertKocViewLog(KocViewLog campaignUgcViewLog);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Update("UPDATE " +
            "   `koc_view_log` " +
            "SET " +
            "   `end_time`=NOW()," +
            "   `diff_second`=TimeStampDiff(SECOND,`start_time`,NOW()) " +
            "WHERE " +
            "   `id`=#{id}")
    void setKocViewLog(long id);

    /**
     * 写入达人榜访问记录
     *
     * @param campaignUgcTopDetailLog 达人榜访问记录
     */
    @Insert("INSERT INTO `koc_top_detail_log`(" +
            "   `customer_id`, " +
            "   `campaign_id`, " +
            "   `stage`, " +
            "   `top_detail_id`" +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{campaignId}, " +
            "   #{stage}, " +
            "   #{topDetailId}" +
            ")")
    void insertKocTopDetailLog(KocTopDetailLog campaignUgcTopDetailLog);

    /**
     * 写入达人榜图片访问记录
     *
     * @param campaignUgcTopDetailImageLog 达人榜图片访问记录
     */
    @Insert("INSERT INTO `koc_top_detail_image_log`(" +
            "   `customer_id`, " +
            "   `campaign_id`, " +
            "   `stage`, " +
            "   `top_detail_id`, " +
            "   `image_id`, " +
            "   `type` " +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{campaignId}, " +
            "   #{stage}, " +
            "   #{topDetailId}, " +
            "   #{imageId}, " +
            "   #{type} " +
            ")")
    void insertKocTopDetailImageLog(KocTopDetailImageLog campaignUgcTopDetailImageLog);

}
