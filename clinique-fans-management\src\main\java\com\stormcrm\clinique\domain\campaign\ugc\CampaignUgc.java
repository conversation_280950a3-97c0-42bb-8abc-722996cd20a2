package com.stormcrm.clinique.domain.campaign.ugc;

import lombok.Data;

import java.util.Date;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Data
public class CampaignUgc {

    private Long id;

    private String name;
    private Date startTime;
    private Date endTime;

    private Integer detailTotal;
    private Integer topTotal;

    private String indexBannerImageUrl;
    private String uploadBannerImageUrl;
    private String ruleImageUrl;
    private String failImageUrl;

    private String shareTitle;
    private String shareImageUrl;

    private Boolean status;
    private Date uploadTime;
    private Date examineTime;

}
