package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.dao.VideoDao;
import com.stormcrm.clinique.dao.VideoTagDao;
import com.stormcrm.clinique.domain.*;
import com.stormcrm.clinique.service.VideoService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 视频
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VideoServiceImpl implements VideoService {

    private final VideoDao videoDao;
    private final VideoTagDao videoTagDao;

    public VideoServiceImpl(
            VideoDao videoDao,
            VideoTagDao videoTagDao
    ) {
        this.videoDao = videoDao;
        this.videoTagDao = videoTagDao;
    }


    /**
     * 查询所有活动带分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索
     * @param type          状态
     * @return 活动列表分页
     */
    @Override
    public List<Video> getPage(int page, int perpage, String generalSearch, Short type) {
        return videoDao.getPage((page - 1) * perpage, perpage, generalSearch, type);
    }

    /**
     * 查询所有活动带分页的记录数
     *
     * @param generalSearch 搜索
     * @param type          状态
     * @return 记录数
     */
    @Override
    public int getPageCount(String generalSearch, Short type) {
        return videoDao.getPageCount(generalSearch, type);
    }

    /**
     * 根据id 获取 video
     *
     * @param id video id
     * @return 视频对象
     */
    @Override
    public Video getById(long id) {
        return videoDao.getVideo(id);
    }

    /**
     * 删除
     *
     * @param id video id
     * @return 删除标记
     */
    @Override
    public int del(long id) {
        return videoDao.delVideo(id);
    }

    /**
     * 保存
     *
     * @param video 视频
     * @return 页面结果
     */
    @Override
    public Result save(Video video, List<Long> tags) {
        int videoId = videoDao.insertVideo(video);
        log.trace(String.format(" save[ add ] video id is ======= : %d", videoId));
        if (tags!= null && !tags.isEmpty()) {
            tags.forEach(tag -> {
                VideoTagDefine videoTagDefine = videoTagDao.getVideoTagDefine(tag);

                VideoTag videoTag = new VideoTag();

                videoTag.setVideoId((long) videoId);
                videoTag.setStatus(1);
                videoTag.setType(1);
                videoTag.setTagId(videoTagDefine.getId());
                videoTag.setTagType(videoTagDefine.getType());
                videoTag.setTitle(videoTagDefine.getTitle());

                videoTagDao.insertVideoTag(videoTag);
            });
        }
        return ResultUtil.success("增加成功!");
    }

    /**
     * 更新
     *
     * @param video 视频
     * @return 页面结果
     */
    @Override
    public Result update(Video video, List<Long> tags) {
        int videoId = videoDao.updateVideo(video);
        log.trace(String.format(" save[ update ] video id is ======= : %d , %d ....", videoId,video.getId()));
        // 简单处理（低效）全部删除后，再全部新增；如果tags为空的话，说明反选了全部标签，delAll
        videoTagDao.delAllTagsByVideoId(video.getId());

        if (tags != null && !tags.isEmpty()) {
            tags.forEach(tag -> {

                VideoTagDefine videoTagDefine = videoTagDao.getVideoTagDefine(tag);

                VideoTag videoTag = new VideoTag();

                videoTag.setVideoId(video.getId());
                videoTag.setStatus(1);
                videoTag.setType(1);
                videoTag.setTagId(videoTagDefine.getId());
                videoTag.setTagType(videoTagDefine.getType());
                videoTag.setTitle(videoTagDefine.getTitle());

                videoTagDao.insertVideoTag(videoTag);
            });
        }

        return ResultUtil.success("编辑成功!");
    }

}
