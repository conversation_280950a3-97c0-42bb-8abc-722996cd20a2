package cn.slashsoft.clinique.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.logging.Logger;

public class CdpDataUtil {
	//测试环境
	protected static String API_HOST = "http://*************:12130/api/elc-enhancement/crm/";
	protected static String APP_ID = "cl_storm_39a246d0ccc75fa6";
	protected static String APP_SECRET = "GfRreSdSdsgI9TGn6BshvVU7I8IyDU04I1dqbTw0MqUB2FytVcCAbNvnyXtvkS1gMDuDRJeyPFLpIfHghecjQ202006==";

	//正式环境
	//protected static String API_HOST = "http://*************:12131/api/elc-enhancement/crm/";
	//protected static String APP_ID = "cl_storm_39a246d0ccc75prod6";
	//protected static String APP_SECRET = "clRreSdSdsgI9TGn6BshvVU7I8IyDU04I1dqbTw0MqUB2FytVcCAbNvnyXtvkS1gMDuDRJey6cl6prod202006==";

	private final static Logger _log = Logger.getLogger(CdpDataUtil.class.getName());
	
	public static String send(String method, HashMap<String, Object> postData){
		try {			
			URL url = new URL(API_HOST + method);
			HttpURLConnection connection = (HttpURLConnection) url.openConnection();
			connection.setDoOutput(true);
			connection.setDoInput(true);
			connection.setUseCaches(false);
			connection.setInstanceFollowRedirects(true);
			connection.setConnectTimeout(4000);
			connection.setReadTimeout(14000);
			connection.setRequestMethod("POST"); 
			connection.setRequestProperty("Accept", "application/json"); 
			connection.setRequestProperty("Content-Type", "application/json"); 
			connection.connect();
			OutputStreamWriter out = new OutputStreamWriter(connection.getOutputStream(), "UTF-8"); 

			postData.put("appId",APP_ID);		
			postData.put("appSecret",APP_SECRET);
			out.append(JsonUtil.toJson(postData) );
			
			out.flush();
			out.close();
	
			int code = connection.getResponseCode();			

            String result = "";
            if(code == 200){
    			BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream(),"utf-8"));
                String line = "";
                StringBuilder stringBuilder = new StringBuilder();
               
                while((line = bufferedReader.readLine()) != null){
                    stringBuilder.append(line);
                }               
                result = stringBuilder.toString();          	
            }else{
            	_log.info("API STATUS Code: "+ code);
            }            
	       return result;
		} catch (IOException e) {
			_log.info("ERROR: "+e.getMessage());
			return e.getMessage();			
		}
	}
}
