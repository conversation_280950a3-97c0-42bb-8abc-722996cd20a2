package cn.slashsoft.clinique.dao.mini;

import cn.slashsoft.clinique.domain.mini.ServiceMessage;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * 模版消息
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface ServiceMessageDao {

    /**
     * 写入客服消息
     *
     * @param message 客服消息
     */
    @Insert("INSERT INTO `message`(" +
            "   `wechat_official_openid`, " +
            "   `type`, " +
            "   `content`, " +
            "   `result`, " +
            "   `status`" +
            ") " +
            "VALUES (" +
            "   #{wechatOfficialOpenid}, " +
            "   #{type}, " +
            "   #{content}, " +
            "   #{result}, " +
            "   #{status}" +
            ")")
    void insertMessage(ServiceMessage message);


}
