package cn.slashsoft.clinique.service.campaign.impl;

import java.io.File;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;

import cn.slashsoft.clinique.dao.mini.KocDao;
import cn.slashsoft.clinique.domain.campaign.ugc.Koc;
import cn.slashsoft.clinique.domain.campaign.ugc.KocDetail;
import cn.slashsoft.clinique.domain.campaign.ugc.KocDetailImage;
import cn.slashsoft.clinique.domain.campaign.ugc.KocLog;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTop;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTopDetail;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTopDetailImage;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTopDetailImageLog;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTopDetailLog;
import cn.slashsoft.clinique.domain.campaign.ugc.KocViewLog;
import cn.slashsoft.clinique.domain.mini.Follow;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.service.campaign.CampaignUgcService;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.RandomUtil;
import cn.slashsoft.clinique.util.ServerUtil;
import cn.slashsoft.clinique.util.StringUtil;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Service
public class CampaignUgcServiceImpl implements CampaignUgcService {

	@Resource
    KocDao kocDao;
    private final WechatService wechatService;
    private final OutsideService outsideService;

    public CampaignUgcServiceImpl( WechatService wechatService, OutsideService outsideService) {

        this.wechatService = wechatService;
        this.outsideService = outsideService;
    }

    /**
     * 读取活动信息
     *
     * @param campaignId 编号
     * @return 活动信息
     */
    @Override
    public Koc getCampaignUgc(long campaignId) {
        return kocDao.getKoc(campaignId);
    }

    /**
     * 获取达人榜信息
     *
     * @param campaignId 活动编号
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    @Override
    public KocDetail getCampaignUgcDetailAlert(long campaignId, long customerId) {
        return kocDao.getKocDetailAlert(campaignId, customerId);
    }

    /**
     * 更新提醒状态
     *
     * @param campaignId 活动编号
     * @param customerId 顾客编号
     */
    @Override
    public void updateCampaignUgcDetailAlert(long campaignId, long customerId) {
        kocDao.updateKocDetailAlert(campaignId, customerId);
    }





    /**
     * 获取达人榜信息
     *
     * @param campaignId 活动编号
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    @Override
    public KocDetail getCampaignUgcDetail(long campaignId, long customerId) {

        KocDetail campaignUgcDetail = kocDao.getKocDetailWithImageCount(campaignId, customerId);

        if (null == campaignUgcDetail) {
            // 生成活动信息
            campaignUgcDetail = new KocDetail();
            campaignUgcDetail.setCampaignId(campaignId);
            campaignUgcDetail.setCustomerId(customerId);
            campaignUgcDetail.setAlert(false);
            campaignUgcDetail.setStatus((short) 1);

            // 读取公众号openid
            Wechat wechat = wechatService.getWechatInfoByCustomerId(customerId);
            if (null != wechat && !StringUtil.isNullOrEmpty(wechat.getWechatOfficialOpenid())) {
                Follow follow = outsideService.isFollow(wechat.getWechatOfficialOpenid());
                if (null != follow) {
                    campaignUgcDetail.setFollow(follow.getFollow());
                    campaignUgcDetail.setFollowSource(follow.getFollowSource());
                    campaignUgcDetail.setFollowFirstTime(follow.getFollowFirstTime());
                    campaignUgcDetail.setFollowLastTime(follow.getFollowLastTime());
                    campaignUgcDetail.setFollowCancelTime(follow.getFollowCancelTime());
                    campaignUgcDetail.setBind(follow.getBind());
                    campaignUgcDetail.setBindTime(follow.getBindTime());
                }
            }

            kocDao.insertKocDetail(campaignUgcDetail);

            // 写入日志
            KocLog campaignUgcLog = new KocLog();
            campaignUgcLog.setCampaignId(campaignId);
            campaignUgcLog.setCustomerId(customerId);
            campaignUgcLog.setStatus((short) 1);
            campaignUgcLog.setContent("生成活动信息");
            kocDao.insertKocLog(campaignUgcLog);
        } else {
            // 状态为未上传时，将所有图片改为无效
            if (1 == campaignUgcDetail.getStatus() && 0 < campaignUgcDetail.getImageCount()) {
                kocDao.updateKocDetailImageStatus(campaignUgcDetail.getId());
            }
        }

        return campaignUgcDetail;
    }


    /**
     * 获取图片
     *
     * @param detailId 顾客活动信息编号
     * @return 图片地址
     */
    @Override
    public List<KocDetailImage> getCampaignUgcImageList(long detailId) {
        return kocDao.getKocDetailImageList(detailId);
    }

    /**
     * 获取当前TOP信息
     *
     * @param campaignId 活动编号
     * @return TOP信息
     */
    @Override
    public KocTop getCampaignUgcTop(long campaignId) {
        return kocDao.getKocTop(campaignId);
    }

    /**
     * 获取入选TOP的名单
     *
     * @param topId TOP信息编号
     * @return 名单
     */
    @Override
    public List<KocTopDetail> getCampaignUgcTopDetailList(long topId) {
        return kocDao.getKocTopDetailList(topId);
    }

    /**
     * 获取入选TOP的图片
     *
     * @param topId TOP信息编号
     * @return 图片
     */
    @Override
    public List<KocTopDetailImage> getCampaignUgcTopDetailImageList(long topId) {
        return kocDao.getKocTopDetailImageList(topId);
    }

    /**
     * 保存图片
     *
     * @param campaignId 活动编号
     * @param customerId 顾客编号
     * @param file       要保存的图片
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean upload(long campaignId, long customerId, MultipartFile file) {

        // 读取活动信息
        KocDetail campaignUgcDetail = kocDao.getKocDetailWithImageCount(campaignId, customerId);
        if (null == campaignUgcDetail) {
            return false;
        }

        KocLog campaignUgcLog;
        switch (campaignUgcDetail.getStatus()) {
            // 未上传
            case 1:
                // 变更状态为2审核中
                if (0 == kocDao.updateKocDetailWithUploadTime(campaignId, customerId, (short) 1, (short) 2)) {
                    return false;
                }
                // 写入日志
                campaignUgcLog = new KocLog();
                campaignUgcLog.setCampaignId(campaignId);
                campaignUgcLog.setCustomerId(customerId);
                campaignUgcLog.setStatus((short) 2);
                campaignUgcLog.setContent("状态从未上传变更为审核中");
                kocDao.insertKocLog(campaignUgcLog);
                break;
            // 审核中
            case 2:
                // 检查当前相片的数量，超过3则不再保存
                if (5 <= campaignUgcDetail.getImageCount()) {
                    return false;
                }
                break;
            // 审核失败
            case 3:
                // 变更状态为2审核中
                if (0 == kocDao.updateKocDetail(campaignId, customerId, (short) 3, (short) 2)) {
                    return false;
                }
                // 原上传图片全部设为无效
                kocDao.updateKocDetailImageStatus(campaignUgcDetail.getId());
                // 写入日志
                campaignUgcLog = new KocLog();
                campaignUgcLog.setCampaignId(campaignId);
                campaignUgcLog.setCustomerId(customerId);
                campaignUgcLog.setStatus((short) 2);
                campaignUgcLog.setContent("状态从审核未通过变更为审核中");
                kocDao.insertKocLog(campaignUgcLog);
                break;
            default:
                return false;
        }

        // 保存图片
        String imageUrl = outsideService.uploadImageOss(file);
        if (StringUtil.isNullOrEmpty(imageUrl)) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }

        // 保存数据库
        KocDetailImage campaignUgcImage = new KocDetailImage();
        campaignUgcImage.setDetailId(campaignUgcDetail.getId());
        campaignUgcImage.setImageUrl(imageUrl);
        kocDao.insertKocDetailImage(campaignUgcImage);

        // 写入日志
        campaignUgcLog = new KocLog();
        campaignUgcLog.setCampaignId(campaignId);
        campaignUgcLog.setCustomerId(customerId);
        campaignUgcLog.setStatus((short) 2);
        campaignUgcLog.setContent("上传图片");
        kocDao.insertKocLog(campaignUgcLog);

        return true;
    }

    /**
     * 保存图片
     *
     * @param path         目录
     * @param originalFile 要保存的图片
     * @return 保存成功的文件地址
     */
    private String saveImageFile(String path, MultipartFile originalFile) {

        try {
            Date now = new Date();

            // 获取静态资源文件的目录地址
            String staticPath = ServerUtil.getStaticPath();

            // 保存位置
            path = path + DateUtil.parseString(now, "yyyyMMdd");
            // 如果目录不存在，创建目录
            File folder = new File(staticPath + path);
            if (!folder.exists()) {
                if (folder.mkdirs()) {
                    throw new Exception("创建目录失败");
                }
            }

            // 获取上传文件名的后缀名
            String originalFileName = originalFile.getOriginalFilename();
            if (StringUtil.isNullOrEmpty(originalFileName)) {
                throw new Exception("上传的文件名为空");
            }
            // 生成文件名
            String fileName = path + "/" + DateUtil.parseString(now, "HHmmssSS") + RandomUtil.getNumberBetween(1000, 9999) + originalFileName.substring(originalFileName.lastIndexOf("."));

            File imageFile = new File(staticPath + fileName);

            originalFile.transferTo(imageFile);

            return fileName;
        } catch (Exception e) {
            return null;
        }

    }

    /**
     * 更新肤质
     *
     * @param campaignId 活动编号
     * @param customerId 顾客编号
     * @param skinType   肤质
     */
    @Override
    public void setCampaignUgcSkinType(long campaignId, long customerId, short skinType) {
        kocDao.setKocSkinType(campaignId, customerId, skinType);
    }

    /**
     * 写入访问开始日志
     *
     * @param campaignId 活动编号
     * @param customerId 顾客编号
     * @param source     来源
     * @param page       页面
     * @return 访问日志编号
     */
    @Override
    public long insertCampaignUgcViewLog(long campaignId, long customerId, String source, String page) {
        KocViewLog campaignUgcViewLog = new KocViewLog();
        campaignUgcViewLog.setCampaignId(campaignId);
        campaignUgcViewLog.setCustomerId(customerId);
        campaignUgcViewLog.setSource(source);
        campaignUgcViewLog.setPage(page);
        kocDao.insertKocViewLog(campaignUgcViewLog);
        return campaignUgcViewLog.getId();
    }

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Override
    public void setCampaignUgcViewLog(long id) {
        kocDao.setKocViewLog(id);
    }

    /**
     * 写入达人榜访问记录
     *
     * @param campaignUgcTopDetailLog 达人榜访问记录
     */
    @Override
    public void insertCampaignUgcTopDetailLog(KocTopDetailLog campaignUgcTopDetailLog) {
        kocDao.insertKocTopDetailLog(campaignUgcTopDetailLog);
    }

    /**
     * 写入达人榜图片访问记录
     *
     * @param campaignUgcTopDetailImageLog 达人榜图片访问记录
     */
    @Override
    public void insertCampaignUgcTopDetailImageLog(KocTopDetailImageLog campaignUgcTopDetailImageLog) {
        kocDao.insertKocTopDetailImageLog(campaignUgcTopDetailImageLog);
    }

}
