package cn.slashsoft.clinique.enums;

import lombok.Getter;

/**
 * C粉值类别
 *
 * <AUTHOR>
 */
public enum CActiveForeignEnum {
    NOTE_VIDEO_READ          ((short) 1, "阅读笔记/观看视频"),
    NOTE_VIDEO_LIKE             ((short) 2, "关注笔记关注视频"),
    NOTE_DISCUSS          ((short) 3, "发布评论"),
    NOTE_PUBLISH             ((short) 4, "成功发布笔记心得"),
    SHARE_READ             ((short) 5, "分享给好友，好友阅读笔记/观看视频"),
    SHARE_READ_BIND            ((short) 6, "分享给好友，好友阅读笔记/观看视频且绑定C粉圈"),
    LOGIN            ((short) 7, "登录"),
    LOGIN_3            ((short) 8, "连续三天登录"),
    LOGIN_7            ((short) 9, "连续七天登录"),
    LOGIN_NO           ((short) 10, "一天未登录"),
    LOGIN_NO_3           ((short) 11, "连续七天未登录"),
    LOGIN_NO_7           ((short) 12, "连续七天未登录")
    ;

    @Getter
    private final short id;

    @Getter
    private final String name;

    CActiveForeignEnum(short id, String name) {
        this.id = id;
        this.name = name;
    }
}
