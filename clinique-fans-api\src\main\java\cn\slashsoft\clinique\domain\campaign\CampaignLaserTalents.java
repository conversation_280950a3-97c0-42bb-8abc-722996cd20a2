package cn.slashsoft.clinique.domain.campaign;

import lombok.Data;

import java.util.Date;

/**
 * Laser Focus 达人榜
 *
 * <AUTHOR>
 */
@Data
public class CampaignLaserTalents {

    private Long id;
    private Long customerId;

    private Boolean follow;
    private String followSource;
    private Date followFirstTime;
    private Date followLastTime;
    private Date followCancelTime;
    private Boolean bind;
    private Date bindTime;

    private Boolean alert;
    private Short status;
    private Integer imageCount;
    private Date uploadTime;
    private Date examineTime;

}
