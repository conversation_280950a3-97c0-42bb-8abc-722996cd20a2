package cn.slashsoft.clinique.dao.mini;

import cn.slashsoft.clinique.domain.mini.Campaign;
import cn.slashsoft.clinique.domain.mini.CampaignLog;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 与活动相关
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignDao {

    /**
     * 获取新客活动排除名单
     * @param openId 小程序openId
     * @return 数量
     */
    @Select("SELECT " +
            "   COUNT(`id`) " +
            "FROM " +
            "   `campaign_newfree_exclude` " +
            "WHERE " +
            "   `openid`=#{openId} " +
            "limit 1")
    int getCampaignNewFreeExclude(String openId);

    /**
     * 获取排除名单
     * @param phoneNumber 手机号码
     * @return 数量
     */
    @Select("SELECT " +
            "   COUNT(`id`) " +
            "FROM " +
            "   `campaign_c520_exclude` " +
            "WHERE " +
            "    `phone_number`=#{phoneNumber} " +
            "limit 1")
    int getCampaignC520Exclude(String phoneNumber);

    /**
     * 获取有效活动信息，按更新时间倒序排列
     * 活动资讯
     *
     * @return 活动信息
     */
    @Select("SELECT " +
            "   `id`, " +
            "   `type`, " +
            "   `name`, " +
            "   `image_url`, " +
            "   `app_id`, " +
            "   `link_url` " +
            "FROM " +
            "   `campaign` " +
            "WHERE " +
            "   `status`=1 " +
            "ORDER BY " +
            "   `id` DESC")
    List<Campaign> getCampaign();

    /**
     * 写入活动日志
     * @param campaignLog 活动日志
     */
    @Insert("INSERT INTO `campaign_log`(" +
            "   `wechat_mini_openid`, " +
            "   `campaign_id` " +
            ") " +
            "VALUES (" +
            "   #{openid}," +
            "   #{campaignId}" +
            ")"
    )
    void insertCampaignLog(CampaignLog campaignLog);

}
