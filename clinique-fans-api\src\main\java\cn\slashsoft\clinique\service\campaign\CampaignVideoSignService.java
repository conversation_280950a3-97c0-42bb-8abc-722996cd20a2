package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignVideoSign;
import cn.slashsoft.clinique.domain.campaign.CampaignVideoSignConfig;
import cn.slashsoft.clinique.domain.campaign.CampaignVideoSignJumpLog;
import cn.slashsoft.clinique.domain.campaign.CampaignVideoSignPlayLog;

import java.util.List;

/**
 * 视频签到活动
 *
 * <AUTHOR>
 */
public interface CampaignVideoSignService {

    /**
     * 获取签到信息
     *
     * @param customerId 顾客编号
     * @return 签到信息
     */
    CampaignVideoSign getCampaignVideoSign(long customerId);

    /**
     * 获取视频列表
     *
     * @return 视频列表
     */
    List<CampaignVideoSignConfig> getCampaignVideoSignConfig();

    /**
     * 签到
     *
     * @param customerId 顾客编号
     * @param level      签到等级
     * @return 是否成功
     */
    boolean sign(long customerId, short level);

    /**
     * 写入访问开始日志
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @return 访问日志编号
     */
    long insertCampaignVideoSignViewLog(long customerId, String source);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    void setCampaignVideoSignViewLog(long id);

    /**
     * 写入播放记录
     *
     * @param campaignVideoSignPlayLog 播放记录
     */
    void insertCampaignVideoSignPlayLog(CampaignVideoSignPlayLog campaignVideoSignPlayLog);

    /**
     * 写入跳转记录
     *
     * @param campaignVideoSignJumpLog 跳转记录
     */
    void insertCampaignVideoSignJumpLog(CampaignVideoSignJumpLog campaignVideoSignJumpLog);

}
