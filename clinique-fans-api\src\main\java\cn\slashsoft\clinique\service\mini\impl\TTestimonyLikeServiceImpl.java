package cn.slashsoft.clinique.service.mini.impl;

import cn.slashsoft.clinique.dao.mini.TTestimonyLikeDao;
import cn.slashsoft.clinique.domain.mini.TTestimonyLike;
import cn.slashsoft.clinique.service.mini.TTestimonyLikeService;
import cn.slashsoft.clinique.vo.mini.TestimonyHelpThumbupVo;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
* <p>
    *
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
@Service
public class TTestimonyLikeServiceImpl implements TTestimonyLikeService {

    @Resource
    private TTestimonyLikeDao tTestimonyLikeDao;

    @Override
    public void insertTTestimonyLike(TTestimonyLike tTestimonyLike) {
        tTestimonyLikeDao.insertTTestimonyLike(tTestimonyLike);
    }

    @Override
    public void updateTTestimonyLike(TTestimonyLike tTestimonyLike) {
        tTestimonyLikeDao.updateTTestimonyLike(tTestimonyLike);
    }

    @Override
    public TTestimonyLike getTTestimonyLike(Long id) {
        return tTestimonyLikeDao.getTTestimonyLike(id);
    }

    @Override
    public TTestimonyLike getTTestimonyLikeByCustomerIdContentId(Long customerId, Long contentId) {
        return tTestimonyLikeDao.getTTestimonyLikeByCustomerIdContentId(customerId, contentId);
    }

    @Override
    public TestimonyHelpThumbupVo getHelpThumbupInfo(Long customerId) {
        return tTestimonyLikeDao.getHelpThumbupInfo(customerId);
    }
}
