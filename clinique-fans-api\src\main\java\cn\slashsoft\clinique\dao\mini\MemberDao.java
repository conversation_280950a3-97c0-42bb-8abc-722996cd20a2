package cn.slashsoft.clinique.dao.mini;

import cn.slashsoft.clinique.domain.cdp.Member;
import cn.slashsoft.clinique.domain.mini.Customer;
import cn.slashsoft.clinique.domain.mini.Wechat;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

/**
 * 新的会员表
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Mapper
@Repository
public interface MemberDao {

    /**
     * 跟据开放平台唯一编号查找会员信息
     *
     * @param unionid 开放平台唯一编号
     * @return 会员信息
     * @since 2023-12-04
     */
    @Select("SELECT " +
            "   `m`.`id`," +
            "   `m`.`customer_id`," +
            "   `m`.`unionid`," +
            "   `m`.`official_openid` `wechat_official_openid`," +
            "   `m`.`mini_openid` `wechat_mini_openid`," +
            "   decryptPhone(`m`.`phone_number`) `phone_number`," +
            "   `v`.`customer_id` `koc_v`," +
            "   `m`.`last_scene_reward_time` `scene_reward_last_time` " +
            "FROM " +
            "   `member` `m` LEFT JOIN `customer_special_koc_v` `v` ON `m`.`customer_id`=`v`.`customer_id` " +
            "WHERE " +
            "   `m`.`unionid`=#{unionid} " +
            "LIMIT 1")
    Wechat getMemberByUnionid(String unionid);

    /**
     * 跟据开放平台唯一编号查找顾客信息
     *
     * @param unionid 开放平台唯一编号
     * @return 顾客信息
     * @since 2023-12-04
     */
    @Select("SELECT " +
            "   `id` " +
            "FROM " +
            "   `customer` " +
            "WHERE " +
            "   `unionid`=#{unionid} " +
            "LIMIT 1")
    Customer getCustomerByUnionid(String unionid);

    /**
     * 跟据开放平台唯一编号查找微信信息
     *
     * @param unionid 开放平台唯一编号
     * @return 微信信息
     * @since 2023-12-04
     */
    @Select("SELECT " +
            "   `id` " +
            "FROM " +
            "   `wechat` " +
            "WHERE " +
            "   `unionid`=#{unionid} " +
            "LIMIT 1")
    Wechat getWechatByUnionid(String unionid);

    /**
     * 创建顾客信息
     *
     * @param customer 顾客信息
     * @since 2023-12-04
     */
    @Insert("INSERT INTO `customer` (" +
            "   `unionid`, " +
            "   `name`," +
            "   `phone_number`, " +
            "   `birthday`," +
            "   `agree_brand_communicate`," +
            "   `agree_corp_communicate`," +
            "   `campaign`," +
            "   `source`," +
            "   `ranking`, " +
            "   `ranking_always` " +
            ") " +
            "VALUES ( " +
            "   #{unionid}," +
            "   encryptName(#{name})," +
            "   encryptPhone(#{phoneNumber})," +
            "   #{birthday}," +
            "   #{agreeBrandCommunicate}," +
            "   #{agreeCorpCommunicate}," +
            "   #{campaign}," +
            "   #{source}," +
            "   (SELECT `count` FROM (SELECT COUNT(*) `count` FROM `customer`) `a`)," +
            "   (SELECT `count` FROM (SELECT COUNT(*) `count` FROM `customer`) `a`) " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertCustomer(Customer customer);

    /**
     * 写入会员信息
     *
     * @param member 会员信息
     * @since 2023-12-04
     */
    @Insert("INSERT INTO `member`(" +
            "   `customer_id`, " +
            "   `unionid`, " +
            "   `official_openid`, " +
            "   `mini_openid`, " +
            "   `customer_sid`, " +
            "   `customer_type`, " +
            "   `level`, " +
            "   `name`, " +
            "   `phone_number`, " +
            "   `gender`, " +
            "   `birthday`, " +
            "   `points`, " +
            "   `due_points`, " +
            "   `skin_type`, " +
            "   `skincare_step`, " +
            "   `skincare_effect`, " +
            "   `cosmetics_effect`, " +
            "   `agree_brand_communicate`, " +
            "   `agree_corp_communicate`, " +
            "   `first_purchase_channel`," +
            "   `first_purchase_date`," +
            "   `new_customer`," +
            "   `four_months_purchase_customer`," +
            "   `source`, " +
            "   `campaign` " +
            ")" +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{unionid}, " +
            "   #{officialOpenid}, " +
            "   #{miniOpenid}, " +
            "   #{customerSid}, " +
            "   #{customerType}, " +
            "   #{level}, " +
            "   encryptName(#{name}), " +
            "   encryptPhone(#{phoneNumber}), " +
            "   #{gender}, " +
            "   #{birthday}, " +
            "   #{points}, " +
            "   #{duePoints}, " +
            "   #{skinType}, " +
            "   #{skincareStep}, " +
            "   #{skincareEffect}, " +
            "   #{cosmeticsEffect}, " +
            "   #{agreeBrandCommunicate}, " +
            "   #{agreeCorpCommunicate}, " +
            "   #{firstPurchaseChannel}," +
            "   #{firstPurchaseDate}," +
            "   #{newCustomer}," +
            "   #{fourMonthsPurchaseCustomer}," +
            "   #{source}, " +
            "   #{campaign} " +
            ")")
    void insertMember(Member member);

    /**
     * 写入微信信息
     *
     * @param wechat 微信信息
     * @since 2023-12-04
     */
    @Insert("INSERT INTO `wechat` (" +
            "   `customer_id`," +
            "   `unionid`," +
            "   `wechat_official_openid`," +
            "   `wechat_mini_openid`," +
            "   `nick_name`," +
            "   `avatar_url`," +
            "   `gender`," +
            "   `country`," +
            "   `province`," +
            "   `city`, " +
            "   `language` " +
            ") " +
            "VALUES ( " +
            "   #{customerId}," +
            "   #{unionid}," +
            "   #{wechatOfficialOpenid}," +
            "   #{wechatMiniOpenid}," +
            "   #{nickName}," +
            "   #{avatarUrl}," +
            "   #{gender}," +
            "   #{country}," +
            "   #{province}," +
            "   #{city}," +
            "   #{language}" +
            ")")
    void insertWechat(Wechat wechat);

    /**
     * 更新会员信息
     *
     * @param member 会员信息
     * @since 2023-12-04
     */
    @Update("UPDATE " +
            "   `customer` " +
            "SET " +
            "   `name`=encryptName(#{name})," +
            "   `phone_number`=encryptPhone(#{phoneNumber})," +
            "   `birthday`=#{birthday}," +
            "   `update_time`=NOW() " +
            "WHERE " +
            "   `unionid`=#{unionid}")
    void updateCustomer(Member member);

    /**
     * 更新会员信息
     *
     * @param member 会员信息
     * @since 2023-12-04
     */
    @Update("UPDATE " +
            "   `member` " +
            "SET " +
            "   `customer_sid`=#{customerSid}," +
            "   `customer_type`=#{customerType}," +
            "   `level`=#{level}," +
            "   `name`=encryptName(#{name})," +
            "   `phone_number`=encryptPhone(#{phoneNumber})," +
            "   `gender`=#{gender}," +
            "   `birthday`=#{birthday}," +
            "   `points`=#{points}," +
            "   `due_points`=#{duePoints}," +
            "   `skin_type`=#{skinType}," +
            "   `skincare_step`=#{skincareStep}," +
            "   `skincare_effect`=#{skincareEffect}," +
            "   `cosmetics_effect`=#{cosmeticsEffect}," +
            "   `last_visit_time`=NOW()," +
            "   `first_purchase_channel`=#{firstPurchaseChannel}," +
            "   `first_purchase_date`=#{firstPurchaseDate}," +
            "   `new_customer`=#{newCustomer}," +
            "   `four_months_purchase_customer`=#{fourMonthsPurchaseCustomer}," +
            "   `update_time`=NOW() " +
            "WHERE " +
            "   `unionid`=#{unionid}")
    void updateMember(Member member);

    /**
     * 更新公众号文章场景值最后发放积分时间
     *
     * @param unionid 开放平台唯一编号
     * @since 2023-12-04
     */
    @Update("UPDATE " +
            "   `member` " +
            "SET " +
            "   `last_scene_reward_time`=NOW() " +
            "WHERE " +
            "   `unionid`=#{unionid} " +
            "LIMIT 1"
    )
    void updateMemberSceneLastTime(String unionid);

}
