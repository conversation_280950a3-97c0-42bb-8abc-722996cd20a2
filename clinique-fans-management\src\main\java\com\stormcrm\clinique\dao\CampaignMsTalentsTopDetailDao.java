package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.CampaignMsTalentsTopDetail;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 新品体验官活动TOP
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignMsTalentsTopDetailDao {

    /**
     * 查询所有活动
     *
     * @param id TOP5编号
     * @return 活动列表
     */
    @Select("SELECT " +
            "   `d`.`id`," +
            "   `t`.`customer_id`," +
            "   `w`.`nick_name`, " +
            "   `w`.`avatar_url`," +
            "   `d`.`talents_id`, " +
            "   `t`.`upload_time`, " +
            "   `t`.`examine_time`, " +
            "   `d`.`sort`, " +
            "   `t`.`status` " +
            "FROM " +
            "   `campaign_ms_talents_top_detail` `d` " +
            "       INNER JOIN " +
            "   `campaign_ms_talents` `t` " +
            "       ON `d`.`talents_id`=`t`.`id` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `t`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "   `d`.`talents_top_id`=#{id} " +
            "   AND `d`.`status`=1 " +
            "ORDER BY " +
            "   `d`.`sort`,`d`.`id`")
    List<CampaignMsTalentsTopDetail> getAll(long id);

    /**
     * 查询
     *
     * @param detailId 自动编号
     * @return 信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `talents_id`," +
            "   `sort` " +
            "FROM " +
            "   `campaign_ms_talents_top_detail` " +
            "WHERE " +
            "   `id`=#{id} " +
            "LIMIT 1")
    CampaignMsTalentsTopDetail getById(long detailId);

    /**
     * 保存
     *
     * @param campaignMsTalentsTopDetail 信息
     */
    @Insert("INSERT INTO `campaign_ms_talents_top_detail`(" +
            "   `talents_top_id`," +
            "   `talents_id`, " +
            "   `sort` " +
            ") " +
            "VALUES (" +
            "   #{talentsTopId}, " +
            "   #{talentsId}, " +
            "   #{sort} " +
            ")")
    void save(CampaignMsTalentsTopDetail campaignMsTalentsTopDetail);

    /**
     * 更新
     *
     * @param campaignMsTalentsTopDetail 信息
     */
    @Update("UPDATE " +
            "   `campaign_ms_talents_top_detail` " +
            "SET " +
            "   `talents_id`=#{talentsId}, " +
            "   `sort`=#{sort} " +
            "WHERE " +
            "   `id`=#{id}")
    void update(CampaignMsTalentsTopDetail campaignMsTalentsTopDetail);

    /**
     * 删除
     *
     * @param id 编号
     * @return 影响的行
     */
    @Delete("UPDATE " +
            "   `campaign_ms_talents_top_detail` " +
            "SET " +
            "   `status`=0 " +
            "WHERE " +
            "   `id`=#{id}")
    int delete(long id);

}
