package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.StoreGift;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 商城礼品管理
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface StoreGiftDao {

    /**
     * 查询所有礼品
     *
     * @return 礼品列表
     */
    @Select("SELECT " +
            "   `id`," +
            "   `name`," +
            "   `points`, " +
            "   `stocks`, " +
            "   `exchange_total`, " +
            "   `status`, " +
            "   `update_time`, " +
            "   `create_time` " +
            "FROM " +
            "   `store_gift` " +
            "WHERE " +
            "   `store_gift_type_id`=1 " +
            "   AND `recovery`=0 " +
            "ORDER BY `id` DESC")
    List<StoreGift> getAll();

    /**
     * 查询所有礼品列表的记录数
     *
     * @return 记录数
     */
    @Select("SELECT " +
            "   COUNT(*) " +
            "FROM " +
            "   `store_gift` " +
            "WHERE " +
            "   `store_gift_type_id`=1 " +
            "   AND `recovery`=0")
    int getAllCount();

    /**
     * 查询所有礼品-分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 礼品列表
     */
    @SelectProvider(type = StoreGiftProvider.class, method = "getPage")
    List<StoreGift> getPage(
            @Param("pageIndex") int pageIndex,
            @Param("pageSize") int pageSize,
            @Param("generalSearch") String generalSearch,
            @Param("status") Boolean status
    );

    /**
     * 查询所有礼品带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    @SelectProvider(type = StoreGiftProvider.class, method = "getPageCount")
    int getPageCount(
            String generalSearch,
            Boolean status
    );

    /**
     * 查询
     *
     * @param id 自动编号
     * @return 信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `name`," +
            "   `image_url`," +
            "   `description`," +
            "   `points`, " +
            "   `stocks` " +
            "FROM " +
            "   `store_gift` " +
            "WHERE " +
            "   `id`=#{id} AND " +
            "   `recovery`=0 " +
            "LIMIT 1"
    )
    StoreGift getById(long id);

    /**
     * 保存
     *
     * @param storeGift 信息
     */
    @Insert("INSERT INTO `store_gift`(" +
            "   `store_gift_type_id`, " +
            "   `name`, " +
            "   `image_url`, " +
            "   `description`, " +
            "   `points`, " +
            "   `stocks`" +
            ") " +
            "VALUES (" +
            "   #{storeGiftTypeId}, " +
            "   #{name}, " +
            "   #{imageUrl}, " +
            "   #{description}, " +
            "   #{points}, " +
            "   #{stocks} " +
            ")")
    void save(StoreGift storeGift);

    /**
     * 更新
     *
     * @param storeGift 信息
     */
    @UpdateProvider(type = StoreGiftProvider.class, method = "update")
    void update(StoreGift storeGift);

    /**
     * 上架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Update("UPDATE " +
            "   `store_gift` " +
            "SET " +
            "   `status`=1 " +
            "WHERE " +
            "   `id`=#{id}"
    )
    int upper(long id);

    /**
     * 下架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Update("UPDATE " +
            "   `store_gift` " +
            "SET " +
            "   `status`=0 " +
            "WHERE " +
            "   `id`=#{id}"
    )
    int lower(long id);

    /**
     * 逻辑删除
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Update("UPDATE " +
            "   `store_gift` " +
            "SET " +
            "   `recovery`=1 " +
            "WHERE " +
            "   `id`=#{id}"
    )
    int del(long id);

}
