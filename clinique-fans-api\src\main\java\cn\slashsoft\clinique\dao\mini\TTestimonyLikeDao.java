package cn.slashsoft.clinique.dao.mini;

import cn.slashsoft.clinique.domain.mini.TTestimonyLike;
import cn.slashsoft.clinique.vo.mini.TestimonyHelpThumbupVo;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

@Repository
@Mapper
public interface TTestimonyLikeDao {

    @Insert("INSERT INTO `t_testimony_like`(" +
        "   `id`, " +
        "   `customer_id`, " +
        "   `content_id`, " +
        "   `create_time`, " +
        "   `update_time` " +
        ") " +
        "VALUES (" +
        "   #{id}, " +
        "   #{customerId}, " +
        "   #{contentId}, " +
        "   #{createTime}, " +
        "   #{updateTime} " +
        ")")
    void insertTTestimonyLike(TTestimonyLike tTestimonyLike);

    @Update("UPDATE " +
        "   `t_testimony_like` " +
        "SET " +
        "   `id` = #{id}, " +
        "   `customer_id` = #{customerId}, " +
        "   `content_id` = #{contentId}, " +
        "   `create_time` = #{createTime}, " +
        "   `update_time` = #{updateTime} " +
        "WHERE " +
        "   `id`=#{id} ")
    void updateTTestimonyLike(TTestimonyLike tTestimonyLike);

    @Select("SELECT " +
        "   `id`, " +
        "   `customer_id`, " +
        "   `content_id`, " +
        "   `create_time`, " +
        "   `update_time` " +
        "FROM " +
        "   `t_testimony_like` " +
        "WHERE " +
        "   `id`=#{id} " +
        "LIMIT 1 ")
    TTestimonyLike getTTestimonyLike(Long id);

    @Select("SELECT " +
            "   `id`, " +
            "   `customer_id`, " +
            "   `content_id`, " +
            "   `create_time`, " +
            "   `update_time` " +
            "FROM " +
            "   `t_testimony_like` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "    AND `content_id`=#{contentId} " +
            "LIMIT 1 ")
    TTestimonyLike getTTestimonyLikeByCustomerIdContentId(Long customerId, Long contentId);

    @Select("SELECT w.customer_id, w.nick_name, w.avatar_url, ifnull(r.rank, 0) rank,t.id as content_id, t.content \n" +
            "\tfrom  ( select customer_id, t.fragment_qty, (@ranknum:=@ranknum+1) as rank      \n" +
            "\t\t\tfrom t_testimony_fragment_rank t, (select (@ranknum :=0) ) b    \n" +
            "\t\t\torder by fragment_qty desc ) r\n" +
            "\tleft join wechat w  on r.customer_id = w.customer_id  \n" +
            "\tleft join t_testimony_content t on w.customer_id = t.customer_id \n" +
            "where w.customer_id = #{customerId}\n" +
            "order by t.create_time desc \n" +
            "LIMIT 1 ")
    TestimonyHelpThumbupVo getHelpThumbupInfo(Long customerId);
}
