package com.stormcrm.clinique.tag.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import com.stormcrm.clinique.domain.mini.CustomerLabel;

/**
 * 用户标签
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface TagsDao {    
    @Select(" SELECT * FROM `customer_labels`"
    		//+ " JOIN  "
    		+ " where unionid=#{unionid} limit 10 ")
    List<CustomerLabel> getLabelsByUioninid(String unionid);
    
}
