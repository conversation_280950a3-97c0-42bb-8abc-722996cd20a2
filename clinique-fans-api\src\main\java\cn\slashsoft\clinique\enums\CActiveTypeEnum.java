package cn.slashsoft.clinique.enums;

import lombok.Getter;

/**
 * C粉值类别
 *
 * <AUTHOR> 
 */
public enum CActiveTypeEnum {

    //NOTE_VIDEO_READ          ((short) 1, "阅读笔记/观看视频", 1,1),
    //NOTE_VIDEO_LIKE             ((short) 2, "关注笔记关注视频", 1,1),
   // NOTE_DISCUSS          ((short) 3, "发布评论", 2,1),
    //NOTE_PUBLISH             ((short) 4, "成功发布笔记心得", 50,1),
    //SHARE_READ             ((short) 5, "分享给好友，好友阅读笔记/观看视频", 10,1),
    //SHARE_READ_BIND            ((short) 6, "分享给好友，好友阅读笔记/观看视频且绑定C粉圈", 20,1),
    SHARE_READ             ((short) 5, "分享给好友，好友首次进入c粉圈", 10,1),
    SHARE_READ_BIND            ((short) 6, "分享给好友，好友进入c粉圈且首次绑定C粉圈", 100,1),
    //LOGIN            ((short) 7, "登录", 2,1),
    //LOGIN_3            ((short) 8, "连续三天登录", 5,1),
    //LOGIN_7            ((short) 9, "连续七天登录", 10,1),
    //LOGIN_NO           ((short) 10, "一天未登录", -1,1),
    //LOGIN_NO_3           ((short) 11, "连续七天未登录", -5,1),
    //LOGIN_NO_7           ((short) 12, "连续七天未登录", -10,1),
  // NOTE_READED_LIKED            ((short) 13, "笔记点赞/评论达标", 20,1),

    SUBSCRIBE            ((short) 14, "关注公众号", 100,1),
    LOGIN            ((short) 15, "登录1天", 5,1),
    LOGIN_2            ((short) 16, "连续登录2天", 10,1),
    LOGIN_3            ((short) 17, "连续登录3天", 20,1),
    LOGIN_4            ((short) 18, "连续登录4天", 40,1),
    LOGIN_5            ((short) 19, "连续登录5天", 80,1),
    LOGIN_6            ((short) 20, "连续登录6天", 160,1),
    LOGIN_7            ((short) 21, "连续登录7天", 320,1)
    ;
	
    @Getter
    private final short id;

    @Getter
    private final String name;

    @Getter
    private final int points;
    
    @Getter
    private final int max;

    CActiveTypeEnum(short id, String name, int points, int max) {
        this.id = id;
        this.name = name;
        this.points = points;
        this.max = max;
    }
}
