package cn.slashsoft.clinique.interceptor;

import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.util.BooleanUtil;
import cn.slashsoft.clinique.util.DuplicateUtil;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;

/**
 * 小程序接口拦截器: 未注册返回402
 *
 * <AUTHOR>
 */
@Component
public class RegisterInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        // 读取Session中的customerId
        Long customerId = (Long) request.getAttribute("customerId");

        ArrayList<String> whiteUrlList = new ArrayList<String>();
        whiteUrlList.add("/api/note/get-banner");
        whiteUrlList.add("/api/note/get-data");
        whiteUrlList.add("/api/note/tag/get-data");
        whiteUrlList.add("/api/note/detail");
        whiteUrlList.add("/api/video/get-data");
        whiteUrlList.add("/api/note/discuss-data");
        whiteUrlList.add("/api/set-nologin-log");
        whiteUrlList.add("/api/private-relish/check");
        whiteUrlList.add("/api/kocbeta/check");
        whiteUrlList.add("/api/campaign/2408/set/tracking");

        boolean needLogin = true;
        for(String url : whiteUrlList) {
            if(request.getRequestURI().startsWith(url)) {
                needLogin = false;
                break;
            }
        }

        if (!BooleanUtil.valueOf(customerId) && needLogin) {
            DuplicateUtil.interceptor(request, response, ResultEnum.UNREGISTER);
            return false;
        }

        return true;

    }

}
