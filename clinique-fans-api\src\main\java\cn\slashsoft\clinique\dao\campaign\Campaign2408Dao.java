package cn.slashsoft.clinique.dao.campaign;

import cn.slashsoft.clinique.domain.campaign.lottery2408.CampaignLotteryAward;
import cn.slashsoft.clinique.domain.campaign.lottery2408.CampaignMaxLotteryConfig;
import cn.slashsoft.clinique.domain.campaign.lottery2408.CampaignUserLotteryAwardLogs;
import cn.slashsoft.clinique.domain.campaign.lottery2408.RankingLists;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 520活动
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface Campaign2408Dao {

    /**
     * 获取抽奖结果名单
     * @return 数量
     */
    @Select(
        "SELECT " +
            " * " +
        "FROM " +
            "`campaign_2408_user_lottery_award_logs` " +
        "WHERE " +
            "`customer_id`=#{customerId} " +
            " AND `campaign_flag`=#{campaignFlag} " +
        "LIMIT 1"
    )
    CampaignUserLotteryAwardLogs getUserLotteryInfo(Long customerId, String campaignFlag);


    /**
     * 获取抽奖结果名单
     * @return 数量
     */
    @Select(
        "SELECT " +
            "* " +
        "from " +
            "campaign_2408_lottery_award " +
        "where " +
            "campaign_flag = #{campaignFlag} " +
            "and is_show = 1 " +
            "and residue_inventory > 0 " +
        "order by lottery_probability desc"
    )
    List<CampaignLotteryAward>  getActivityAward(String campaignFlag);


    /**
     * 获取抽奖结果名单
     * @return 数量
     */
    @Update("update campaign_2408_lottery_award " +
            "set residue_inventory = residue_inventory - 1 " +
            "where id = #{id} and residue_inventory > 0")
    Integer lotteryAwardResidueInventoryById(Long id);

    /**
     * 插入抽奖结果
     * @return 数量
     */
    @Insert(
        "INSERT INTO `campaign_2408_user_lottery_award_logs`(" +
            "`customer_id`," +
            "`award_id`," +
            "`award_name`," +
            "`award_img`," +
            "`award_type`," +
            "`coupon_id`," +
            "`coin_num`," +
            "`campaign_flag`," +
            "`content_json`," +
            "`create_time`" +
        ") " +
        "VALUES (" +
            "#{uid}," +
            "#{fId}," +
            "#{awardName}," +
            "#{awardImg}," +
            "#{awardType}," +
            "#{couponId}," +
            "#{coinNum}," +
            "#{campaignFlag}," +
            "#{jsonContent}," +
            "now()" +
    ")")
    Integer setLotteryAward(Long uid,String campaignFlag,Long fId,String awardName,String awardImg,Integer awardType,String couponId,Integer coinNum,String jsonContent);

    /**
     * 获取活动排行榜
     * @return 数量
     */
    @Select(
        "SET @row_num := 0, @customer_id := '';" +
        "SET @rank_num := 0;" +
        "SELECT tablea.id,tablea.customer_id,tablea.createTime,tablea.interact_num,row_number,wechat.avatar_url,wechat.nick_name,(@rank_num:=@rank_num + 1) as rank_num from (" +
        "  SELECT" +
        "      id,customer_id,createTime,interact_num,@row_num := IF(@customer_id = customer_id, @row_num + 1, 1) AS row_number,@customer_id := customer_id" +
        "  FROM" +
        "  (" +
        "      SELECT" +
        "        id,customer_id,createTime,(select count(1) from campaign_2408_note_like_log as nll where nll.note_id = note.note_id and nll.createTime BETWEEN #{startTime} AND #{endTime}) as interact_num" +
        "      FROM campaign_2408_note as note" +
        "      WHERE" +
        "        note.createTime BETWEEN #{startTime} AND #{endTime} AND note.status > 1" +
        "       ORDER BY customer_id desc, interact_num DESC,note.createTime ASC" +
        "   ) AS sorted_notes" +
        " ) as tablea join wechat on tablea.customer_id = wechat.customer_id" +
        " where tablea.row_number = 1 " +
        "ORDER BY tablea.interact_num desc , tablea.createTime ASC " +
        " limit 50 "
    )
    List<RankingLists> getActivityLikeRanking(Date startTime, Date endTime);


    /**
     * 获取活动排行榜
     * @return 数量
     */
    /**
     * 获取活动排行榜
     * @return 数量
     */
    @Select(
        "SET @row_num := 0, @customer_id := '';" +
        "SET @rank_num := 0;" +
        "select * from (" +
            "SELECT tablea.id,tablea.customer_id,tablea.createTime,tablea.interact_num,row_number,wechat.avatar_url,wechat.nick_name,(@rank_num:=@rank_num + 1) as rank_num from (" +
            "  SELECT" +
            "      id,customer_id,createTime,interact_num,@row_num := IF(@customer_id = customer_id, @row_num + 1, 1) AS row_number,@customer_id := customer_id" +
            "  FROM" +
            "  (" +
            "      SELECT" +
            "        id,customer_id,createTime,(select count(1) from campaign_2408_note_like_log as nll where nll.note_id = note.note_id and nll.createTime BETWEEN #{startTime} AND #{endTime}) as interact_num" +
            "      FROM campaign_2408_note as note" +
            "      WHERE" +
            "        note.createTime BETWEEN #{startTime} AND #{endTime} AND note.status > 1" +
            "       ORDER BY customer_id desc, interact_num DESC,note.createTime ASC" +
            "   ) AS sorted_notes" +
            " ) as tablea join wechat on tablea.customer_id = wechat.customer_id" +
            " where tablea.row_number = 1 " +
            "ORDER BY tablea.interact_num desc , tablea.createTime ASC " +
        ") tableb " +
        " where tableb.customer_id = #{customerId} " +
        " limit 1 "
    )
    RankingLists getActivityLikeUserRanking(Long customerId,Date startTime, Date endTime);


    /**
     * 获取活动排行榜
     * @return 数量
     */
    @Select(
        "SET @row_num := 0, @customer_id := '';" +
        "SET @rank_num := 0;" +
        "SELECT tablea.id,tablea.customer_id,tablea.createTime,tablea.interact_num,row_number,wechat.avatar_url,wechat.nick_name,(@rank_num:=@rank_num + 1) as rank_num from (" +
        "  SELECT" +
        "      id,customer_id,createTime,interact_num,@row_num := IF(@customer_id = customer_id, @row_num + 1, 1) AS row_number,@customer_id := customer_id" +
        "  FROM" +
        "  (" +
        "      SELECT" +
        "        id,customer_id,note.createTime,(select count(1) from campaign_2408_note_discuss as nd where nd.note_id = note.note_id and nd.status = 1 and nd.updateTime BETWEEN #{startTime} AND #{endTime}) as interact_num" +
        "      FROM campaign_2408_note as note" +
        "      WHERE" +
        "        note.createTime BETWEEN #{startTime} AND #{endTime} AND note.status > 1" +
        "       ORDER BY customer_id desc, interact_num DESC,note.createTime ASC" +
        "   ) AS sorted_notes" +
        " ) as tablea join wechat on tablea.customer_id = wechat.customer_id" +
        " where tablea.row_number = 1 " +
        " ORDER BY tablea.interact_num desc , tablea.createTime ASC " +
        " limit 50 "
    )
    List<RankingLists> getActivityCommentRanking(Date startTime, Date endTime);

    /**
     * 获取活动排行榜
     * @return 数量
     */
    @Select(
        "SET @row_num := 0, @customer_id := '';" +
        "SET @rank_num := 0;" +
        "select * from (" +
            "SELECT tablea.id,tablea.customer_id,tablea.createTime,tablea.interact_num,row_number,wechat.avatar_url,wechat.nick_name,(@rank_num:=@rank_num + 1) as rank_num from (" +
            "  SELECT" +
            "      id,customer_id,createTime,interact_num,@row_num := IF(@customer_id = customer_id, @row_num + 1, 1) AS row_number,@customer_id := customer_id" +
            "  FROM" +
            "  (" +
            "      SELECT" +
            "        id,customer_id,note.createTime,(select count(1) from campaign_2408_note_discuss as nd where nd.note_id = note.note_id and nd.status = 1 and nd.updateTime BETWEEN #{startTime} AND #{endTime}) as interact_num" +
            "      FROM campaign_2408_note as note" +
            "      WHERE" +
            "        note.createTime BETWEEN #{startTime} AND #{endTime} AND note.status > 1" +
            "       ORDER BY customer_id desc, interact_num DESC,note.createTime ASC" +
            "   ) AS sorted_notes" +
            " ) as tablea join wechat on tablea.customer_id = wechat.customer_id" +
            " where tablea.row_number = 1 " +
            "ORDER BY tablea.interact_num desc , tablea.createTime ASC " +
        ") tableb " +
        " where  tableb.customer_id = #{customerId} " +
        " limit 1 "
    )
    RankingLists getActivityCommentUserRanking(Long customerId,Date startTime, Date endTime);

    /**
     * 插入抽奖结果
     * @return 数量
     */
    @Insert(
        "INSERT INTO `campaign_2408_tracking_logs`(" +
            "`campaign_flag`," +
            "`event`," +
            "`event_id`," +
            "`event_ext`," +
            "`unionid`," +
            "`create_time`" +
        ") " +
        "VALUES (" +
            "#{campaignFlag}," +
            "#{event}," +
            "#{eventId}," +
            "#{eventExt}," +
            "#{unionid}," +
            "now()" +
        ")"
    )
    Integer setTracking(String campaignFlag,String event,String eventId,String eventExt,String unionid);

    /**
     * 获取抽奖结果名单
     * @return 数量
     */
    @Select(
        "SELECT " +
            " * " +
        "FROM " +
        "`campaign_2408_max_lottery_config` " +
        "WHERE " +
        "`lottery_award_id`=#{lotteryAwardId} " +
        " AND `campaign_flag`=#{campaignFlag} " +
        " AND start_time <= now() " +
        " AND end_time >= now() " +
        " LIMIT 1 "
    )
    CampaignMaxLotteryConfig getLotteryMaxConfig(Long lotteryAwardId, String campaignFlag);


    /**
     * 获取抽奖结果名单
     * @return 数量
     */
    @Select(
        "SELECT " +
        " count(1) as num " +
        "FROM " +
        "`campaign_2408_user_lottery_award_logs` " +
        "WHERE " +
        "`award_id`=#{awardId} " +
        " AND `campaign_flag`=#{campaignFlag} " +
        " AND create_time BETWEEN #{startTime} AND #{endTime} " +
        " LIMIT 1 "
    )
    Integer getLotteryNumByTime(Long awardId, String campaignFlag,Date startTime,Date endTime);


}
