package cn.slashsoft.clinique.dao.mini;

import cn.slashsoft.clinique.domain.mini.Account;
import cn.slashsoft.clinique.domain.mini.StoreExchange;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * 核销
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface VerificationDao {

    /**
     * 跟据用户名获取帐户
     * @param username 用户名
     * @return 帐户
     */
    @Select("SELECT " +
            "   `id`," +
            "   `password`," +
            "   `status` " +
            "FROM " +
            "   `account` " +
            "WHERE " +
            "   `username`=#{username}"
    )
    Account getAccountByUsername(String username);

    /**
     * 跟据订单编号查找兑换订单
     * @param order 订单编号
     * @return 订单
     */
    @Select("SELECT " +
            "   `e`.`exchange_order`, " +
            "   `g`.`name`, " +
            "   `g`.`image_url`, " +
            "   `e`.`store_exchange_type_id`, " +
            "   `e`.`store_exchange_logistics_id`, " +
            "   `l`.`name` `store_exchange_logistics_name`, " +
            "   `s`.`name` `store_area_place_name`, " +
            "   `s`.`city` `store_area_name`, " +
            "   `e`.`points`, " +
            "   `e`.`create_time` " +
            "FROM " +
            "   `store_exchange` `e` " +
            "       INNER JOIN " +
            "   `store_gift` `g` " +
            "       ON `e`.`store_gift_id`=`g`.`id` " +
            "       INNER JOIN " +
            "   `store_exchange_logistics` `l` " +
            "       ON `e`.`store_exchange_logistics_id`=`l`.`id` " +
            "       LEFT JOIN " +
            "   `store_exchange_pickup` `p` " +
            "       ON `p`.`store_exchange_id`=`e`.`id` " +
            "       LEFT JOIN " +
            "   `customer_service_counter` `s` " +
            "       ON `p`.`store_area_place_id`=`s`.`id` " +
            "WHERE " +
            "   `e`.`exchange_order`=#{order} " +
            "   AND `e`.`status`=1 " +
            "   AND `e`.`recovery`=0 "
    )
    StoreExchange getStoreExchangeByOrder(String order);

    /**
     * 更新订单状态
     * @param order 订单编号
     * @return 处理结果
     */
    @Update("UPDATE " +
            "   `store_exchange` " +
            "SET " +
            "   `store_exchange_logistics_id`=2 " +
            "WHERE " +
            "   `exchange_order`=#{order} " +
            "   AND `store_exchange_logistics_id`=1"
    )
    int updateStoreExchangeLogisticsByOrder(String order);

}
