package cn.slashsoft.clinique.dao.official;

import cn.slashsoft.clinique.domain.official.PrivateRelish;
import cn.slashsoft.clinique.domain.official.PrivateRelishCustomer;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * 公众号相关的操作
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface PrivateRelishlDao {
    /**
     * 获取用户是否在名单
     *
     * @param official openid 公众号openid
     * @return 数量
     */
    @Select("SELECT * " +
            "FROM " +
            "   `private_relish_customer`  " +
            "WHERE " +
            "   `official_open_id`=#{officialOpenId} " +
            "LIMIT 1")
    PrivateRelishCustomer getByOpenId(String officialOpenId);

    /**
     * 获取用户预约信息
     *
     * @param phoneNumber 手机号
     * @return 数量
     */
    @Select("SELECT * " +
            "FROM " +
            "   `private_relish`  " +
            "WHERE " +
            "   `phone_number`=#{phoneNumber} " +
            "LIMIT 1")
    PrivateRelish getByPhone(String phoneNumber);

    /**
     * 写入预约信息
     *
     * @param PrivateRelish 
     */
    @Insert("INSERT into " +
            "   `private_relish` " +
            " ( "
            + " `store_code`, "
            + " `staff_code`, "
            + " `phone_number` "
            + " ) " +
            "   VALUES "
            + "("
            + " #{storeCode}, "
            + " #{staffCode}, "
            + " #{phoneNumber} "
            + " ) " )
    void addPrivateRelish(PrivateRelish pr);

    /**
     * 获取用户是否在名单
     *
     * @param official openid 公众号openid
     * @return 数量
     */
    @Select("SELECT count(*) " +
            "FROM " +
            "   `t_testimony_koc_user_list`  " +
            "WHERE " +
            "   `customer_id`=#{customer} ")
	Integer getKocByCustomerId(String customer);


}
