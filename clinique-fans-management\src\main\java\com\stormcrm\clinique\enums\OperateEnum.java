package com.stormcrm.clinique.enums;

import lombok.Getter;

public enum OperateEnum {
	EQUALS((short) 1, " = "), GT((short) 2, " > "), LT((short) 3, " < "), GTE((short) 4, " >= "),
	LTE((short) 5, " <= "), BETWEEN((short) 6, " between {start} AND {end} ");

	@Getter
	private final short id;

	@Getter
	private final String sql;

	OperateEnum(short id, String sql) {
		this.id = id;
		this.sql = sql;
	}

	public static String getById(int id) {
		switch (id) {
		case 1:
			return OperateEnum.EQUALS.sql;
		case 2:
			return OperateEnum.GT.sql;
		case 3:
			return OperateEnum.LT.sql;
		case 4:
			return OperateEnum.GTE.sql;
		case 5:
			return OperateEnum.LTE.sql;
		case 6:
			return OperateEnum.BETWEEN.sql;
		}
		return "";
	}
}
