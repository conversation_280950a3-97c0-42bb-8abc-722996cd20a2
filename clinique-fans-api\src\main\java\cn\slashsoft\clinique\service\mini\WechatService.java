package cn.slashsoft.clinique.service.mini;

import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.domain.mini.WechatAuthorize;
import cn.slashsoft.clinique.domain.mini.WechatRegisterStepLog;
import cn.slashsoft.clinique.domain.official.WechatFriend;
import cn.slashsoft.clinique.vo.Result;
import cn.slashsoft.clinique.vo.mini.Authorize;
import cn.slashsoft.clinique.vo.mini.PhoneNumber;
import cn.slashsoft.clinique.vo.mini.Register;

/**
 * 微信公众号、小程序相关的接口
 *
 * <AUTHOR>
 */
public interface WechatService {

    /**
     * 调用小程序服务端接口，用JS Code换取Unionid
     * 登录
     *
     * @param code  用户登录凭证
     * @param scene 景响值
     * @return 结果
     */
    Result code2Unionid(String code, String scene);

    /**
     * 检查更新
     *
     * @param unionid    开放平台唯一编号
     * @param miniOpenid 小程序唯一编号
     * @param scene 景响值
     * @return 是否更新成功
     */
    Wechat checkRegister(String unionid, String miniOpenid, String scene);

    /**
     * 获取是微信信息
     * 首页
     *
     * @param openid 小程序唯一编号
     * @return 微信信息
     */
    Wechat getWechatByOpenid(String openid);

    Wechat getWechatByOfficialOpenid(String openid);

    /**
     * 更新微信基本信息
     *
     * @param authorize 微信小程序传入的授权信息
     * @return 结果: 1,成功; 2，控糖密码不存在或已失效; 3,控糖密码已被绑定; 4,顾客已绑定; 5,异常
     */
    Result setWechat(Authorize authorize);

    /**
     * 授权
     *
     * @param authorize 微信小程序传入的授权信息
     * @return 结果: 1,成功; 2，控糖密码不存在或已失效; 3,控糖密码已被绑定; 4,顾客已绑定; 5,异常
     */
    Result authorize(Authorize authorize);

    /**
     * 解密手机号码
     *
     * @param phoneNumber 手机号码
     * @return 手机号码
     */
    Result getPhoneNumber(PhoneNumber phoneNumber);

    /**
     * 解密手机号码
     *
     * @param phoneNumber 手机号码
     * @return 手机号码
     */
    Result setPhoneNumber(PhoneNumber phoneNumber);

    /**
     * 注册
     *
     * @param register 注册字段
     * @return 结果: 1,成功; 2，控糖密码不存在或已失效; 3,控糖密码已被绑定; 4,顾客已绑定; 5,异常
     */
    Result register(Register register, String miniOpenId);

    /**
     * 注册
     *
     * @param wechatAuthorize 注册字段
     * @return 结果: 1,成功; 2，控糖密码不存在或已失效; 3,控糖密码已被绑定; 4,顾客已绑定; 5,异常
     */
    Result registerWithOutside(WechatAuthorize wechatAuthorize);

    /**
     * 跟据顾客编号查找昵称和头像
     * 个人中心
     * 编辑资料
     *
     * @param customerId 顾客编号
     * @return 昵称和头像
     */
    Wechat getWechatInfoByCustomerId(long customerId);

    /**
     * 跟据开放平台的唯一编号查找
     * 登录
     *
     * @param unionid 开放平台的唯一编号
     * @return 微信信息
     */
    Wechat getWechatByUnionId(String unionid);

    /**
     * 写入公众号唯一编号
     *
     * @param unionid              开放平台编号
     * @param wechatOfficialOpenid 公众号唯一编号
     */
    void setOfficialOpenid(String unionid, String wechatOfficialOpenid);

    /**
     * 写小程序首页的来源
     *
     * @param source 来源
     * @param openid 微信小程序唯一编号
     */
    void setOriginate(String source, String openid);

    /**
     * 发放场景值福利
     *
     * @param scene  场景值
     * @param openid 微信小程序唯一编号
     */
    void setSceneReward(String scene, String openid);

    void setNoLoginLog(String scene, String openid);

    /**
     * 写入日志
     *
     * @param wechatRegisterStepLog 日志
     */
    void setRegisterStepLog(WechatRegisterStepLog wechatRegisterStepLog);

    WechatFriend getWechatFriend(String unionid);

    WechatFriend getWechatFriendByOpenid(String openid);

    void addWechatFriend(WechatFriend wf);

    void updateWechatFriend(WechatFriend wf);

    void saveEvent(String string);

    /**
     * 更新小程序文案配置
     */
    void updateConfig();

    String getMiniOpenidByJsCode(String code);

}
