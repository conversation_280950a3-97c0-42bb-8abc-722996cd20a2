package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.Video;
import com.stormcrm.clinique.util.StringUtil;
import org.apache.ibatis.jdbc.SQL;

/**
 *
 * <AUTHOR> zhang
 */
public class VideoProvider {

    /**
     * 查询所有礼品-分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param type        状态
     * @return 礼品列表
     */
    public String getPage(int pageIndex, int pageSize, String generalSearch, Short type) {

        return new SQL() {{

            SELECT("`t`.* ");
            FROM("`video` `t` ");
            WHERE("1=1");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`t`.`brandName` LIKE '%${generalSearch}%'");
            }
            if (null != type && 0 <= type) {
                AND();
                WHERE("`t`.`type`=#{type}");
            }
            ORDER_BY("`t`.`id` DESC LIMIT #{pageIndex},#{pageSize}");

        }}.toString();

    }

    /**
     * 查询所有礼品带分页的记录数
     *
     * @param generalSearch 搜索
     * @param type        状态
     * @return 记录数
     */
    public String getPageCount(String generalSearch, Short type) {

        return new SQL() {{

            SELECT("COUNT(*)");
            FROM("`video` `t` ");
            WHERE("1=1");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`t`.`brandName` LIKE '%${generalSearch}%'");
            }
            if (null != type && 0 <= type) {
                AND();
                WHERE("`t`.`type`=#{type}");
            }

        }}.toString();

    }


    /**
     * 查询所有礼品-分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 礼品列表
     */
    public String getTagPage(int pageIndex, int pageSize, String generalSearch, Short status) {

        return new SQL() {{

            SELECT("`t`.`*` ");
            FROM("`video_tag` `t` ");
            WHERE("1=1");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`t`.`title` LIKE '%${generalSearch}%'");
            }
            if (null != status && 0 <= status) {
                AND();
                WHERE("`t`.`status`=#{status}");
            }
            ORDER_BY("`t`.`id` DESC LIMIT #{pageIndex},#{pageSize}");

        }}.toString();

    }

    /**
     * 查询所有礼品带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    public String getTagPageCount(String generalSearch, Short status) {

        return new SQL() {{

            SELECT("COUNT(*)");
            FROM("`video_tag` `t` ");
            WHERE("1=1");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`t`.`title` LIKE '%${generalSearch}%'");
            }
            if (null != status && 0 <= status) {
                AND();
                WHERE("`t`.`status`=#{status}");
            }

        }}.toString();

    }

    /**
     * 更新
     *
     * @param video 信息
     */
    public String update(Video video) {

        return new SQL() {{

            UPDATE("`video`");

            SET("`title`=#{title}");
            SET("`brand_name`=#{brandName}");
            if (!StringUtil.isNullOrEmpty(video.getBrandLogo())) {
                SET("`brand_logo`=#{brandLogo}");
            }
            if (!StringUtil.isNullOrEmpty(video.getCoverImage())) {
                SET("`cover_image`=#{coverImage}");
            }
            if (!StringUtil.isNullOrEmpty(video.getFileUrl())) {
                SET("`file_url`=#{fileUrl}");
            }
            SET("`type`=#{type}");
            SET("`update_time`=now()");
            SET("`status`=#{status}");

            WHERE("`id`=#{id}");

        }}.toString();

    }
}
