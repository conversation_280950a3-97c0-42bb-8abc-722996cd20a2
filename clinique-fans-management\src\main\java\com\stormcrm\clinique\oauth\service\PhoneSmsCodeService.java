package com.stormcrm.clinique.oauth.service;


import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;

import com.stormcrm.clinique.dao.oauth.OauthPhoneCodeDao;
import com.stormcrm.clinique.dao.oauth.OauthSmsDao;
import com.stormcrm.clinique.dao.oauth.OauthUserDao;
import com.stormcrm.clinique.oauth.domain.OauthPhoneCode;
import com.stormcrm.clinique.oauth.domain.OauthSms;
import com.stormcrm.clinique.oauth.phone.PhoneCodeSendException;
import com.stormcrm.clinique.util.HttpUtil;
import com.stormcrm.clinique.util.RandomUtil;
import com.stormcrm.clinique.util.StringUtil;
import com.stormcrm.clinique.vo.Result;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
//@Service
@Slf4j
public class PhoneSmsCodeService {

    private static final String SMS_URL = "http://43.240.124.37:3308/sms/mt?command=MT_REQUEST&spid=CL00J1&sppassword=61423e&da=PHONE_NUMBER&dc=8&sm=MESSAGE";

    private static final Long LOCK_DURATION = 30L;

    public static final Long CODE_EXPIRED = 10 * 60L;

    private final OauthSmsDao oauthSmsDao;

    private final OauthUserDao oauthUserDao;

    private final OauthPhoneCodeDao oauthPhoneCodeDao;

    private final StringRedisTemplate stringRedisTemplate;

    public PhoneSmsCodeService(OauthSmsDao oauthSmsDao, OauthUserDao oauthUserDao, OauthPhoneCodeDao oauthPhoneCodeDao, StringRedisTemplate stringRedisTemplate) {
        this.oauthSmsDao = oauthSmsDao;
        this.oauthUserDao = oauthUserDao;
        this.oauthPhoneCodeDao = oauthPhoneCodeDao;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * controller 调用的短信验证码发送方法。
     *
     * @param phoneNumber 管理员手机号码
     * @return 返回完成标记
     */
    public String sendPhoneSmsCode(String phoneNumber) {
        // 增加缓存锁 30秒 ,防重复发送
        String cacheLockKey = "pl:" + getCurrentSessionId() + ":" + Base64.getEncoder().encodeToString(phoneNumber.getBytes());
        log.debug(" cache key is :::: {}", cacheLockKey);
        //  没有处理 redis 不能用的情况
        boolean lock = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(cacheLockKey, "lock", 5, TimeUnit.SECONDS));
        //   如果获取到锁，执行发送，否则直接返回
        if (lock) {
            // 锁时间
            stringRedisTemplate.expire(cacheLockKey, LOCK_DURATION, TimeUnit.SECONDS);
            sendingSmsCode(phoneNumber);
            return "done";
        } else {
            return "done.";
        }

    }

    public void sendingSmsCode(String phoneNumber) {
        log.debug(" sendingSmsCode  :::: {}", phoneNumber);
        //  check phone number in db
        boolean phoneExist = this.oauthUserDao.checkPhoneExists(phoneNumber);
        if (phoneExist) {
            String code = getPhoneSmsCode(phoneNumber);
            sendSmsCode(phoneNumber, code);
        }
    }

    /**
     * 若缓存失效，从数据库查询已存在的验证码，通过短信发送
     *
     * @param phoneNumber 前端传来的手机号
     * @return 验证码
     */
    public String getPhoneSmsCode(String phoneNumber) {

        // 查询尚未到期的验证码，如果查询到，直接返回，查询不到，创建一个之后返回
        OauthPhoneCode code = this.oauthPhoneCodeDao.getPhoneValidCode(phoneNumber, CODE_EXPIRED);
        if (code != null) {
            return code.getCode();
        } else {
            code = new OauthPhoneCode();

            String newCode = createRandomCode();
            code.setSessionId(this.getCurrentSessionId());
            code.setPhoneNumber(phoneNumber);
            code.setCode(newCode);
            code.setTimes((short) 3);
            this.oauthPhoneCodeDao.savePhoneCode(code);

            return newCode;
        }
    }

    /**
     * 调用短信接口
     */
    private void sendSmsCode(String number, String code) {
        log.debug("sending admin {} login code: {}", number, code);
        //  测试的时候可以关掉这个
        sendVerificationCode(number, code);
    }

    /**
     * 创建一个随机的 4 位 验证码。
     */
    private String createRandomCode() {
        return RandomUtil.getVerifyCode();
    }


    /**
     * 发送短信验证码
     *
     * @param phoneNumber 手机号码
     */

    public Result sendVerificationCode(String phoneNumber, String verificationCode) {

        try {
            // 短信内容
            String message = "您的验证码是：" + verificationCode + "，10分钟有效，如非本人操作请忽略";
            // 发送内容
            String sendUrl = SMS_URL.replace("PHONE_NUMBER", phoneNumber).replace("MESSAGE", encodeHexStr(message.getBytes(StandardCharsets.UTF_16BE)));
            log.debug(" sms sending url is : {}", sendUrl);
            //  发送
            String response = HttpUtil.get(sendUrl);
            // 返回值
            String code;
            // 返回异常
            if (StringUtil.isNullOrEmpty(response) || 3 > response.length()) {
                code = "999";
            } else {
                code = StringUtil.right(response, 3);
            }

            // 保存日志
            OauthSms sms = new OauthSms();
            sms.setPhoneNumber(phoneNumber);
            sms.setMessage(message);
            sms.setParam(sendUrl);
            sms.setResponseCode(code);
            oauthSmsDao.insertSms(sms);

            if (!"000".equals(code)) {
                throw new PhoneCodeSendException("发送失败");
            }

            return new Result(0, verificationCode);

        } catch (Exception e) {
            return new Result(1, e.getMessage());
        }

    }


    /**
     * 字节转Hex
     *
     * @param src 字节码
     * @return Hex字符串
     */
    private String encodeHexStr(byte[] src) {
        StringBuilder sb = new StringBuilder();
        for (byte b : src) {
            String strHex = Integer.toHexString(b & 0xFF);
            sb.append((strHex.length() == 1) ? "0" + strHex : strHex);
        }
        return sb.toString().trim();
    }

    private String getCurrentSessionId() {
        return RequestContextHolder.currentRequestAttributes().getSessionId();
    }

}
