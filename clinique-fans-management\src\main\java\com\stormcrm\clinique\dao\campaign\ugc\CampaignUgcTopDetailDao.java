package com.stormcrm.clinique.dao.campaign.ugc;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgcTopDetail;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignUgcTopDetailDao {

    /**
     * 查询所有活动
     *
     * @param topId TOP编号
     * @return 活动列表
     */
    @Select("SELECT " +
            "   `d`.`id`," +
            "   `t`.`customer_id`," +
            "   `d`.`title`," +
            "   `w`.`nick_name`, " +
            "   `w`.`avatar_url`," +
            "   `d`.`detail_id`, " +
            "   `t`.`upload_time`, " +
            "   `t`.`examine_time`, " +
            "   `d`.`sort`, " +
            "   `t`.`status` " +
            "FROM " +
            "   `koc_top_detail` `d` " +
            "       INNER JOIN " +
            "   `koc_detail` `t` " +
            "       ON `d`.`detail_id`=`t`.`id` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `t`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "   `d`.`top_id`=#{topId} " +
            "   AND `d`.`status`=1 " +
            "ORDER BY " +
            "   `d`.`sort`,`d`.`id`")
    List<CampaignUgcTopDetail> getAll(long topId);

    /**
     * 查询
     *
     * @param id 自动编号
     * @return 信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `title`," +
            "   `detail_id`," +
            "   `sort` " +
            "FROM " +
            "   `koc_top_detail` " +
            "WHERE " +
            "   `id`=#{id} " +
            "LIMIT 1")
    CampaignUgcTopDetail getById(long id);

    /**
     * 保存
     *
     * @param campaignUgcTopDetail 信息
     */
    @Insert("INSERT INTO `koc_top_detail`(" +
            "   `top_id`," +
            "   `detail_id`, " +
            "   `title`," +
            "   `sort` " +
            ") " +
            "VALUES (" +
            "   #{topId}, " +
            "   #{detailId}, " +
            "   #{title}," +
            "   #{sort} " +
            ")")
    void save(CampaignUgcTopDetail campaignUgcTopDetail);

    /**
     * 更新
     *
     * @param campaignUgcTopDetail 信息
     */
    @Update("UPDATE " +
            "   `koc_top_detail` " +
            "SET " +
            "   `detail_id`=#{detailId}, " +
            "   `title`=#{title}," +
            "   `sort`=#{sort} " +
            "WHERE " +
            "   `id`=#{id}")
    void update(CampaignUgcTopDetail campaignUgcTopDetail);

    /**
     * 删除
     *
     * @param id 编号
     * @return 影响的行
     */
    @Delete("UPDATE " +
            "   `koc_top_detail` " +
            "SET " +
            "   `status`=0 " +
            "WHERE " +
            "   `id`=#{id}")
    int delete(long id);

}
