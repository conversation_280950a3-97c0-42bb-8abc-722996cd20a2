package com.stormcrm.clinique.service.campaign.ugc.impl;

import com.stormcrm.clinique.dao.CampaignMsTalentsTopDetailDao;
import com.stormcrm.clinique.dao.campaign.ugc.CampaignUgcTopDetailDao;
import com.stormcrm.clinique.domain.CampaignMsTalentsTopDetail;
import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgcTopDetail;
import com.stormcrm.clinique.service.CampaignMsTalentsTopDetailService;
import com.stormcrm.clinique.service.campaign.ugc.CampaignUgcTopDetailService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.vo.Result;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Service
public class CampaignUgcTopDetailServiceImpl implements CampaignUgcTopDetailService {

    private final CampaignUgcTopDetailDao campaignUgcTopDetailDao;

    public CampaignUgcTopDetailServiceImpl(CampaignUgcTopDetailDao campaignUgcTopDetailDao) {
        this.campaignUgcTopDetailDao = campaignUgcTopDetailDao;
    }

    /**
     * 查询所有活动
     *
     * @param topId TOP编号
     * @return 活动列表
     */
    @Override
    public List<CampaignUgcTopDetail> getAll(long topId) {
        return campaignUgcTopDetailDao.getAll(topId);
    }

    /**
     * 查询信息
     *
     * @param id 自动编号
     * @return 信息
     */
    @Override
    public CampaignUgcTopDetail getById(long id) {
        return campaignUgcTopDetailDao.getById(id);
    }

    /**
     * 保存
     *
     * @param campaignUgcTopDetail 信息
     * @return 影响的行数
     */
    @Override
    public Result save(CampaignUgcTopDetail campaignUgcTopDetail) {
        campaignUgcTopDetailDao.save(campaignUgcTopDetail);
        return ResultUtil.success("增加成功!");
    }

    /**
     * 更新
     *
     * @param campaignUgcTopDetail 信息
     * @return 影响的行数
     */
    @Override
    public Result update(CampaignUgcTopDetail campaignUgcTopDetail) {
        campaignUgcTopDetailDao.update(campaignUgcTopDetail);
        return ResultUtil.success("增加成功!");
    }

    /**
     * 更新
     *
     * @param id 编号
     * @return 影响的行数
     */
    @Override
    public int delete(long id) {
        return campaignUgcTopDetailDao.delete(id);
    }
}
