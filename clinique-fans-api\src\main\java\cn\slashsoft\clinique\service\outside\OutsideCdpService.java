package cn.slashsoft.clinique.service.outside;

import cn.slashsoft.clinique.domain.cdp.Member;

/**
 * CDP接口
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
public interface OutsideCdpService {

    /**
     * 获取CDP用户信息
     *
     * @param miniOpenid 小程序唯一编号
     * @param unionid    开放平台唯一编号
     * @return 用户信息
     * @since 2023-12-04
     */
    Member getCdpCustomerByUnionid(String unionid, String miniOpenid);

}
