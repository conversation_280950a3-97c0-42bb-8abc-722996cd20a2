package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.domain.CampaignTalents;
import com.stormcrm.clinique.domain.CampaignTalentsImage;
import com.stormcrm.clinique.domain.PointTransaction;
import com.stormcrm.clinique.enums.PointForeignEnum;
import com.stormcrm.clinique.enums.PointTypeEnum;
import com.stormcrm.clinique.service.CampaignTalentsService;
import com.stormcrm.clinique.service.SmotService;
import com.stormcrm.clinique.util.DateUtil;
import com.stormcrm.clinique.util.LongUtil;
import com.stormcrm.clinique.dao.CampaignTalentsDao;
import com.stormcrm.clinique.dao.PointDao;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 新品体验官活动
 *
 * <AUTHOR>
 */
@Service
public class CampaignTalentsServiceImpl implements CampaignTalentsService {

    @Value("${wechat.subscribe-template.examine-result}")
    private String templateExamineResult;

    private final CampaignTalentsDao campaignTalentsDao;
    private final PointDao pointDao;
    private final SmotService smotService;

    public CampaignTalentsServiceImpl(CampaignTalentsDao campaignTalentsDao, PointDao pointDao, SmotService smotService) {
        this.campaignTalentsDao = campaignTalentsDao;
        this.pointDao = pointDao;
        this.smotService = smotService;
    }

    /**
     * 查询所有活动带分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索
     * @param status        状态
     * @return 活动列表分页
     */
    @Override
    public List<CampaignTalents> getPage(int page, int perpage, String generalSearch, Short status) {
        return campaignTalentsDao.getPage((page - 1) * perpage, perpage, generalSearch, status);
    }

    /**
     * 查询所有活动带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    @Override
    public int getPageCount(String generalSearch, Short status) {
        return campaignTalentsDao.getPageCount(generalSearch, status);
    }

    /**
     * 获取美白达人官信息
     *
     * @param id 编号
     * @return 美白达人官信息
     */
    @Override
    public CampaignTalents getById(long id) {
        return campaignTalentsDao.getById(id);
    }

    /**
     * 获取图片
     *
     * @param id 顾客活动信息编号
     * @return 图片地址
     */
    @Override
    public List<CampaignTalentsImage> getImageById(long id) {
        return campaignTalentsDao.getImageById(id);
    }

    /**
     * 审核通过
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Override
    public int accept(long id) {

        // 读取用户及活动信息
        CampaignTalents campaignTalents = campaignTalentsDao.getById(id);
        if(null == campaignTalents){
            return 0;
        }

        // 只在审核中和审核未通过才可以审核通过
        if(0 == campaignTalentsDao.accept(id)){
            return 0;
        }

        // 发放积分
        Date now = new Date();
        PointTransaction pointTransaction = new PointTransaction();
        pointTransaction.setCustomerId(campaignTalents.getCustomerId());
        pointTransaction.setPointTypeId(PointTypeEnum.EBCI_TALENTS.getId());
        pointTransaction.setPoints(PointTypeEnum.EBCI_TALENTS.getPoints());
        pointTransaction.setRemainingPoints(PointTypeEnum.EBCI_TALENTS.getPoints());
        pointTransaction.setStartTime(now);
        pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
        pointTransaction.setForeignId(PointForeignEnum.EBCI_TALENTS.getId());
        pointTransaction.setForeignMasterId(id);
        pointTransaction.setForeignDetailId(0L);
        pointDao.insertPointTransaction(pointTransaction);

        String data = "{" +
                "\"thing2\":{\"value\":\"审核通过\"}," +
                "\"thing8\":{\"value\":\"200积分已到账，更多好礼，请锁定C粉圈。\"}" +
                "}";

        // 发送订阅消息
        smotService.send((short) 1, campaignTalents.getWechatMiniOpenid(), templateExamineResult, "ebci/mine/mine?source=SuccessUploadMot", data);

        return 1;
    }

    /**
     * 批量同意
     *
     * @param ids 自动编号
     * @return 影响的行数
     */
    @Override
    public int acceptBatch(String ids) {

        String[] idArray = ids.split(",");

        List<Long> idList = new ArrayList<>();
        for (String idString : idArray) {
            long id = LongUtil.parseInt(idString);
            if(0 < id){
                idList.add(id);
            }
        }

        if(0 == idList.size()){
            return 0;
        }

        List<CampaignTalents> campaignTalentsList = campaignTalentsDao.getByIds(idList);
        if (null == campaignTalentsList || 0 == campaignTalentsList.size()){
            return 0;
        }

        for (CampaignTalents campaignTalents : campaignTalentsList) {

            // 只在审核中和审核未通过才可以审核通过
            if(0 == campaignTalentsDao.accept(campaignTalents.getId())){
                continue;
            }

            // 发放积分
            Date now = new Date();
            PointTransaction pointTransaction = new PointTransaction();
            pointTransaction.setCustomerId(campaignTalents.getCustomerId());
            pointTransaction.setPointTypeId(PointTypeEnum.EBCI_TALENTS.getId());
            pointTransaction.setPoints(PointTypeEnum.EBCI_TALENTS.getPoints());
            pointTransaction.setRemainingPoints(PointTypeEnum.EBCI_TALENTS.getPoints());
            pointTransaction.setStartTime(now);
            pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
            pointTransaction.setForeignId(PointForeignEnum.EBCI_TALENTS.getId());
            pointTransaction.setForeignMasterId(campaignTalents.getId());
            pointTransaction.setForeignDetailId(0L);
            pointDao.insertPointTransaction(pointTransaction);

            String data = "{" +
                    "\"thing2\":{\"value\":\"审核通过\"}," +
                    "\"thing8\":{\"value\":\"200积分已到账，更多好礼，请锁定C粉圈。\"}" +
                    "}";

            // 发送订阅消息
            smotService.send((short) 1, campaignTalents.getWechatMiniOpenid(), templateExamineResult, "ebci/mine/mine?source=SuccessUploadMot", data);

        }

        return 1;
    }

    /**
     * 审核失败
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Override
    public int reject(long id) {

        // 读取用户及活动信息
        CampaignTalents campaignTalents = campaignTalentsDao.getById(id);
        if(null == campaignTalents){
            return 0;
        }

        // 改变状态
        if(0 == campaignTalentsDao.reject(id)){
            return 0;
        }

        // 重置图片
        campaignTalentsDao.setImageStatus(id);

        String data = "{" +
                "\"thing2\":{\"value\":\"审核失败\"}," +
                "\"thing8\":{\"value\":\"请返回活动主页，重新上传您的美白新得。\"}" +
                "}";

        // 发送订阅消息
        smotService.send((short) 1, campaignTalents.getWechatMiniOpenid(), templateExamineResult, "ebci/mine/mine?source=FailedUploadMot", data);

        return 1;
    }

    /**
     * 批量拒绝
     *
     * @param ids 自动编号
     * @return 影响的行数
     */
    @Override
    public int rejectBatch(String ids) {

        String[] idArray = ids.split(",");

        List<Long> idList = new ArrayList<>();
        for (String idString : idArray) {
            long id = LongUtil.parseInt(idString);
            if(0 < id){
                idList.add(id);
            }
        }

        if(0 == idList.size()){
            return 0;
        }

        List<CampaignTalents> campaignTalentsList = campaignTalentsDao.getByIds(idList);
        if (null == campaignTalentsList || 0 == campaignTalentsList.size()){
            return 0;
        }

        for (CampaignTalents campaignTalents : campaignTalentsList) {

            // 改变状态
            if(0 == campaignTalentsDao.reject(campaignTalents.getId())){
                continue;
            }

            // 重置图片
            campaignTalentsDao.setImageStatus(campaignTalents.getId());

            String data = "{" +
                    "\"thing2\":{\"value\":\"审核失败\"}," +
                    "\"thing8\":{\"value\":\"请返回活动主页，重新上传您的美白心得。\"}" +
                    "}";

            // 发送订阅消息
            smotService.send((short) 1, campaignTalents.getWechatMiniOpenid(), templateExamineResult, "ebci/mine/mine?source=FailedUploadMot", data);

        }

        return 1;
    }
}
