package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignViewLog;
import cn.slashsoft.clinique.domain.campaign.CampaignWechatWorkBinding;
import cn.slashsoft.clinique.domain.campaign.CampaignWechatWorkBindingCounter;
import cn.slashsoft.clinique.domain.campaign.CampaignWechatWorkBindingPickup;
import cn.slashsoft.clinique.service.campaign.CampaignWechatWorkBindingService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * 企业微信绑定活动
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/official")
public class CampaignWechatWorkBindingController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final CampaignWechatWorkBindingService campaignWechatWorkBindingService;
    private final StringRedisTemplate stringRedisTemplate;

    public CampaignWechatWorkBindingController(CampaignWechatWorkBindingService campaignWechatWorkBindingService, StringRedisTemplate stringRedisTemplate) {
        this.campaignWechatWorkBindingService = campaignWechatWorkBindingService;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 配置活动信息
     *
     * @param verification 验证
     * @return 处理结果
     * https://clinique.stormcrm.com/api/campaign-config/g520/20-15-04-32-29/ON
     */
    @GetMapping("/campaign-wechat-work-binding-config/{verification}/{code}")
    @ResponseBody
    public String setConfig(
            @PathVariable("verification") String verification,
            @PathVariable("code") String code
    ) {
        if (DateUtil.parseVerification(new Date()).equals(verification)) {
            stringRedisTemplate.opsForValue().set("CampaignWechatWorkBindingVerifyCode", code);
            return "success";
        }
        return "fail";
    }

    /**
     * 活动主页
     *
     * @param source 来源
     * @param model  模型
     * @return 模版
     */
    @GetMapping("/campaign-wechat-work-binding/{source}.html")
    public String index(
            @PathVariable("source") String source,
            Model model
    ) {
        String officialOpenid = (String) request.getAttribute("miniOpenid");
        String nickName = (String) request.getAttribute("nickName");
        String avatarUrl = (String) request.getAttribute("avatarUrl");

        CampaignWechatWorkBinding campaignWechatWorkBinding = campaignWechatWorkBindingService.getCampaignWechatWorkBinding(officialOpenid, nickName, avatarUrl);

        List<CampaignWechatWorkBindingCounter> campaignWechatWorkBindingCounterList = campaignWechatWorkBindingService.getCampaignWechatWorkBindingCounter();

        model.addAttribute("nickName", nickName);
        model.addAttribute("avatarUrl", avatarUrl);
        model.addAttribute("campaignWechatWorkBinding", campaignWechatWorkBinding);
        model.addAttribute("campaignWechatWorkBindingCounterList", campaignWechatWorkBindingCounterList);

        List<CampaignWechatWorkBinding> campaignWechatWorkBindingList = campaignWechatWorkBindingService.getCampaignWechatWorkBindingList();
        model.addAttribute("campaignWechatWorkBindingList", campaignWechatWorkBindingList);

        CampaignViewLog campaignViewLog = new CampaignViewLog();
        campaignViewLog.setOfficialOpenid(officialOpenid);
        campaignViewLog.setSource(source);
        campaignViewLog.setPage("index");
        campaignWechatWorkBindingService.setCampaignViewLog(campaignViewLog);
       
            model.addAttribute("staticDomain", staticDomain);
        return "w/fans/official/wechat-work-binding/index";
    }

    /**
     * 抽奖
     *
     * @return 抽奖结果
     */
    @GetMapping("/campaign-wechat-work-binding/draw.html")
    @ResponseBody
    public String draw() {

        String unionid = (String) request.getAttribute("unionid");
        String officialOpenid = (String) request.getAttribute("miniOpenid");

        Result result = campaignWechatWorkBindingService.draw(unionid, officialOpenid);
        if (0 != result.getCode()) {
            return "{\"code\":1,\"message\":\"" + result.getMessage() + "\"}";
        }

        return "{\"code\":0,\"data\":" + result.getMessage() + "}";
    }

    /**
     * 选择领取柜台
     *
     * @return 结果
     */
    @GetMapping("/campaign-wechat-work-binding/counter.html")
    @ResponseBody
    public String counter(
            @RequestParam("name") String name,
            @RequestParam("city") String city,
            @RequestParam("counter") String counter
    ) {
        String officialOpenid = (String) request.getAttribute("miniOpenid");

        Result result = campaignWechatWorkBindingService.counter(officialOpenid, name, city, counter);
        if (0 != result.getCode()) {
            return "{\"code\":1,\"message\":\"" + result.getMessage() + "\"}";
        }

        return "{\"code\":0}";
    }

    /**
     * 核销
     *
     * @return 结果
     */
    @GetMapping("/campaign-wechat-work-binding/pickup.html")
    @ResponseBody
    public String pickup(@RequestParam("code") String code) {

        String verifyCode = stringRedisTemplate.opsForValue().get("CampaignWechatWorkBindingVerifyCode");

        if(StringUtil.isNullOrEmpty(code) || !code.equals(verifyCode)){
            return "{\"code\":1}";
        }

        String officialOpenid = (String) request.getAttribute("miniOpenid");

        campaignWechatWorkBindingService.pickup(officialOpenid);

        return "{\"code\":0,\"data\":{\"receiveDate\":\""+DateUtil.parseDayString(new Date())+"\"}}";

    }

    /**
     * 填写收货地址
     * @param name           姓名
     * @param phoneNumber    手机号码
     * @param province       省份
     * @param city           城市
     * @param district       区域
     * @param address        具体地址
     * @return 结果
     */
    @GetMapping("/campaign-wechat-work-binding/address.html")
    @ResponseBody
    public String address(
            @RequestParam("name") String name,
            @RequestParam("phoneNumber") String phoneNumber,
            @RequestParam("province") String province,
            @RequestParam("city") String city,
            @RequestParam("district") String district,
            @RequestParam("address") String address
    ) {
        String officialOpenid = (String) request.getAttribute("miniOpenid");

        Result result = campaignWechatWorkBindingService.address(officialOpenid, name, phoneNumber, province, city, district, address);
        if (0 != result.getCode()) {
            return "{\"code\":1,\"message\":\"" + result.getMessage() + "\"}";
        }

        return "{\"code\":0}";
    }

}
