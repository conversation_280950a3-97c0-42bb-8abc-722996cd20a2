package cn.slashsoft.clinique.domain.campaign;

import lombok.Data;

import java.util.Date;

/**
 * 520 活动
 * <AUTHOR>
 */
@Data
public class CampaignG520 {

    private Long id;
    private Long customerId;
    private String avatarUrl;
    private String nickName;
    private String phoneNumber;
    private Boolean follow;
    private String followSource;
    private Date followFirstTime;
    private Date followLastTime;
    private Date followCancelTime;
    private Boolean bind;
    private Date bindTime;
    private Boolean locationAuthorize;
    private String source;
    private Boolean status;
    private Date createTime;

}
