package cn.slashsoft.clinique.service.mini;

import cn.slashsoft.clinique.domain.mini.TTestimonyKocUserList;

/**
* <p>
    * 盲盒申领
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
public interface TTestimonyKocUserListService {
    void insertTTestimonyKocUserList(TTestimonyKocUserList tTestimonyKocUserList);
    void updateTTestimonyKocUserList(TTestimonyKocUserList tTestimonyKocUserList);
    TTestimonyKocUserList getTTestimonyKocUserList(Long id);
    boolean getKocUser(Long customerId);
}
