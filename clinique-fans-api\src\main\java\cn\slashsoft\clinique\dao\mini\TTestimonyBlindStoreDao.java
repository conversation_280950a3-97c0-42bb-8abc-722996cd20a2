package cn.slashsoft.clinique.dao.mini;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.mini.TTestimonyBlindStore;

@Repository
@Mapper
public interface TTestimonyBlindStoreDao {

    @Insert("INSERT INTO `t_testimony_blind_store`(" +
        "   `id`, " +
        "   `store_id`, " +
        "   `create_time`, " +
        "   `update_time`, " +
        "   `product_id`, " +
        "   `qty` " +
        ") " +
        "VALUES (" +
        "   #{id}, " +
        "   #{storeId}, " +
        "   #{createTime}, " +
        "   #{updateTime}, " +
        "   #{productId}, " +
        "   #{qty} " +
            ")")
    void insertTTestimonyBlindStore(TTestimonyBlindStore tTestimonyBlindStore);

    @Update("UPDATE " +
        "   `t_testimony_blind_store` " +
        "SET " +
        "   `id` = #{id}, " +
        "   `store_id` = #{storeId}, " +
        "   `create_time` = #{createTime}, " +
        "   `update_time` = #{updateTime}, " +
        "   `product_id` = #{productId}, " +
        "   `qty` = #{qty} " +
        "WHERE " +
        "   `id`=#{id} ")
    void updateTTestimonyBlindStore(TTestimonyBlindStore tTestimonyBlindStore);

    @Select("SELECT " +
        "   `id`, " +
        "   `store_id`, " +
        "   `create_time`, " +
        "   `update_time`, " +
        "   `product_id`, " +
        "   `qty` " +
        "FROM " +
        "   `t_testimony_blind_store` " +
        "WHERE " +
        "   `id`=#{id} " +
        "LIMIT 1 ")
    TTestimonyBlindStore getTTestimonyBlindStore(Long id);

    @Update("UPDATE " +
            "   `t_testimony_blind_store` " +
            "SET " +
            "   `update_time` = now(), " +
            "   `qty` = `qty` - 1 " +
            "WHERE " +
            "   `store_id` = #{storeId} and " +
            "   `product_id` = #{productId} " )
    void subtractTTestimonyBlindStore(Integer storeId, Integer productId);

    @Select("SELECT qty " +
            "FROM " +
            "   `t_testimony_blind_store` " +
            "WHERE " +
            "   `store_id` = #{storeId} and " +
            "   `product_id` = #{productId} " )
    Integer getTTestimonyBlindStoreAmount(Integer storeId, Integer productId);
}
