package cn.slashsoft.clinique.service.mini;

import cn.slashsoft.clinique.domain.mini.PointTransaction;

import java.util.List;

/**
 * 积分相关的服务
 *
 * <AUTHOR>
 */
public interface PointService {

    /**
     * 读取历史积分合计
     * @param customerId 顾客编号
     * @return 历史积分合计
     */
    int getPointTotal(long customerId);

    /**
     * 读取有效积分合计
     *
     * @param customerId 顾客编号
     * @return 有效积分合计
     */
    int getRemainingPointTotal(long customerId);

    /**
     * 读取本月即将过期的积分合计
     * @param customerId 顾客编号
     * @return 本月即将过期的积分合计
     */
    int getExpiredPointTotal(long customerId);

    /**
     * 读取所有有效积分
     *
     * @param customerId 顾客编号
     * @return 积分
     */
    List<PointTransaction> getPointTransaction(long customerId);

    /**
     * 通过API写入新的积分
     * @param customerId 顾客编号
     * @param pointTypeId 积分类型
     * @param points 积分
     */
    void addPointTransactionForApi(long customerId, short pointTypeId, int points);

    /**
     * 通过API写入新的积分
     * @param unionid 开放平台唯一编号
     * @param pointTypeId 积分类型
     * @param points 积分
     */
    void addPointTransactionTempForApi(String unionid, short pointTypeId, int points);

}
