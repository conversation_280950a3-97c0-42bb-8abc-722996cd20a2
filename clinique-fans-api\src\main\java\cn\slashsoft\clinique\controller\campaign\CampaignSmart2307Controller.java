package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.campaign.CampaignSmart2307Service;
import cn.slashsoft.clinique.util.IntegerUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 智能活动
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/campaign-smart-2307")
public class CampaignSmart2307Controller {

    @Resource
    private CampaignSmart2307Service campaignSmart2307Service;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @PostMapping("/get-index-data")
    public String getIndexData() {

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignSmart2307Service.getTop15());

    }

}
