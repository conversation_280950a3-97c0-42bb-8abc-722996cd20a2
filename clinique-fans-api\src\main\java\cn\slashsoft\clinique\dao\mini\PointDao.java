package cn.slashsoft.clinique.dao.mini;

import cn.slashsoft.clinique.domain.mini.PointTransaction;
import cn.slashsoft.clinique.domain.mini.PointTransactionDeduct;
import cn.slashsoft.clinique.domain.mini.PointTransactionTemp;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 积分相关的数据库操作
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface PointDao {

    /**
     * 读取某类型本月加CB次数
     *
     * @param customerId 顾客编号
     * @return 加CB次数
     */
    @Select("SELECT " +
            "   count(`id`) " +
            "FROM " +
            "   `point_transaction` " +
            "WHERE " +
            "   `customer_id` = #{customerId} " +
            "   AND `status` = 1"
            + " AND `point_type_id`= #{type}"
            + " AND  MONTH(`create_time`) = MONTH(now()) "
            + " AND  YEAR(`create_time`) = YEAR(now()) "
    )
    int getCustomerMonthPointCountByType(int type, Long customerId);

    /**
     * 读取内容是否已加CB
     *
     * @param customerId 顾客编号
     * @return 加CB次数
     */
    @Select("SELECT " +
            "   count(`id`) " +
            "FROM " +
            "   `point_transaction` " +
            "WHERE " +
            "   `customer_id` = #{customerId} " +
            "   AND `status` = 1"
            + " AND `point_type_id`= #{type}"
            + " AND  MONTH(`create_time`) = MONTH(now()) "
            + " AND  YEAR(`create_time`) = YEAR(now()) "
            + " AND `foreign_master_id`=#{detailId} "
    )
    int getCustomerPointCountByDetailId(short type, Long customerId, Long detailId);

    /**
     * 读取内容是否已加CB
     *
     * @return 加CB次数
     */
    @Select("SELECT " +
            "   count(`id`) " +
            "FROM " +
            "   `point_transaction` " +
            "WHERE " +
            "   `customer_id` = #{customerId} " +
            "   AND `status` = 1"
            + " AND `point_type_id`= #{pointTypeId} "
            + " AND `foreign_master_id`=#{foreignMasterId} "
            + " AND `remark`=#{remark} "
    )
    int has(PointTransaction pt);
    
    /**
     * 读取内容是否已加CB
     *
     * @return 加CB次数
     */
    @Select("SELECT " +
            "   count(`id`) " +
            "FROM " +
            "   `point_transaction` " +
            "WHERE " +
            "   `customer_id` = #{customerId} " +
            "   AND `status` = 1"
            + " AND `point_type_id`= #{pointTypeId} "
    )
    int hasEditInfo(PointTransaction pt);
    /**
     * 读取历史积分合计
     *
     * @param customerId 顾客编号
     * @return 历史积分合计
     */
    @Select("SELECT " +
            "   IFNULL(SUM(`points`),0) " +
            "FROM " +
            "   `point_transaction` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "   AND `status`=1 " +
            "   AND `recovery`=0 " +
            "   AND `points`>0 "
    )
    int getPointTotal(long customerId);

    /**
     * 读取有效积分合计
     * 个人中心页
     *
     * @param customerId 顾客编号
     * @return 有效积分合计
     */
    @Select("SELECT " +
            "   IFNULL(SUM(`remaining_points`),0) " +
            "FROM " +
            "   `point_transaction` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "   AND `status`=1 " +
            "   AND `recovery`=0 " +
            "   AND `remaining_points`>0 " +
            "   AND NOW() < `expired_time` "
    )
    int getRemainingPointTotal(long customerId);

    /**
     * 读取本月即将过期的积分合计
     *
     * @param customerId 顾客编号
     * @return 本月即将过期的积分合计
     */
    @Select("SELECT " +
            "   IFNULL(SUM(`remaining_points`),0) " +
            "FROM " +
            "   `point_transaction` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "   AND `status`=1 " +
            "   AND `recovery`=0 " +
            "   AND `remaining_points`>0 " +
            "   AND `expired_running`=0 " +
            "   AND `expired`=0 " +
            "   AND (YEAR(NOW())=YEAR(`expired_time`) AND MONTH(NOW())=MONTH(`expired_time`))"
    )
    int getExpiredPointTotal(long customerId);

    /**
     * 读取所有有效积分
     *
     * @param customerId 顾客编号
     * @return 积分
     */
    @Select("SELECT " +
            "   `p`.`id`," +
            "   `t`.`name` `point_type_name`," +
            "   `t`.`image_url` `point_type_image_url`," +
            "   `p`.`points`, " +
            "   `p`.`create_time` " +
            "FROM " +
            "   `point_transaction` `p` " +
            "       inner join " +
            "   `point_type` `t` " +
            "       on `p`.`point_type_id`=`t`.`id` " +
            "WHERE " +
            "   `p`.`customer_id`=#{customerId} " +
            "   AND `p`.`status`=1 " +
            "   AND `p`.`recovery`=0 " +
            "ORDER BY " +
            "   `p`.`id` DESC "
    )
    List<PointTransaction> getPointTransaction(long customerId);

    /**
     * 读取所有有效积分
     *
     * @param customerId 顾客编号
     * @return 有效积分
     */
    @Select("SELECT " +
            "   `id`," +
            "   `remaining_points` " +
            "FROM " +
            "   `point_transaction` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "   AND `status`=1 " +
            "   AND `recovery`=0 " +
            "   AND `expired_running`=0 " +
            "   AND `expired`=0 " +
            "   AND `remaining_points`>0 " +
            "   AND NOW() < `expired_time` " +
            "ORDER BY " +
            "   `expired_time`"
    )
    List<PointTransaction> getRemainingPointTransaction(long customerId);

    /**
     * 扣除有效积分
     *
     * @param id                 积分变化信息
     * @param remainingPoints    原金币
     * @param newRemainingPoints 新的金币
     * @return 影响的行数
     */
    @Update("UPDATE " +
            "   `point_transaction` " +
            "SET " +
            "   `remaining_points`=#{newRemainingPoints} " +
            "WHERE " +
            "   `id`=#{id} " +
            "   AND `remaining_points`=#{remainingPoints}"
    )
    int updateRemainingPoints(
            @Param("id") long id,
            @Param("remainingPoints") int remainingPoints,
            @Param("newRemainingPoints") int newRemainingPoints
    );

    /**
     * 写入积分变动表
     *
     * @param pointTransaction 积分变动表
     */
    @Insert("INSERT INTO `point_transaction`(" +
            "   `customer_id`, " +
            "   `point_type_id`, " +
            "   `points`, " +
            "   `remaining_points`, " +
            "   `remark`, " +
            "   `start_time`, " +
            "   `expired_time`, " +
            "   `foreign_id`, " +
            "   `foreign_master_id`, " +
            "   `foreign_detail_id`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{pointTypeId}," +
            "   #{points}," +
            "   #{remainingPoints}," +
            "   #{remark}," +
            "   #{startTime}," +
            "   #{expiredTime}," +
            "   #{foreignId}," +
            "   #{foreignMasterId}," +
            "   #{foreignDetailId}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertPointTransaction(PointTransaction pointTransaction);

    /**
     * 写入积分扣除表
     *
     * @param pointTransactionDeduct 积分扣除
     */
    @Insert("INSERT INTO `point_transaction_deduct`(" +
            "   `point_transaction_id`, " +
            "   `deduct_point_transaction_id`, " +
            "   `points`" +
            ") " +
            "VALUES (" +
            "   #{pointTransactionId}," +
            "   #{deductPointTransactionId}," +
            "   #{points}" +
            ")"
    )
    void insertPointTransactionDeduct(PointTransactionDeduct pointTransactionDeduct);

    /**
     * 过期积分更新为处理中状态
     */
    @Update("UPDATE " +
            "   `point_transaction` " +
            "SET " +
            "   `expired_running`=1 " +
            "WHERE " +
            "   `expired`=0 " +
            "   AND `status`=1 " +
            "   AND `expired_time`<NOW() " +
            "   AND `recovery`=0"
    )
    void expiredRunning();

    /**
     * 去掉处理中状态，并标记录已过期
     */
    @Update("UPDATE " +
            "   `point_transaction` " +
            "SET " +
            "   `expired_running`=0," +
            "   `expired`=1 " +
            "WHERE " +
            "   `expired_running`=1 "
    )
    void expired();

    /**
     * 每月第1天过期积分
     */
    @Insert("INSERT INTO `point_transaction`(" +
            "   `customer_id`," +
            "   `point_type_id`," +
            "   `points`, " +
            "   `remaining_points`, " +
            "   `start_time`, " +
            "   `expired_time`, " +
            "   `foreign_id`, " +
            "   `foreign_master_id`, " +
            "   `foreign_detail_id` " +
            ") " +
            "SELECT " +
            "   `customer_id`," +
            "   8," +
            "   -sum(`remaining_points`)," +
            "   0," +
            "   NOW()," +
            "   NOW()," +
            "   8," +
            "   0," +
            "   0 " +
            "FROM " +
            "   `point_transaction` " +
            "WHERE " +
            "   `expired_running`=1 " +
            "GROUP BY `customer_id`"
    )
    void expiredPointTransaction();

    /**
     * 写入临时积分
     *
     * @param pointTransactionTemp 临时积分
     */
    @Insert("INSERT INTO `point_transaction_temp`(" +
            "   `unionid`, " +
            "   `point_type_id`, " +
            "   `points`, " +
            "   `start_time`, " +
            "   `expired_time`" +
            ") " +
            "VALUES (" +
            "   #{unionid}, " +
            "   #{pointTypeId}, " +
            "   #{points}, " +
            "   #{startTime}, " +
            "   #{expiredTime} " +
            ")")
    void insertPointTransactionTemp(PointTransactionTemp pointTransactionTemp);

    /**
     * 发放临时积分
     * @param unionid 开放平台唯一编号
     */
    @Insert("INSERT INTO `point_transaction`(" +
            "   `customer_id`," +
            "   `point_type_id`," +
            "   `points`, " +
            "   `remaining_points`, " +
            "   `start_time`, " +
            "   `expired_time`, " +
            "   `foreign_id`, " +
            "   `foreign_master_id`, " +
            "   `foreign_detail_id` " +
            ") " +
            "SELECT " +
            "   (SELECT `customer_id` FROM `wechat` WHERE `unionid`=#{unionid} LIMIT 1), " +
            "   `point_type_id`, " +
            "   `points`, " +
            "   `points`, " +
            "   `start_time`," +
            "   `expired_time`," +
            "   0," +
            "   0," +
            "   0 " +
            "FROM " +
            "   `point_transaction_temp` " +
            "WHERE " +
            "   `unionid`=#{unionid} " +
            "   AND `status`=1")
    void insertPointTransactionFromTemp(String unionid);

    /**
     * 变更状态
     * @param unionid 开放平台唯一编号
     */
    @Update("UPDATE " +
            "   `point_transaction_temp` " +
            "SET " +
            "   `status`=0 " +
            "WHERE " +
            "   `unionid`=#{unionid}")
    void updatePointTransactionTemp(String unionid);

}
