package com.stormcrm.clinique.service.tag;

import java.util.List;

import com.stormcrm.clinique.domain.mini.CustomerLabel;
import com.stormcrm.clinique.domain.tag.Label;
import com.stormcrm.clinique.domain.tag.LabelOutput;
import com.stormcrm.clinique.domain.tag.LabelResult;
import com.stormcrm.clinique.vo.FilterTemplateBiVo;
import com.stormcrm.clinique.vo.TargetBiVo;

public interface LabelService {
	
	List<Label> getAll();
	
	Label getById(int id);
	
	LabelOutput getOutput(Label label);

	List<LabelOutput> getTargetAll();
	

	List<FilterTemplateBiVo> getFilterTemplateAllForBI();

	List<TargetBiVo> getTargetBiVoAll();

	void addLabel(Label label);

	void updateLabel(Label label);

    int getLabelCount(Label label);

    List<CustomerLabel> getLabelList(Label label);

    void delLabel(int labelId);

	void addLabelResult(LabelResult result);

	LabelResult getLastResult(int labelId);
}
