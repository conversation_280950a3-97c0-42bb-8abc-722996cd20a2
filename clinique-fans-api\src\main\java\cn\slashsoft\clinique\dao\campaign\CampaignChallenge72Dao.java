package cn.slashsoft.clinique.dao.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignChallenge72;
import cn.slashsoft.clinique.domain.campaign.CampaignChallenge72CityConfig;
import cn.slashsoft.clinique.domain.campaign.CampaignViewLog;
import cn.slashsoft.clinique.domain.mini.Wechat;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 挑战72小时
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignChallenge72Dao {

    /**
     * 获取活动信息
     *
     * @param customerId 会员编号
     * @return 活动信息
     */
    @Select("SELECT " +
            "   `first_round`," +
            "   `second_round`," +
            "   `third_round`, " +
            "   `result`, " +
            "   `result_experience`, " +
            "   `result_share`, " +
            "   `invite_done` " +
            "FROM " +
            "   `campaign_challenge72` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    CampaignChallenge72 getCampaignChallenge72(long customerId);

    /**
     * 获取头像
     * @param customerId 顾客信息
     * @return 头像
     */
    @Select("SELECT " +
            "   `avatar_url` " +
            "FROM " +
            "   `wechat` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    String getAvatarUrl(long customerId);

    /**
     * 获取开放平台唯一编号
     * @param miniOpenid 小程序唯一编号
     * @return 开放平台唯一编号
     */
    @Select("SELECT " +
            "   `customer_id`, " +
            "   `unionid`, " +
            "   `wechat_official_openid` " +
            "FROM " +
            "   `wechat` " +
            "WHERE " +
            "   `wechat_mini_openid`=#{miniOpenid} " +
            "LIMIT 1")
    Wechat getUnionidByMiniOpenid(String miniOpenid);

    /**
     * 获取被邀请者头像
     * @param openid 邀请者openid
     * @return 头像
     */
    @Select("SELECT " +
            "   `w`.`avatar_url` " +
            "FROM " +
            "   `campaign_challenge72_invite` `c` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `c`.`invitee_mini_openid`=`w`.`wechat_mini_openid` " +
            "WHERE " +
            "   `c`.`inviter_mini_openid`=#{openid} " +
            "ORDER BY " +
            "   `c`.`id` " +
            "LIMIT 2")
    List<String> getInviteeAvatarUrl(String openid);

    /**
     * 写入活动信息
     * @param campaignChallenge72 活动信息
     */
    @Insert("INSERT INTO `campaign_challenge72`(" +
            "   `customer_id`," +
            "   `source`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{source}" +
            ")")
    void insertCampaignChallenge72(CampaignChallenge72 campaignChallenge72);

    /**
     * 设置第一轮完成
     * @param customerId 顾客编号
     */
    @Update("UPDATE " +
            "   `campaign_challenge72` " +
            "SET " +
            "   `first_round`=1," +
            "   `first_round_time`=NOW() " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    void setCampaignChallenge72FirstRound(long customerId);

    /**
     * 设置第二轮完成
     * @param customerId 顾客编号
     */
    @Update("UPDATE " +
            "   `campaign_challenge72` " +
            "SET " +
            "   `second_round`=1," +
            "   `second_round_time`=NOW() " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    void setCampaignChallenge72SecondRound(long customerId);

    /**
     * 设置第三轮完成
     * @param customerId 顾客编号
     */
    @Update("UPDATE " +
            "   `campaign_challenge72` " +
            "SET " +
            "   `third_round`=1," +
            "   `third_round_time`=NOW() " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    void setCampaignChallenge72ThirdRound(long customerId);

    /**
     * 获取所有城市
     * @param customerId 顾客编号
     * @return 所有城市
     */
    @Select("SELECT " +
            "   `id`," +
            "   `type` " +
            "FROM " +
            "   `campaign_challenge72_city_config` " +
            "WHERE " +
            "   `id` NOT IN (" +
            "       SELECT `city_id` FROM `campaign_challenge72_city` WHERE `customer_id`=#{customerId}" +
            "   )")
    List<CampaignChallenge72CityConfig> getCampaignChallenge72CityConfig(long customerId);

    /**
     * 写入摇到的城市
     * @param customerId 顾客编号
     * @param cityId 城市编号
     */
    @Insert("INSERT INTO `campaign_challenge72_city` (" +
            "   `customer_id`," +
            "   `city_id`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{cityId}" +
            ")")
    void insertCampaignChallenge72City(@Param("customerId") long customerId, @Param("cityId") short cityId);

    /**
     * 选择心得大师
     * @param customerId 顾客编号
     */
    @Update("UPDATE " +
            "   `campaign_challenge72` " +
            "SET " +
            "   `result`=1," +
            "   `result_experience`=1," +
            "   `result_share`=0," +
            "   `result_time`=NOW() " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    void setResultExperience(long customerId);

    /**
     * 选择分享大师
     * @param customerId 顾客编号
     */
    @Update("UPDATE " +
            "   `campaign_challenge72` " +
            "SET " +
            "   `result`=1," +
            "   `result_experience`=0," +
            "   `result_share`=1," +
            "   `result_time`=NOW() " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    void setResultShare(long customerId);

    /**
     * 邀请
     * @param inviterMiniOpenid 邀请者
     * @param inviteeMiniOpenid 被邀请者
     */
    @Insert("INSERT INTO `campaign_challenge72_invite`(" +
            "   `inviter_mini_openid`, " +
            "   `invitee_mini_openid`" +
            ") " +
            "VALUES (" +
            "   #{inviterMiniOpenid}, " +
            "   #{inviteeMiniOpenid}" +
            ")")
    void insertCampaignChallenge72Invite(
            @Param("inviterMiniOpenid") String inviterMiniOpenid,
            @Param("inviteeMiniOpenid") String inviteeMiniOpenid
    );

    /**
     * 获取邀请数量
     * @param inviterMiniOpenid 邀请者
     * @return 数量
     */
    @Select("SELECT " +
            "   count(`id`) " +
            "from " +
            "   `campaign_challenge72_invite` " +
            "where " +
            "   `inviter_mini_openid`=#{inviterMiniOpenid}")
    int getCampaignChallenge72InviteCount(String inviterMiniOpenid);

    /**
     * 更新邀请状态
     * @param inviterMiniOpenid 邀请者
     */
    @Update("UPDATE " +
            "   `campaign_challenge72` " +
            "SET " +
            "   `Invite_done`=1," +
            "   `Invite_done_time`=NOW() " +
            "WHERE " +
            "   `customer_id`=(SELECT `customer_id` FROM `wechat` WHERE `wechat_mini_openid`=#{inviterMiniOpenid} LIMIT 1)" +
            "   AND `Invite_done`=0")
    int setCampaignChallenge72InviteDone(String inviterMiniOpenid);

    /**
     * 写入操作日志
     * @param customerId 顾客编号
     * @param type 操作类型
     */
    @Insert("INSERT INTO `campaign_challenge72_log`(" +
            "   `customer_id`," +
            "   `type`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{type}" +
            ")")
    void insertCampaignChallenge72Log(@Param("customerId") long customerId, @Param("type") String type);

    /**
     * 写入访问日志
     *
     * @param campaignViewLog 日志
     */
    @Insert("INSERT INTO `campaign_challenge72_view_log`(" +
            "   `customer_id`," +
            "   `source`," +
            "   `page`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{source}," +
            "   #{page}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertCampaignChallenge72ViewLog(CampaignViewLog campaignViewLog);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Update("UPDATE " +
            "   `campaign_challenge72_view_log` " +
            "SET " +
            "   `end_time`=NOW()," +
            "   `diff_second`=TimeStampDiff(SECOND,`start_time`,NOW()) " +
            "WHERE " +
            "   `id`=#{id} " +
            "LIMIT 1")
    void setCampaignChallenge72ViewLog(long id);

}
