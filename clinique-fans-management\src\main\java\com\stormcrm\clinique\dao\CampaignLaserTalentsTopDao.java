package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.CampaignLaserTalentsTop;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 新品体验官活动TOP
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignLaserTalentsTopDao {

    /**
     * 查询所有活动
     *
     * @return 活动列表
     */
    @Select("SELECT " +
            "   `id`," +
            "   `stage`," +
            "   `start_time`, " +
            "   `end_time`, " +
            "   (" +
            "       SELECT " +
            "           COUNT(`id`) " +
            "       FROM " +
            "           `campaign_laser_talents_top_detail` " +
            "       WHERE " +
            "           `talents_top_id`=`campaign_laser_talents_top`.`id` " +
            "           AND `status`=1" +
            "   ) `count` " +
            "FROM " +
            "   `campaign_laser_talents_top` ")
    List<CampaignLaserTalentsTop> getAll();

    /**
     * 查询
     *
     * @param id 自动编号
     * @return 信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `stage`," +
            "   `start_time`, " +
            "   `end_time`, " +
            "   (" +
            "       SELECT " +
            "           COUNT(`id`) " +
            "       FROM " +
            "           `campaign_laser_talents_top_detail` " +
            "       WHERE " +
            "           `talents_top_id`=`campaign_laser_talents_top`.`id` " +
            "           AND `status`=1" +
            "   ) `count` " +
            "FROM " +
            "   `campaign_laser_talents_top` " +
            "WHERE " +
            "   `id`=#{id} " +
            "LIMIT 1")
    CampaignLaserTalentsTop getById(long id);

    /**
     * 保存
     *
     * @param campaignLaserTalentsTop 信息
     */
    @Insert("INSERT INTO `campaign_laser_talents_top`(" +
            "   `stage`," +
            "   `start_time`, " +
            "   `end_time` " +
            ") " +
            "VALUES (" +
            "   #{stage}, " +
            "   #{startTime}, " +
            "   #{endTime} " +
            ")")
    void save(CampaignLaserTalentsTop campaignLaserTalentsTop);

    /**
     * 更新
     *
     * @param campaignLaserTalentsTop 信息
     */
    @Update("UPDATE " +
            "   `campaign_laser_talents_top` " +
            "SET " +
            "   `start_time`=#{startTime}, " +
            "   `end_time`=#{endTime} " +
            "WHERE " +
            "   `id`=#{id}")
    void update(CampaignLaserTalentsTop campaignLaserTalentsTop);

}
