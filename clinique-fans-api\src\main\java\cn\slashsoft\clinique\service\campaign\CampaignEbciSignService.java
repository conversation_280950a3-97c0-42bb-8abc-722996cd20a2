package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignEbciSign;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciSignLog;

import java.util.List;

/**
 * EBCI 7日打卡活动
 *
 * <AUTHOR>
 */
public interface CampaignEbciSignService {

    /**
     * 获取签到信息
     *
     * @param customerId 顾客编号
     * @return 签到信息
     */
    CampaignEbciSign getCampaignEbciSign(long customerId);

    /**
     * 获取签到日志
     * @param customerId 顾客编号
     * @return 签到日志
     */
    List<CampaignEbciSignLog> getCampaignEbciSignLog(long customerId);

    /**
     * 签到
     *
     * @param customerId 顾客编号
     * @return 签到次数
     */
    Short sign(long customerId);

    /**
     * 重置
     *
     * @param customerId 顾客编号
     * @return 是否成功
     */
    boolean reset(long customerId);

    /**
     * 邀请
     *
     * @param customerId 顾客编号
     * @return 是否成功
     */
    boolean inviting(long customerId);

    /**
     * 邀请
     *
     * @param customerId 顾客编号
     * @return 是否成功
     */
    boolean invited(long customerId);

    /**
     * 申领
     *
     * @param customerId 顾客编号
     * @return 是否成功
     */
    boolean apply(long customerId);


    /**
     * 写入访问开始日志
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @return 访问日志编号
     */
    long insertCampaignEbciSignViewLog(long customerId, String source);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    void setCampaignEbciSignViewLog(long id);

}
