package cn.slashsoft.clinique.service.mini;


import cn.slashsoft.clinique.domain.mini.TTestimonyFragmentType;

/**
* <p>
    * 记录获得碎片信息
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
public interface TTestimonyFragmentTypeService {
    void insertTTestimonyFragmentType(TTestimonyFragmentType tTestimonyFragmentType);
    void updateTTestimonyFragmentType(TTestimonyFragmentType tTestimonyFragmentType);
    TTestimonyFragmentType getTTestimonyFragmentType(Long id);
}
