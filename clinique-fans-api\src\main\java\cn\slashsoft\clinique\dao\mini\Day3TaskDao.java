package cn.slashsoft.clinique.dao.mini;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.mini.Day3Task;

/**
 * 三天打卡任务
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface Day3TaskDao {

    /**
     * 创建
     *
     * @param Day3Task
     */
    @Insert("insert into `day3task` ( " +
            "   `unionid`, " +
            "   `customer_id`, " +
            "   `status`, " +
            "   `create_time`, " +
            "   `task_type` " +
            ") " +
            "VALUES ( " +
            "   #{unionid}, " +
            "   #{customerId}, " +
            "   1, " +
            "   now(), " +
            "   #{taskType} " +
            ")")
    void createDay3Task(Day3Task task);
    
    @Update("UPDATE `day3task` SET " +
            "   `customer_id`=#{customerId}, " +
            "   `status`= #{status}" +
            " where " +
            "   unionid=#{unionid} " +
            "")
    void editDay3Task(Day3Task task);
    

    @Update("UPDATE `day3task` SET " +
            "   `notice`=#{notice} " +
            " where " +
            "   unionid=#{unionid} " +
            "")
    void noticeChangeDay3Task(Day3Task task);

    @Select("SELECT * from day3task where unionid = #{unionid} limit 1 ")
    Day3Task getDay3TaskByUnionid(String unionid);

    @Update("UPDATE `day3task` SET " +
            "   `status`= 2" +
            " where " +
            "   `unionid`=#{unionid} " +
            "")
	int finishDay3Task(String unionid);

    @Select("SELECT count(*) from phone_city where `phone`=#{phone} AND `city` in (SELECT city from day3task_city) ")
	int enabledByPhone(String phone);
}
