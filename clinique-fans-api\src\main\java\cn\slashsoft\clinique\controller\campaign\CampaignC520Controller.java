package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignC520;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.CustomerService;
import cn.slashsoft.clinique.service.mini.SmotService;
import cn.slashsoft.clinique.service.campaign.CampaignC520Service;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.vo.campaign.CampaignC520Vo;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * 520活动
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class CampaignC520Controller {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final CampaignC520Service campaignC520Service;
    private final CustomerService customerService;
    private final SmotService smotService;
    private final StringRedisTemplate stringRedisTemplate;

    public CampaignC520Controller(CampaignC520Service campaignC520Service, CustomerService customerService, StringRedisTemplate stringRedisTemplate, SmotService smotService) {
        this.campaignC520Service = campaignC520Service;
        this.customerService = customerService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.smotService = smotService;
    }

    /**
     * 配置活动信息
     *
     * @param verification 验证
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 处理结果
     * https://clinique.stormcrm.com/api/campaign-config/c520/ON/20-15-04-32-29/2051/2020-04-28-00-00-00/2020-05-05-23-59-59
     */
    @GetMapping("/campaign-config/c520/{online}/{verification}/{code}/{startTime}/{endTime}")
    public String setConfig(
            @PathVariable("online") String online,
            @PathVariable("verification") String verification,
            @PathVariable("code") String code,
            @PathVariable("startTime") String startTime,
            @PathVariable("endTime") String endTime
    ) {
        if (DateUtil.parseVerification(new Date()).equals(verification)) {
            stringRedisTemplate.opsForValue().set("campaignC520", online);
            stringRedisTemplate.opsForValue().set("campaignC520Code", code);
            stringRedisTemplate.opsForValue().set("campaignC520StartTime", DateUtil.parseString(DateUtil.valueOf(startTime, "yyyy-MM-dd-HH-mm-ss")));
            stringRedisTemplate.opsForValue().set("campaignC520EndTime", DateUtil.parseString(DateUtil.valueOf(endTime, "yyyy-MM-dd-HH-mm-ss")));
            return "success";
        }
        return "fail";
    }

    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-c520/get-data/{source}")
    public String getData(
            @PathVariable("source") String source
    ) {

        CampaignC520Vo campaignC520Vo = new CampaignC520Vo();

        campaignC520Vo.setStartTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignC520StartTime")));
        campaignC520Vo.setEndTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignC520EndTime")));

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        String phoneNumber = customerService.getCustomerPhoneNumberById(customerId);

        // 手机不为空并且在名单里
        if (!StringUtil.isNullOrEmpty(phoneNumber) && 0 < campaignC520Service.getCampaignC520Exclude(phoneNumber)) {

            CampaignC520 campaignC520 = campaignC520Service.getCampaignC520(customerId);
            if (null != campaignC520 && campaignC520.getStatus()) {
                String page;
                if (campaignC520.getIsReceive()) {
                    campaignC520Vo.setStage((short) 3);
                    campaignC520Vo.setIsApply(campaignC520.getIsApply());
                    campaignC520Vo.setApplyTime(campaignC520.getApplyTime());
                    campaignC520Vo.setIsReceive(campaignC520.getIsReceive());
                    campaignC520Vo.setReceiveTime(campaignC520.getReceiveTime());
                    page = "Receive";
                } else if (campaignC520.getIsApply()) {
                    campaignC520Vo.setStage((short) 2);
                    campaignC520Vo.setIsApply(campaignC520.getIsApply());
                    campaignC520Vo.setApplyTime(campaignC520.getApplyTime());
                    campaignC520Vo.setIsReceive(campaignC520.getIsReceive());
                    page = "Apply";
                } else {
                    campaignC520Vo.setStage((short) 1);
                    page = "Index";
                    campaignC520Vo.setIsApply(campaignC520.getIsApply());
                    campaignC520Vo.setIsReceive(campaignC520.getIsReceive());
                }
                campaignC520Vo.setAvatarUrl(campaignC520.getAvatarUrl());
                campaignC520Vo.setNickName(campaignC520.getNickName());
                campaignC520Vo.setPhoneNumber(phoneNumber);

                // 写入访问日志
                campaignC520Vo.setViewLogId(campaignC520Service.insertCampaignC520ViewLog(customerId, source, page));

            } else {
                campaignC520Vo.setStage((short) 0);
            }
        } else {
            campaignC520Vo.setStage((short) 0);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignC520Vo);
    }

    /**
     * 申领
     *
     * @return 申领结果
     */
    @PostMapping("/campaign-c520/apply")
    public String apply() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        // 申领
        if (!campaignC520Service.applyCampaignC520(customerId)) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 申领
     *
     * @return 申领结果
     */
    @PostMapping("/campaign-c520/receive/{code}")
    public String receive(
            @PathVariable("code") String code
    ) {
        String verification = stringRedisTemplate.opsForValue().get("campaignC520Code");
        if (null == verification || !verification.equals(code)) {
            return ResultUtil.customer(ResultEnum.OTHER);
        }

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        // 获取缓存中的小程序编号
        String openid = (String) request.getAttribute("miniOpenid");
        // 申领
        if (!campaignC520Service.receiveCampaignC520(customerId)) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        // 发送一次性订阅消息
        smotService.receiveCampaignC520(openid, customerId);

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 写入访问结束日志
     *
     * @param id 来源
     * @return 成功
     */
    @PostMapping("/campaign-c520-end-view-log/{id}")
    public String setEndViewLog(
            @PathVariable("id") long id
    ) {
        campaignC520Service.setCampaignC520ViewLog(id);
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

}
