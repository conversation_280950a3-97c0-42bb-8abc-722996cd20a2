package com.stormcrm.clinique.service;

import com.stormcrm.clinique.domain.Wechat;

import java.util.List;

/**
 * 点赞积分权益相关服务
 * <AUTHOR>
 */
public interface DiscussPointService {

    /**
     * 获取用户列表
     *
     * @param nickName 用户昵称
     * @return 用户列表
     */
    List<Wechat> getAll(String nickName);
    /**
     * 获取用户
     *
     * @param customerId 
     * @return 用户
     */
    Wechat getOne(String customerId);

}
