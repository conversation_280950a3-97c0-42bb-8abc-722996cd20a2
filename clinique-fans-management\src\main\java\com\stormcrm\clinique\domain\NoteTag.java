package com.stormcrm.clinique.domain;

import lombok.Data;

import java.util.Date;

@Data
public class NoteTag {
    private Long id;
    private Date createTime;

    private String image;

    private String title;
    /* 1 产品  2 笔记 */
    private int type;
    /* 新增置顶、首页置顶、搜索置顶 */
    private int top;
    private int topIndex;
    private int topSearch;

    /**
     * 活动奖励分  关联 point type
     */
    private Short pointTypeId = 0;

    /**
     * 活动奖励分  来自 point_type
     */
    private String name;
    private Integer rewardPoint;
    private Date rewardActiveStart;
    private Date rewardActiveEnd;

    private Integer rewardMax;
    private Integer rewardMaxForever;
}