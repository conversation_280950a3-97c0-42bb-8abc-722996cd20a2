package com.stormcrm.clinique.controller;

import com.stormcrm.clinique.domain.CampaignLaserTalents;
import com.stormcrm.clinique.service.CampaignLaserTalentsService;
import com.stormcrm.clinique.util.ResultUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 新品体验官活动
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("campaign-laser-talents")
public class CampaignLaserTalentsController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    private final CampaignLaserTalentsService campaignLaserTalentsService;

    public CampaignLaserTalentsController(CampaignLaserTalentsService campaignLaserTalentsService) {
        this.campaignLaserTalentsService = campaignLaserTalentsService;
    }

    /**
     * 页面模版
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_LASER_TALENTS')")
    @GetMapping("")
    public String index(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/campaign-laser-talents/index";
    }

    /**
     * 查询所有-分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 列表
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_LASER_TALENTS')")
    @PostMapping("get-page")
    @ResponseBody
    public String getPage(
            @RequestParam(value = "pagination[page]", required = false) Integer page,
            @RequestParam(value = "pagination[perpage]", required = false) Integer perpage,
            @RequestParam(value = "query[generalSearch]", required = false) String generalSearch,
            @RequestParam(value = "query[status]", required = false) Short status
    ) {

        if (null == page) {
            page = 1;
        }

        if (null == perpage) {
            perpage = 10;
        }

        List<CampaignLaserTalents> campaignLaserTalentsList = campaignLaserTalentsService.getPage(page, 500, generalSearch, status);
        int count = campaignLaserTalentsService.getPageCount(generalSearch, status);

        JSONObject mata = new JSONObject();
        mata.put("page", page);
        mata.put("pages", (count + perpage - 1) / perpage);
        mata.put("perpage", perpage);
        mata.put("total", count);
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("mata", mata);
        result.put("data", campaignLaserTalentsList);

        return JSONObject.toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }

    /**
     * 编辑模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_LASER_TALENTS')")
    @GetMapping("edit/{id}")
    public String edit(@PathVariable long id, Model model) {
        model.addAttribute("staticDomain", staticDomain);
        // 读数据库
        CampaignLaserTalents campaignLaserTalents = campaignLaserTalentsService.getById(id);
        if (null == campaignLaserTalents) {
            return "m/fans/common/empty";
        }
        campaignLaserTalents.setImageList(campaignLaserTalentsService.getImageById(id));
        model.addAttribute("id", id);
        model.addAttribute("campaignLaserTalents", campaignLaserTalents);
        return "m/fans/campaign-laser-talents/edit";
    }

    /**
     * 同意
     *
     * @param id 自动编号
     * @return 处理结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_LASER_TALENTS_ACCEPT')")
    @GetMapping("accept/{id}")
    @ResponseBody
    public String accept(@PathVariable("id") long id) {
        if (0 == campaignLaserTalentsService.accept(id)) {
            return ResultUtil.failToJson();
        }
        return ResultUtil.successToJson();
    }

    /**
     * 批量同意
     *
     * @param ids 自动编号
     * @return 处理结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_LASER_TALENTS_ACCEPT')")
    @GetMapping("accept-batch/{ids}")
    @ResponseBody
    public String acceptBatch(@PathVariable("ids") String ids) {
        if (0 == campaignLaserTalentsService.acceptBatch(ids)) {
            return ResultUtil.failToJson();
        }
        return ResultUtil.successToJson();
    }

    /**
     * 拒绝
     *
     * @param id 自动编号
     * @return 处理结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_LASER_TALENTS_REJECT')")
    @GetMapping("reject/{id}")
    @ResponseBody
    public String reject(@PathVariable("id") long id) {
        if (0 == campaignLaserTalentsService.reject(id)) {
            return ResultUtil.failToJson();
        }
        return ResultUtil.successToJson();
    }

    /**
     * 批量拒绝
     *
     * @param ids 自动编号
     * @return 处理结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_LASER_TALENTS_REJECT')")
    @GetMapping("reject-batch/{ids}")
    @ResponseBody
    public String rejectBatch(@PathVariable("ids") String ids) {
        if (0 == campaignLaserTalentsService.rejectBatch(ids)) {
            return ResultUtil.failToJson();
        }
        return ResultUtil.successToJson();
    }

}
