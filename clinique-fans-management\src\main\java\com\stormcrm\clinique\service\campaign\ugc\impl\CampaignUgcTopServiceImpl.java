package com.stormcrm.clinique.service.campaign.ugc.impl;

import com.stormcrm.clinique.dao.campaign.ugc.CampaignUgcTopDao;
import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgcTop;
import com.stormcrm.clinique.service.campaign.ugc.CampaignUgcTopService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.vo.Result;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Service
public class CampaignUgcTopServiceImpl implements CampaignUgcTopService {

    private final CampaignUgcTopDao campaignUgcTopDao;

    public CampaignUgcTopServiceImpl(CampaignUgcTopDao campaignUgcTopDao) {
        this.campaignUgcTopDao = campaignUgcTopDao;
    }

    /**
     * 查询所有活动
     *
     * @param campaignId 活动编号
     * @return 活动列表
     */
    @Override
    public List<CampaignUgcTop> getAll(long campaignId) {
        return campaignUgcTopDao.getAll(campaignId);
    }

    /**
     * 查询信息
     *
     * @param id 自动编号
     * @return 信息
     */
    @Override
    public CampaignUgcTop getById(long id) {
        return campaignUgcTopDao.getById(id);
    }

    /**
     * 保存
     *
     * @param campaignUgcTop 信息
     * @return 影响的行数
     */
    @Override
    public Result save(CampaignUgcTop campaignUgcTop) {
        campaignUgcTopDao.save(campaignUgcTop);
        return ResultUtil.success("增加成功!");
    }

    /**
     * 更新
     *
     * @param campaignUgcTop 信息
     * @return 影响的行数
     */
    @Override
    public Result update(CampaignUgcTop campaignUgcTop) {
        campaignUgcTopDao.update(campaignUgcTop);
        return ResultUtil.success("编辑成功!");
    }
}
