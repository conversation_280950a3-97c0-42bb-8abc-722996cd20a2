package com.stormcrm.clinique.service.campaign.ugc;

import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgcTopDetail;
import com.stormcrm.clinique.vo.Result;

import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
public interface CampaignUgcTopDetailService {

    /**
     * 查询所有活动
     *
     * @param id TOP5编号
     * @return 活动列表
     */
    List<CampaignUgcTopDetail> getAll(long topId);

    /**
     * 查询信息
     *
     * @param id 自动编号
     * @return 信息
     */
    CampaignUgcTopDetail getById(long id);

    /**
     * 保存
     *
     * @param campaignUgcTopDetail 信息
     * @return 影响的行数
     */
    Result save(CampaignUgcTopDetail campaignUgcTopDetail);

    /**
     * 更新
     *
     * @param campaignUgcTopDetail 信息
     * @return 影响的行数
     */
    Result update(CampaignUgcTopDetail campaignUgcTopDetail);

    /**
     * 更新
     *
     * @param id 编号
     * @return 影响的行数
     */
    int delete(long id);
}
