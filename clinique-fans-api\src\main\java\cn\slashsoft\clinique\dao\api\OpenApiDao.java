package cn.slashsoft.clinique.dao.api;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.api.CouponApiLog;

/**
 * 券
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface OpenApiDao {


    @Insert("INSERT INTO `open_api_log`(" +
            "`api`," + 
            "`params`," + 
            "`result_code`," + 
            "`create_time`," + 
            "`server_time`," + 
            "`result_time`," + 
            "`source`" + 
            ") " +
            "VALUES (" +
            "#{api}," + 
            "#{params}," + 
            "#{resultCode}," + 
            "#{createTime}," + 
            "#{serverTime}," + 
            "#{resultTime}," + 
            "#{source}" + 
            ")")
	void addApiLog(CouponApiLog log);

}
