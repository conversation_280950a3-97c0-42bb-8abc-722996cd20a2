package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.dao.PointTypeDao;
import com.stormcrm.clinique.domain.PointType;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 奖励类型
 *
 * <AUTHOR>
 */
@Service
public class PointTypeServiceImpl{
    
    private final PointTypeDao pointTypeDao;

    public PointTypeServiceImpl(PointTypeDao pointTypeDao) {
        this.pointTypeDao = pointTypeDao;
    }
   
    /**
     * 添加奖励类型
     *
     * @param pointType 奖励类型
     */
    public void insert(PointType pointType) {
    	this.pointTypeDao.insertPointType(pointType);
    }

    public void insertById(PointType pointType) {
        this.pointTypeDao.insertPointTypeWithId(pointType);
    }
    
    /**
     * 修改奖励类型
     *
     * @param pointType 奖励类型
     */
    public void update(PointType pointType) {
        this.pointTypeDao.updatePointTypeWithNoteTagEdit(pointType);

    }
    /**
     * 删除奖励类型
     *
     * @param id 奖励类型id
     */
    public int delete(Long id) {
    	return pointTypeDao.delete(id);
    }


    /**
     * 查询所有带分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索
     * @param top        状态
     * @return 列表分页
     */
    public List<PointType> getPage(int page, int perpage, String generalSearch, Short top) {
        return pointTypeDao.getPage((page - 1) * perpage, perpage, generalSearch, top);
    }

    /**
     * 查询所有带分页的记录数
     *
     * @param generalSearch 搜索
     * @param top        状态
     * @return 记录数
     */
    public int getPageCount(String generalSearch, Short top) {
        return pointTypeDao.getPageCount(generalSearch, top);
    }


    /**
     * 获取单个
     *
     * @param id        状态
     * @return 记录数
     */
	public PointType getById(Short id) {
    	return this.pointTypeDao.getById(id);
	}

    /**
     * 奖励类型中 低于100的id 系统保留，可能用在 cbtypeenum中
     * 后台管理操作，这个设置属于低频处理，可以这样获取id
     * @return 最大可用id
     */
    public Short getMaxId() {
        return this.pointTypeDao.getMaxId();
    }


}
