package com.stormcrm.clinique.tag.dao;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.stormcrm.clinique.domain.tag.LogMot;
import com.stormcrm.clinique.domain.tag.LogSmot;
import com.stormcrm.clinique.domain.tag.LogSms;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
public interface LogDao {

	/**
	 * 写入短信发送记录
	 * @param log  短信发送记录
	 */
	@Insert("INSERT INTO `sns`(" +
			"   `sns_type_id`, " +
			"   `customer_id`, " +
			"   `phone_number`, " +
			"   `message`, " +
			"   `param`, " +
			"   `response_code`" +
			") " +
			"VALUES (" +
			"   #{snsTypeId}," +
			"   #{customerId}," +
			"   #{phoneNumber}," +
			"   #{message}," +
			"   #{param}," +
			"   #{responseCode}" +
			")")
	int addSmsLog(LogSms log);

	/**
	 * 写入微信模板消息发送记录
	 * @param log  微信模板消息发送记录
	 */
	@Insert("INSERT INTO `log_mot`(" +
			"   `wechat_official_openid`, " +
			"   `type`, " +
			"   `param`, " +
			"   `result`, " +
			"   `status`" +
			") " +
			"VALUES (" +
			"   #{wechatOfficialOpenid}, " +
			"   #{type}, " +
			"   #{param}, " +
			"   #{result}, " +
			"   #{status}" +
			")")
	void addMotLog(LogMot log);

	/**
	 * 写入微信服务通知发送记录
	 * @param log  微信服务通知发送记录
	 */
	@Insert("INSERT INTO `log_smot`(" +
			"   `wechat_official_openid`, " +
			"   `type`, " +
			"   `param`, " +
			"   `result`, " +
			"   `status`" +
			") " +
			"VALUES (" +
			"   #{wechatOfficialOpenid}, " +
			"   #{type}, " +
			"   #{param}, " +
			"   #{result}, " +
			"   #{status}" +
			")")
	void addSmotLog(LogSmot log);
}
