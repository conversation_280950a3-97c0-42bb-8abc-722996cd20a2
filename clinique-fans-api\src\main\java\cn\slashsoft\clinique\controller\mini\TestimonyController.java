package cn.slashsoft.clinique.controller.mini;

import cn.slashsoft.clinique.domain.mini.TTestimonyAmount;
import cn.slashsoft.clinique.domain.mini.TTestimonyBlindBox;
import cn.slashsoft.clinique.domain.mini.TTestimonyBlindBoxMotLog;
import cn.slashsoft.clinique.domain.mini.TTestimonyContent;
import cn.slashsoft.clinique.domain.mini.TTestimonyFragmentTop15;
import cn.slashsoft.clinique.domain.mini.TTestimonyHelp;
import cn.slashsoft.clinique.domain.mini.TTestimonyLike;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.CustomerService;
import cn.slashsoft.clinique.service.mini.MotService;
import cn.slashsoft.clinique.service.mini.TTestimonyAmountService;
import cn.slashsoft.clinique.service.mini.TTestimonyBlindBoxService;
import cn.slashsoft.clinique.service.mini.TTestimonyContentService;
import cn.slashsoft.clinique.service.mini.TTestimonyFragmentService;
import cn.slashsoft.clinique.service.mini.TTestimonyLikeService;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.mini.TestimonyFragmentRankVo;
import cn.slashsoft.clinique.vo.mini.TestimonyFragmentRewardPrivilegeVo;
import cn.slashsoft.clinique.vo.mini.TestimonyFragmentRewardVo;
import cn.slashsoft.clinique.vo.mini.TestimonyFragmentSumVo;
import cn.slashsoft.clinique.vo.mini.TestimonyHelpThumbupVo;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * 证言活动
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
@Slf4j
public class TestimonyController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;
    @Resource
    private TTestimonyContentService tTestimonyContentService;
    @Resource
    private TTestimonyLikeService tTestimonyLikeService;
    @Resource
    private TTestimonyAmountService tTestimonyAmountService;
    @Resource
    private TTestimonyFragmentService tTestimonyFragmentService;
    @Resource
    private TTestimonyBlindBoxService tTestimonyBlindBoxService;
    @Resource
    private MotService motService;
    @Resource
    private CustomerService customerService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 新建证言
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/addcontent")
    public String addContent(@RequestBody JSONObject obj) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        Integer iCount = tTestimonyContentService.getTTestimonyContentCountByCustomerId(customerId);
        String content = obj.getString("content");

        Integer contentCount = tTestimonyContentService.getTTestimonyContentCountByCustomerIdContent(customerId, content);
        // 不能重复发送同样证言
        if (contentCount > 0) {
            return ResultUtil.customer(ResultEnum.DUPLICATE_REC, contentCount);
        }

        Integer todayCount = tTestimonyContentService.getTTestimonyContentCountByCustomerIdToday(customerId);
        // 每天限3条证言
        if (todayCount >= 3) {
            return ResultUtil.customer(ResultEnum.DAYMAX, contentCount);
        }
        TTestimonyContent tTestimonyContent = new TTestimonyContent();
        tTestimonyContent.setCustomerId(customerId);
        tTestimonyContent.setContent(content);
        tTestimonyContent.setCreateTime(new Date());

        // 判断是否包含需过滤的词汇
        if (tTestimonyContentService.containsFiltersWord(content)) {
            tTestimonyContentService.insertFilteredTTestimonyContent(tTestimonyContent);
        } else {
            tTestimonyContentService.insertTTestimonyContent(tTestimonyContent);
        }

        // 获得当前用户的证言数，如果证言数大于0，则不再获得碎片
        return ResultUtil.customer(ResultEnum.SUCCESS, iCount);
    }

    /**
     * 点赞
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/thumbup")
    public String thumbUp(@RequestBody JSONObject obj) {
        // 获取缓存中的顾客编号
        TTestimonyLike tTestimonyLike = new TTestimonyLike();
        long customerId = (long) request.getAttribute("customerId");
        long contentId = (long) Integer.parseInt(obj.getString("contentId"));
        Integer addCount = 0;
        // 不能给自己点赞
        Long custId = tTestimonyContentService.getCustomerIdByContentId(contentId);
        if (customerId == custId) {
            return ResultUtil.customer(ResultEnum.SELFTHUMBUP);
        }

        if (null == tTestimonyLikeService.getTTestimonyLikeByCustomerIdContentId(customerId, contentId)) {
            tTestimonyLike.setCustomerId(customerId);
            tTestimonyLike.setContentId(contentId);
            tTestimonyLike.setCreateTime(new Date());
            tTestimonyLikeService.insertTTestimonyLike(tTestimonyLike);

            // 判断是否存在
            if (null == tTestimonyAmountService.getTTestimonyAmountByCustomerId(customerId)) {
                // 不存在则新建
                TTestimonyAmount tTestimonyAmount = new TTestimonyAmount();
                tTestimonyAmount.setLikeCount(1);
                tTestimonyAmount.setCustomerId(customerId);
                tTestimonyAmount.setCreateTime(new Date());
                tTestimonyAmountService.insertTTestimonyAmount(tTestimonyAmount);
            } else {
                // 存在则统计数加1
                tTestimonyAmountService.updateTTestimonyAmountByCustomerId(customerId);
            }

            // 获得碎片处理
            // 自己获得碎片 1 最多4
            addCount = tTestimonyFragmentService.addFragmentRecords(customerId, 8);

            // 对方获得碎片 1
            TTestimonyContent tTestimonyContent = tTestimonyContentService.getTTestimonyContent(contentId);
            Long id = tTestimonyContent.getCustomerId();
            tTestimonyFragmentService.addFragmentRecords(id, 7);

            return ResultUtil.customer(ResultEnum.SUCCESS, addCount);
        } else {
            return ResultUtil.customer(ResultEnum.DUPLICATE_REC);
        }
    }

    /**
     * 通过分享点赞
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/sharethumbup")
    public String shareThumbUp(@RequestBody JSONObject obj) {
        // 获取缓存中的顾客编号
        TTestimonyLike tTestimonyLike = new TTestimonyLike();
        long customerId = (long) request.getAttribute("customerId");
        long contentId = (long) Integer.parseInt(obj.getString("contentId"));
        Integer fragmentCount = 0;

        // 不能给自己点赞
        Long custId = tTestimonyContentService.getCustomerIdByContentId(contentId);
        if (customerId == custId) {
            return ResultUtil.customer(ResultEnum.SELFSHARETHUMBUP);
        }
        // 只能助力一次
        TTestimonyHelp chkTestimonyHelp = tTestimonyContentService.getHelpByCustomerIdHelpCustomerId(customerId, custId);
        if (chkTestimonyHelp != null) {
            return ResultUtil.customer(ResultEnum.SELFSHARETHUMBUPDUP);
        }
        // 添加助力log
        TTestimonyHelp tTestimonyHelp = new TTestimonyHelp();
        tTestimonyHelp.setContentId(contentId);
        tTestimonyHelp.setCustomerId(customerId);
        tTestimonyHelp.setHelpCustomerId(custId);
        tTestimonyHelp.setCreateTime(new Date());
        tTestimonyFragmentService.addTTestimonyHelp(tTestimonyHelp);

        if (null == tTestimonyLikeService.getTTestimonyLikeByCustomerIdContentId(customerId, contentId)) {
            tTestimonyLike.setCustomerId(customerId);
            tTestimonyLike.setContentId(contentId);
            tTestimonyLike.setCreateTime(new Date());
            tTestimonyLikeService.insertTTestimonyLike(tTestimonyLike);

            // 判断是否存在
            if (null == tTestimonyAmountService.getTTestimonyAmountByCustomerId(customerId)) {
                // 不存在则新建
                TTestimonyAmount tTestimonyAmount = new TTestimonyAmount();
                tTestimonyAmount.setLikeCount(1);
                tTestimonyAmount.setCustomerId(customerId);
                tTestimonyAmount.setCreateTime(new Date());
                tTestimonyAmountService.insertTTestimonyAmount(tTestimonyAmount);
            } else {
                // 存在则统计数加1
                tTestimonyAmountService.updateTTestimonyAmountByCustomerId(customerId);
            }

            // 获得碎片处理
            // 自己获得碎片 1 最多4
            fragmentCount = tTestimonyFragmentService.addFragmentRecords(customerId, 8);

            // 对方获得碎片 2
            TTestimonyContent tTestimonyContent = tTestimonyContentService.getTTestimonyContent(contentId);
            Long id = tTestimonyContent.getCustomerId();
            tTestimonyFragmentService.addFragmentRecords(id, 6);

            return ResultUtil.customer(ResultEnum.SUCCESS, fragmentCount);
        } else {
            return ResultUtil.customer(ResultEnum.DUPLICATE_REC);
        }
    }

    /**
     * 获取最新10条证言信息
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/getcontentlist")
    public String getContentList() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        return ResultUtil.customer(ResultEnum.SUCCESS,
                tTestimonyContentService.getTestimonyContentVo(customerId));
    }

    /**
     * 获取排名
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/getrank")
    public String getRank() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        Integer rank = tTestimonyAmountService.getTTestimonyLikeRankByCustomerId(customerId);
        if (rank == null) {
            rank = 0;
        }
        return ResultUtil.customer(ResultEnum.SUCCESS, rank);
    }

    /**
     * 获取碎片数
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/getfragment")
    public String getFragment() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        Integer cnt = tTestimonyFragmentService.getTTestimonyFragmentCountByCustomerId(customerId);
        if (cnt == null) {
            cnt = 0;
        }
        return ResultUtil.customer(ResultEnum.SUCCESS, cnt);
    }

    /**
     * 获取个人碎片排名
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/getfragmentrank")
    public String getFragmentRank() {
        // 获取缓存中的顾客编号
        // 还没有cust时会存在问题
        long customerId = (long) request.getAttribute("customerId");

        TestimonyFragmentRankVo testimonyFragmentRankVo =
                tTestimonyFragmentService.getTTestimonyFragmentRankByCustomerId(customerId);
        return ResultUtil.customer(ResultEnum.SUCCESS, testimonyFragmentRankVo);

    }

    /**
     * 获取碎片排名列表
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/getfragmentranklist")
    public String getFragmentRankList() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        List<TestimonyFragmentRankVo> lst =
                tTestimonyFragmentService.getTTestimonyFragmentRankListByCustomerId(customerId);
        return ResultUtil.customer(ResultEnum.SUCCESS, lst);
    }

    /**
     * 助力界面获取信息
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/gethelpthumupinfo")
    public String getHelpThumupInfo(@RequestBody JSONObject obj) {
        // 获取缓存中的顾客编号
        Long customerId = obj.getLong("customerId");
        TestimonyHelpThumbupVo testimonyHelpThumbupVo =
                tTestimonyLikeService.getHelpThumbupInfo(customerId);
        return ResultUtil.customer(ResultEnum.SUCCESS, testimonyHelpThumbupVo);
    }

    /**
     * 助力界面获取信息
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/getfragmentsuminfo")
    public String getFragmentSumInfo() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        List<TestimonyFragmentSumVo> lst =
                tTestimonyFragmentService.getFragmentSumInfo(customerId);
        return ResultUtil.customer(ResultEnum.SUCCESS, lst);
    }

    /**
     * 获取兌礼礼品(C粉盲盒大礼包)
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/getfragmentrewards")
    public String getFragmentRewards(@RequestBody JSONObject obj) {
        // 获取缓存中的顾客编号
        Integer storeId = obj.getInteger("storeId");
        String userName = obj.getString("userName");
        long customerId = (long) request.getAttribute("customerId");
        String openid = (String) request.getAttribute("miniOpenid");
        // 需要转换为公众号openid


        // 判断该用户是否有兑过奖
        if (null == tTestimonyBlindBoxService.getTTestimonyBlindBoxByCustomerId(customerId)) {
            TestimonyFragmentRewardVo testimonyFragmentRewardVo =
                    tTestimonyFragmentService.getFragmentRewards(customerId, storeId, userName);

            //抽中盲盒
            if (null != testimonyFragmentRewardVo) {
                // 获取盲盒密语 store名称
                String storeName = tTestimonyFragmentService.getStoreNameByStoreId(storeId);
                String pwd = testimonyFragmentRewardVo.getProductPwd();
                // 发送模板消息
                motService.testimonyDrawSuccess(customerId, pwd, storeName);

                TTestimonyBlindBoxMotLog tTestimonyBlindBoxMotLog = new TTestimonyBlindBoxMotLog();
                tTestimonyBlindBoxMotLog.setCustomerId(customerId);
                tTestimonyBlindBoxMotLog.setProductId(testimonyFragmentRewardVo.getProductId());
                tTestimonyBlindBoxMotLog.setProductName(testimonyFragmentRewardVo.getProductName());
                tTestimonyBlindBoxMotLog.setProductPwd(testimonyFragmentRewardVo.getProductPwd());
                tTestimonyBlindBoxMotLog.setStoreId(storeId);
                tTestimonyBlindBoxMotLog.setStoreName(storeName);
                tTestimonyBlindBoxMotLog.setCreateTime(new Date());
                tTestimonyFragmentService.saveTestimonyBlindBoxMotLog(tTestimonyBlindBoxMotLog);
            }
            return ResultUtil.customer(ResultEnum.SUCCESS, testimonyFragmentRewardVo);
        } else {
            return ResultUtil.customer(ResultEnum.DUPLICATE_REC);
        }

    }

    /**
     * 这个活动就是每个用户每天有三次发言的机会，别人可以给他点赞，他可以给别人点赞，靠做任务积瓶子，满20个瓶子可以领盲盒。盲盒的库存是提前在各个门店配好的，可以看看blindbox那张表
     * <p>
     * 活动31号23点59分59秒结束
     * <p>
     * 排行榜前15的人 在 t_testimony_fragment_top15 表里 （仅仅有 customer_id
     * <p>
     * top15的礼品没有提前放在门店里
     * <p>
     * 15个人随便他们选哪家店
     * <p>
     * 把他们选的信息存下来
     */
    @PostMapping("/testimony/saveTop15CustomerChooseStore")
    public String saveTop15CustomerChooseStore(@RequestBody JSONObject obj) {
        // 获取缓存中的顾客编号
        Integer storeId = obj.getInteger("storeId");
        String userName = obj.getString("userName");
        long customerId = (long) request.getAttribute("customerId");
        // 需要转换为公众号openid
        String openid = (String) request.getAttribute("miniOpenid");
        log.error("saveTop15CustomerChooseStore openid ====== " + openid);

        TTestimonyFragmentTop15 customerChooser = tTestimonyFragmentService.getFragmentTop15ByCustomerId(customerId);
        if (null != customerChooser) {

            customerChooser.setStoreId(storeId);
            customerChooser.setUserName(userName);

            tTestimonyFragmentService.saveTop15CustomerChooseStore(customerChooser);

            return ResultUtil.customer(ResultEnum.SUCCESS);
        } else {
            return ResultUtil.customer(ResultEnum.DUPLICATE_REC);
        }

    }

    /**
     * 获取是否有权限兌礼【TOP15】
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/getrewardsprivilege")
    public String getRewardsPrivilege() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
//        log.error("customerid ======= " + customerId);
        Integer eligible20 = 0;
        // TOP 15 前端判断标记，  0 没资格， 1 有资格未领取， 2 已提交资料，并预约了门店，3 已核销
        Integer eligibleTop15 = 0;

        // 判断该用户是否有抽取盲盒 是否集齐20碎片
        TTestimonyBlindBox tTestimonyBlindBox =
                tTestimonyBlindBoxService.getTTestimonyBlindBoxByCustomerId(customerId);
        if (null == tTestimonyBlindBox) {
            Integer cnt = tTestimonyFragmentService.getTTestimonyFragmentCountByCustomerId(customerId);
            if (cnt != null) {
                if (cnt >= 20) eligible20 = 1; // 有资格 未申请
            }
        } else {
            // 有抽取盲盒 判断是否已领取
            eligible20 = 2; // 已申请，未领取
            if (null != tTestimonyBlindBox.getDrawed() && tTestimonyBlindBox.getDrawed()) {
                eligible20 = 3; // 已领取
            }
        }

        // 是否 top15 且没有兑奖
        TTestimonyFragmentTop15 tTestimonyFragmentTop15 =
                tTestimonyFragmentService.getFragmentTop15ByCustomerId(customerId);
        if (null != tTestimonyFragmentTop15) {
            // drawed = 1
            if (null != tTestimonyFragmentTop15.getDrawed() && tTestimonyFragmentTop15.getDrawed()) {
                eligibleTop15 = 3; // 已领取
            // store_id 有值了的情况
            } else if (null != tTestimonyFragmentTop15.getStoreId() && tTestimonyFragmentTop15.getStoreId() > 0) {
                eligibleTop15 = 2; // 已提交资料，并预约了门店
            } else {
                eligibleTop15 = 1; // 有资格，未申领
            }
        }

        // 是否有笔记权限领取功能
        Boolean eligibleNote =
                tTestimonyFragmentService.getNotePrivilegeByCustomerId(customerId);

        TestimonyFragmentRewardPrivilegeVo testimonyFragmentRewardPrivilegeVo =
                new TestimonyFragmentRewardPrivilegeVo();
        testimonyFragmentRewardPrivilegeVo.setEligibleNote(eligibleNote);
        testimonyFragmentRewardPrivilegeVo.setEligible20(eligible20);
        testimonyFragmentRewardPrivilegeVo.setEligibleTop15(eligibleTop15);
        return ResultUtil.customer(ResultEnum.SUCCESS, testimonyFragmentRewardPrivilegeVo);
    }

    /**
     * 更新top15领取标识
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/updatetop15flag")
    public String updateTop15Flag() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        tTestimonyFragmentService.updateTop15Flag(customerId);
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 更新top15领取标识（盲盒？）
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/updateblindboxdrawflag")
    public String updateBlindBoxDrawFlag() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        tTestimonyBlindBoxService.updateBlindBoxDrawFlag(customerId);

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 获取盲盒暗语
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/getblindboxpassword")
    public String getBlindBoxPassword() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        String pwd = tTestimonyBlindBoxService.getBlindBoxPassword(customerId);
        return ResultUtil.customer(ResultEnum.SUCCESS, pwd);
    }

    /**
     * 领取KOC碎片
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/getkocrewardsprivilege")
    public String getKocRewardsPrivilege() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        Integer fragmentCount = 0;

        // 判断用户是否是该类别的用户
        if (tTestimonyFragmentService.isKocUser(customerId)) {
            // 获取是否 已领取
            if (null == tTestimonyFragmentService.getFragmentByCustomerIdAndType(customerId, 2)) {

                // 获得碎片处理
                fragmentCount = tTestimonyFragmentService.addFragmentRecords(customerId, 2);

                return ResultUtil.customer(ResultEnum.SUCCESS, fragmentCount);
            } else {
                return ResultUtil.customer(ResultEnum.DUPLICATE_REC);
            }
        } else {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

    }

    /**
     * 领取cust302碎片
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/getcust302rewardsprivilege")
    public String getCust302RewardsPrivilege() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        Integer fragmentCount = 0;

        // 判断用户是否是该类别的用户
        if (tTestimonyFragmentService.isCust302User(customerId)) {
            // 获取是否 已领取
            if (null == tTestimonyFragmentService.getFragmentByCustomerIdAndType(customerId, 3)) {

                // 获得碎片处理
                fragmentCount = tTestimonyFragmentService.addFragmentRecords(customerId, 3);

                return ResultUtil.customer(ResultEnum.SUCCESS, fragmentCount);
            } else {
                return ResultUtil.customer(ResultEnum.DUPLICATE_REC);
            }
        } else {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

    }

    /**
     * 领取笔记碎片
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/getnoterewardsprivilege")
    public String getNoteRewardsPrivilege() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        Integer fragmentCount = 0;

        // 判断用户是否是该类别的用户
        if (tTestimonyFragmentService.hasNote(customerId)) {
            // 获取是否 已领取
            if (null == tTestimonyFragmentService.getFragmentByCustomerIdAndType(customerId, 4)) {

                // 获得碎片处理
                fragmentCount = tTestimonyFragmentService.addFragmentRecords(customerId, 4);

                return ResultUtil.customer(ResultEnum.SUCCESS, fragmentCount);
            } else {
                return ResultUtil.customer(ResultEnum.DUPLICATE_REC);
            }
        } else {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

    }

    /**
     * 领取打卡碎片
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/getdailyrewardsprivilege")
    public String getDailyRewardsPrivilege() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        Integer fragmentCount = 0;

        // 判断用户当天是否已领取
        if (!tTestimonyFragmentService.hasAlreadyDrawed(customerId)) {

            // 获得碎片处理
            fragmentCount = tTestimonyFragmentService.addFragmentRecords(customerId, 5);

            return ResultUtil.customer(ResultEnum.SUCCESS, fragmentCount);
        } else {
            return ResultUtil.customer(ResultEnum.DUPLICATE_REC);
        }

    }

    /**
     * 判断是否已领取打卡碎片
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/hasalreadydraweddaily")
    public String hasAlreadyDrawedDaily() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        Integer fragmentCount = 0;

        // 判断用户当天是否已领取
        Boolean alreadyDrawed = tTestimonyFragmentService.hasAlreadyDrawed(customerId);
        return ResultUtil.customer(ResultEnum.SUCCESS, alreadyDrawed);
    }

    /**
     * 前端传入openid
     * 设定 customerid
     *
     * @return 处理结果
     */
    @PostMapping("/testimony/getCustomerId")
    public String getCustomerId(@RequestBody JSONObject obj) {
        // 获取缓存中的顾客编号
        String openId = obj.getString("openId");

        String customerId = tTestimonyFragmentService.getCustomerIdbyOpenId(openId);
        // 临时添加customerid保存
        request.setAttribute("customerId", customerId);
        return ResultUtil.customer(ResultEnum.SUCCESS, customerId);
    }

    /**
     * 写入访问开始日志
     *
     * @param source 来源
     * @return 成功
     */
    @PostMapping("/testimony/start-view-log/{source}/{page}")
    public String setStartViewLog(
            @PathVariable("source") String source,
            @PathVariable("page") String page
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        long id = tTestimonyFragmentService.insertTestimonyViewLog(customerId, source, page);

        return ResultUtil.customer(ResultEnum.SUCCESS, id);
    }

    /**
     * 写入访问结束日志
     *
     * @param id 来源
     * @return 成功
     */
    @PostMapping("/testimony/end-view-log/{id}")
    public String setEndViewLog(
            @PathVariable("id") long id
    ) {
        tTestimonyFragmentService.setTestimonyViewLog(id);
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 分享日志
     *
     * @return 成功
     */
    @PostMapping("/testimony/share")
    public String addShareLog() {
        long customerId = (long) request.getAttribute("customerId");

        tTestimonyFragmentService.addShareLog(customerId);
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 分享日志
     *
     * @return 成功
     */
    @PostMapping("/testimony/getlastcontent")
    public String getLastontent() {
        long customerId = (long) request.getAttribute("customerId");

        String lastContent = tTestimonyContentService.getLastontent(customerId);
        return ResultUtil.customer(ResultEnum.SUCCESS, lastContent);
    }
}
