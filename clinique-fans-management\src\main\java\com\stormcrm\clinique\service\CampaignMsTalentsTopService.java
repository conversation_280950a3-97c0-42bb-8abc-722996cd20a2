package com.stormcrm.clinique.service;

import com.stormcrm.clinique.domain.CampaignMsTalentsTop;
import com.stormcrm.clinique.vo.Result;

import java.util.List;

/**
 * 新品体验官活动TOP
 *
 * <AUTHOR>
 */
public interface CampaignMsTalentsTopService {

    /**
     * 查询所有活动
     *
     * @return 活动列表
     */
    List<CampaignMsTalentsTop> getAll();

    /**
     * 查询信息
     *
     * @param id 自动编号
     * @return 信息
     */
    CampaignMsTalentsTop getById(long id);

    /**
     * 保存
     *
     * @param campaignMsTalentsTop 信息
     * @return 影响的行数
     */
    Result save(CampaignMsTalentsTop campaignMsTalentsTop);

    /**
     * 更新
     *
     * @param campaignMsTalentsTop 信息
     * @return 影响的行数
     */
    Result update(CampaignMsTalentsTop campaignMsTalentsTop);

}
