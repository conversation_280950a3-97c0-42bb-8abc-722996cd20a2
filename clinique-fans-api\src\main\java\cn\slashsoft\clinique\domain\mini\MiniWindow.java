package cn.slashsoft.clinique.domain.mini;

import lombok.Data;

import java.util.Date;

/**
 * 小程序弹窗
 * <AUTHOR>
 */
@Data
public class MiniWindow {

    private Long id;
    //标题
    private String title;
    //小标题
    private String subTitle;
    //内容
    private String desc;
    //按钮文字
    private String buttonText = "确定";

    //按钮跳转页面
    private String buttonPage;
    
    //显示action id
    private int showActionId;
    //手动关闭action id
    private int closeActionId;
    //点击按钮action id
    private int buttonActionId;

    //类型 1浮窗 2 弹窗
    private int type = 2;
    

    //自动关闭时间  0 必须手动关闭
    private int timeout;

    //是否显示 关闭按钮  timeout 0 必显示
    private Boolean showClose;

    //状态
    private Boolean status;
    private Date createTime;

}
