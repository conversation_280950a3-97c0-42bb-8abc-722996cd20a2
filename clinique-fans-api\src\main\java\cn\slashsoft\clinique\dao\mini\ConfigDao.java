package cn.slashsoft.clinique.dao.mini;

import cn.slashsoft.clinique.domain.mini.Config;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 配置相关
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface ConfigDao {

    /**
     * 读取所有文案配置
     *
     * @return 文案配置
     */
    @Select("SELECT " +
            "   `placeholder`," +
            "   `description` " +
            "FROM " +
            "   `config`")
    List<Config> getConfig();

}
