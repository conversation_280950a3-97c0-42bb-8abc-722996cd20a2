package cn.slashsoft.clinique.service.mini.impl;

import cn.slashsoft.clinique.dao.mini.TTestimonyProductDao;
import cn.slashsoft.clinique.domain.mini.TTestimonyProduct;
import cn.slashsoft.clinique.service.mini.TTestimonyProductService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
* <p>
    * 盲盒店铺库存设置
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
@Service
public class TTestimonyProductServiceImpl implements TTestimonyProductService {

    @Resource
    private TTestimonyProductDao tTestimonyProductDao;

    @Override
    public void insertTTestimonyProduct(TTestimonyProduct tTestimonyProduct) {
        tTestimonyProductDao.insertTTestimonyProduct(tTestimonyProduct);
    }

    @Override
    public void updateTTestimonyProduct(TTestimonyProduct tTestimonyProduct) {
        tTestimonyProductDao.updateTTestimonyProduct(tTestimonyProduct);
    }

    @Override
    public TTestimonyProduct getTTestimonyProduct(Long id) {
        return tTestimonyProductDao.getTTestimonyProduct(id);
    }
}
