package com.stormcrm.clinique.service.campaign.ugc.impl;

import com.stormcrm.clinique.dao.campaign.ugc.CampaignUgcDao;
import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgc;
import com.stormcrm.clinique.service.campaign.ugc.CampaignUgcService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.vo.Result;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Service
public class CampaignUgcServiceImpl implements CampaignUgcService {

    private final CampaignUgcDao campaignUgcDao;

    public CampaignUgcServiceImpl(CampaignUgcDao campaignUgcDao) {
        this.campaignUgcDao = campaignUgcDao;
    }

    /**
     * 查询所有活动
     *
     * @return 活动列表
     */
    @Override
    public List<CampaignUgc> getAll() {
        return campaignUgcDao.getAll();
    }

    /**
     * 查询所有活动列表的记录数
     *
     * @return 记录数
     */
    @Override
    public int getAllCount() {
        return campaignUgcDao.getAllCount();
    }

    /**
     * 查询
     *
     * @param id 自动编号
     * @return 胸章信息
     */
    @Override
    public CampaignUgc getById(long id) {
        return campaignUgcDao.getById(id);
    }

    /**
     * 保存
     *
     * @param campaign 信息
     * @return 影响的行数
     */
    @Override
    public Result save(CampaignUgc campaignUgc) {
        campaignUgcDao.save(campaignUgc);
        return ResultUtil.success("增加成功!");
    }

    /**
     * 更新
     *
     * @param campaign 信息
     * @return 影响的行数
     */
    @Override
    public Result update(CampaignUgc campaignUgc) {
        campaignUgcDao.update(campaignUgc);
        return ResultUtil.success("编辑成功!");
    }

    /**
     * 上架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Override
    public int upper(long id) {
        return campaignUgcDao.upper(id);
    }

    /**
     * 下架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Override
    public int lower(long id) {
        return campaignUgcDao.lower(id);
    }

    /**
     * 逻辑删除
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Override
    public int del(long id) {
        return campaignUgcDao.del(id);
    }

}
