package com.stormcrm.clinique.domain.tag;

import lombok.Data;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 *
 * <AUTHOR>
 */

@Data
public class TagTask {
    //id
    int id;
    String name;
    String desc;

    int labelId;

    //1卡券2短信3模板消息4订阅通知
    int target;
    int targetId;
    //定时周期， 1 天 2周 3 月 4 年 5 小时 6 分 7 秒
    int cycleType;
    //定时间隔
    int cycleInterval;
    //指定周期
    String cycleValue;

    int runType;
    //定时模式， 执行时间 （保存完整日期时间，但是只使用时间。当cycleType<5时，使用此时间）
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    Date runTime;
    
    // 指定时间执行模式，指定的执行时间。 定时模式，用作开始时间
    @DateTimeFormat(pattern = "yyyy-MM-dd")
	Date actionStartTime;
	//结束时间
    @DateTimeFormat(pattern = "yyyy-MM-dd")
	Date actionEndTime;
	//执行次数 当结束时间不为null 忽略此值
	int actionCount;
		
	int user;
	Date createTime;
	Date updateTime;

	//1设计中2待审批3进行中
	int progress;
	int status;
	
	// For VO
	String labelName;
	int labelCount;
	String targetName;
	public String getTargetName() {
		String name = "";
		switch(this.target){
			case 1:
				name = "卡券";
				break;
			case 2:
				name = "短信";
				break;
			case 3:
				name = "微信模板消息";
				break;
			case 4:
				name = "微信订阅通知";
				break;
			default:
				name = "未知";
		}
		return name;
	}
	String runName;
	public String getRunName() {
		String name = "";
		switch(this.runType){
			case 1:
				name = "指定日期";
				break;
			case 2:
				name = "定时任务";
				break;
			default:
				name = "未知";
		}
		return name;
	}
	String progressName;	
	public String getProgressName() {
		String name = "";
		switch(this.progress){
			case 1:
				name = "设计中";
				break;
			case 2:
				name = "待审批";
				break;
			case 3:
				name = "进行中";
				break;
			default:
				name = "未知";
		}
		return name;
	}
}

