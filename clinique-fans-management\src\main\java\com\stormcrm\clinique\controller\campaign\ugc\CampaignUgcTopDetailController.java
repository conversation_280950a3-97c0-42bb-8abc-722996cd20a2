package com.stormcrm.clinique.controller.campaign.ugc;

import com.stormcrm.clinique.domain.CampaignMsTalentsTop;
import com.stormcrm.clinique.domain.CampaignMsTalentsTopDetail;
import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgc;
import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgcTop;
import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgcTopDetail;
import com.stormcrm.clinique.service.CampaignMsTalentsTopDetailService;
import com.stormcrm.clinique.service.CampaignMsTalentsTopService;
import com.stormcrm.clinique.service.campaign.ugc.CampaignUgcService;
import com.stormcrm.clinique.service.campaign.ugc.CampaignUgcTopDetailService;
import com.stormcrm.clinique.service.campaign.ugc.CampaignUgcTopService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.util.VerifyUtil;
import com.stormcrm.clinique.vo.Result;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("campaign-ugc-top-detail")
public class CampaignUgcTopDetailController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;

    private final CampaignUgcService campaignUgcService;
    private final CampaignUgcTopService campaignUgcTopService;
    private final CampaignUgcTopDetailService campaignUgcTopDetailService;

    public CampaignUgcTopDetailController(CampaignUgcService campaignUgcService, CampaignUgcTopService campaignUgcTopService, CampaignUgcTopDetailService campaignUgcTopDetailService) {
        this.campaignUgcTopDetailService = campaignUgcTopDetailService;
        this.campaignUgcTopService = campaignUgcTopService;
        this.campaignUgcService = campaignUgcService;
    }

    /**
     * 页面模版
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_TOP')")
    @GetMapping("/{campaignId}/{topId}")
    public String index(
            @PathVariable("campaignId") long campaignId,
            @PathVariable("topId") long topId,
            Model model
    ) {

        CampaignUgc campaignUgc = campaignUgcService.getById(campaignId);
        if(null == campaignUgc){
            return "m/fans/common/empty";
        }

        CampaignUgcTop campaignUgcTop = campaignUgcTopService.getById(topId);
        if(null == campaignUgcTop){
            return "m/fans/common/empty";
        }

        model.addAttribute("staticDomain", staticDomain);
        model.addAttribute("campaignId", campaignId);
        model.addAttribute("topId", topId);
        model.addAttribute("campaignUgc", campaignUgc);
        model.addAttribute("campaignUgcTop", campaignUgcTop);
        return "m/fans/campaign-ugc-top-detail/index";
    }

    /**
     * 查询所有
     *
     * @return 列表
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_TOP')")
    @PostMapping("get-all/{topId}")
    @ResponseBody
    public String getAll(
            @PathVariable("topId") long topId
    ) {

        List<CampaignUgcTopDetail> campaignUgcTopDetailList = campaignUgcTopDetailService.getAll(topId);

        JSONObject mata = new JSONObject();
        mata.put("page", 1);
        mata.put("pages", 1);
        mata.put("perpage", -1);
        mata.put("total", campaignUgcTopDetailList.size());
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("mata", mata);
        result.put("data", campaignUgcTopDetailList);

        return JSONObject.toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }

    /**
     * 增加模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_TOP')")
    @RequestMapping("add/{topId}")
    public String add(
            @PathVariable("topId") long topId,
            Model model
    ) {
        model.addAttribute("staticDomain", staticDomain);
        model.addAttribute("topId", topId);
        return "m/fans/campaign-ugc-top-detail/add";
    }

    /**
     * 新增
     *
     * @param topId     TOP5编号
     * @param id    达人官编号
     * @param sort 排序
     * @return 结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_TOP')")
    @PostMapping(value = "add-submit/{topId}", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String addSubmit(
            @PathVariable("topId") long topId,
            @RequestParam(value = "form-detail-id", required = false) long detailId,
            @RequestParam(value = "form-sort", required = false) short sort
    ) {

        // 验证
        if (!VerifyUtil.required(detailId)) {
            return ResultUtil.verifyFailToJson("form-detail-id", "这是必填字段");
        }
        if (!VerifyUtil.required(sort)) {
            return ResultUtil.verifyFailToJson("form-sort", "这是必填字段");
        }

        // 生成对象
        CampaignUgcTopDetail campaignUgcTopDetail = new CampaignUgcTopDetail();
        campaignUgcTopDetail.setTopId(topId);
        campaignUgcTopDetail.setDetailId(detailId);
        campaignUgcTopDetail.setSort(sort);

        // 传到Service服务中保存
        Result result = campaignUgcTopDetailService.save(campaignUgcTopDetail);

        // 返回绍果给前端
        return JSONObject.toJSONString(result);
    }

    /**
     * 编辑模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_TOP')")
    @GetMapping("edit/{id}")
    public String edit(@PathVariable("id") long id, Model model) {
        model.addAttribute("staticDomain", staticDomain);
        // 读数据库
        CampaignUgcTopDetail campaignUgcTopDetail = campaignUgcTopDetailService.getById(id);
        if (null == campaignUgcTopDetail) {
            return "m/fans/common/empty";
        }
        model.addAttribute("id", id);
        model.addAttribute("campaignUgcTopDetail", campaignUgcTopDetail);
        return "m/fans/campaign-ugc-top-detail/edit";
    }

    /**
     * 编辑
     *
     * @param detailId     TOP5 DETAIL 编号
     * @param id    达人官编号
     * @param sort 排序
     * @return 结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_TOP')")
    @PostMapping(value = "edit-submit/{id}", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String editSubmit(
            @PathVariable("id") long id,
            @RequestParam(value = "form-detail-id", required = false) long detailId,
            @RequestParam(value = "form-sort", required = false) short sort
    ) {

        // 验证
        if (!VerifyUtil.required(detailId)) {
            return ResultUtil.verifyFailToJson("form-detail-id", "这是必填字段");
        }

        // 验证
        if (!VerifyUtil.required(sort)) {
            return ResultUtil.verifyFailToJson("form-sort", "这是必填字段");
        }

        // 生成对象
        CampaignUgcTopDetail campaignUgcTopDetail = new CampaignUgcTopDetail();
        campaignUgcTopDetail.setId(id);
        campaignUgcTopDetail.setDetailId(detailId);
        campaignUgcTopDetail.setSort(sort);

        // 传到Service服务中保存
        Result result = campaignUgcTopDetailService.update(campaignUgcTopDetail);

        // 返回绍果给前端
        return JSONObject.toJSONString(result);
    }

    /**
     * 逻辑删除
     *
     * @param detailId 编号
     * @return 影响的行数
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_UGC_TOP')")
    @GetMapping("del/{id}")
    @ResponseBody
    public String del(@PathVariable("id") long id) {

        if (0 == campaignUgcTopDetailService.delete(id)) {
            return ResultUtil.failToJson();
        }

        return ResultUtil.successToJson();

    }

}
