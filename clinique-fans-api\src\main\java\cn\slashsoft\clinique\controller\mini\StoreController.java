package cn.slashsoft.clinique.controller.mini;

import cn.slashsoft.clinique.domain.mini.Customer;
import cn.slashsoft.clinique.domain.mini.StoreExchangeLog;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.enums.StoreExchangeTypeEnum;
import cn.slashsoft.clinique.service.mini.*;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.util.IntegerUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.vo.Result;
import cn.slashsoft.clinique.vo.mini.*;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 商城相关控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class StoreController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final PointService pointService;
    private final StoreService storeService;
    private final RankingService rankingService;
    private final CustomerService customerService;
    private final OutsideService outsideService;
    private final SnsService snsService;
    private final MotService motService;
    private final StringRedisTemplate stringRedisTemplate;

    public StoreController(PointService pointService, StoreService storeService, CustomerService customerService, RankingService rankingService, StringRedisTemplate stringRedisTemplate, SnsService snsService, MotService motService, OutsideService outsideService) {
        this.pointService = pointService;
        this.storeService = storeService;
        this.customerService = customerService;
        this.rankingService = rankingService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.snsService = snsService;
        this.motService = motService;
        this.outsideService = outsideService;
    }
    
    /**
     * 是否有线下购买记录
     *
     * 
     */
    @GetMapping("/no-store-record")
    public String noRealStoreData() {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        String phone = customerService.getCustomerPhoneNumberById(customerId);
        if(StringUtil.isNullOrEmpty(phone)) {
        	return ResultUtil.customer(ResultEnum.SUCCESS);
        }
    	if(outsideService.getRecord(phone)) {
    		return ResultUtil.customer(ResultEnum.FAILED);
    	}
    	return ResultUtil.customer(ResultEnum.SUCCESS);
    }
    /**
     * 获取商城首页页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/store/get-data")
    public String getStoreData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        int rankingStatus = IntegerUtil.parseInt(stringRedisTemplate.opsForValue().get("configRankingStatus"));

        StoreVo storeVo = new StoreVo();
        storeVo.setPointTotal(pointService.getRemainingPointTotal(customerId));
        storeVo.setRankingStatus(rankingStatus);
        // 排名期间
        if (1 == rankingStatus || 2 == rankingStatus) {
            Customer customer = rankingService.getCustomerRankingById(customerId);
            storeVo.setRanking(customer.getRanking());
            storeVo.setRankingGiftList(rankingService.getRankingGiftList(customerId));
        }
        storeVo.setStoreGiftList(storeService.getStoreGiftList(customerId));

        return ResultUtil.customer(ResultEnum.SUCCESS, storeVo);

    }

    /**
     * 获取商城兑换页页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/store/get-exchange-data/{id}")
    public String getStoreExchangeData(
            @PathVariable("id") long id
    ) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        StoreExchangeVo storeExchangeVo = new StoreExchangeVo();
        storeExchangeVo.setPointTotal(pointService.getRemainingPointTotal(customerId));
        storeExchangeVo.setStoreAreaList(storeService.getStoreAreaList());
        storeExchangeVo.setStoreAreaPlaceList(storeService.getStoreAreaPlaceList());
        storeExchangeVo.setStoreGift(storeService.getStoreGiftById(id, customerId));

        return ResultUtil.customer(ResultEnum.SUCCESS, storeExchangeVo);

    }

    /**
     * 获取商城兑换页页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/store/get-exchange-express-data/{id}")
    public String getStoreExchangeExpressData(
            @PathVariable("id") long id
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        StoreExchangeExpressVo storeExchangeExpressVo = new StoreExchangeExpressVo();
        storeExchangeExpressVo.setPointTotal(pointService.getRemainingPointTotal(customerId));
        storeExchangeExpressVo.setCustomerAddressList(customerService.getCustomerAddressByCustomerId(customerId));
        storeExchangeExpressVo.setStoreGift(storeService.getStoreGiftById(id, customerId));

        return ResultUtil.customer(ResultEnum.SUCCESS, storeExchangeExpressVo);
    }

    /**
     * 立即兑换
     *
     * @return 页面数据
     */
    @PostMapping("/store/exchange-for-pickup")
    public String exchangeForPlace(
            @RequestBody Exchange exchange
    ) {

        if (0 >= exchange.getId() || StringUtil.isNullOrEmpty(exchange.getAreaId()) || 0 >= exchange.getAreaPlaceId()) {
            return ResultUtil.customer(ResultEnum.PARAM_ERROR, "参数错误");
        }

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        String unionid = (String) request.getAttribute("unionid");
        exchange.setCustomerId(customerId);

        // 1,成功； 2,礼品不存在或已下架; 3,库存不足; 4,系统繁忙;
        Result result = storeService.exchange(unionid, exchange, StoreExchangeTypeEnum.PICKUP);
        if (1 != result.getCode()) {
            return ResultUtil.customer(ResultEnum.FAILED, result.getMessage());
        }

        // 发送验证码
        // TODO: 暂时取消验证码功能
        // snsService.exchangePickupSend(customerId, exchange.getAreaPlaceId());

        // 发送MOT
        motService.exchangePickupSend(customerId, exchange.getId(), exchange.getAreaPlaceId());

        // 兑礼日志
        StoreExchangeLog storeExchangeLog = new StoreExchangeLog();
        storeExchangeLog.setOpenid((String) request.getAttribute("miniOpenid"));
        storeExchangeLog.setStoreGiftId(exchange.getId());
        storeExchangeLog.setStep((short) 3);
        storeService.setStoreExchangeLog(storeExchangeLog);

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 立即兑换
     *
     * @return 页面数据
     */
    @PostMapping("/store/exchange-for-express")
    public String exchangeForExpress(
            @RequestBody Exchange exchange
    ) {

        if (0 >= exchange.getId() || 0 >= exchange.getCustomerAddressId()) {
            return ResultUtil.customer(ResultEnum.PARAM_ERROR, "参数错误");
        }

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        String unionid = (String) request.getAttribute("unionid");
        exchange.setCustomerId(customerId);

        // 1,成功； 2,礼品不存在或已下架; 3,库存不足; 4,系统繁忙;
        Result result = storeService.exchange(unionid, exchange, StoreExchangeTypeEnum.EXPRESS);
        if (1 != result.getCode()) {
            return ResultUtil.customer(ResultEnum.FAILED, result.getMessage());
        }

        // 发送验证码
        // TODO: 暂时取消验证码功能
        // snsService.exchangeExpress(customerId);

        // 发送MOT
        motService.exchangeExpress(customerId, exchange.getId());

        // 兑礼日志
        StoreExchangeLog storeExchangeLog = new StoreExchangeLog();
        storeExchangeLog.setOpenid((String) request.getAttribute("miniOpenid"));
        storeExchangeLog.setStoreGiftId(exchange.getId());
        storeExchangeLog.setStep((short) 3);
        storeService.setStoreExchangeLog(storeExchangeLog);

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 获取商城兑换明细页页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/store/get-detail-data")
    public String getStoreDetailData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        StoreDetailVo storeDetailVo = new StoreDetailVo();
        storeDetailVo.setStoreExchangeList(storeService.getStoreExchangeByCustomer(customerId));

        return ResultUtil.customer(ResultEnum.SUCCESS, storeDetailVo);

    }

    /**
     * 写入兑换日志
     *
     * @param storeExchangeLog 日志
     * @return 处理结果
     */
    @PostMapping("/store/set-store-exchange-log")
    public String setStoreExchangeLog(
            @RequestBody StoreExchangeLog storeExchangeLog
    ) {

        storeExchangeLog.setOpenid((String) request.getAttribute("miniOpenid"));
        storeService.setStoreExchangeLog(storeExchangeLog);

        return ResultUtil.customer(ResultEnum.SUCCESS);

    }

    /**
     * 写入商城列表页访问来源的日志
     *
     * @param source 来源
     * @return 处理结果
     */
    @PostMapping("/store/set-store-view-log/{source}")
    @ResponseBody
    public String setStoreViewLog(
            @PathVariable("source") String source
    ) {
        storeService.setViewLog(source, (String) request.getAttribute("miniOpenid"));
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

}
