package cn.slashsoft.clinique.domain.mini;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 盲盒申领
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TTestimonyKocUserList implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private Long customerId;
    private String openId;
    private Date createTime;
    private Date updateTime;

}
