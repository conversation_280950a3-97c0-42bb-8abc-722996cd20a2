package cn.slashsoft.clinique.service.mini;

import cn.slashsoft.clinique.domain.mini.TTestimonyBlindBox;

/**
* <p>
    * 盲盒申领
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
public interface TTestimonyBlindBoxService {
    void insertTTestimonyBlindBox(TTestimonyBlindBox tTestimonyBlindBox);
    void updateTTestimonyBlindBox(TTestimonyBlindBox tTestimonyBlindBox);
    TTestimonyBlindBox getTTestimonyBlindBox(Long id);
    TTestimonyBlindBox getTTestimonyBlindBoxByCustomerId(Long customerId);
    /**
     * 更新盲盒领取状态
     * @param customerId
     */
    void updateBlindBoxDrawFlag(long customerId);

    /**
     * 获取已抽取盲盒暗语
     * @param customerId
     * @return
     */
    String getBlindBoxPassword(long customerId);

    /**
     * 添加抽奖的log记录
     * @param tTestimonyBlindBox
     */
    void insertTTestimonyBlindBoxLog(TTestimonyBlindBox tTestimonyBlindBox);
}
