package cn.slashsoft.clinique.vo.campaign.ugc;

import cn.slashsoft.clinique.domain.campaign.ugc.KocDetailImage;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Data
public class CampaignUgcMineVo {
	
	private String title;

    private Date startTime;
    private Date endTime;

    private String shareTitle;
    private String shareImageUrl;

    private String nickName;
    private String avatarUrl;

    private Short status;
    private Date uploadTime;
    private Date examineTime;

    private List<KocDetailImage> imageList;

}
