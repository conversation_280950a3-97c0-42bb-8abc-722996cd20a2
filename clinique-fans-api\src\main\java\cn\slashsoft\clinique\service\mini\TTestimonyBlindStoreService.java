package cn.slashsoft.clinique.service.mini;


import cn.slashsoft.clinique.domain.mini.TTestimonyBlindStore;

/**
* <p>
    * 盲盒店铺库存设置
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
public interface TTestimonyBlindStoreService {
    void insertTTestimonyBlindStore(TTestimonyBlindStore tTestimonyBlindStore);
    void updateTTestimonyBlindStore(TTestimonyBlindStore tTestimonyBlindStore);
    TTestimonyBlindStore getTTestimonyBlindStore(Long id);
    void subtractTTestimonyBlindStore(Integer storeId, Integer productId);

    /**
     * 获取指定商品库存
     * @param storeId
     * @param productId
     * @return
     */
    Integer getTTestimonyBlindStoreAmount(Integer storeId, Integer productId);
}
