package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.domain.campaign.*;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.service.campaign.CampaignLaserTalentsService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.campaign.CampaignLaserTalentsTopVo;
import cn.slashsoft.clinique.vo.campaign.CampaignLaserTalentsVo;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * Laser Focus 达人榜
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class CampaignLaserTalentsController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final CampaignLaserTalentsService campaignLaserTalentsService;
    private final StringRedisTemplate stringRedisTemplate;
    private final WechatService wechatService;

    public CampaignLaserTalentsController(CampaignLaserTalentsService campaignLaserTalentsService, StringRedisTemplate stringRedisTemplate, WechatService wechatService) {
        this.campaignLaserTalentsService = campaignLaserTalentsService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.wechatService = wechatService;
    }

    /**
     * 配置活动信息
     *
     * @param verification 验证
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 处理结果
     * https://clinique.stormcrm.com/api/campaign-config/laser-talents/20-23-04-00-28/2020-06-01-00-00-00/2020-06-30-23-59-59
     * http://127.0.0.1:9121/api/campaign-config/laser-talents/20-15-07-07-31/2020-07-01-00-00-00/2020-08-31-23-59-59
     */
    @GetMapping("/campaign-config/laser-talents/{verification}/{startTime}/{endTime}")
    public String setConfig(
            @PathVariable("verification") String verification,
            @PathVariable("startTime") String startTime,
            @PathVariable("endTime") String endTime
    ) {
        if (DateUtil.parseVerification(new Date()).equals(verification)) {
            stringRedisTemplate.opsForValue().set("campaignLaserTalentsStartTime", DateUtil.parseString(DateUtil.valueOf(startTime, "yyyy-MM-dd-HH-mm-ss")));
            stringRedisTemplate.opsForValue().set("campaignLaserTalentsEndTime", DateUtil.parseString(DateUtil.valueOf(endTime, "yyyy-MM-dd-HH-mm-ss")));
            return "success";
        }
        return "fail";
    }

    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-laser-talents/get-data")
    public String getData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 获取美白达人官活动参与信息
        CampaignLaserTalents campaignLaserTalents = campaignLaserTalentsService.getCampaignLaserTalents(customerId);

        CampaignLaserTalentsVo campaignLaserTalentsVo = new CampaignLaserTalentsVo();

        campaignLaserTalentsVo.setStartTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignLaserTalentsStartTime")));
        campaignLaserTalentsVo.setEndTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignLaserTalentsEndTime")));

        campaignLaserTalentsVo.setAlert(campaignLaserTalents.getAlert());
        campaignLaserTalentsVo.setStatus(campaignLaserTalents.getStatus());

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignLaserTalentsVo);

    }

    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-laser-talents/get-alert-data")
    public String getAlertData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 获取美白达人官活动参与信息
        CampaignLaserTalents campaignLaserTalents = campaignLaserTalentsService.getCampaignLaserTalentsAlert(customerId);
        if (null == campaignLaserTalents){
            return ResultUtil.customer(ResultEnum.SUCCESS);

        }

        CampaignLaserTalentsVo campaignLaserTalentsVo = new CampaignLaserTalentsVo();
        campaignLaserTalentsVo.setAlert(campaignLaserTalents.getAlert());
        campaignLaserTalentsVo.setStatus(campaignLaserTalents.getStatus());

        // 首次变更状态时，设置提醒状态
        if (campaignLaserTalents.getAlert()) {
            campaignLaserTalentsService.updateCampaignLaserTalentsAlert(customerId);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignLaserTalentsVo);

    }

    /**
     * 图片上传
     *
     * @param file 图片
     * @return 处理结果
     */
    @PostMapping("/campaign-laser-talents/upload")
    public String upload(@RequestParam("file") MultipartFile file, @RequestParam("index") int index) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        if (!campaignLaserTalentsService.upload(customerId, file)) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, index);

    }

    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-laser-talents-mine/get-data")
    public String getMineData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 获取美白达人官活动参与信息
        CampaignLaserTalents campaignLaserTalents = campaignLaserTalentsService.getCampaignLaserTalents(customerId);
        if(campaignLaserTalents == null) {
        	return ResultUtil.customer(ResultEnum.SUCCESS, 0);
        }

        CampaignLaserTalentsVo campaignLaserTalentsVo = new CampaignLaserTalentsVo();

        campaignLaserTalentsVo.setStartTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignLaserTalentsStartTime")));
        campaignLaserTalentsVo.setEndTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignLaserTalentsEndTime")));

        campaignLaserTalentsVo.setStatus(campaignLaserTalents.getStatus());
        campaignLaserTalentsVo.setUploadTime(campaignLaserTalents.getUploadTime());
        campaignLaserTalentsVo.setExamineTime(campaignLaserTalents.getExamineTime());

        List<CampaignLaserTalentsImage> campaignLaserTalentsImageList = campaignLaserTalentsService.getCampaignLaserTalentsImageList(campaignLaserTalents.getId());
        campaignLaserTalentsVo.setTalentsImageList(campaignLaserTalentsImageList);

        Wechat wechat = wechatService.getWechatInfoByCustomerId(customerId);
        campaignLaserTalentsVo.setNickName(wechat.getNickName());
        campaignLaserTalentsVo.setAvatarUrl(wechat.getAvatarUrl());

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignLaserTalentsVo);

    }

    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-laser-talents-top/get-data")
    public String getTopData() {

        // 获取美白达人官活动TOP信息
        CampaignLaserTalentsTop campaignLaserTalentsTop = campaignLaserTalentsService.getCampaignLaserTalentsTop();
        if (null == campaignLaserTalentsTop) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        CampaignLaserTalentsTopVo campaignLaserTalentsTopVo = new CampaignLaserTalentsTopVo();
        campaignLaserTalentsTopVo.setStage(campaignLaserTalentsTop.getStage());
        campaignLaserTalentsTopVo.setTalentsTopDetailList(campaignLaserTalentsService.getCampaignLaserTalentsTopDetailList(campaignLaserTalentsTop.getId()));
        campaignLaserTalentsTopVo.setTalentsTopDetailImageList(campaignLaserTalentsService.getCampaignLaserTalentsTopDetailImageList(campaignLaserTalentsTop.getId()));

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignLaserTalentsTopVo);

    }
    @PostMapping("/campaign-laser-talents/skin-type/{skinType}")
    public String setCampaignLaserTalentsSkinType(
            @PathVariable("skinType") short skinType
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        campaignLaserTalentsService.setCampaignLaserTalentsSkinType(customerId, skinType);
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 写入访问开始日志
     *
     * @param source 来源
     * @return 成功
     */
    @PostMapping("/campaign-laser-talents/start-view-log/{source}/{page}")
    public String setStartViewLog(
            @PathVariable("source") String source,
            @PathVariable("page") String page
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        long id = campaignLaserTalentsService.insertCampaignLaserTalentsViewLog(customerId, source, page);

        return ResultUtil.customer(ResultEnum.SUCCESS, id);
    }

    /**
     * 写入访问结束日志
     *
     * @param id 来源
     * @return 成功
     */
    @PostMapping("/campaign-laser-talents/end-view-log/{id}")
    public String setEndViewLog(
            @PathVariable("id") long id
    ) {
        campaignLaserTalentsService.setCampaignLaserTalentsViewLog(id);
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 写入达人傍访问日志
     *
     * @param stage       达人榜编号
     * @param topDetailId 达人榜明细编号
     * @return 成功
     */
    @PostMapping("/campaign-laser-talents/top-detail-log/{stage}/{topDetailId}")
    public String setTalentsTopDetailLog(
            @PathVariable("stage") short stage,
            @PathVariable("topDetailId") long topDetailId
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        CampaignLaserTalentsTopDetailLog campaignLaserTalentsTopDetailLog = new CampaignLaserTalentsTopDetailLog();
        campaignLaserTalentsTopDetailLog.setCustomerId(customerId);
        campaignLaserTalentsTopDetailLog.setStage(stage);
        campaignLaserTalentsTopDetailLog.setTalentsTopDetailId(topDetailId);
        campaignLaserTalentsService.insertCampaignLaserTalentsTopDetailLog(campaignLaserTalentsTopDetailLog);

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 写入达人傍访问日志
     *
     * @param stage       达人榜编号
     * @param topDetailId 达人榜明细编号
     * @return 成功
     */
    @PostMapping("/campaign-laser-talents/top-detail-image-log/{stage}/{topDetailId}/{imageId}/{type}")
    public String setTalentsTopDetailImageLog(
            @PathVariable("stage") short stage,
            @PathVariable("topDetailId") long topDetailId,
            @PathVariable("imageId") long imageId,
            @PathVariable("type") short type
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        CampaignLaserTalentsTopDetailImageLog campaignLaserTalentsTopDetailImageLog = new CampaignLaserTalentsTopDetailImageLog();
        campaignLaserTalentsTopDetailImageLog.setCustomerId(customerId);
        campaignLaserTalentsTopDetailImageLog.setStage(stage);
        campaignLaserTalentsTopDetailImageLog.setTalentsTopDetailId(topDetailId);
        campaignLaserTalentsTopDetailImageLog.setTalentsImageId(imageId);
        campaignLaserTalentsTopDetailImageLog.setType(type);
        campaignLaserTalentsService.insertCampaignLaserTalentsTopDetailImageLog(campaignLaserTalentsTopDetailImageLog);

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

}
