package cn.slashsoft.clinique.controller.api;


import java.util.Date;
import java.util.HashMap;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;

import cn.slashsoft.clinique.api.service.OpenApiService;
import cn.slashsoft.clinique.domain.api.CouponApiLog;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.domain.official.WechatFriend;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.CactiveService;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.api.GetOpenIdByUnionIdVo;
import io.netty.util.internal.StringUtil;

/**
 * 接口
 * <AUTHOR> Berg
 */
@RestController
@RequestMapping("/open-api")
public class ApiController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    
	@Resource
	private CactiveService caService;
    @Resource
    private OpenApiService openApiService;
	
    private final WechatService wechatService;

    public ApiController(WechatService wechatService) {
        this.wechatService = wechatService;
    }

    @GetMapping("/getOpenIdByUnionId")
    public String getOpenIdByUnionId(
            @RequestParam("unionId") String unionid
    ){

        Wechat wechat = wechatService.getWechatByUnionId(unionid);

        if(null == wechat){
            return ResultUtil.customerToJson(-1, "获取数据失败");
        }

        GetOpenIdByUnionIdVo getOpenIdByUnionIdVo = new GetOpenIdByUnionIdVo();
        getOpenIdByUnionIdVo.setMobile(wechat.getPhoneNumber());
        getOpenIdByUnionIdVo.setOpenId(wechat.getWechatOfficialOpenid());
        getOpenIdByUnionIdVo.setAvatarUrl(wechat.getAvatarUrl());

        return ResultUtil.customer(ResultEnum.SUCCESS, getOpenIdByUnionIdVo);

    }
    /**
     * 是否已经关注公众号
     * <AUTHOR>
     */
    @GetMapping("/is-friend")
    public String isFriend(
            @RequestParam("unionId") String unionid
            ) {

    	if(null == unionid || StringUtil.isNullOrEmpty(unionid)) {
    		return ResultUtil.customer(ResultEnum.FAILED,"");
    	}
    	
        WechatFriend wf = wechatService.getWechatFriend(unionid);
        if(null != wf && wf.getStatus()==1) {
            return ResultUtil.customer(ResultEnum.SUCCESS, wf.getOpenid());
        }
        return ResultUtil.customer(ResultEnum.FAILED,"");
    }

    /**
     * 是否已经注册C粉圈
     * <AUTHOR>
     */
    @GetMapping("/check-registered-by-unionid")
    public String getByUnionId(
    		@RequestParam("unionid") String unionid,
            @RequestParam("sign") String code,
            @RequestParam("secret") String secret,
            @RequestParam("source") String from,
            @RequestParam("time") String time
    ){

    	HashMap<String, String> params = new HashMap<String,String>();
    	params.put("sign", code);
    	params.put("secret", secret);
    	params.put("source", from);
    	params.put("time", time); 
    	params.put("unionid", unionid);   

    	CouponApiLog log = new CouponApiLog();
    	log.setApi("check-registered-by-unionid");
    	log.setCreateTime(time);
    	log.setServerTime(new Date());
		log.setParams(JSON.toJSONString(params));
    	
    	String source = openApiService.checkAndGetSource(params);
    	if(source.length() == 0 ) {
    		log.setResultCode(ResultEnum.ERROR.getStatus()+"");
    		log.setResultTime(new Date());
    		log.setSource(from);
    		openApiService.saveApiLog(log);
    		return ResultUtil.customer(ResultEnum.ERROR, "授权检测失败");
    	}
        Wechat wechat = wechatService.getWechatByUnionId(unionid);
		log.setResultTime(new Date());
		log.setSource(source);
        if(null == wechat){
        	log.setResultCode(ResultEnum.FAILED.getStatus()+"");
    		openApiService.saveApiLog(log);
            return ResultUtil.customer(ResultEnum.SUCCESS, "No");
        }
    	log.setResultCode(ResultEnum.SUCCESS.getStatus()+"");
		openApiService.saveApiLog(log);

        return ResultUtil.customer(ResultEnum.SUCCESS, "Yes");

    }
}
