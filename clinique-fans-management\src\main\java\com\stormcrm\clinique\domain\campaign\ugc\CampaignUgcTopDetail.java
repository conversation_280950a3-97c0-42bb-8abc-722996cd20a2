package com.stormcrm.clinique.domain.campaign.ugc;

import com.stormcrm.clinique.domain.CampaignMsTalentsImage;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Data
public class CampaignUgcTopDetail {

    private Long id;
    private String wechatMiniOpenid;
    private Long customerId;
    private String nickName;
    private String avatarUrl;
    private Short sort;
    private Short status;
    private Date uploadTime;
    private Date examineTime;
    private Date createTime;
    
    private String title;

    private Long topId;
    private Long detailId;
    private List<CampaignUgcImage> imageList;

}
