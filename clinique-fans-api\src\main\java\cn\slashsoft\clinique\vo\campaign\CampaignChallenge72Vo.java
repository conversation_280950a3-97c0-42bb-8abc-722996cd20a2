package cn.slashsoft.clinique.vo.campaign;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 挑战72小时
 * <AUTHOR>
 */
@Data
public class CampaignChallenge72Vo {

    private Date startTime;
    private Date endTime;

    private Boolean firstRound;
    private Boolean secondRound;
    private Boolean thirdRound;

    private Boolean result;
    private Boolean resultExperience;
    private Boolean resultShare;

    private Boolean inviteDone;
    private String avatarUrl;
    private List<String> inviteeAvatarUrl;

}
