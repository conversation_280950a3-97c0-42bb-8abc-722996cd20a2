package cn.slashsoft.clinique.service.mini.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import cn.slashsoft.clinique.dao.mini.CactiveDao;
import cn.slashsoft.clinique.dao.mini.CustomerDao;
import cn.slashsoft.clinique.domain.mini.CactiveRank;
import cn.slashsoft.clinique.domain.mini.PointTransaction;
import cn.slashsoft.clinique.enums.CActiveTypeEnum;
import cn.slashsoft.clinique.service.mini.CactiveService;
import cn.slashsoft.clinique.service.mini.CustomerService;
import cn.slashsoft.clinique.util.DateUtil;
import io.netty.util.internal.StringUtil;

/**
 * 
 *
 * <AUTHOR>
 */
@Service
public class CactiveServiceImpl implements CactiveService {

    private final CactiveDao cactiveDao;
    
    @Resource 
    private CustomerService customerService;
    
    @Resource 
    private CustomerDao customerDao;

    public CactiveServiceImpl(CactiveDao cactiveDao) {
        this.cactiveDao = cactiveDao;
    }

	@Override
	public List<CactiveRank> getRank(int length) {
		return this.cactiveDao.getRank(length);
	}

	@Override
	public int getCustomerRank(Long customerId) {
		int all = this.customerDao.getCustomerAll(customerId);	
		
		return this.customerDao.getPointRank(all)+1;
	}

	@Override
	public int getRankCount() {
		int count = 0;
		count = this.cactiveDao.getAllCount();
		return count;
	}

	@Override
	public int getCustomerAll(Long customerId) {
		int all = this.customerDao.getCustomerAll(customerId);
		return all;
	}
 
	@Override
	public int addPoints(
			Long customerId, 
			CActiveTypeEnum cactiveType, 
			Long masterId, 
			Long detailId, 
			String remark ) {

		/*
		 * 关闭实时加c粉值，改为定时任务固定事件刷
		 */
		if(1==1) {
			return 0;
		}
        Date now = new Date();
        
		PointTransaction activeTransaction = new PointTransaction();
        activeTransaction.setPointTypeId(cactiveType.getId());
        activeTransaction.setPoints(cactiveType.getPoints());
        activeTransaction.setRemainingPoints(cactiveType.getPoints());
        activeTransaction.setStartTime(now);
        activeTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
        activeTransaction.setForeignId(cactiveType.getId());        

        activeTransaction.setCustomerId(customerId);
        activeTransaction.setForeignMasterId( masterId);
        activeTransaction.setForeignDetailId(detailId);
        activeTransaction.setRemark(
        		StringUtil.isNullOrEmpty(remark)?
        				cactiveType.getName():
        					remark);

        if(cactiveDao.has(activeTransaction) == 0) {
        	int result =  cactiveDao.insertPointTransaction(activeTransaction);
        	if(result > 0) {
        		customerService.updateCustomerActivePoints(cactiveType.getPoints(), customerId);
        	}
        	return result;
        }
		return 1;
	}

	@Override
	public HashMap<String, String> getRankDetail(Long customerId) {
		HashMap<String, String> detail = new HashMap<String,String>();
		detail.put("all", this.getCustomerAll(customerId) +"");
		detail.put("rank", this.getCustomerRank(customerId) +"");
		return detail;
	}
}
