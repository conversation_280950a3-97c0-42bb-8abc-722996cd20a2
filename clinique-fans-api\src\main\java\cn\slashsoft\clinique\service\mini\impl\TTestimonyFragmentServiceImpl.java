package cn.slashsoft.clinique.service.mini.impl;

import cn.slashsoft.clinique.dao.mini.TTestimonyFragmentDao;
import cn.slashsoft.clinique.domain.campaign.*;
import cn.slashsoft.clinique.domain.mini.TTestimonyBlindBox;
import cn.slashsoft.clinique.domain.mini.TTestimonyBlindBoxMotLog;
import cn.slashsoft.clinique.domain.mini.TTestimonyFragment;
import cn.slashsoft.clinique.domain.mini.TTestimonyFragmentRank;
import cn.slashsoft.clinique.domain.mini.TTestimonyFragmentTop15;
import cn.slashsoft.clinique.domain.mini.TTestimonyFragmentType;
import cn.slashsoft.clinique.domain.mini.TTestimonyHelp;
import cn.slashsoft.clinique.domain.mini.TTestimonyViewLog;
import cn.slashsoft.clinique.service.mini.TTestimonyBlindBoxService;
import cn.slashsoft.clinique.service.mini.TTestimonyBlindStoreService;
import cn.slashsoft.clinique.service.mini.TTestimonyFragmentService;
import cn.slashsoft.clinique.service.mini.TTestimonyFragmentTypeService;
import cn.slashsoft.clinique.vo.mini.TestimonyFragmentRankVo;
import cn.slashsoft.clinique.vo.mini.TestimonyFragmentRewardVo;
import cn.slashsoft.clinique.vo.mini.TestimonyFragmentSumVo;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
* <p>
    * 记录获得碎片信息
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
@Service
public class TTestimonyFragmentServiceImpl implements TTestimonyFragmentService {

    @Resource
    private TTestimonyFragmentDao tTestimonyFragmentDao;

    @Resource
    private TTestimonyFragmentTypeService tTestimonyFragmentTypeService;

    @Resource
    private TTestimonyBlindBoxService tTestimonyBlindBoxService;

    @Resource
    private TTestimonyBlindStoreService tTestimonyBlindStoreService;

    @Override
    public void insertTTestimonyFragment(TTestimonyFragment tTestimonyFragment) {
        tTestimonyFragmentDao.insertTTestimonyFragment(tTestimonyFragment);
    }

    @Override
    public void updateTTestimonyFragment(TTestimonyFragment tTestimonyFragment) {
        tTestimonyFragmentDao.updateTTestimonyFragment(tTestimonyFragment);
    }

    @Override
    public TTestimonyFragment getTTestimonyFragment(Long id) {
        return tTestimonyFragmentDao.getTTestimonyFragment(id);
    }
    @Override
    public Integer getTTestimonyFragmentCountByCustomerId(Long customerId) {
        return  tTestimonyFragmentDao.getTTestimonyFragmentCountByCustomerId(customerId);
    }
    @Override
    public TestimonyFragmentRankVo getTTestimonyFragmentRankByCustomerId(Long customerId) {
        return  tTestimonyFragmentDao.getTTestimonyFragmentRankByCustomerId(customerId);
    }
    @Override
    public List<TestimonyFragmentRankVo> getTTestimonyFragmentRankListByCustomerId(Long customerId) {
        return  tTestimonyFragmentDao.getTTestimonyFragmentRankListByCustomerId(customerId);
    }
    @Override
    public void insertTTestimonyFragmentRank(TTestimonyFragmentRank tTestimonyFragmentRank) {
        tTestimonyFragmentDao.insertTTestimonyFragmentRank(tTestimonyFragmentRank);
    }
    @Override
    public void addFragmentRankQty(TTestimonyFragment tTestimonyFragment) {
        tTestimonyFragmentDao.addFragmentRankQty(tTestimonyFragment);
    }
    @Override
    public TTestimonyFragmentRank getFragmentRankByCustomerId(Long customerId) {
        return tTestimonyFragmentDao.getFragmentRankByCustomerId(customerId);
    }

    @Override
    public TTestimonyFragment getTTestimonyFragmentByCustomerId(Long customerId) {
        return tTestimonyFragmentDao.getTTestimonyFragmentByCustomerId(customerId);
    }

    @Override
    public Integer getFragmentCountByType(Long customerId, Integer fragType) {
        return tTestimonyFragmentDao.getFragmentCountByType(customerId, fragType);
    }

    /**
     * 根据类型添加碎片记录，并更新rank总计
     * @param customerId
     * @param fragType
     * @return 添加碎片数量
     */
    @Override
    public Integer addFragmentRecords(Long customerId, Integer fragType) {
        Integer addedCount = 0;
        TTestimonyFragmentType tTestimonyFragmentType =
                tTestimonyFragmentTypeService.getTTestimonyFragmentType((long)fragType);
        TTestimonyFragment tTestimonyFragment;
        TTestimonyFragmentRank tTestimonyFragmentRank;
        Integer iCnt = getFragmentCountByType(customerId, fragType);
        if (iCnt<tTestimonyFragmentType.getFragmentLimit()) {
            // 添加fragment记录
            tTestimonyFragment = new TTestimonyFragment();
            tTestimonyFragment.setCustomerId(customerId);
            tTestimonyFragment.setFragmentQty(tTestimonyFragmentType.getFragmentQty());
            tTestimonyFragment.setFragmentType(fragType);
            tTestimonyFragment.setCreateTime(new Date());
            insertTTestimonyFragment(tTestimonyFragment);

            // 同时更新汇总数据
            tTestimonyFragmentRank = getFragmentRankByCustomerId(customerId);
            if (null == tTestimonyFragmentRank) {
                tTestimonyFragmentRank = new TTestimonyFragmentRank();
                tTestimonyFragmentRank.setCustomerId(customerId);
                tTestimonyFragmentRank.setCreateTime(new Date());
                tTestimonyFragmentRank.setFragmentQty(tTestimonyFragmentType.getFragmentQty());
                insertTTestimonyFragmentRank(tTestimonyFragmentRank);
            } else {
                //汇总数据+1
                addFragmentRankQty(tTestimonyFragment);
            }
            addedCount = tTestimonyFragmentType.getFragmentQty();
        }
        return addedCount;
    }

    /**
     * 获取碎片统计信息 用户策略界面
     * @param customerId
     * @return
     */
    @Override
    public List<TestimonyFragmentSumVo> getFragmentSumInfo(Long customerId) {
        return tTestimonyFragmentDao.getFragmentSumInfo(customerId);
    }

    /**
     * 获取兑奖
     * @param customerId
     * @param storeId
     * @return
     */
    @Override
    @Transactional
    public TestimonyFragmentRewardVo getFragmentRewards(long customerId, Integer storeId, String userName) {
        TestimonyFragmentRewardVo testimonyFragmentRewardVo =
                tTestimonyFragmentDao.getFragmentRewards(customerId, storeId);
        TTestimonyBlindBox tTestimonyBlindBox = new TTestimonyBlindBox();
        tTestimonyBlindBox.setCustomerId(customerId);
        tTestimonyBlindBox.setStoreId(storeId);
        tTestimonyBlindBox.setUserName(userName);
        tTestimonyBlindBox.setCreateTime(new Date());
        if (null != testimonyFragmentRewardVo) {
            // 需同时记录
            tTestimonyBlindBox.setProductId(testimonyFragmentRewardVo.getProductId());
            tTestimonyBlindBoxService.insertTTestimonyBlindBox(tTestimonyBlindBox);

            // 扣减库存
            tTestimonyBlindStoreService.subtractTTestimonyBlindStore(
                    storeId,
                    testimonyFragmentRewardVo.getProductId()
            );

            // 保险起见再查一次，如果库存小于0 则返回0
            Integer leftAmount = tTestimonyBlindStoreService.getTTestimonyBlindStoreAmount(
                    storeId,
                    testimonyFragmentRewardVo.getProductId()
            );
            if (leftAmount == null || leftAmount < 0) {
                tTestimonyBlindBox.setProductId(0);
            }

        } else {
            //添加为0的记录
            tTestimonyBlindBox.setProductId(0);
        }
        tTestimonyBlindBoxService.insertTTestimonyBlindBoxLog(tTestimonyBlindBox);

        return testimonyFragmentRewardVo;
    }

    /**
     * 通过id获取top15记录
     * @param customerId
     * @return
     */
    @Override
    public TTestimonyFragmentTop15 getFragmentTop15ByCustomerId(long customerId) {
        return tTestimonyFragmentDao.getFragmentTop15ByCustomerId(customerId);
    }

    /**
     * 更新top15礼品领取标识
     * @param customerId
     */
    @Override
    public void updateTop15Flag(long customerId) {
        tTestimonyFragmentDao.updateTop15Flag(customerId);
    }

    /**
     * 根据id，type查询是否存在记录
     * @param customerId
     * @param fragmentType
     * @return
     */
    @Override
    public TTestimonyFragment getFragmentByCustomerIdAndType(Long customerId, Integer fragmentType) {
        return tTestimonyFragmentDao.getFragmentByCustomerIdAndType(customerId, fragmentType);
    }

    /**
     * 是否是koc用户
     * @param customerId
     * @return
     */
    public Boolean isKocUser(Long customerId) {
        Integer cnt = tTestimonyFragmentDao.isKocUser(customerId);
        return  cnt>0 ? true: false;
    }

    /**
     * 是否是Cust 302用户
     * @param customerId
     * @return
     */
    public Boolean isCust302User(Long customerId) {
        Integer cnt = tTestimonyFragmentDao.isCust302User(customerId);
        return  cnt>0 ? true: false;
    }

    /**
     * 是否有发布笔记
     * @param customerId
     * @return
     */
    public Boolean hasNote(long customerId) {
        Integer cnt = tTestimonyFragmentDao.hasNote(customerId);
        return  cnt>0 ? true: false;
    }

    /**
     * 判断用户当天是否已领取打卡碎片
     * @param customerId
     * @return
     */
    public Boolean hasAlreadyDrawed(long customerId) {
        Integer cnt = tTestimonyFragmentDao.hasAlreadyDrawed(customerId);
        return  cnt>0 ? true: false;
    }

    /**
     * 判断用户是否有领取笔记碎片权限
     * @param customerId
     * @return
     */
    public Boolean getNotePrivilegeByCustomerId(long customerId) {
        Integer cnt = tTestimonyFragmentDao.hasNotePrivilege(customerId);
        return  cnt>0 ? true: false;
    }


    /**
     * 临时添加从小程序openid获取customerId
     * @param openId
     */
    public String getCustomerIdbyOpenId(String openId) {
        return tTestimonyFragmentDao.getCustomerIdbyOpenId(openId);
    }

    /**
     * 添加日志记录
     * @param customerId
     * @param source
     * @param page
     * @return
     */
    public long insertTestimonyViewLog(long customerId, String source, String page) {
        TTestimonyViewLog tTestimonyViewLog = new TTestimonyViewLog();
        tTestimonyViewLog.setCustomerId(customerId);
        tTestimonyViewLog.setSource(source);
        tTestimonyViewLog.setPage(page);
        tTestimonyFragmentDao.insertTestmonyViewLog(tTestimonyViewLog);
        return tTestimonyViewLog.getId();
    }

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Override
    public void setTestimonyViewLog(long id) {
        tTestimonyFragmentDao.setTestimonyViewLog(id);
    }

    /**
     * 添加助力的日志功能
     * @param tTestimonyHelp
     */
    public void addTTestimonyHelp(TTestimonyHelp tTestimonyHelp) {
        tTestimonyFragmentDao.addTTestimonyHelp(tTestimonyHelp);
    }

    /**
     * 添加转发log
     * @param customerId
     */
    public void addShareLog(long customerId) {
        tTestimonyFragmentDao.addShareLog(customerId);
    }

    /**
     * 从店铺id获取店铺名称
     * @param storeId
     * @return
     */
    public String getStoreNameByStoreId(Integer storeId) {
        return tTestimonyFragmentDao.getStoreNameByStoreId(storeId);
    }

    /**
     * 保存mot日志信息
     * @param tTestimonyBlindBoxMotLog
     */
    public void saveTestimonyBlindBoxMotLog(TTestimonyBlindBoxMotLog tTestimonyBlindBoxMotLog) {
        tTestimonyFragmentDao.saveTestimonyBlindBoxMotLog(tTestimonyBlindBoxMotLog);
    }

    /**
     * 保存TOP15客户对门店的选择
     *
     * @param customerChooser 客户的选择
     */
    @Override
    public void saveTop15CustomerChooseStore(TTestimonyFragmentTop15 customerChooser) {
        tTestimonyFragmentDao.saveTop15CustomerChooseStore(customerChooser);
    }
}
