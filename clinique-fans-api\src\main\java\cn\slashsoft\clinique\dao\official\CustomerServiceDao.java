package cn.slashsoft.clinique.dao.official;

import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.domain.official.CustomerServiceStaff;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * 公众号相关的操作
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CustomerServiceDao {

    /**
     * 获取导购员信息
     *
     * @param code 导购员 code
     * @return 导购员信息
     */
    @Select("SELECT " +
            "   `w`.`name` `name`, " +
            "   `c`.`code` `title` , " +
            "   `c`.`qrcode` `qrcode` " +
            "FROM " +
            "   `customer_service_staff` `c` " +
            "       inner join " +
            "   `customer_service_counter` `w` " +
            "       on `w`.`id`=`c`. `customer_service_counter_id` " +
            "WHERE " +
            "   `c`.`code`=#{code} " +
            "LIMIT 1")
    CustomerServiceStaff getByCode(String code);

    /**
     * 获取导购员信息
     *
     * @param storeCode 柜台code
     * @return 导购员信息
     */
    @Select("SELECT " +
            "   `w`.`name` `name`, " +
            "   `c`.`code` `title` , " +
            "   `c`.`qrcode` `qrcode` " +
            "FROM " +
            "   `customer_service_staff` `c` " +
            "       inner join " +
            "   `customer_service_counter` `w` " +
            "       on `w`.`id`=`c`. `customer_service_counter_id` " +
            "WHERE " +
            "   `w`.`code`=#{storeCode} " )
    List<CustomerServiceStaff> getByStoreCode(String storeCode);
}
