package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.Campaign;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 活动管理
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignDao {

    /**
     * 查询所有活动
     *
     * @return 活动列表
     */
    @Select("SELECT " +
            "   `id`," +
            "   `name`," +
            "   `link_url`, " +
            "   `status`, " +
            "   `update_time`, " +
            "   `create_time` " +
            "FROM " +
            "   `campaign` " +
            "WHERE " +
            "   `recovery`=0 " +
            "ORDER BY `id` DESC")
    List<Campaign> getAll();

    /**
     * 查询所有活动列表的记录数
     *
     * @return 记录数
     */
    @Select("SELECT " +
            "   COUNT(*) " +
            "FROM " +
            "   `campaign` " +
            "WHERE " +
            "   `recovery`=0")
    int getAllCount();

    /**
     * 查询所有活动-分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 活动列表
     */
    @SelectProvider(type = CampaignProvider.class, method = "getPage")
    List<Campaign> getPage(
            @Param("pageIndex") int pageIndex,
            @Param("pageSize") int pageSize,
            @Param("generalSearch") String generalSearch,
            @Param("status") Boolean status
    );

    /**
     * 查询所有活动带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    @SelectProvider(type = CampaignProvider.class, method = "getPageCount")
    int getPageCount(
            String generalSearch,
            Boolean status
    );

    /**
     * 查询
     *
     * @param id 自动编号
     * @return 信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `name`," +
            "   `image_url`," +
            "   `link_url` " +
            "FROM " +
            "   `campaign` " +
            "WHERE " +
            "   `id`=#{id} AND " +
            "   `recovery`=0 " +
            "LIMIT 1"
    )
    Campaign getById(long id);

    /**
     * 保存
     *
     * @param campaign 信息
     */
    @Insert("INSERT INTO `campaign`(" +
            "   `name`, " +
            "   `image_url`, " +
            "   `link_url` " +
            ") " +
            "VALUES (" +
            "   #{name}, " +
            "   #{imageUrl}, " +
            "   #{linkUrl} " +
            ")")
    void save(Campaign campaign);

    /**
     * 更新
     *
     * @param campaign 信息
     */
    @UpdateProvider(type = CampaignProvider.class, method = "update")
    void update(Campaign campaign);

    /**
     * 上架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Update("UPDATE " +
            "   `campaign` " +
            "SET " +
            "   `status`=1 " +
            "WHERE " +
            "   `id`=#{id}"
    )
    int upper(long id);

    /**
     * 下架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Update("UPDATE " +
            "   `campaign` " +
            "SET " +
            "   `status`=0 " +
            "WHERE " +
            "   `id`=#{id}"
    )
    int lower(long id);

    /**
     * 逻辑删除
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Update("UPDATE " +
            "   `campaign` " +
            "SET " +
            "   `recovery`=1 " +
            "WHERE " +
            "   `id`=#{id}"
    )
    int del(long id);

}
