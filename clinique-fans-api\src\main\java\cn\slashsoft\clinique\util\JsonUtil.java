package cn.slashsoft.clinique.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Map;

public class JsonUtil {

    private static ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 把JavaBean转换为json字符串
     *
     * @param object
     * @return
     */
    public static String toJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 使用泛型方法，把json字符串转换为相应的JavaBean对象。
     * (1)转换为普通JavaBean：readValue(json,Student.class)
     * (2)转换为List,如List<Student>,将第二个参数传递为Student[].class.然后使用Arrays.asList();方法把得到的数组转换为特定类型的List
     *
     * @param jsonString
     * @param objectClass
     * @return
     */
    public static <T> T toObject(String jsonString, TypeReference<Map<String, Object>> objectClass) {
        try {
            return objectMapper.readValue(jsonString, objectClass);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * json数组转List
     *
     * @param jsonString
     * @param objectReference
     * @return
     */
    public static <T> T toList(String jsonString, TypeReference<T> objectReference) {
        try {
            return objectMapper.readValue(jsonString, objectReference);
        } catch (Exception e) {
            return null;
        }
    }

}
