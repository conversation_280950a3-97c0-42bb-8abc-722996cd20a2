package cn.slashsoft.clinique.service.campaign.impl;

import cn.slashsoft.clinique.dao.campaign.CampaignNtfDao;
import cn.slashsoft.clinique.dao.mini.CounterDao;
import cn.slashsoft.clinique.dao.mini.PointDao;
import cn.slashsoft.clinique.dao.mini.WechatDao;
import cn.slashsoft.clinique.domain.campaign.CampaignViewLog;
import cn.slashsoft.clinique.domain.campaign.ntf.*;
import cn.slashsoft.clinique.domain.mini.Counter;
import cn.slashsoft.clinique.domain.mini.Member;
import cn.slashsoft.clinique.domain.mini.PointTransaction;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.enums.CBTypeEnum;
import cn.slashsoft.clinique.service.campaign.CampaignNtfService;
import cn.slashsoft.clinique.service.mini.MotService;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.util.*;
import cn.slashsoft.clinique.vo.Result;
import cn.slashsoft.clinique.vo.mini.Location;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.io.ByteArrayInputStream;
import java.util.Date;
import java.util.List;
import java.util.logging.Logger;

/**
 * NTF
 *
 * <AUTHOR>
 */
@Service
public class CampaignNtfServiceImpl implements CampaignNtfService {

    private final Logger logger = Logger.getLogger(CampaignNtfServiceImpl.class.getName());

    @Value("${wechat.cdp.select-customer-info-by-unionid}")
    private String urlCdpSelectCustomerInfoByUnionid;

    private final CampaignNtfDao campaignNtfDao;
    private final CounterDao counterDao;
    private final PointDao pointDao;
    private final MotService motService;
    private final WechatDao wechatDao;
    private final OutsideService outsideService;
    private final StringRedisTemplate stringRedisTemplate;

    public CampaignNtfServiceImpl(CampaignNtfDao campaignNtfDao, CounterDao counterDao, WechatDao wechatDao, OutsideService outsideService, PointDao pointDao, MotService motService, StringRedisTemplate stringRedisTemplate) {
        this.campaignNtfDao = campaignNtfDao;
        this.counterDao = counterDao;
        this.wechatDao = wechatDao;
        this.outsideService = outsideService;
        this.pointDao = pointDao;
        this.motService = motService;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 获取活动信息
     *
     * @param unionid    开放平台唯一编号
     * @param customerId 顾客编号
     * @return 活动信息
     */
    @Override
    public CampaignNtf getOrCreateCampaignNtf(String unionid, long customerId) {

        CampaignNtf campaignNtf = campaignNtfDao.getInfo(customerId);

        if (null == campaignNtf) {
            // 创建活动
            campaignNtf = new CampaignNtf();
            campaignNtf.setCustomerId(customerId);
            // 有首次消费记录是老客，没有是新客
            campaignNtf.setMemberType(getCdpMemberByUnionid(unionid));
            campaignNtf.setBack(false);
            campaignNtf.setDrawTimes((short) 0);
            campaignNtf.setInviteTimes((short) 0);
            campaignNtfDao.insertInfo(campaignNtf);
        }

        return campaignNtf;
    }

    /**
     * 获取活动信息
     *
     * @param customerId 顾客编号
     * @return 活动信息
     */
    @Override
    public CampaignNtf getCampaignNtf(long customerId) {
        return campaignNtfDao.getInfo(customerId);
    }

    /**
     * 获取CDP用户信息
     *
     * @param unionid 开放平台唯一编号
     * @return 用户信息
     */
    private short getCdpMemberByUnionid(String unionid) {
        try {

            logger.warning(urlCdpSelectCustomerInfoByUnionid + unionid);
            System.out.println(urlCdpSelectCustomerInfoByUnionid + unionid);

            String response = HttpUtil.get(urlCdpSelectCustomerInfoByUnionid + unionid);

            logger.warning(response);
            System.out.println(response);
            Document document = new SAXReader().read(new ByteArrayInputStream(response.getBytes("UTF-8")));
            Element root = document.getRootElement();
            if (!"True".equals(root.elementText("Code"))) {
                return 2;
            }

            Element result = root.element("ResultList");
            String firstChannelPurchase = result.elementText("FirstChannelPurchase");

            return (short) (StringUtil.isNullOrEmpty(firstChannelPurchase) ? 2 : 1);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return 2;
    }

    /**
     * 获取我的抽奖记录
     *
     * @param customerId 顾客编号
     * @return 我的抽奖记录列表
     */
    @Override
    public List<CampaignNtfRecord> getCampaignNtfRecord(long customerId) {
        return campaignNtfDao.getCampaignNtfRecord(customerId);
    }

    /**
     * 获取被邀请者头像
     *
     * @param miniOpenid 邀请者openid
     * @return 头像
     */
    @Override
    public List<String> getInviteeAvatarUrl(String miniOpenid) {
        return campaignNtfDao.getInviteeAvatarUrl(miniOpenid);
    }

    /**
     * 开启
     *
     * @param customerId 顾客编号
     * @param location   定位
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result back(long customerId, Location location) {

        // 读取活动信息
        CampaignNtf campaignNtf = campaignNtfDao.getInfo(customerId);
        if (null == campaignNtf) {
            return ResultUtil.customer(1, "未找到活动信息");
        }

        if (1 == campaignNtf.getMemberType()) {
            return ResultUtil.customer(1, "只有新用户才能领取");
        }

        if (campaignNtf.getBack()) {
            return ResultUtil.customer(1, "礼品已经领取过了");
        }

        // 读取最近的门店
        Counter counter = counterDao.getNearestCounter(location.getLatitude(), location.getLongitude());
        if (null == counter) {
            return ResultUtil.customer(1, "未找门店信息");
        }

        // 读取礼品
        CampaignNtfBackGift campaignNtfBackGift = campaignNtfDao.getBackGift();
        if (null == campaignNtfBackGift) {
            return ResultUtil.customer(1, "礼品未配置");
        }

        if (0 >= campaignNtfBackGift.getStocks()) {
            return ResultUtil.customer(1, "礼品没有库存了");
        }

        Wechat wechat = wechatDao.getWechatInfoByCustomerId(customerId);
        if (null == wechat || StringUtil.isNullOrEmpty(wechat.getUnionid())) {
            return ResultUtil.customer(1, "未找到微信信息");
        }

        // 变更活动信息
        if (0 == campaignNtfDao.back(campaignNtf.getId())) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResultUtil.customer(1, "业务繁忙，请稍后再试");
        }

        // 扣除库存
        if (0 == campaignNtfDao.updateBackGift(campaignNtfBackGift.getId())) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResultUtil.customer(1, "库存扣除失败");
        }

        // 发放券
        Result backResult = outsideService.getCoupon(wechat.getUnionid(), campaignNtfBackGift.getCouponId());
        if (0 != backResult.getCode()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResultUtil.customer(1, "发放礼券失败");
        }

        // 写入领取记录
        CampaignNtfRecord backRecord = new CampaignNtfRecord();
        backRecord.setCustomerId(customerId);
        backRecord.setLatitude(location.getLatitude());
        backRecord.setLongitude(location.getLongitude());
        backRecord.setCounterId(counter.getId());
        backRecord.setDistance(counter.getDistance());
        backRecord.setType((short) 1);
        backRecord.setGiftId(campaignNtfBackGift.getId());
        backRecord.setCouponCode(backResult.getMessage());
        backRecord.setGroup(campaignNtfBackGift.getGroup());
        backRecord.setName(campaignNtfBackGift.getName());
        backRecord.setDescription(campaignNtfBackGift.getDescription());
        backRecord.setImageUrl(campaignNtfBackGift.getImageUrl());
        if (0 == campaignNtfDao.insertCampaignNtfRecord(backRecord)) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResultUtil.customer(1, "写入抽奖记录失败");
        }

        // 发送MOT
        if (!StringUtil.isNullOrEmpty(wechat.getWechatOfficialOpenid())) {
            String backParam = "{" +
                    "\"first\":{\"value\":\"亲爱的 C 粉，你的圣诞礼包已经在柜台啦，赶紧前往领取吧！\"}," +
                    "\"keyword1\":{\"value\":\"" + campaignNtfBackGift.getName() + "\"}," +
                    "\"keyword2\":{\"value\":\"待选择\"}," +
                    "\"remark\":{\"value\":\"请于1/6前至倩碧柜台领取，还能抽中奖率100%的圣诞盲盒哦～\"}" +
                    "}";
            outsideService.templateSendWithMp((short) 9, wechat.getWechatOfficialOpenid(), "tWzDYkNitXzpzkSGjjmJMlXRNJvRmdzX3iq-vcw2ZT8", backParam, "wxc0099da8e25e2948", "/pages/coupon/coupon?source=NTF-BACK-MOT");
        }


        return ResultUtil.customer(0, "成功");
    }

    /**
     * 抽奖
     *
     * @param customerId 顾客编号
     * @param location   定位
     * @return 处理结果
     */
    @Override
    public Result draw(long customerId, Location location) {

        // 读取活动信息
        CampaignNtf campaignNtf = campaignNtfDao.getInfo(customerId);
        if (null == campaignNtf) {
            return ResultUtil.customer(1, "未找到活动信息");
        }

        if (1 == campaignNtf.getMemberType()) {
            return ResultUtil.customer(1, "只有新用户才能抽取");
        }

        if (!campaignNtf.getBack()) {
            return ResultUtil.customer(1, "你还未领取回柜礼品");
        }

        if (4 <= campaignNtf.getDrawTimes()) {
            return ResultUtil.customer(2, "你的盲盒已抽完");
        }

        if (0 < campaignNtf.getDrawTimes() && campaignNtf.getDrawTimes() * 3 > campaignNtf.getInviteTimes()) {
            return ResultUtil.customer(3, "带上3个好姐妹加入C粉圈，就能再抽一次盲盒哦！");
        }

        // 读取最近的门店
        Counter counter = counterDao.getNearestCounter(location.getLatitude(), location.getLongitude());
        if (null == counter) {
            return ResultUtil.customer(1, "未找门店信息");
        }

        if (0 == campaignNtf.getDrawTimes() && 500 < counter.getDistance()) {
            return ResultUtil.customer(4, "你好像不在倩碧柜台哦～");
        }

        List<CampaignNtfDrawGift> campaignNtfDrawGiftList = campaignNtfDao.getDrawGift();
        if (null == campaignNtfDrawGiftList || 0 == campaignNtfDrawGiftList.size()) {
            return ResultUtil.customer(1, "礼品未配置");
        }

        Wechat wechat = wechatDao.getWechatInfoByCustomerId(customerId);
        if (null == wechat || StringUtil.isNullOrEmpty(wechat.getUnionid())) {
            return ResultUtil.customer(1, "未找到微信信息");
        }

        //抽取奖品
        CampaignNtfDrawGift campaignNtfDrawGift = null;

        if (1 == campaignNtfDrawGiftList.size()) {
            campaignNtfDrawGift = campaignNtfDrawGiftList.get(0);
        } else {
            campaignNtfDrawGift = getCampaignNtfDrawGiftByRatio(campaignNtfDrawGiftList);
        }

        if (null == campaignNtfDrawGift) {
            return new Result(1, "系统出小差了，请稍后再试");
        }

        // 变更活动信息
        if (0 == campaignNtfDao.draw(campaignNtf.getId(), campaignNtf.getDrawTimes())) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResultUtil.customer(1, "业务繁忙，请稍后再试");
        }

        // 扣除库存
        if (0 == campaignNtfDao.updateDrawGift(campaignNtfDrawGift.getId())) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResultUtil.customer(1, "库存扣除失败");
        }

        String couponCode = "";
        Date now = new Date();

        // 礼品类型
        switch (campaignNtfDrawGift.getType()) {
            case 1:
                // 优惠券类
                // 发放券
                Result drawResult = outsideService.getCoupon(wechat.getUnionid(), campaignNtfDrawGift.getCouponId());
                if (0 != drawResult.getCode()) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return ResultUtil.customer(1, "发放礼券失败");
                }
                couponCode = drawResult.getMessage();
                break;
            case 2:
                // 发放积分
                PointTransaction pointTransaction = new PointTransaction();
                pointTransaction.setCustomerId(customerId);
                pointTransaction.setPointTypeId(CBTypeEnum.NTF.getId());
                pointTransaction.setPoints(CBTypeEnum.NTF.getPoints());
                pointTransaction.setRemainingPoints(CBTypeEnum.NTF.getPoints());
                pointTransaction.setStartTime(now);
                pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
                pointTransaction.setForeignId(CBTypeEnum.NTF.getId());
                pointTransaction.setForeignMasterId(campaignNtf.getId());
                pointTransaction.setForeignDetailId(0L);
                pointDao.insertPointTransaction(pointTransaction);
                break;
            default:
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return ResultUtil.customer(1, "盲盒礼品类型配置有误");
        }

        // 写入领取记录
        CampaignNtfRecord campaignNtfRecord = new CampaignNtfRecord();
        campaignNtfRecord.setCustomerId(customerId);
        campaignNtfRecord.setLatitude(location.getLatitude());
        campaignNtfRecord.setLongitude(location.getLongitude());
        campaignNtfRecord.setCounterId(counter.getId());
        campaignNtfRecord.setDistance(counter.getDistance());
        campaignNtfRecord.setType((short) 2);
        campaignNtfRecord.setGiftId(campaignNtfDrawGift.getId());
        campaignNtfRecord.setCouponCode(couponCode);
        campaignNtfRecord.setGroup(campaignNtfDrawGift.getGroup());
        campaignNtfRecord.setName(campaignNtfDrawGift.getName());
        campaignNtfRecord.setDescription(campaignNtfDrawGift.getDescription());
        campaignNtfRecord.setImageUrl(campaignNtfDrawGift.getImageUrl());
        if (0 == campaignNtfDao.insertCampaignNtfRecord(campaignNtfRecord)) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResultUtil.customer(1, "写入抽奖记录失败");
        }

        // 发送MOT
        if (!StringUtil.isNullOrEmpty(wechat.getWechatOfficialOpenid())) {
            switch (campaignNtfDrawGift.getType()) {
                case 1:
                    String data = "{" +
                            "\"first\":{\"value\":\"亲爱的 C 粉，您的圣诞盲盒礼品已到账。\"}," +
                            "\"keyword1\":{\"value\":\"" + wechat.getNickName() + "\"}," +
                            "\"keyword2\":{\"value\":\"" + campaignNtfDrawGift.getName() + "\"}," +
                            "\"keyword3\":{\"value\":\"" + DateUtil.parseString(now) + "\"}," +
                            "\"remark\":{\"value\":\"前往“会员中心-我的礼券”填写你的收件资料，盲盒礼品活动后统一邮寄发送，逾期视同作废。\"}" +
                            "}";
                    outsideService.templateSendWithMp((short) 9, wechat.getWechatOfficialOpenid(), "oBAY1-fjuUZpHwl1CK7T_Q0teSGm1mvCV3q6AzeH8YA", data, "wxc0099da8e25e2948", "/pages/coupon/coupon?source=NTF-DRAW-GIFT-MOT");
                    break;
                case 2:
                    motService.getPoints(customerId, 50, "恭喜您成功开启圣诞盲盒！礼品 50 C 币已成功到账。");
                    break;
                default:
                    break;
            }
        }

        // 首次抽盲盒时发放消费礼
        if(0 == campaignNtf.getDrawTimes() && BooleanUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignNtfConsumeEnable")) && 0 < campaignNtfDao.getConsumeCounter(counter.getId())){

            CampaignNtfConsumeGift campaignNtfConsumeGift = campaignNtfDao.getConsumeGift();
            if (null == campaignNtfConsumeGift) {
                return ResultUtil.customer(1, "礼品未配置");
            }

            if (0 < campaignNtfConsumeGift.getStocks() && 0 < campaignNtfDao.updateConsumeGift(campaignNtfConsumeGift.getId())) {
                // 发放券
                Result consumeResult = outsideService.getCoupon(wechat.getUnionid(), campaignNtfConsumeGift.getCouponId());
                if (0 == consumeResult.getCode()) {
                    // 写入领取记录
                    CampaignNtfRecord consumeRecord = new CampaignNtfRecord();
                    consumeRecord.setCustomerId(customerId);
                    consumeRecord.setLatitude(location.getLatitude());
                    consumeRecord.setLongitude(location.getLongitude());
                    consumeRecord.setCounterId(counter.getId());
                    consumeRecord.setDistance(counter.getDistance());
                    consumeRecord.setType((short) 3);
                    consumeRecord.setGiftId(campaignNtfConsumeGift.getId());
                    consumeRecord.setCouponCode(consumeResult.getMessage());
                    consumeRecord.setGroup(campaignNtfConsumeGift.getGroup());
                    consumeRecord.setName(campaignNtfConsumeGift.getName());
                    consumeRecord.setDescription(campaignNtfConsumeGift.getDescription());
                    consumeRecord.setImageUrl(campaignNtfConsumeGift.getImageUrl());
                    campaignNtfDao.insertCampaignNtfRecord(consumeRecord);

                    // 发送MOT
                    if (!StringUtil.isNullOrEmpty(wechat.getWechatOfficialOpenid())) {
                        String consumeParam = "{" +
                                "\"first\":{\"value\":\"限时惊喜来啦！今日于倩碧柜台购买任意正装，即可再领取-302美白瓶3日体验装哦！\"}," +
                                "\"keyword1\":{\"value\":\"" + wechat.getNickName() + "\"}," +
                                "\"keyword2\":{\"value\":\"" + campaignNtfConsumeGift.getName() + "\"}," +
                                "\"keyword3\":{\"value\":\"" + DateUtil.parseString(now) + "\"}," +
                                "\"remark\":{\"value\":\"备注：活动仅限今日，点击前往“会员中心-我的礼券”查看你的独家惊喜！正装产品不包含倩碧化妆棉，详请询问柜台美容顾问。\"}" +
                                "}";
                        outsideService.templateSendWithMp((short) 9, wechat.getWechatOfficialOpenid(), "oBAY1-fjuUZpHwl1CK7T_Q0teSGm1mvCV3q6AzeH8YA", consumeParam, "wxc0099da8e25e2948", "/pages/coupon/coupon?source=NTF-CONSUME-MOT");
                    }
                }

            }

        }

        return ResultUtil.customer(0, "成功", campaignNtfDrawGift);
    }

    /**
     * 按比率抽奖一个奖项
     *
     * @param campaignNtfDrawGiftList 奖池
     * @return 中奖
     */
    private CampaignNtfDrawGift getCampaignNtfDrawGiftByRatio(List<CampaignNtfDrawGift> campaignNtfDrawGiftList) {

        // 中奖率
        int ratio = campaignNtfDrawGiftList.parallelStream().mapToInt(CampaignNtfDrawGift::getRatio).sum();

        if (0 == ratio) {
            return null;
        }

        int random = RandomUtil.getNumberBetween(1, ratio);

        for (CampaignNtfDrawGift campaignNtfDrawGift : campaignNtfDrawGiftList) {
            if (random <= campaignNtfDrawGift.getRatio()) {
                return campaignNtfDrawGift;
            }
            random -= campaignNtfDrawGift.getRatio();
        }

        return null;
    }

    /**
     * 邀请
     *
     * @param inviterMiniOpenid 邀请者
     * @param inviteeMiniOpenid 被邀请者
     */
    @Override
    public void invite(String inviterMiniOpenid, String inviteeMiniOpenid) {
        try {
            // 写入邀请表
            campaignNtfDao.insertInvite(inviterMiniOpenid, inviteeMiniOpenid);
            // 更新邀请数量
            campaignNtfDao.invite(inviterMiniOpenid);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 写入访问日志
     *
     * @param campaignViewLog 日志
     * @return 日志编号
     */
    @Override
    public long insertViewLog(CampaignViewLog campaignViewLog) {
        campaignNtfDao.insertViewLog(campaignViewLog);
        return campaignViewLog.getId();
    }

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Override
    public void setViewLog(long id) {
        campaignNtfDao.setViewLog(id);
    }

    /**
     * 是否是新客
     *
     * @param unionid    开放平台唯一编号
     * @param customerId 顾客编号
     * @return 是否
     */
    @Override
    public boolean isNewCustomer(String unionid, long customerId) {

        CampaignNtf campaignNtf = campaignNtfDao.getInfo(customerId);

        if(null == campaignNtf){
            return 2 == getCdpMemberByUnionid(unionid);
        }

        return 2 == campaignNtf.getMemberType();

    }
}
