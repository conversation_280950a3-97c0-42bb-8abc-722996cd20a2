package cn.slashsoft.clinique.util;

import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.KeyGenerator;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

/**
 * 加解密工具类
 *
 * <AUTHOR>
 */
public class EncryptUtil {


    /**
     * MD5加密
     *
     * @param sourceString 待加密码字符串
     * @return 加密后的字符串
     */
    public static String md5Encode(String sourceString) {
        String resultString = null;
        try {
            resultString = new String(sourceString);
            MessageDigest md = MessageDigest.getInstance("MD5");
            resultString = byte2hexString(md.digest(resultString.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception ignored) {
        }
        return resultString;
    }

    /**
     * 字节转HEX
     *
     * @param bytes 字节
     * @return HEX字符串
     */
    private static String byte2hexString(byte[] bytes) {
        StringBuilder bf = new StringBuilder(bytes.length * 2);
        for (byte aByte : bytes) {
            if ((aByte & 0xFF) < 16) {
                bf.append("0");
            }
            bf.append(Long.toString(aByte & 0xFF, 16));
        }
        return bf.toString();
    }

    /**
     * 微信开放平台唯一编号加密
     *
     * @param unionid 唯一编号
     * @return 加密后的字符串
     */
    public static String unionidEncode(String unionid) {
        if(StringUtil.isNullOrEmpty(unionid)){
            return null;
        }
        char[] chars = unionid.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            if (48 <= chars[i] && chars[i] <= 57) {
                int v = i % (10);
                chars[i] = (char) (chars[i] + v > 57 ? chars[i] + v - 10 : chars[i] + v);
            } else if (65 <= chars[i] && chars[i] <= 90) {
                int v = i % (26);
                chars[i] = (char) (chars[i] + v > 90 ? chars[i] + v - 26 : chars[i] + v);
            } else if (97 <= chars[i] && chars[i] <= 122) {
                int v = i % (26);
                chars[i] = (char) (chars[i] + v > 122 ? chars[i] + v - 26 : chars[i] + v);
            }
        }
        return String.valueOf(chars);
    }

    /**
     * 微信开放平台唯一编号解密
     *
     * @param unionid 唯一编号
     * @return 解密后的字符串
     */
    public static String unionidDecode(String unionid) {
        if(StringUtil.isNullOrEmpty(unionid)){
            return null;
        }
        char[] chars = unionid.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            if (48 <= chars[i] && chars[i] <= 57) {
                int v = i % (10);
                chars[i] = (char) (chars[i] - v < 48 ? chars[i] - v + 10 : chars[i] - v);
            }
            if (65 <= chars[i] && chars[i] <= 90) {
                int v = i % (26);
                chars[i] = (char) (chars[i] - v < 65 ? chars[i] - v + 26 : chars[i] - v);
            }
            if (97 <= chars[i] && chars[i] <= 122) {
                int v = i % (26);
                chars[i] = (char) (chars[i] - v < 97 ? chars[i] - v + 26 : chars[i] - v);
            }
        }
        return String.valueOf(chars);
    }

    /**
     * 优惠券签名
     * @param params 参数
     * @param secret 密钥
     * @return 签名
     */
    public static String couponSign(Map<String, String> params, String secret){
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        StringBuilder content = new StringBuilder();
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = params.get(key);
            content.append(i == 0 ? "" : "&").append(key).append("=").append(value);
        }
        String ciphertext = md5Encode(secret + content.toString() + secret);
        ciphertext = md5Encode(secret + ciphertext);
        return content.append("&sign=").append(ciphertext).toString().replace(" ", "%20");
    }
    
    public static String mysqlDecode(String content) {
    	 try {
    		 EncryptUtil ep = new EncryptUtil();
             KeyGenerator kgen = KeyGenerator.getInstance("AES");// 创建AES的Key生产者
             kgen.init(128, new SecureRandom("".getBytes()));//TODO
             SecretKey secretKey = kgen.generateKey();// 根据用户密码，生成一个密钥
             byte[] enCodeFormat = secretKey.getEncoded();// 返回基本编码格式的密钥
             SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");// 转换为AES专用密钥
             Cipher cipher = Cipher.getInstance("AES");// 创建密码器
             cipher.init(Cipher.DECRYPT_MODE, key);// 初始化为解密模式的密码器
             byte[] result = cipher.doFinal(content.getBytes());  
             return result.toString();
         } catch (NoSuchAlgorithmException e) {
             e.printStackTrace();
         } catch (NoSuchPaddingException e) {
             e.printStackTrace();
         } catch (InvalidKeyException e) {
             e.printStackTrace();
         } catch (IllegalBlockSizeException e) {
             e.printStackTrace();
         } catch (BadPaddingException e) {
             e.printStackTrace();
         }

    	return "";
    }


}
