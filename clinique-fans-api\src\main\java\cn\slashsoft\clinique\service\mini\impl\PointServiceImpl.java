package cn.slashsoft.clinique.service.mini.impl;

import java.util.Date;
import java.util.List;

import cn.slashsoft.clinique.domain.mini.PointTransactionTemp;
import cn.slashsoft.clinique.enums.CBTypeEnum;
import cn.slashsoft.clinique.util.DateUtil;
import org.springframework.stereotype.Service;

import cn.slashsoft.clinique.dao.mini.PointDao;
import cn.slashsoft.clinique.domain.mini.PointTransaction;
import cn.slashsoft.clinique.service.mini.PointService;

/**
 * 积分相关的服务
 *
 * <AUTHOR>
 */
@Service
public class PointServiceImpl implements PointService {

    private final PointDao pointDao;

    public PointServiceImpl(PointDao pointDao) {
        this.pointDao = pointDao;
    }

    /**
     * 读取历史积分合计
     *
     * @param customerId 顾客编号
     * @return 历史积分合计
     */
    @Override
    public int getPointTotal(long customerId) {
        return pointDao.getPointTotal(customerId);
    }

    /**
     * 读取有效积分合计
     *
     * @param customerId 顾客编号
     * @return 有效积分合计
     */
    @Override
    public int getRemainingPointTotal(long customerId) {
        return pointDao.getRemainingPointTotal(customerId);
    }

    /**
     * 读取本月即将过期的积分合计
     *
     * @param customerId 顾客编号
     * @return 本月即将过期的积分合计
     */
    @Override
    public int getExpiredPointTotal(long customerId) {
        return pointDao.getExpiredPointTotal(customerId);
    }

    /**
     * 读取所有有效积分
     *
     * @param customerId 顾客编号
     * @return 积分
     */
    @Override
    public List<PointTransaction> getPointTransaction(long customerId) {
        return pointDao.getPointTransaction(customerId);
    }

    /**
     * 通过API写入新的积分
     *
     * @param customerId 顾客编号
     * @param pointTypeId  积分类型
     * @param points     积分
     */
    @Override
    public void addPointTransactionForApi(long customerId, short pointTypeId, int points) {
        Date now = new Date();
        PointTransaction pointTransaction = new PointTransaction();
        pointTransaction.setCustomerId(customerId);
        pointTransaction.setPointTypeId(pointTypeId);
        pointTransaction.setPoints(points);
        pointTransaction.setRemainingPoints(points);
        pointTransaction.setStartTime(now);
        pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
        pointTransaction.setForeignId(pointTypeId);
        pointTransaction.setForeignMasterId(0L);
        pointTransaction.setForeignDetailId(0L);
        pointDao.insertPointTransaction(pointTransaction);
    }

    /**
     * 通过API写入新的积分
     *
     * @param unionid 开放平台唯一编号
     * @param pointTypeId  积分类型
     * @param points     积分
     */
    @Override
    public void addPointTransactionTempForApi(String unionid, short pointTypeId, int points) {
        Date now = new Date();
        PointTransactionTemp pointTransactionTemp = new PointTransactionTemp();
        pointTransactionTemp.setUnionid(unionid);
        pointTransactionTemp.setPointTypeId(pointTypeId);
        pointTransactionTemp.setPoints(points);
        pointTransactionTemp.setStartTime(now);
        pointTransactionTemp.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
        pointDao.insertPointTransactionTemp(pointTransactionTemp);
    }

}
