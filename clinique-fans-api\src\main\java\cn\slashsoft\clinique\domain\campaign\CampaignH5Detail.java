package cn.slashsoft.clinique.domain.campaign;

import lombok.Data;

import java.util.Date;

/**
 * 申领
 * <AUTHOR>
 */
@Data
public class CampaignH5Detail {

    private Long id;
    private Short type;
    private String uniqueId;
    private String name;
    private String phoneNumber;
    private String province;
    private String city;
    private String store;
    private String source;
    private Boolean follow;
    private String followSource;
    private Date followFirstTime;
    private Date followLastTime;
    private Date followCancelTime;
    private Boolean bind;
    private Date bindTime;
    private Boolean status;
    private Date updateTime;
    private Boolean receive;
    private String receiveCity;
    private String receiveStore;
    private String receiveStoreId;
    private Date receiveTime;
    private String scene;
    private Date createTime;
    private String storeId;
    private Long campaignId;

}
