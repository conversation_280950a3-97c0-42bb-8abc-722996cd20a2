package cn.slashsoft.clinique.controller.mini;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.slashsoft.clinique.dao.mini.SpecialBannerDao;
import cn.slashsoft.clinique.domain.mini.CIndexBanner;
import cn.slashsoft.clinique.domain.mini.Customer;
import cn.slashsoft.clinique.domain.mini.SpecialBanner;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.campaign.CampaignNtfService;
import cn.slashsoft.clinique.service.mini.CustomerService;
import cn.slashsoft.clinique.service.mini.SpecialService;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.service.outside.PrivateRelishService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.ResultUtil;

/**
 * berg 我的特权banner
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class SpecialController {

	@Resource
	private HttpServletRequest request;


	@Resource
	private CustomerService customerService;

	@Resource
	private PrivateRelishService prService;
	
	@Resource 
	private SpecialService specialService;

	private final WechatService wechatService;

	public SpecialController(PrivateRelishService prService, WechatService wechatService,
			CustomerService customerService) {

		this.wechatService = wechatService;
	}

	/**
	 * 是否可以预约以及是否已经预约
	 *
	 * 已经预约： message 返回 顾问编码 可以预约 ： message 返回1
	 */
	@PostMapping("/private-relish/check")
	public String check(@RequestParam(value = "openid", required = false) String openid) {

		if (openid == null || openid.length() == 0) {
			long customerId = (long) request.getAttribute("customerId");
			Wechat we = this.wechatService.getWechatInfoByCustomerId(customerId);
			Customer cus = this.customerService.getCustomerById(customerId);
			if (cus == null || we == null) {
				return ResultUtil.customer(ResultEnum.PROCESS_BUSY);
			} else {
				String starffCode = this.prService.joined(cus.getPhoneNumber());
				if (!"no".equals(starffCode)) {
					return ResultUtil.customer(ResultEnum.SUCCESS, starffCode);
				}

				openid = we.getWechatOfficialOpenid();
				if (this.prService.canSee(openid)) {
					return ResultUtil.customer(ResultEnum.SUCCESS, 1);
				}
			}
		} else {
			Customer cus = this.customerService.getCustomerByOpenId(openid);
			if (cus == null) {
				cus = this.customerService.getCustomerByOpenIdFromMember(openid);
				if (cus == null) {
					return ResultUtil.customer(ResultEnum.PROCESS_BUSY);
				} else {
					String starffCode = this.prService.joined(cus.getPhoneNumber());
					if (!"no".equals(starffCode)) {
						return ResultUtil.customer(ResultEnum.SUCCESS, starffCode);
					}
				}
			} else {
				String starffCode = this.prService.joined(cus.getPhoneNumber());
				if (!"no".equals(starffCode)) {
					return ResultUtil.customer(ResultEnum.SUCCESS, starffCode);
				}
			}
			if (this.prService.canSee(openid)) {
				return ResultUtil.customer(ResultEnum.SUCCESS, 1);
			}
		}
		return ResultUtil.customer(ResultEnum.PARAM_ERROR);
	}

	
	@PostMapping("/special/get-banner")
	public String specialcheck() {
		System.out.println("/special/get-banner");
		long customerId = (long) request.getAttribute("customerId");
		String unionid= (String )request.getAttribute("unionid");
		String openid= (String )request.getAttribute("miniOpenid");
		List<SpecialBanner> all = specialService.getNormalSpecial();
		SpecialBanner koc = specialService.koccheck(customerId);
		if (koc != null) {
			all.add(koc);
		}
		

		koc = specialService.oldnewcheck(unionid, customerId);
		if (koc != null) {
			all.add(0,koc);
		}
		koc = specialService.days3signCheck(unionid,openid, customerId);
		if (koc != null) {
			all.add(0,koc);
		}

		koc = specialService.onlineDownCheck(unionid,openid, customerId);
		if (koc != null) {
			all.add(0,koc);
		}
		
		return ResultUtil.customer(ResultEnum.SUCCESS, all);
	}
}
