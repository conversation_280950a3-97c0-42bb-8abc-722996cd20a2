package cn.slashsoft.clinique.service.mini;

import cn.slashsoft.clinique.domain.mini.Note;
import cn.slashsoft.clinique.domain.mini.NoteTag;
import cn.slashsoft.clinique.domain.mini.NoteDiscuss;
import cn.slashsoft.clinique.domain.mini.NoteLikeLog;
import cn.slashsoft.clinique.domain.mini.NoteReadLog;
import cn.slashsoft.clinique.domain.mini.NoteShareLog;
import cn.slashsoft.clinique.domain.mini.NotePhoto;
import cn.slashsoft.clinique.domain.mini.NotePhotoTag;
import java.util.ArrayList;

import org.springframework.web.multipart.MultipartFile;

/**
 * 笔记
 *
 * <AUTHOR>
 */
public interface FileService {
   
    /**
	 * 获取用户笔记
	 *
	 *@param noteId
	 */
    String upload(Long customerId, MultipartFile file);
}
