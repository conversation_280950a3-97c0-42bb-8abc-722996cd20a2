package com.stormcrm.clinique.domain;

import lombok.Data;

import java.util.Date;

/**
 * 积分类型
 *
 * <AUTHOR>
 */
@Data
public class PointType {
    private Short id;
    private String name;
    // 这个是给前端展示的，图片默认： /assets/iconPointTypeAdd.png
    private String imageUrl="/assets/iconPointTypeAdd.png";

    private Integer status;

    private Boolean forNoteTag;
    private Integer rewardPoint;
    private Date rewardActiveStart;
    private Date rewardActiveEnd;

    /**
     * 这两个值，最好根据奖励的不同类型，建立不同的log表，
     * 需要的字段：id、被奖励目标id、客户id、奖励日期（log create time）、点数
     * */
    private Integer rewardMax;
    private Integer rewardMaxForever;

    private Date updateTime;
    private Date createTime;

}
