package cn.slashsoft.clinique.service.campaign.impl;

import cn.slashsoft.clinique.dao.campaign.CampaignSmart2307Dao;
import cn.slashsoft.clinique.domain.campaign.smart2307.Ranking;
import cn.slashsoft.clinique.service.campaign.CampaignSmart2307Service;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 智能活动
 *
 * <AUTHOR>
 */
@Service
public class CampaignSmart2307ServiceImpl implements CampaignSmart2307Service {

    @Resource
    private CampaignSmart2307Dao campaignSmart2307Dao;

    /**
     * 获取点选排名前15名
     *
     * @param tagId 标签编号
     * @return 排名列表
     */
    @Override
    public List<Ranking> getTop15RankingList(int tagId) {
        return campaignSmart2307Dao.getTop15RankingList(tagId);
    }

    /**
     * 获取点选排名前15名
     *
     * @return 排名列表
     */
    @Override
    public List<Ranking> getTop15() {
        return campaignSmart2307Dao.getTop15();
    }
}
