package cn.slashsoft.cliniqueapi;

import cn.slashsoft.clinique.dao.campaign.CampaignEbciDao;
import cn.slashsoft.clinique.dao.mini.MotDao;
import cn.slashsoft.clinique.dao.mini.ServiceMessageDao;
import cn.slashsoft.clinique.dao.mini.SmotDao;
import cn.slashsoft.clinique.dao.mini.WechatDao;
import cn.slashsoft.clinique.dao.official.OfficialDao;
import cn.slashsoft.clinique.service.outside.impl.OutsideServiceImpl;
import cn.slashsoft.clinique.util.EncryptUtil;
import cn.slashsoft.clinique.util.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.hamcrest.core.Is.is;
import static org.junit.Assert.assertThat;

public class OutsideServiceTests {

    @MockBean
    private OfficialDao officialDao;
    @MockBean
    private WechatDao wechatDao;
    @MockBean
    private MotDao motDao;
    @MockBean
    private ServiceMessageDao messageDao;
    @MockBean
    private SmotDao smotDao;
    @MockBean
    private CampaignEbciDao campaignEbciDao;
    @MockBean
    private StringRedisTemplate stringRedisTemplate;

    @Test
    public void getMember2unionidTest() {

        String unionid = "olSYu1SSfHeFuGQfaMC6jdM0LqfY";

        String url = "https://clinique.api.ibluesocial.com/clinique/getMember2unionid/" + getUnixStamp() + "?unionid=" + unionid;

        System.out.println(getUnixStamp());

        try {
            String result = HttpUtil.get(url);
            // 解析JSON
            JSONObject json = (JSONObject) JSON.parse(result);


            System.out.println(json.toJSONString());


        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testMemberPhoneIsXianxia() {
        OutsideServiceImpl outsideService = new OutsideServiceImpl(officialDao, stringRedisTemplate, wechatDao, motDao, messageDao, smotDao, campaignEbciDao);

        // urlCdpBase
        outsideService.setUrlCdpBase("http://47.103.190.7:12119/api/elc-data/newservice.asmx/");
        // urlGetRecordByMobile
        outsideService.setUrlGetRecordByMobile("CustomerSearchByMobile?brand=CL&appId=wx93d4fc2221a8408d&account=");

// <?xml version="1.0" encoding="utf-8"?><Results><CustomerLevel></CustomerLevel><TotalCount></TotalCount><ResultList><CustNearStoreCode></CustNearStoreCode><LastStoreName></LastStoreName><CustomerCid>601d1176c345a65ec3cf008a</CustomerCid><IsPurchase>否</IsPurchase><CustNearStore></CustNearStore><CustomerName>谢思宁</CustomerName><Mobile>***********</Mobile><LastStoreID></LastStoreID></ResultList><Message>已查询到顾客信息</Message><SucessCount></SucessCount><Code>True</Code><ResultLists></ResultLists><CodeStr></CodeStr></Results>
        assertThat(outsideService.isOnlineCustomer("***********"), is(true)) ;


    }


    @Test
    public void getMallMemberWithUnionid() {

        // Aaron: olSYu1SSfHeFuGQfaMC6jdM0LqfY

        String unionid = "olSYu1RwsJpdXIFBO1RiwdT--Rhg";

        try {

            String url = "https://miniappapi.clinique.com.cn/api/5e3a6e7f4a611.html";
            String version = "v2.0";
            String accessToken = "3ccfB8ab8NKjP8XZqDNqg2Xq8GYSwUd9";
            String key = "connext123123";

            Map<String, String> header = new HashMap<>();
            header.put("version", version);
            header.put("access-token", accessToken);

            System.out.println("url                    : " + url);
            System.out.println("accessToken            : " + accessToken);
            System.out.println("version                : " + version);
            System.out.println("key                    : " + key);
            System.out.println();
            System.out.println("unionid                : " + unionid);
            System.out.println("jsonString             : " + "{\"unionId\":\"" + unionid + "\"}");

            String sign = EncryptUtil.md5Encode(("{\"unionId\":\"" + unionid + "\"}" + key).toUpperCase()).toUpperCase();
            String param = "unionId=" + unionid + "&sign=" + sign;

            System.out.println("jsonString with Secret : " + "{\"unionId\":\"" + unionid + "\"}" + key);
            System.out.println("toUpperCase            : " + ("{\"unionId\":\"" + unionid + "\"}" + key).toUpperCase());
            System.out.println("sign                   : " + sign);
            System.out.println("toUpperCase            : " + sign.toUpperCase());
            System.out.println("param                  : " + "unionId=" + unionid + "&sign=" + sign);

            JSONObject result = (JSONObject) JSON.parse(HttpUtil.post(url, param, "x-www-form-urlencoded", header));

            System.out.println();
            System.out.println(result.toJSONString());

            if (1 == result.getInteger("code")) {
                JSONObject member = result.getJSONObject("data");
                System.out.println();
                System.out.println(member.getString("phone"));
            }

        } catch (Exception ignored) {
        }

    }

    /**
     * 获取系统时间
     *
     * @return 系统时间
     */
    private String getUnixStamp() {
        return String.valueOf(System.currentTimeMillis() / 1000);
    }

}
