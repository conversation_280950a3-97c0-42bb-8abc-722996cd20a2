package cn.slashsoft.clinique.service.mini.impl;

import cn.slashsoft.clinique.dao.mini.TTestimonyContentDao;
import cn.slashsoft.clinique.domain.mini.TTestimonyContent;
import cn.slashsoft.clinique.domain.mini.TTestimonyContentFilters;
import cn.slashsoft.clinique.domain.mini.TTestimonyFragment;
import cn.slashsoft.clinique.domain.mini.TTestimonyHelp;
import cn.slashsoft.clinique.service.mini.TTestimonyContentService;
import cn.slashsoft.clinique.service.mini.TTestimonyFragmentService;
import cn.slashsoft.clinique.vo.mini.TestimonyContentVo;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
* <p>
    *
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
@Service
public class TTestimonyContentServiceImpl implements TTestimonyContentService {

    @Resource
    private TTestimonyContentDao tTestimonyContentDao;

    @Resource
    private TTestimonyFragmentService tTestimonyFragmentService;

    @Override
    public void insertTTestimonyContent(TTestimonyContent tTestimonyContent) {
        // 获得积分 type  1 -> 8积分
        tTestimonyContentDao.insertTTestimonyContent(tTestimonyContent);
        tTestimonyFragmentService.addFragmentRecords(tTestimonyContent.getCustomerId(), 1);
    }

    @Override
    public void updateTTestimonyContent(TTestimonyContent tTestimonyContent) {
        tTestimonyContentDao.updateTTestimonyContent(tTestimonyContent);
    }

    @Override
    public TTestimonyContent getTTestimonyContent(Long id) {
        return tTestimonyContentDao.getTTestimonyContent(id);
    }

    public List<TestimonyContentVo> getTestimonyContentVo(Long customerId) {
    	List<TestimonyContentVo> all = new ArrayList<TestimonyContentVo>();
    	List<TestimonyContentVo> last = tTestimonyContentDao.getTestimonyContentVo(customerId);
    	List<TestimonyContentVo> top = tTestimonyContentDao.getInnerTestimonyContent(customerId);
    	Collections.shuffle(top);
    	int topLength = top.size();
    	int lastLength = last.size();
    	for(int i = 0;i<topLength;i++) {
    		all.add(top.get(i));
    		if(i<lastLength) {
    			all.add(last.get(i));
    		}
    	}
        return all;
    }

    /**
     * 获取证言条数
     * @param customerId
     */
    @Override
    public Integer getTTestimonyContentCountByCustomerId(long customerId) {
        return tTestimonyContentDao.getTTestimonyContentCountByCustomerId(customerId);
    }

    /**
     * 获取证言条数
     * @param customerId
     */
    @Override
    public Integer getTTestimonyContentCountByCustomerIdContent(long customerId, String content) {
        return tTestimonyContentDao.getTTestimonyContentCountByCustomerIdContent(customerId, content);
    }

    /**
     * 通过contentId获取用户id
     * @param contentId
     * @return
     */
    public Long getCustomerIdByContentId(long contentId) {
        return tTestimonyContentDao.getCustomerIdByContentId(contentId);
    }

    /**
     * 根据customerid contentid 获取记录
     * @param customerId
     * @param helpCustomerId
     * @return
     */
    public TTestimonyHelp getHelpByCustomerIdHelpCustomerId(long customerId, long helpCustomerId) {
        return tTestimonyContentDao.getHelpByCustomerIdHelpCustomerId(customerId, helpCustomerId);
    }

    /**
     * 获取最后一条证言
     * @param customerId
     * @return
     */
    public String getLastontent(long customerId) {
        return tTestimonyContentDao.getLastontent(customerId);
    }

    /**
     * 判断证言中是否包含需过滤的词汇
     * @param content
     * @return
     */
    public Boolean containsFiltersWord(String content) {
        Boolean bRet = false;
        //获得列表
        List<TTestimonyContentFilters> lst = tTestimonyContentDao.getContentFilters();

        //循环判断
        for(TTestimonyContentFilters tContentFilters: lst)  {
            if (content.contains(tContentFilters.getKeyWord())) {
                bRet = true;
                return bRet;
            }
        }
        return bRet;
    }

    /**
     * 添加过滤的记录
     * @param tTestimonyContent
     */
    @Override
    public void insertFilteredTTestimonyContent(TTestimonyContent tTestimonyContent) {
        tTestimonyContentDao.insertFilteredTTestimonyContent(tTestimonyContent);
    }

	@Override
	public Integer getTTestimonyContentCountByCustomerIdToday(long customerId) {
        return tTestimonyContentDao.getTTestimonyContentCountByCustomerIdTodady(customerId);
	}
}
