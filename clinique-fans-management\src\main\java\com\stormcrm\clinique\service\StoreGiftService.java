package com.stormcrm.clinique.service;

import com.stormcrm.clinique.domain.StoreGift;
import com.stormcrm.clinique.vo.Result;

import java.util.List;

/**
 * 商城礼品管理
 *
 * <AUTHOR>
 */
public interface StoreGiftService {

    /**
     * 查询所有礼品
     *
     * @return 礼品列表
     */
    List<StoreGift> getAll();

    /**
     * 查询所有礼品列表的记录数
     *
     * @return 记录数
     */
    int getAllCount();

    /**
     * 查询所有礼品带分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索
     * @param status        状态
     * @return 礼品列表分页
     */
    List<StoreGift> getPage(
            int page,
            int perpage,
            String generalSearch,
            Boolean status
    );

    /**
     * 查询所有礼品带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    int getPageCount(
            String generalSearch,
            Boolean status
    );

    /**
     * 查询信息
     *
     * @param id 自动编号
     * @return 胸章信息
     */
    StoreGift getById(long id);

    /**
     * 保存
     *
     * @param storeGift 信息
     * @return 影响的行数
     */
    Result save(StoreGift storeGift);

    /**
     * 更新
     *
     * @param storeGift 信息
     * @return 影响的行数
     */
    Result update(StoreGift storeGift);

    /**
     * 上架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    int upper(long id);

    /**
     * 下架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    int lower(long id);

    /**
     * 逻辑删除
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    int del(long id);

}
