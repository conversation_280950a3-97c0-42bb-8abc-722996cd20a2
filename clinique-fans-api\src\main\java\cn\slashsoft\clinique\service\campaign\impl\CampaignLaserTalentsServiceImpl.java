package cn.slashsoft.clinique.service.campaign.impl;

import cn.slashsoft.clinique.dao.campaign.CampaignLaserTalentsDao;
import cn.slashsoft.clinique.domain.mini.Follow;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.domain.campaign.*;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.service.campaign.CampaignLaserTalentsService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.RandomUtil;
import cn.slashsoft.clinique.util.ServerUtil;
import cn.slashsoft.clinique.util.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.Date;
import java.util.List;

/**
 * Laser 达人榜
 *
 * <AUTHOR>
 */
@Service
public class CampaignLaserTalentsServiceImpl implements CampaignLaserTalentsService {

    private final CampaignLaserTalentsDao campaignLaserTalentsDao;
    private final WechatService wechatService;
    private final OutsideService outsideService;

    public CampaignLaserTalentsServiceImpl(CampaignLaserTalentsDao campaignLaserTalentsDao, WechatService wechatService, OutsideService outsideService) {
        this.campaignLaserTalentsDao = campaignLaserTalentsDao;
        this.wechatService = wechatService;
        this.outsideService = outsideService;
    }

    /**
     * 获取达人榜信息
     *
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    @Override
    public CampaignLaserTalents getCampaignLaserTalentsAlert(long customerId) {
        return campaignLaserTalentsDao.getCampaignLaserTalentsWithImageCount(customerId);
    }

    /**
     * 获取达人榜信息
     *
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    @Override
    public CampaignLaserTalents getCampaignLaserTalents(long customerId) {

        CampaignLaserTalents campaignLaserTalents = campaignLaserTalentsDao.getCampaignLaserTalentsWithImageCount(customerId);

        if (null == campaignLaserTalents) {
            // 生成活动信息
            campaignLaserTalents = new CampaignLaserTalents();
            campaignLaserTalents.setCustomerId(customerId);
            campaignLaserTalents.setAlert(false);
            campaignLaserTalents.setStatus((short) 1);

            // 读取公众号openid
            Wechat wechat = wechatService.getWechatInfoByCustomerId(customerId);
            if (null != wechat && !StringUtil.isNullOrEmpty(wechat.getWechatOfficialOpenid())) {
                Follow follow = outsideService.isFollow(wechat.getWechatOfficialOpenid());
                if (null != follow) {
                    campaignLaserTalents.setFollow(follow.getFollow());
                    campaignLaserTalents.setFollowSource(follow.getFollowSource());
                    campaignLaserTalents.setFollowFirstTime(follow.getFollowFirstTime());
                    campaignLaserTalents.setFollowLastTime(follow.getFollowLastTime());
                    campaignLaserTalents.setFollowCancelTime(follow.getFollowCancelTime());
                    campaignLaserTalents.setBind(follow.getBind());
                    campaignLaserTalents.setBindTime(follow.getBindTime());
                }
            }

            campaignLaserTalentsDao.insertCampaignLaserTalents(campaignLaserTalents);

            // 写入日志
            CampaignLaserTalentsLog campaignLaserTalentsLog = new CampaignLaserTalentsLog();
            campaignLaserTalentsLog.setCustomerId(customerId);
            campaignLaserTalentsLog.setStatus((short) 1);
            campaignLaserTalentsLog.setContent("生成活动信息");
            campaignLaserTalentsDao.insertCampaignLaserTalentsLog(campaignLaserTalentsLog);
        } else {
            // 状态为未上传时，将所有图片改为无效
            if (1 == campaignLaserTalents.getStatus() && 0 < campaignLaserTalents.getImageCount()) {
                campaignLaserTalentsDao.updateCampaignLaserTalentsImageStatus(customerId);
            }
        }

        return campaignLaserTalents;
    }

    /**
     * 更新提醒状态
     *
     * @param customerId 顾客编号
     */
    @Override
    public void updateCampaignLaserTalentsAlert(long customerId) {
        campaignLaserTalentsDao.updateCampaignLaserTalentsAlert(customerId);
    }

    /**
     * 获取图片
     *
     * @param talentsId 顾客活动信息编号
     * @return 图片地址
     */
    @Override
    public List<CampaignLaserTalentsImage> getCampaignLaserTalentsImageList(long talentsId) {
        return campaignLaserTalentsDao.getCampaignLaserTalentsImageList(talentsId);
    }

    /**
     * 获取当前TOP信息
     *
     * @return TOP信息
     */
    @Override
    public CampaignLaserTalentsTop getCampaignLaserTalentsTop() {
        return campaignLaserTalentsDao.getCampaignLaserTalentsTop();
    }

    /**
     * 获取入选TOP的名单
     *
     * @param talentsTopId TOP信息编号
     * @return 名单
     */
    @Override
    public List<CampaignLaserTalentsTopDetail> getCampaignLaserTalentsTopDetailList(long talentsTopId) {
        return campaignLaserTalentsDao.getCampaignLaserTalentsTopDetailList(talentsTopId);
    }

    /**
     * 获取入选TOP的图片
     *
     * @param talentsTopId TOP信息编号
     * @return 图片
     */
    @Override
    public List<CampaignLaserTalentsTopDetailImage> getCampaignLaserTalentsTopDetailImageList(long talentsTopId) {
        return campaignLaserTalentsDao.getCampaignLaserTalentsTopDetailImageList(talentsTopId);
    }

    /**
     * 保存图片
     *
     * @param customerId 顾客编号
     * @param file       要保存的图片
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean upload(long customerId, MultipartFile file) {

        // 读取活动信息
        CampaignLaserTalents campaignLaserTalents = campaignLaserTalentsDao.getCampaignLaserTalentsWithImageCount(customerId);
        if (null == campaignLaserTalents) {
            return false;
        }

        CampaignLaserTalentsLog campaignLaserTalentsLog;
        switch (campaignLaserTalents.getStatus()) {
            // 未上传
            case 1:
                // 变更状态为2审核中
                if (0 == campaignLaserTalentsDao.updateCampaignLaserTalentsWithUploadTime(customerId, (short) 1, (short) 2)) {
                    return false;
                }
                // 写入日志
                campaignLaserTalentsLog = new CampaignLaserTalentsLog();
                campaignLaserTalentsLog.setCustomerId(customerId);
                campaignLaserTalentsLog.setStatus((short) 2);
                campaignLaserTalentsLog.setContent("状态从未上传变更为审核中");
                campaignLaserTalentsDao.insertCampaignLaserTalentsLog(campaignLaserTalentsLog);
                break;
            // 审核中
            case 2:
                // 检查当前相片的数量，超过3则不再保存
                if (3 <= campaignLaserTalents.getImageCount()) {
                    return false;
                }
                break;
            // 审核失败
            case 3:
                // 变更状态为2审核中
                if (0 == campaignLaserTalentsDao.updateCampaignLaserTalents(customerId, (short) 3, (short) 2)) {
                    return false;
                }
                // 原上传图片全部设为无效
                campaignLaserTalentsDao.updateCampaignLaserTalentsImageStatus(campaignLaserTalents.getId());
                // 写入日志
                campaignLaserTalentsLog = new CampaignLaserTalentsLog();
                campaignLaserTalentsLog.setCustomerId(customerId);
                campaignLaserTalentsLog.setStatus((short) 2);
                campaignLaserTalentsLog.setContent("状态从审核未通过变更为审核中");
                campaignLaserTalentsDao.insertCampaignLaserTalentsLog(campaignLaserTalentsLog);
                break;
            default:
                return false;
        }

        // 保存图片
        String imageUrl = outsideService.uploadImageOss(file);
        if (StringUtil.isNullOrEmpty(imageUrl)) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }

        // 保存数据库
        CampaignLaserTalentsImage campaignLaserTalentsImage = new CampaignLaserTalentsImage();
        campaignLaserTalentsImage.setTalentsId(campaignLaserTalents.getId());
        campaignLaserTalentsImage.setImageUrl(imageUrl);
        campaignLaserTalentsDao.insertCampaignLaserTalentsImage(campaignLaserTalentsImage);

        // 写入日志
        campaignLaserTalentsLog = new CampaignLaserTalentsLog();
        campaignLaserTalentsLog.setCustomerId(customerId);
        campaignLaserTalentsLog.setStatus((short) 2);
        campaignLaserTalentsLog.setContent("上传图片");
        campaignLaserTalentsDao.insertCampaignLaserTalentsLog(campaignLaserTalentsLog);

        return true;
    }

    /**
     * 保存图片
     *
     * @param path         目录
     * @param originalFile 要保存的图片
     * @return 保存成功的文件地址
     */
    private String saveImageFile(String path, MultipartFile originalFile) {

        try {
            Date now = new Date();

            // 获取静态资源文件的目录地址
            String staticPath = ServerUtil.getStaticPath();

            // 保存位置
            path = path + DateUtil.parseString(now, "yyyyMMdd");
            // 如果目录不存在，创建目录
            File folder = new File(staticPath + path);
            if (!folder.exists()) {
                if (folder.mkdirs()) {
                    throw new Exception("创建目录失败");
                }
            }

            // 获取上传文件名的后缀名
            String originalFileName = originalFile.getOriginalFilename();
            if (StringUtil.isNullOrEmpty(originalFileName)) {
                throw new Exception("上传的文件名为空");
            }
            // 生成文件名
            String fileName = path + "/" + DateUtil.parseString(now, "HHmmssSS") + RandomUtil.getNumberBetween(1000, 9999) + originalFileName.substring(originalFileName.lastIndexOf("."));

            File imageFile = new File(staticPath + fileName);

            originalFile.transferTo(imageFile);

            return fileName;
        } catch (Exception e) {
            return null;
        }

    }

    /**
     * 更新肤质
     *
     * @param customerId 顾客编号
     * @param skinType   肤质
     */
    @Override
    public void setCampaignLaserTalentsSkinType(long customerId, short skinType) {
        campaignLaserTalentsDao.setCampaignLaserTalentsSkinType(customerId, skinType);
    }

    /**
     * 写入访问开始日志
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @param page       页面
     * @return 访问日志编号
     */
    @Override
    public long insertCampaignLaserTalentsViewLog(long customerId, String source, String page) {
        CampaignLaserTalentsViewLog campaignLaserTalentsViewLog = new CampaignLaserTalentsViewLog();
        campaignLaserTalentsViewLog.setCustomerId(customerId);
        campaignLaserTalentsViewLog.setSource(source);
        campaignLaserTalentsViewLog.setPage(page);
        campaignLaserTalentsDao.insertCampaignLaserTalentsViewLog(campaignLaserTalentsViewLog);
        return campaignLaserTalentsViewLog.getId();
    }

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Override
    public void setCampaignLaserTalentsViewLog(long id) {
        campaignLaserTalentsDao.setCampaignLaserTalentsViewLog(id);
    }

    /**
     * 写入达人榜访问记录
     *
     * @param campaignLaserTalentsTopDetailLog 达人榜访问记录
     */
    @Override
    public void insertCampaignLaserTalentsTopDetailLog(CampaignLaserTalentsTopDetailLog campaignLaserTalentsTopDetailLog) {
        campaignLaserTalentsDao.insertCampaignLaserTalentsTopDetailLog(campaignLaserTalentsTopDetailLog);
    }

    /**
     * 写入达人榜图片访问记录
     *
     * @param campaignLaserTalentsTopDetailImageLog 达人榜图片访问记录
     */
    @Override
    public void insertCampaignLaserTalentsTopDetailImageLog(CampaignLaserTalentsTopDetailImageLog campaignLaserTalentsTopDetailImageLog) {
        campaignLaserTalentsDao.insertCampaignLaserTalentsTopDetailImageLog(campaignLaserTalentsTopDetailImageLog);
    }

}
