package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.PointType;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 积分类型
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface PointTypeDao {

    @Insert("INSERT INTO `point_type`(" +
            "   `name`, " +
            "   `image_url`, " +
//            "   `status`, " +
            "   `for_note_tag`, " +
            "   `reward_point`, " +
            "   `reward_active_start`, " +
            "   `reward_active_end` ," +
            "   `reward_max` ," +
            "   `reward_max_forever`" +
            ") " +
            "VALUES (" +
            "   #{name}," +
            "   #{imageUrl}," +
//            "   #{status}," +
            "   #{forNoteTag}," +
            "   #{rewardPoint}," +
            "   #{rewardActiveStart}," +
            "   #{rewardActiveEnd} ," +
            "   #{rewardMax} ," +
            "   #{rewardMaxForever}" +
            ")")
    Integer insertPointType(PointType pointType);

    @Insert("INSERT INTO `point_type`(" +
            "   `id`, " +
            "   `name`, " +
            "   `image_url`, " +
            "   `for_note_tag`, " +
            "   `reward_point`, " +
            "   `reward_active_start`, " +
            "   `reward_active_end` ," +
            "   `reward_max` ," +
            "   `reward_max_forever`" +
            ") " +
            "VALUES (" +
            "   #{id}," +
            "   #{name}," +
            "   #{imageUrl}," +
            "   #{forNoteTag}," +
            "   #{rewardPoint}," +
            "   #{rewardActiveStart}," +
            "   #{rewardActiveEnd} ," +
            "   #{rewardMax} ," +
            "   #{rewardMaxForever}" +
            ")")
    Integer insertPointTypeWithId(PointType pointType);

    @Update("UPDATE `point_type` set " +
            "   `name` = #{name} , " +
            "   `for_note_tag`= #{forNoteTag} , " +
            "   `reward_point`= #{rewardPoint} , " +
            "   `reward_active_start` = #{rewardActiveStart} ," +
            "   `reward_active_end` = #{rewardActiveEnd} ," +
            "   `reward_max` = #{rewardMax} ," +
            "   `reward_max_forever` = #{rewardMaxForever} " +
            "  WHERE id=#{id} ")
    Integer updatePointTypeWithNoteTagEdit(PointType pointType);

    @Update("UPDATE `point_type` set " +
            "   `name` = #{name} , " +

            "   `for_note_tag`= #{forNoteTag} , " +
            "   `reward_point`= #{rewardPoint} , " +
            "   `reward_active_start` = #{rewardActiveStart} ," +
            "   `reward_active_end` = #{rewardActiveEnd} ," +
            "   `reward_max` = #{rewardMax} ," +
            "   `reward_max_forever` = #{rewardMaxForever} " +
            "  WHERE id=#{id} ")
    Integer update(PointType pointType);

    /**
     * note tag 规则添加的 id 从100 开始
     * 第100个给最新的那个用了
     * 返回 100 之后的 id ；这是一个低频的后台设置操作，查出来后用这个做id 插入新的规则。
     */
    @Select("SELECT " +
            "   if(max(id)>=100,max(id) + 1,100) as id " +
            "FROM " +
            "   `point_type` ")
    Short getMaxId();

    /**
     * 查询 forNoteTag 通用奖励
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `point_type` " +
            "WHERE " +
            "   `for_note_tag`=1 ")
    List<PointType> getNormalNoteTagReward();

    /**
     * 查询 note_tag 中 关联的奖励条件
     *
     * @param pointTypeId point_type id
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `point_type` " +
            "WHERE " +
            "    `id`=#{pointTypeId} ")
    PointType getSpecialNoteTagReward(Short pointTypeId);

    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `point_type` " +
            "WHERE " +
            "   `id`=#{id} " +
            "LIMIT 1")
    PointType getById(Short id);

    /**
     * 查询所有奖励条件-分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 活动列表
     */
    @SelectProvider(type = PointTypesProvider.class, method = "getPage")
    List<PointType> getPage(
            @Param("pageIndex") int pageIndex,
            @Param("pageSize") int pageSize,
            @Param("generalSearch") String generalSearch,
            @Param("status") Short status
    );

    /**
     * 查询所有奖励条件带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    @SelectProvider(type = PointTypesProvider.class, method = "getPageCount")
    int getPageCount(
            @Param("generalSearch") String generalSearch,
            @Param("status") Short status
    );

    @Delete("DELETE FROM `point_type`" +
            " WHERE " +
            " `id`=#{id} ")
    int delete(Long id);

}
