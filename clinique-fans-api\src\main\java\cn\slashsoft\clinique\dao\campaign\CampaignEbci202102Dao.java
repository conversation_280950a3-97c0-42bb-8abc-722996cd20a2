package cn.slashsoft.clinique.dao.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignEbciDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciLog;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciStock;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciAD;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciCity;
import cn.slashsoft.clinique.domain.campaign.CampaignTemp;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

/**
 * 302美白镭射瓶
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignEbci202102Dao {

    /**
     * 保存日志
     *
     * @param campaignEbciLog 日志
     */
    @Insert("INSERT INTO `campaign_ebci_202102_log`(" +
            "   `type`," +
            "   `unique_id`, " +
            "   `source`," +
            "   `click_id`," +
            "   `page`" +
            ") " +
            "VALUES (" +
            "   #{type}, " +
            "   #{uniqueId}, " +
            "   #{source}," +
            "   #{clickId}, " +
            "   #{page}" +
            ")")
    void insertLog(CampaignEbciLog campaignEbciLog);

    /**
     * 获取是否已经审领
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    @Select("SELECT " +
            "   COUNT(*) " +
            "FROM " +
            "   `campaign_ebci_202102_detail` " +
            "WHERE " +
            "    `phone_number`=#{phoneNumber} " +
            "LIMIT 1")
    int hasDetail(String phoneNumber);

    /**
     * 获取是否已经审领
     *
     * @param openid 公众号唯一编号
     * @return 数量
     */
    @Select("SELECT " +
            "   COUNT(*) " +
            "FROM " +
            "   `campaign_ebci_202102_detail` " +
            "WHERE " +
            "   `unique_id`=#{openid} " +
            "LIMIT 1")
    int hasDetailByOpenid(String openid);

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    @Select("SELECT " +
            "   `id`, `name`,  `phone_number`, `city`, `store`, `follow` " +
            "FROM " +
            "   `campaign_ebci_202102_detail` " +
            "WHERE " +
            "    `phone_number`=#{phoneNumber} " +
            "LIMIT 1")
    CampaignEbciDetail getDetail(String phoneNumber);

    /**
     * 获取审领信息
     *
     * @param openid 公众号唯一编号
     * @return 审领信息
     */
    @Select("SELECT " +
            "   `id`, `name`,  `phone_number`, `city`, `store`, `follow`, `receive` " +
            "FROM " +
            "   `campaign_ebci_202102_detail` " +
            "WHERE " +
            "   `unique_id`=#{openid} " +
            "LIMIT 1")
    CampaignEbciDetail getDetailByOpenid(String openid);

    /**
     * 获取手机号码
     *
     * @param unionid 开放平台唯一编号
     * @return 手机号码
     */
    @Select("SELECT " +
            "    decryptPhone(`c`.`phone_number`) phone_number " +
            "FROM " +
            "   `wechat` `w` " +
            "       INNER JOIN " +
            "   `customer` `c` " +
            "       ON `w`.`customer_id`=`c`.`id` " +
            "WHERE " +
            "   `w`.`unionid`=#{unionid}")
    String getPhoneNumberByUnionid(String unionid);

    /**
     * 获取手机号码
     *
     * @param openid 公众号唯一编号
     * @return 手机号码
     */
    @Select("SELECT " +
            "    decryptPhone(`c`.`phone_number`) phone_number " +
            "FROM " +
            "   `wechat` `w` " +
            "       INNER JOIN " +
            "   `customer` `c` " +
            "       ON `w`.`customer_id`=`c`.`id` " +
            "WHERE " +
            "   `w`.`wechat_official_openid`=#{openid}")
    String getPhoneNumberByOpenid(String openid);

    /**
     * 扣库存
     *
     * @param campaignEbciDetail 申领信息
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_ebci_202102_stock` " +
            "SET " +
            "   `stock`=`stock`-1, " +
            "   `total`=`total`+1 " +
            "WHERE " +
            "   `city`=#{city} " +
            "   AND `store`=#{store} " +
            "   AND `stock`>0")
    int updateStock(CampaignEbciDetail campaignEbciDetail);

    /**
     * 定入申领信息
     *
     * @param campaignEbciDetail 申领信息
     */
    @Insert("INSERT INTO `campaign_ebci_202102_detail`(" +
            "   `type`, " +
            "   `unique_id`, " +
            "   `name`, " +
            "   `phone_number`, " +
            "   `city`, " +
            "   `store`, " +
            "   `store_id`, " +
            "   `source` " +
            ") " +
            "VALUES (" +
            "   #{type}, " +
            "   #{uniqueId}, " +
            "   #{name}, " +
            "   #{phoneNumber}, " +
            "   #{city}, " +
            "   #{store}, " +
            "   #{storeId}, " +
            "   #{source} " +
            ")")
    void insetDetail(CampaignEbciDetail campaignEbciDetail);

    /**
     * 更新是否关注
     *
     * @param campaignEbciDetail 申领信息
     */
    @Update("UPDATE " +
            "   `campaign_ebci_202102_detail` " +
            "SET " +
            "   `follow`=#{follow}," +
            "   `follow_source`=#{followSource}," +
            "   `follow_first_time`=#{followFirstTime}," +
            "   `follow_last_time`=#{followLastTime}," +
            "   `follow_cancel_time`=#{followCancelTime}," +
            "   `bind`=#{bind}," +
            "   `bind_time`=#{bindTime} " +
            "WHERE " +
            "   `id`=#{id}")
    void setFollow(CampaignEbciDetail campaignEbciDetail);

    /**
     * 跟据场景值获取门店
     *
     * @param scene 场景值
     * @return 门店
     */
    @Select("SELECT " +
            "   `city`,`store`,`store_id` " +
            "FROM " +
            "   `campaign_ebci_202102_stock` " +
            "WHERE " +
            "   `scene`=#{scene} " +
            "LIMIT 1")
    CampaignEbciStock getByScene(String scene);

    /**
     * 跟据门店名称获取门店状态
     *
     * @param scene 场景值
     * @return 门店
     */
    @Select("SELECT " +
            "   `city`,`store`,`store_id`,`status` " +
            "FROM " +
            "   `campaign_ebci_202102_stock` " +
            "WHERE " +
            "   `city`=#{city} " +
            "   AND `store`=#{store} " +
            "LIMIT 1")
    CampaignEbciStock getByStoreName(CampaignEbciDetail campaignEbciDetail);
    /**
     * 更新核销信息
     *
     * @param campaignEbciDetail 核销信息
     */
    @Update("UPDATE " +
            "   `campaign_ebci_202102_detail` " +
            "SET " +
            "   `receive`=1, " +
            "   `receive_city`=#{receiveCity}, " +
            "   `receive_store`=#{receiveStore}, " +
            "   `receive_store_id`=#{receiveStoreId}, " +
            "   `receive_time`=NOW(), " +
            "   `scene`=#{scene}," +
            "   `cdp`=0 " +
            "WHERE " +
            "   `id`=#{id} ")
    void setReceive(CampaignEbciDetail campaignEbciDetail);

  

     /**
      * 获取是否已审领   临时需求
      *
      * @param phoneNumber 手机号码
      * @return 审领信息
      */
     @Select("SELECT " +
             "   `id`, `name`, `phone_number`, `city`, `store`, `follow` " +
             "FROM " +
             "   `campaign_202102_detail` " +
             "WHERE " +
             "   `phone_number`=#{phoneNumber} " +
             "LIMIT 1")
     CampaignEbciDetail getTmpDetail(String phoneNumber);

     
     /**
      * 定入申领信息   临时需求
      *
      * @param campaignEbciDetail 申领信息
      */
     @Insert("INSERT INTO `campaign_202102_detail`(" +
             "   `type`, " +
             "   `unique_id`, " +
             "   `name`, " +
             "   `phone_number`, " +
             "   `city`, " +
             "   `store`, " +
             "   `store_id`, " +
             "   `source` " +
             ") " +
             "VALUES (" +
             "   #{type}, " +
             "   #{uniqueId}, " +
             "   #{name}, " +
             "   #{phoneNumber}, " +
             "   #{city}, " +
             "   #{store}, " +
             "   #{storeId}, " +
             "   #{source} " +
             ")")
     void insetTmpDetail(CampaignEbciDetail campaignEbciDetail);


     @Select("SELECT * from campaign_ebci_202102_city where `code` = #{city} AND `campaign` = #{campaign}")
     CampaignEbciCity getCity(String city, long campaign);

}
