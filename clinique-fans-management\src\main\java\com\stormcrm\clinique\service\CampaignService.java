package com.stormcrm.clinique.service;

import com.stormcrm.clinique.domain.Campaign;
import com.stormcrm.clinique.vo.Result;

import java.util.List;

/**
 * 活动管理
 *
 * <AUTHOR>
 */
public interface CampaignService {

    /**
     * 查询所有活动
     *
     * @return 活动列表
     */
    List<Campaign> getAll();

    /**
     * 查询所有活动列表的记录数
     *
     * @return 记录数
     */
    int getAllCount();

    /**
     * 查询所有活动带分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索
     * @param status        状态
     * @return 活动列表分页
     */
    List<Campaign> getPage(
            int page,
            int perpage,
            String generalSearch,
            Boolean status
    );

    /**
     * 查询所有活动带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    int getPageCount(
            String generalSearch,
            Boolean status
    );

    /**
     * 查询信息
     *
     * @param id 自动编号
     * @return 信息
     */
    Campaign getById(long id);

    /**
     * 保存
     *
     * @param campaign 信息
     * @return 影响的行数
     */
    Result save(Campaign campaign);

    /**
     * 更新
     *
     * @param campaign 信息
     * @return 影响的行数
     */
    Result update(Campaign campaign);

    /**
     * 上架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    int upper(long id);

    /**
     * 下架
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    int lower(long id);

    /**
     * 逻辑删除
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    int del(long id);

}
