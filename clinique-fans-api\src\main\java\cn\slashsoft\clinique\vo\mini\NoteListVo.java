package cn.slashsoft.clinique.vo.mini;

import java.util.List;

import cn.slashsoft.clinique.domain.mini.CIndexBanner;
import cn.slashsoft.clinique.domain.mini.MiniWindow;
import cn.slashsoft.clinique.domain.mini.Note;
import lombok.Data;

/**
 * 活动页面返回数据
 * <AUTHOR>
 */
@Data
public class NoteListVo {
	private int allCount ;
	private int page;
	private int pageSize;
    private List<Note> noteList;
	private List<CIndexBanner>  bannerList;
	
	private MiniWindow window;
}
