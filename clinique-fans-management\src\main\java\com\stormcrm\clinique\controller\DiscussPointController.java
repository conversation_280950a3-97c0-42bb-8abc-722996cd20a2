package com.stormcrm.clinique.controller;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import com.stormcrm.clinique.dao.PointDao;
import com.stormcrm.clinique.domain.PointTransaction;
import com.stormcrm.clinique.domain.Wechat;
import com.stormcrm.clinique.enums.PointForeignEnum;
import com.stormcrm.clinique.enums.PointTypeEnum;
import com.stormcrm.clinique.service.DiscussPointService;
import com.stormcrm.clinique.util.DateUtil;


@Controller
@RequestMapping("/discuss")
public class DiscussPointController {
    private final DiscussPointService discussPointService;
    private final PointDao pointDao;

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    public DiscussPointController(DiscussPointService discussPointService, PointDao pointDao) {
        this.discussPointService = discussPointService;
        this.pointDao = pointDao;
    }

    @PreAuthorize("hasAuthority('DISCUSS_POINT')")
    @GetMapping("")
	public String Look(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/discuss/index";		
	}
    
    @PreAuthorize("hasAuthority('DISCUSS_POINT')")
    @PostMapping(value = "get-all", produces = "application/json;charset=UTF-8")
    @ResponseBody
	public String All(
            @RequestParam(value = "pagination[page]", required = false) Integer page,
            @RequestParam(value = "pagination[pages]", required = false) Integer pages,
            @RequestParam(value = "pagination[perpage]", required = false) Integer perpage,
            @RequestParam(value = "pagination[total]", required = false) Integer total,
            @RequestParam(value = "query[generalSearch]", required = false) String nickName) {
    	 List<Wechat> WechatList = discussPointService.getAll(nickName);
    	 
    	 JSONObject mata = new JSONObject();
         mata.put("page", page);
         mata.put("pages", pages);
         mata.put("perpage", perpage);
         mata.put("total", WechatList.size());
         mata.put("sort", "asc");
         mata.put("field", "id");

         JSONObject result = new JSONObject();
         result.put("mata", mata);
         result.put("data", WechatList);

         return JSONObject.toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);	
	}
    

    @PreAuthorize("hasAuthority('DISCUSS_POINT')")
    @GetMapping("edit/{id}")
	public String Edit(@PathVariable("id") String id, Model model) {
        model.addAttribute("staticDomain", staticDomain);
    	Wechat customer = discussPointService.getOne(id);
        
        model.addAttribute("customer", customer);
        return "m/fans/discuss/addPoint";		
	}
    
    @PreAuthorize("hasAuthority('DISCUSS_POINT')")
    @PostMapping(value = "addPoint", produces = "application/json;charset=UTF-8")
    @ResponseBody
	public String AddPoint(
            @RequestParam(value = "customerId", required = false) String id,
            @RequestParam(value = "disucsstype", required = false) String type) {
    	 Date now = new Date();
   
         PointTransaction pointTransaction = new PointTransaction();
         pointTransaction.setCustomerId(Long.parseLong(id.trim()));
         pointTransaction.setPointTypeId(Short.parseShort(type.trim()));
         pointTransaction.setStartTime(now);
         pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
         pointTransaction.setForeignMasterId(0L);
         pointTransaction.setForeignDetailId(Long.parseLong("1"));
         switch(type.trim()) {
	        case "3":
	             pointTransaction.setPoints(PointTypeEnum.COMMENT.getPoints());
	             pointTransaction.setRemainingPoints(PointTypeEnum.COMMENT.getPoints());
	             pointTransaction.setForeignId(PointForeignEnum.COMMENT.getId());
	             break;
         	case "4":
                pointTransaction.setPoints(PointTypeEnum.LIKE.getPoints());
                pointTransaction.setRemainingPoints(PointTypeEnum.LIKE.getPoints());
                pointTransaction.setForeignId(PointForeignEnum.LIKE.getId());
         		break;
         }
         pointDao.insertPointTransaction(pointTransaction);

         JSONObject result = new JSONObject();
         result.put("code", 1);
         result.put("message", "ok");

         return JSONObject.toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);	
	}
    
}
