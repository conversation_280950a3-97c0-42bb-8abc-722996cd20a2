package cn.slashsoft.clinique.controller.official;

import java.io.InputStream;
import java.util.Date;
import java.util.Map;
import java.util.logging.Logger;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONObject;

import cn.slashsoft.clinique.dao.mini.WelcomeMediaDao;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.domain.mini.WechatJssdk;
import cn.slashsoft.clinique.domain.mini.WelcomeMedia;
import cn.slashsoft.clinique.domain.official.WechatFriend;
import cn.slashsoft.clinique.domain.official.WechatUserInfo;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.MapUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.util.XmlUtil;

/**
 * 公众号相关页面
 *
 * <AUTHOR> ,berg
 */
@Controller
@RequestMapping("/official")
public class OfficialController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    private final Logger logger = Logger.getLogger(OfficialController.class.getName());

    @Value("${wechat.template.campaign-receive}")
    private String templateCampaignReceive;

    @Resource
    private HttpSession session;

    @Resource
    private HttpServletRequest request;
    
    @Resource
    private WelcomeMediaDao welcomeMediaDao;

    private final OutsideService outsideService;
    private final WechatService wechatService;

    public OfficialController(OutsideService outsideService, WechatService wechatService) {
        this.outsideService = outsideService;
        this.wechatService = wechatService;
    }

    /**
     * 授权回调页 - 静默授权
     *
     * @return 处理绍果
     */
    @GetMapping("/auth")
    public String auth(
            @RequestParam(value = "openid") String openid,
            @RequestParam(value = "unionid", required = false) String unionid
    ) {

        session.setAttribute("unionid", unionid);
        session.setAttribute("officialOpenid", openid);

        return "redirect:" + session.getAttribute("officialOauthPath");
    }

    /**
     * 授权回调页 - 主动授权
     *
     * @return 处理绍果
     */
    @GetMapping("/auth-pro")
    public String authPro(
            @RequestParam(value = "openid") String openid,
            @RequestParam(value = "accesstoken") String accesstoken,
            @RequestParam(value = "unionid", required = false) String unionid
    ) {

        logger.info("授权回调：unionid - " + unionid + " , openid - " + openid);

        WechatUserInfo wechatUserInfo = outsideService.getUserInfo(openid, accesstoken);
        if(null == wechatUserInfo){
            return null;
        }

        session.setAttribute("officialOpenid", openid);
        session.setAttribute("unionid", wechatUserInfo.getUnionid());
        session.setAttribute("nickName", wechatUserInfo.getNickName());
        session.setAttribute("avatarUrl", wechatUserInfo.getAvatarUrl());

        return "redirect:" + session.getAttribute("officialOauthPath");
    }

    /**
     * 欢迎页
     *
     * @return 模版
     */
    @GetMapping("/welcome")
    public String welcome(
            @RequestParam("unionid") String unionid,
            @RequestParam(value = "source", required = false) String source,
            @RequestParam(value = "registeredUrl", required = false) String registeredUrl,
            Model model) {
        model.addAttribute("staticDomain", staticDomain);

        if (StringUtil.isNullOrEmpty(unionid)){
            unionid = (String) session.getAttribute("unionid");
        }

        String openid = (String) request.getAttribute("miniOpenid");
        if(StringUtil.isNullOrEmpty(openid)){
            openid = (String) session.getAttribute("officialOpenid");
        }

        logger.info("欢迎页：unionid - " + unionid + " , openid - " + openid);

        if (StringUtil.isNullOrEmpty(registeredUrl)) {
            registeredUrl = "/pages/community/index/index";
        }

        if (!StringUtil.isNullOrEmpty(unionid) && !StringUtil.isNullOrEmpty(openid)){
            // 更新微信信息
            wechatService.setOfficialOpenid(unionid, openid);

            // 传给公众号信息
            //outsideService.bindMember(unionid, openid);
        }
        if(! StringUtil.isNullOrEmpty(source) ) {
        	if(registeredUrl.indexOf("?") > 0) {
        		registeredUrl = registeredUrl + "&source=" + source;
        	}else {
        		registeredUrl = registeredUrl + "?source=" + source;
        	}
        	
        }

        logger.info("registeredUrl - " + registeredUrl);
        model.addAttribute("registeredUrl", registeredUrl );
        return "w/fans/official/welcome";

    }

    @PostMapping("/server")
    @ResponseBody
    public String server() {
        try {

            InputStream inputStream = request.getInputStream();
            Map<String, Object> result = XmlUtil.xmlToMap(inputStream);
            inputStream.close();

            if (null == result || 0 == result.size()) {
                throw new Exception("参数格式错误");
            }

            wechatService.saveEvent(new JSONObject(result).toString());
            String msgType = MapUtil.getString(result, "MsgType");
            String event = MapUtil.getString(result, "Event");
            if ("event".equals(msgType) ) {
            	int friendStatus = -1;
            	if("subscribe".equals(event)) {
            		friendStatus = 1;            		
            	}else if("unsubscribe".equals(event)) {
            		friendStatus = 0;
            	}
            	if(friendStatus >=0) {
                    String openid = MapUtil.getString(result, "FromUserName");
            		//System.out.println(openid + "///-----------------------------///");
                    if (!StringUtil.isNullOrEmpty(openid) ) {
                    	WechatFriend wf = wechatService.getWechatFriendByOpenid(openid);
                		Date now = new Date();
                    	if(null == wf) {
                    		wf = new WechatFriend();
                    		
                    		Wechat we  = wechatService.getWechatByOfficialOpenid(openid);
                    		if(null == we || StringUtil.isNullOrEmpty(we.getUnionid())) {
                        		wf.setUnionid("");
                    		}else {
                        		wf.setUnionid(we.getUnionid());
                    		}
                    		//System.out.println(we.getUnionid() + "///-----------------------------///");
                    		
                    		wf.setAttentionTime(now);
                    		wf.setStatus(friendStatus);
                    		wf.setSyncStatus(0);
                    		wf.setFirstAttentionTime(now);
                    		wf.setOpenid(openid);
                    		wechatService.addWechatFriend(wf);
                    	}else {
                    		if(friendStatus ==1) {
                        		wf.setAttentionTime(now);                    			
                    		}else {
                    			wf.setUnAttentionTime(now);
                    		}
                    		wf.setStatus(friendStatus);
                    		wf.setSyncStatus(0);
                    		wechatService.updateWechatFriend(wf);
                    	}
                    }
            	}
            	
            	if(!DateUtil.earlierThanNow(DateUtil.valueOf("2021-07-31 23:59:59"))) {
            		WelcomeMedia wm = null;
                    String eventKey = MapUtil.getString(result, "EventKey");
                	if("subscribe".equals(event)) {
    	            	//扫描关注发送语音二维码事件
    	                if(null != eventKey ) {
    	                	if(eventKey.startsWith("qrscene_")) {
    		                	wm = welcomeMediaDao.getWelcomeMediaByKey(eventKey);
    		                }
    	                }
                	}else if("SCAN".equals(event) && !StringUtil.isNullOrEmpty(eventKey) ) {
                		wm = welcomeMediaDao.getWelcomeMediaByKey(eventKey);
                	}
    	
                    String openid = MapUtil.getString(result, "FromUserName");
                	if(null != wm 
                			&& !StringUtil.isNullOrEmpty(wm.getMediaId())                			
                			&& !StringUtil.isNullOrEmpty(openid)) {
        				String voice = "{\"touser\":\"" + openid + "\"," 
     			    		   + "\"msgtype\":\"voice\"," 
     			    		   + "\"voice\":"
     			    		   + "{"
     			    		   + "   \"media_id\":\"" + wm.getMediaId() + "\" "
     			    		   + "}"
     			    		   + "}";
                    	outsideService.customerServiceMessageSend((short)14, openid , voice);
                	}
            	}
                    
        	}
            
        } catch (Exception e) {
            logger.warning("微信事件消息处理错误：" + e.getMessage());
        }
        return "{\"code\":200}";
    }

    @GetMapping("/getJssdk")
    @ResponseBody
    public String getJssdk(
            @RequestParam("url") String url,
            @RequestParam("api") String api
    ){
        if (StringUtil.isNullOrEmpty(url) || StringUtil.isNullOrEmpty(api)){
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        String[] apiArray = api.split(",");

        WechatJssdk wechatJssdk = outsideService.getJssdk(url, apiArray);
        if(null == wechatJssdk){
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, "SUCCESS", wechatJssdk);

    }

    /**
     * 是否包含openid
     *
     * @param openids 多个openid
     * @param openid  openid
     * @return 是否
     */
    private boolean have(String openids, String openid) {

        if (StringUtil.isNullOrEmpty(openid) || StringUtil.isNullOrEmpty(openids)) {
            return false;
        }

        String[] openidArray = openids.split(",");
        for (String s : openidArray) {
            if (openid.equals(s)) {
                return true;
            }
        }

        return false;
    }
}
