package cn.slashsoft.clinique.domain.mini;

import lombok.Data;

import java.util.Date;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;

import org.springframework.beans.factory.annotation.Value;

import cn.slashsoft.clinique.util.EncryptUtil;
import io.netty.util.internal.StringUtil;

/**
 * 顾客表
 *
 * <AUTHOR>
 */
@Data
public class Customer {

    private Long id;
    private String unionid;
    private String phoneNumber;
    private String name;
    private String avatarUrl;
    private Date birthday;
    private Short birthdayUpdateTimes;
    private String xiaohongshuAccount;
    private Boolean updateReward;
    private Integer pointsTotal;
    private Long ranking;
    private Long rankingAlways;
    private Boolean rankingExchange;
    private String campaign;
    private String source;
    private Boolean status;
    private Date updateTime;
    private Date createTime;
    /* 给用户加一个小绿色V */
    private Integer kocV;
    private Boolean agreeBrandCommunicate;
    private Boolean agreeCorpCommunicate;
    //For 加密
    private String userPhone;
    private String userName;

    /*
    public String getPhoneNumber() {
    	if(StringUtil.isNullOrEmpty(this.userPhone)) {
    		this.userPhone = EncryptUtil.mysqlDecode(this.phoneNumber);
    	}
    	return this.userName;
    }
    public String getName() {
    	if(StringUtil.isNullOrEmpty(this.userName)) {
    		this.userName = EncryptUtil.mysqlDecode(this.name);
    	}
    	return this.userName;
    }
    */
}
