package com.stormcrm.clinique.service;

import java.util.ArrayList;
import java.util.List;

import com.stormcrm.clinique.domain.NoteTag;

/**
 * 笔记
 *
 * <AUTHOR>
 */
public interface NoteTagService {
   
    /**
     * 添加标签
    *
    * @param noteTag
    */
    void insertTag(NoteTag noteTag) ;
   /**
    * 修改标签
    *
    * @param noteTag
    */
   void updateTag(NoteTag noteTag, boolean withImage) ;
   /**
    * 修改标签
    *
    * @param id
    */
   NoteTag getTag(Long id) ;
   /**
    * 删除标签
    *
    * @param id
    */
    int deleteTag(Long id) ;

    /**
     * 查询所有活动带分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索
     * @param top        状态
     * @return 活动列表分页
     */

    List<NoteTag> getPage(int page, int perpage, String generalSearch, Short top) ;

    /**
     * 查询所有活动带分页的记录数
     *
     * @param generalSearch 搜索
     * @param top        状态
     * @return 记录数
     */
    int getPageCount(String generalSearch, Short top);


}
