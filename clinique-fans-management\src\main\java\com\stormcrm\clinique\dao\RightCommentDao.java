package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.RightComment;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 评论权益相关的数据库操作
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface RightCommentDao {

    /**
     * 获取记录总数
     * @return 记录总数
     */
    @Select("SELECT " +
            "   COUNT(*) " +
            "FROM " +
            "   `right_comment`"
    )
    int getCount();

    /**
     * 按页读取评论权益
     *
     * @param page 页码
     * @param size 分页大小
     * @return 评论权益
     */
    @Select("SELECT " +
            "   `id`, " +
            "   `name`, " +
            "   `total`, " +
            "   `file_url`, " +
            "   `is_done`, " +
            "   `done_time`, " +
            "   `create_time` " +
            "from " +
            "   `right_comment` " +
            "order by " +
            "   `id` desc " +
            "limit " +
            "   #{page},#{size}"
    )
    List<RightComment> getPage(@Param("page") int page, @Param("size") int size);

    /**
     * 写入评论导入日志
     * @param rightComment 导入日志
     */
    @Insert("INSERT INTO `right_comment`(" +
            "   `name`, " +
            "   `file_url`, " +
            "   `data_count` " +
            ") " +
            "VALUES (" +
            "   #{name}, " +
            "   #{fileUrl}, " +
            "   #{dataCount} " +
            ")"
    )
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertRightComment(RightComment rightComment);

    /**
     * 更新评论处理完成信息
     * @param rightComment 评论处理完成信息
     */
    @Update("UPDATE " +
            "   `right_comment` " +
            "SET " +
            "   `is_done`=1," +
            "   `done_time`=NOW(), " +
            "   `done_count`=#{doneCount} " +
            "WHERE " +
            "   `id`=#{id}")
    void setRightCommentDone(RightComment rightComment);

}
