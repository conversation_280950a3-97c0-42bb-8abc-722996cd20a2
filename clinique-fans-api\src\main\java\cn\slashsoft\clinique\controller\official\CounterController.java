package cn.slashsoft.clinique.controller.official;

import cn.slashsoft.clinique.domain.mini.Counter;
import cn.slashsoft.clinique.domain.mini.CounterLog;
import cn.slashsoft.clinique.domain.mini.WechatJssdk;
import cn.slashsoft.clinique.service.mini.CounterService;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.util.ServerUtil;
import com.alibaba.fastjson.JSON;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;

/**
 * 柜台
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/official")
public class CounterController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final OutsideService outsideService;
    private final CounterService counterService;

    public CounterController(OutsideService outsideService, CounterService counterService) {
        this.outsideService = outsideService;
        this.counterService = counterService;
    }

    /**
     * 首页
     *
     * @param model 模型
     * @return 首页
     */
    @GetMapping("/counter/index")
    public String index(Model model) {
        model.addAttribute("staticDomain", staticDomain);

        String url = ServerUtil.getFullUrl(request, "/fans");
        String[] apiArray = {"openLocation", "getLocation"};
        WechatJssdk wechatJssdk = outsideService.getJssdk(url, apiArray);

        model.addAttribute("wechatJssdk", JSON.toJSONString(wechatJssdk));
        return "w/fans/official/counter/index";
    }

    /**
     * 查找门店
     *
     * @param latitude  经度
     * @param longitude 维度
     * @param model     模型
     * @return 门店
     */
    @RequestMapping("/counter/geo")
    public String geo(
            @RequestParam("latitude") BigDecimal latitude,
            @RequestParam("longitude") BigDecimal longitude,
            @RequestParam("init") String init,
            Model model
    ) {
        List<Counter> counterList = counterService.getCounter(latitude, longitude);

        if("INIT".equals(init) && null != counterList && 0 < counterList.size()){
            String openid = (String) request.getAttribute("miniOpenid");
            Counter counter = counterList.get(0);
            CounterLog counterLog = new CounterLog();
            counterLog.setType((short) 2);
            counterLog.setWechatOfficialOpenid(openid);
            counterLog.setLatitude(latitude);
            counterLog.setLongitude(longitude);
            counterLog.setProvince(counter.getProvince());
            counterLog.setCity(counter.getCity());
            counterLog.setDistrict(counter.getDistrict());
            counterLog.setName(counter.getName());
            counterLog.setAddress(counter.getAddress());
            counterLog.setDistance(counter.getDistance());
            counterService.insertCounterLog(counterLog);
        }
      
            model.addAttribute("staticDomain", staticDomain);
        model.addAttribute("counterList", counterList);
        return "w/fans/official/counter/geo";
    }

    /**
     * 查找门店
     *
     * @param latitude  经度
     * @param longitude 维度
     * @param province  省份
     * @param city      城市
     * @param name      门店名称
     * @param model     模型
     * @return 门店
     */
    @RequestMapping("/counter/search")
    public String search(
            @RequestParam("latitude") BigDecimal latitude,
            @RequestParam("longitude") BigDecimal longitude,
            @RequestParam(value = "province", required = false) String province,
            @RequestParam(value = "city", required = false) String city,
            @RequestParam(value = "name", required = false) String name,
            Model model
    ) {
        List<Counter> counterList = counterService.getCounterBySearch(latitude, longitude, province, city, name);
    
            model.addAttribute("staticDomain", staticDomain);
        model.addAttribute("counterList", counterList);
        return "w/fans/official/counter/search";
    }

}
