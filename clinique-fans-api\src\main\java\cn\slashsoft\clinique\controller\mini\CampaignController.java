package cn.slashsoft.clinique.controller.mini;

import cn.slashsoft.clinique.domain.mini.Campaign;
import cn.slashsoft.clinique.domain.mini.CampaignLog;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.CampaignService;
import cn.slashsoft.clinique.service.mini.CustomerService;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.vo.mini.CampaignVo;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 活动相关控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class CampaignController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final CampaignService campaignService;
    private final CustomerService customerService;
    private final StringRedisTemplate stringRedisTemplate;
    private final WechatService wechatService;

    public CampaignController(CampaignService campaignService, CustomerService customerService, WechatService wechatService, StringRedisTemplate stringRedisTemplate) {
        this.campaignService = campaignService;
        this.customerService = customerService;
        this.wechatService = wechatService;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 获取所有有效活动信息接口
     * 活动资讯
     *
     * @return 小程序返回值，携带活动信息
     */
    @PostMapping("/campaign/get-data")
    public String getCampaignData() {

        List<Campaign> campaignList = campaignService.getCampaign();

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        // 公众号openid
        String openid = (String) request.getAttribute("officialOpenid");
        // session中获取
        if (StringUtil.isNullOrEmpty(openid)) {
            openid = (String) request.getSession().getAttribute("officialOpenid");
        }
        // 缓存中没有公众号openid，
        if (StringUtil.isNullOrEmpty(openid)) {
            Wechat wechat = wechatService.getWechatInfoByCustomerId(customerId);
            if (null !=wechat) {
                openid = wechat.getWechatOfficialOpenid();
            }
        }
        String phoneNumber = customerService.getCustomerPhoneNumberById(customerId);

        CampaignVo campaignVo = new CampaignVo();
        campaignVo.setCampaignList(campaignList);

        // 活动开启，手机号不空，并且不在排除名单里
        // campaignVo.setC520("ON".equals(stringRedisTemplate.opsForValue().get("campaignC520")) && !StringUtil.isNullOrEmpty(phoneNumber) && 0 < campaignService.getCampaignC520Exclude(phoneNumber));
        campaignVo.setC520(false);
        campaignVo.setG520("ON".equals(stringRedisTemplate.opsForValue().get("campaignG520")));
        // 不在黑名单内可见
        Boolean newFree = "ON".equals(stringRedisTemplate.opsForValue().get("campaignNewFree")) && !StringUtil.isNullOrEmpty(openid) && 0 == campaignService.getCampaignNewFreeExclude(openid);
        campaignVo.setNewFree(newFree);
        // 获取活动地址
        if (newFree) {
            String newFreeUrl = stringRedisTemplate.opsForValue().get("campaignNewFreeUrl");
            if (StringUtil.isNullOrEmpty(newFreeUrl)) newFreeUrl = "https://mp.weixin.qq.com/s/TWVeCFUlMJQZoTKge0rpVw";
            campaignVo.setNewFreeUrl(newFreeUrl);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignVo);
    }

    /**
     * 写入日志
     *
     * @param campaignLog 日志
     * @return 处理结果
     */
    @PostMapping("/campaign/set-campaign-log")
    public String setCampaignLog(
            @RequestBody CampaignLog campaignLog
    ) {

        campaignLog.setOpenid((String) request.getAttribute("miniOpenid"));
        campaignService.setCampaignLog(campaignLog);

        return ResultUtil.customer(ResultEnum.SUCCESS);

    }

}
