package cn.slashsoft.clinique.dao.mini;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.mini.TTestimonyBlindBox;

@Repository
@Mapper
public interface TTestimonyBlindBoxDao {

    @Insert("INSERT INTO `t_testimony_blind_box`(" +
        "   `id`, " +
        "   `customer_id`, " +
        "   `store_id`, " +
        "   `product_id`, " +
        "   `drawed`, " +
        "   `user_name`, " +
        "   `create_time`, " +
        "   `update_time` " +
        ") " +
        "VALUES (" +
        "   #{id}, " +
        "   #{customerId}, " +
        "   #{storeId}, " +
        "   #{productId}, " +
        "   #{drawed}, " +
        "   #{userName}, " +
        "   #{createTime}, " +
        "   #{updateTime} " +
        ")")
    void insertTTestimonyBlindBox(TTestimonyBlindBox tTestimonyBlindBox);

    @Update("UPDATE " +
        "   `t_testimony_blind_box` " +
        "SET " +
        "   `id` = #{id}, " +
        "   `customer_id` = #{customerId}, " +
        "   `store_id` = #{storeId}, " +
        "   `product_id` = #{productId}, " +
        "   `drawed` = #{drawed}, " +
        "   `create_time` = #{createTime}, " +
        "   `update_time` = #{updateTime} " +
        "WHERE " +
        "   `id`=#{id} ")
    void updateTTestimonyBlindBox(TTestimonyBlindBox tTestimonyBlindBox);

    @Select("SELECT " +
        "   `id`, " +
        "   `customer_id`, " +
        "   `store_id`, " +
        "   `product_id`, " +
        "   `drawed`, " +
        "   `create_time`, " +
        "   `update_time` " +
        "FROM " +
        "   `t_testimony_blind_box` " +
        "WHERE " +
        "   `id`=#{id} " +
        "LIMIT 1 ")
    TTestimonyBlindBox getTTestimonyBlindBox(Long id);

    @Select("SELECT " +
            "   `id`, " +
            "   `customer_id`, " +
            "   `store_id`, " +
            "   `product_id`, " +
            "   `drawed`, " +
            "   `create_time`, " +
            "   `update_time` " +
            "FROM " +
            "   `t_testimony_blind_box` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1 ")
    TTestimonyBlindBox getTTestimonyBlindBoxByCustomerId(Long customerId);

    @Update("UPDATE " +
            "   `t_testimony_blind_box` " +
            "SET " +
            "   `drawed` = 1, " +
            "   `update_time` = Now() " +
            "WHERE " +
            "   `customer_id`=#{customerId} ")
    void updateBlindBoxDrawFlag(long customerId);

    @Select("select IFNULL(p.product_pwd,'') product_pwd\n" +
            "from t_testimony_blind_box  b left join t_testimony_product p\n" +
            "\ton b.product_id = p.id \n" +
            "where b.customer_id = #{customerId} " +
            "LIMIT 1 ")
    String getBlindBoxPassword(long customerId);

    @Insert("INSERT INTO `t_testimony_blind_box_draw_log`(" +
            "   `id`, " +
            "   `customer_id`, " +
            "   `store_id`, " +
            "   `product_id`, " +
            "   `drawed`, " +
            "   `user_name`, " +
            "   `create_time`, " +
            "   `update_time` " +
            ") " +
            "VALUES (" +
            "   #{id}, " +
            "   #{customerId}, " +
            "   #{storeId}, " +
            "   #{productId}, " +
            "   #{drawed}, " +
            "   #{userName}, " +
            "   #{createTime}, " +
            "   #{updateTime} " +
            ")")
    void insertTTestimonyBlindBoxLog(TTestimonyBlindBox tTestimonyBlindBox);
}
