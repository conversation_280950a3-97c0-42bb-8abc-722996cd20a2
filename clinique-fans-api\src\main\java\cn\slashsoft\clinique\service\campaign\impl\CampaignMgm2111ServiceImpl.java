package cn.slashsoft.clinique.service.campaign.impl;

import cn.slashsoft.clinique.client.MemberClient;
import cn.slashsoft.clinique.dao.campaign.CampaignMgm2111Dao;
import cn.slashsoft.clinique.domain.campaign.mgm2111.Detail;
import cn.slashsoft.clinique.domain.campaign.mgm2111.Invite;
import cn.slashsoft.clinique.domain.campaign.mgm2111.ViewLog;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.campaign.CampaignMgm2111Service;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.Result;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 2021年11月裂变活动
 *
 * <AUTHOR>
 */
@Service
public class CampaignMgm2111ServiceImpl implements CampaignMgm2111Service {

    @Resource
    private CampaignMgm2111Dao campaignMgm2111Dao;

    @Resource
    private MemberClient memberClient;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取活动信息
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @return 活动信息
     */
    @Override
    public Detail getDetailWithCreate(long customerId, String source) {

        Detail detail = campaignMgm2111Dao.getDetailByCustomerId(customerId);

        if (null == detail) {
            detail = new Detail();
            detail.setCustomerId(customerId);
            detail.setSignTotal(1);
            detail.setSignCoupon5(false);
            detail.setSignCoupon8(false);
            detail.setInviteRank(0);
            detail.setInviteTotal(0);
            detail.setInviteCoupon3(false);
            detail.setInviteCoupon8(false);
            detail.setSource(source);

            try {
                campaignMgm2111Dao.insertDetail(detail);
                // campaignMgm2111Dao.insertSign(customerId, 1);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        // else if (!DateUtil.isToday(detail.getLastSignTime())) {
        //     detail.setSignTotal(detail.getSignTotal() + 1);
        //     campaignMgm2111Dao.updateDetailSignTotal(customerId);
        //     campaignMgm2111Dao.insertSign(customerId, detail.getSignTotal());
        // }

        return detail;
    }

    /**
     * 获取活动信息
     *
     * @param customerId 顾客编号
     * @return 活动信息
     */
    @Override
    public Detail getDetail(long customerId) {
        return campaignMgm2111Dao.getDetailByCustomerId(customerId);
    }

    /**
     * 获取活动信息
     *
     * @param customerId 顾客编号
     * @return 活动信息
     */
    @Override
    public Detail getDetailWithAvatar(long customerId) {
        return campaignMgm2111Dao.getDetailWithAvatarByCustomerId(customerId);
    }

    /**
     * 获取邀请排名
     *
     * @return 用户列表
     */
    @Override
    public List<Invite> getInviteByRank() {
        return campaignMgm2111Dao.getInviteByRank();
    }

    /**
     * 领取签到的礼品
     *
     * @param customerId            会员编号
     * @param unionid               开放平台唯一编号
     * @param signCoupon5CampaignId 签到5次的礼品编号
     * @param signCoupon8CampaignId 签到8次的礼品编号
     * @return 处理结果
     */
    @Override
    public Result getSignCoupon(long customerId, String unionid, long signCoupon5CampaignId, long signCoupon8CampaignId) {

        Detail detail = campaignMgm2111Dao.getDetailByCustomerId(customerId);
        boolean receive = false;
        String message = "";

        if (null == detail) {
            return ResultUtil.customer(1, "尚未参加活动");
        }

        if (detail.getSignTotal() >= 5 && !detail.getSignCoupon5()) {

            Result result = signCoupon5(customerId, unionid, signCoupon5CampaignId);
            if (0 != result.getCode()) {
                message = message + "紫光抗老精华领取失败：" + result.getMessage() + "；";
            }
            else{
                message = message + "紫光抗老精华领取失败；";
            }
            receive = true;

        }

        if (detail.getSignTotal() >= 8 && !detail.getSignCoupon8()) {

            Result result = signCoupon8(customerId, unionid, signCoupon8CampaignId);
            if (0 != result.getCode()) {
                message = message + "全新紫光精华眼霜领取失败：" + result.getMessage() + "；";
            }
            else{
                message = message + "全新紫光精华眼霜领取失败；";
            }
            receive = true;

        }

        if(!receive){
            return ResultUtil.customer(1, "未符合领券要求");
        }

        return ResultUtil.customer(0, message);

    }

    /**
     * 发放签到5的礼品
     * @param customerId 顾客编号
     * @param unionid 开放平台唯一编号
     * @param signCoupon5CampaignId 签到5的礼品
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Result signCoupon5(long customerId, String unionid, long signCoupon5CampaignId){

        Result result = receive(unionid, signCoupon5CampaignId);
        if (0 != result.getCode()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResultUtil.customer(1, result.getMessage());
        }

        campaignMgm2111Dao.updateDetailSignCoupon5(customerId, result.getMessage());

        return ResultUtil.customer(0, "成功");

    }

    /**
     * 发放签到8的礼品
     * @param customerId 顾客编号
     * @param unionid 开放平台唯一编号
     * @param signCoupon8CampaignId 签到8的礼品
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Result signCoupon8(long customerId, String unionid, long signCoupon8CampaignId){

        Result result = receive(unionid, signCoupon8CampaignId);
        if (0 != result.getCode()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResultUtil.customer(1, result.getMessage());
        }

        campaignMgm2111Dao.updateDetailSignCoupon8(customerId, result.getMessage());

        return ResultUtil.customer(0, "成功");

    }

    /**
     * 领取邀请的礼品
     *
     * @param customerId              会员编号
     * @param unionid                 开放平台唯一编号
     * @param inviteCoupon3CampaignId 邀请3次的礼品编号
     * @param inviteCoupon8CampaignId 邀请8次的礼品编号
     * @return 处理结果
     */
    @Override
    public Result getInviteCoupon(long customerId, String unionid, long inviteCoupon3CampaignId, long inviteCoupon8CampaignId) {

        Detail detail = campaignMgm2111Dao.getDetailByCustomerId(customerId);
        boolean receive = false;
        String message = "";

        if (null == detail) {
            return ResultUtil.customer(1, "尚未参加活动");
        }

        if (detail.getInviteTotal() >=3 && !detail.getInviteCoupon3()) {

            Result result = inviteCoupon3(customerId, unionid, inviteCoupon3CampaignId);
            if (0 != result.getCode()) {
                message = message + "紫光抗老精华领取失败：" + result.getMessage() + "；";
            }
            else{
                message = message + "紫光抗老精华领取失败；";
            }
            receive = true;

        }

        if (detail.getInviteTotal() >=8 && !detail.getInviteCoupon8()) {

            Result result = inviteCoupon8(customerId, unionid, inviteCoupon8CampaignId);
            if (0 != result.getCode()) {
                message = message + "紫光抗老精华领取失败：" + result.getMessage() + "；";
            }
            else{
                message = message + "紫光抗老精华领取失败；";
            }
            receive = true;

        }

        if(!receive){
            return ResultUtil.customer(1, "未符合领券要求");
        }

        return ResultUtil.customer(0, message);

    }

    /**
     * 发放邀请3的礼品
     * @param customerId 顾客编号
     * @param unionid 开放平台唯一编号
     * @param inviteCoupon3CampaignId 邀请3的礼品
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Result inviteCoupon3(long customerId, String unionid, long inviteCoupon3CampaignId){

        Result result = receive(unionid, inviteCoupon3CampaignId);
        if (0 != result.getCode()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResultUtil.customer(1, result.getMessage());
        }

        campaignMgm2111Dao.updateDetailInviteCoupon3(customerId, result.getMessage());

        return ResultUtil.customer(0, "成功");

    }

    /**
     * 发放邀请8的礼品
     * @param customerId 顾客编号
     * @param unionid 开放平台唯一编号
     * @param inviteCoupon8CampaignId 邀请8的礼品
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Result inviteCoupon8(long customerId, String unionid, long inviteCoupon8CampaignId){

        Result result = receive(unionid, inviteCoupon8CampaignId);
        if (0 != result.getCode()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResultUtil.customer(1, result.getMessage());
        }

        campaignMgm2111Dao.updateDetailInviteCoupon8(customerId, result.getMessage());

        return ResultUtil.customer(0, "成功");

    }

    /**
     * 写入经纬度
     *
     * @param customerId 顾客编号
     * @param latitude   经度
     * @param longitude  纬度
     */
    @Override
    public void insertLocation(long customerId, String latitude, String longitude) {
        campaignMgm2111Dao.insertLocation(customerId, latitude, longitude);
    }

    /**
     * 写入访问日志
     *
     * @param viewLog 日志
     * @return 日志的编号
     */
    @Override
    public long insertViewLog(ViewLog viewLog) {
        campaignMgm2111Dao.insertViewLog(viewLog);
        return viewLog.getId();
    }

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Override
    public void setSignViewLog(long id) {
        campaignMgm2111Dao.setSignViewLog(id);
    }

    /**
     * 邀请
     *
     * @param inviterMiniOpenid 邀请者
     * @param inviteeMiniOpenid 被邀请者
     */
    @Override
    public void invite(String inviterMiniOpenid, String inviteeMiniOpenid) {

        Date inviteStartTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaign-mgm2111-invite-start-time"));
        Date inviteEndTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaign-mgm2111-invite-end-time"));
        if(DateUtil.laterThanNow(inviteStartTime) || DateUtil.earlierThanNow(inviteEndTime)){
            return;
        }

        try {
            // 写入邀请表
            campaignMgm2111Dao.insertInvite(inviterMiniOpenid, inviteeMiniOpenid);
            // 更新邀请数量
            campaignMgm2111Dao.invite(inviterMiniOpenid);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 发券
     * @param unionid 开放平台唯一编号
     * @param couponCampaignId 优惠券模版编号
     * @return 0成功，1失败
     */
    private Result receive(String unionid, long couponCampaignId){
        try {
            JSONObject response = JSON.parseObject(memberClient.receive(unionid, couponCampaignId));
            if(null == response){
                throw new Exception("返回值为空");
            }

            if(200 != response.getIntValue("code")){
                throw new Exception(response.getString("message"));
            }

            return new Result(0, response.getString("message"));
        }
        catch (Exception e){
            return new Result(1, e.getMessage());
        }
    }

}
