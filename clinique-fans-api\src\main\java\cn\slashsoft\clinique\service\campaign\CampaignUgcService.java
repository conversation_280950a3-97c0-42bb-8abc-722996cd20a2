package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.ugc.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
public interface CampaignUgcService {

    /**
     * 读取活动信息
     * @param campaignId 编号
     * @return 活动信息
     */
    Koc getCampaignUgc(long campaignId);

    /**
     * 获取达人榜信息
     *
     * @param campaignId 活动编号
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    KocDetail getCampaignUgcDetailAlert(long campaignId, long customerId);

    /**
     * 更新提醒状态
     *
     * @param campaignId 活动编号
     * @param customerId 顾客编号
     */
    void updateCampaignUgcDetailAlert(long campaignId, long customerId);

    /**
     * 获取达人榜信息
     *
     * @param campaignId 活动编号
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    KocDetail getCampaignUgcDetail(long campaignId, long customerId);

    /**
     * 获取图片
     *
     * @param detailId 顾客活动信息编号
     * @return 图片地址
     */
    List<KocDetailImage> getCampaignUgcImageList(long detailId);

    /**
     * 获取当前TOP信息
     *
     * @param campaignId 活动编号
     * @return TOP信息
     */
    KocTop getCampaignUgcTop(long campaignId);

    /**
     * 获取入选TOP的名单
     *
     * @param topId TOP信息编号
     * @return 名单
     */
    List<KocTopDetail> getCampaignUgcTopDetailList(long topId);

    /**
     * 获取入选TOP的图片
     *
     * @param topId TOP信息编号
     * @return 图片
     */
    List<KocTopDetailImage> getCampaignUgcTopDetailImageList(long topId);

    /**
     * 保存图片
     *
     * @param campaignId 活动编号
     * @param customerId 顾客编号
     * @param file       要保存的图片
     * @return 处理结果
     */
    boolean upload(long campaignId, long customerId, MultipartFile file);

    /**
     * 更新肤质
     *
     * @param campaignId 活动编号
     * @param customerId 顾客编号
     * @param skinType   肤质
     */
    void setCampaignUgcSkinType(long campaignId, long customerId, short skinType);

    /**
     * 写入访问开始日志
     *
     * @param campaignId 活动编号
     * @param customerId 顾客编号
     * @param source     来源
     * @param page       页面
     * @return 访问日志编号
     */
    long insertCampaignUgcViewLog(long campaignId, long customerId, String source, String page);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    void setCampaignUgcViewLog(long id);

    /**
     * 写入达人榜访问记录
     *
     * @param campaignUgcTopDetailLog 达人榜访问记录
     */
    void insertCampaignUgcTopDetailLog(KocTopDetailLog campaignUgcTopDetailLog);

    /**
     * 写入达人榜图片访问记录
     *
     * @param campaignUgcTopDetailImageLog 达人榜图片访问记录
     */
    void insertCampaignUgcTopDetailImageLog(KocTopDetailImageLog campaignUgcTopDetailImageLog);

}
