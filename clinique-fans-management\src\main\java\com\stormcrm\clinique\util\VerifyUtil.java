package com.stormcrm.clinique.util;

import java.util.regex.Pattern;

/**
 * 验证工具类
 *
 * <AUTHOR>
 */
public class VerifyUtil {

    /**
     * 验证必填字段是否为空
     *
     * @param s 字段
     * @return 是否为空
     */
    public static boolean required(String s) {
        return null != s && !s.isEmpty();
    }

    /**
     * 验证必填字段是否为空
     *
     * @param i 字段
     * @return 是否为空
     */
    public static boolean required(Integer i) {
        return null != i && 0 != i;
    }

    /**
     * 验证必填字段是否为空
     *
     * @param i 字段
     * @return 是否为空
     */
    public static boolean required(Long i) {
        return null != i && 0 != i;
    }

    /**
     * 验证必填字段是否为空
     *
     * @param i 字段
     * @return 是否为空
     */
    public static boolean required(Short i) {
        return null != i && 0 != i;
    }

    /**
     * 跟据指定的正则表达式验证字段
     *
     * @param pattern 正则表达式
     * @param s       字段
     * @return 是否通过
     */
    public static boolean is(String pattern, String s) {
        try {
            return Pattern.compile(pattern).matcher(s).matches();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证字段是否是手机号码
     *
     * @param s 字段
     * @return 是否通过
     */
    public static boolean isMobile(String s) {
        return is("^1[0-9]{10}$", s);
    }

    /**
     * 验证字段是否是验证码
     *
     * @param s 字段
     * @return 是否通过
     */
    public static boolean isCode(String s) {
        return is("^[0-9]{6}$", s);
    }

    /**
     * 验证字段是否是8个字符
     *
     * @param s 字段
     * @return 是否通过
     */
    public static boolean isChar8(String s) {
        return is("^[a-zA-Z0-9]{8}$", s);
    }

    /**
     * 验证字段是否是32位的HASH值
     *
     * @param s 字段
     * @return 是否通过
     */
    public static boolean isHash(String s) {
        return is("^[a-z0-9]{32}$", s);
    }

    /**
     * 验证字段是否全是数字
     *
     * @param s 字段
     * @return 是否通过
     */
    public static boolean isNumber(String s) {
        return is("^[0-9]$", s);
    }

    /**
     * 验证字段是否为小程序的JS Code
     *
     * @param s 字段
     * @return 是否通过
     */
    public static boolean isJsCode(String s) {
        return is("^[a-zA-Z0-9]{32}$", s);
    }

    /**
     * 验证字段是否为微信开放平台的unionid
     *
     * @param s 字段
     * @return 是否通过
     */
    public static boolean isUnionid(String s) {
        return is("^[a-zA-Z0-9_-]{28}$", s);
    }

}
