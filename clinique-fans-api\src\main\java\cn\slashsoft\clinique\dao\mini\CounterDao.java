package cn.slashsoft.clinique.dao.mini;

import cn.slashsoft.clinique.domain.mini.Counter;
import cn.slashsoft.clinique.domain.mini.CounterLog;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 柜台
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CounterDao {

    /**
     * 跟据定位查找附近的门店
     *
     * @param latitude 经度
     * @param longitude 纬度
     * @return 门店
     */
    @Select("SELECT " +
            "   `province`,`city`,`district`,`name`,`address`,`distance`,`latitude`,`longitude` " +
            "FROM " +
            "   (" +
            "       SELECT " +
            "           `province`," +
            "           `city`," +
            "           `district`," +
            "           `name`," +
            "           `address`," +
            "           `latitude`," +
            "           `longitude`," +
            "           ROUND(6378.138*2*ASIN(SQRT(POW(SIN((#{latitude}*PI()/180-`latitude`*PI()/180)/2),2)+COS(#{latitude}*PI()/180)*COS(`latitude`*PI()/180)*POW(SIN((#{longitude}*PI()/180-`longitude`*PI()/180)/2),2)))*1000) AS `distance` " +
            "       FROM " +
            "           `customer_service_counter` " +
            "       WHERE " +
            "           `status`=1 " +
            "   ) `customer_service_counter` " +
            "WHERE " +
            "   `distance` IS NOT NULL " +
            "ORDER BY " +
            "   `distance` ASC " +
            "LIMIT 5")
    List<Counter> getCounter(
            @Param("latitude") BigDecimal latitude,
            @Param("longitude") BigDecimal longitude
    );

    /**
     * 跟据定位查找附近的门店
     *
     * @param latitude 经度
     * @param longitude 纬度
     * @param province 省份
     * @param city 城市
     * @param name 门店名称
     * @return 门店
     */
    @Select("<script>" +
            "SELECT " +
            "   `province`,`city`,`district`,`name`,`address`,`distance`,`latitude`,`longitude` " +
            "FROM " +
            "   (" +
            "       SELECT " +
            "           `province`," +
            "           `city`," +
            "           `district`," +
            "           `name`," +
            "           `address`," +
            "           `latitude`," +
            "           `longitude`," +
            "           ROUND(6378.138*2*ASIN(SQRT(POW(SIN((#{latitude}*PI()/180-`latitude`*PI()/180)/2),2)+COS(#{latitude}*PI()/180)*COS(`latitude`*PI()/180)*POW(SIN((#{longitude}*PI()/180-`longitude`*PI()/180)/2),2)))*1000) AS `distance` " +
            "       FROM " +
            "           `customer_service_counter` " +
            "       WHERE " +
            "           `status`=1 " +
            "           <if test=\"province!=null and province!=''\"> AND `province`=#{province}</if>" +
            "           <if test=\"city!=null and city!=''\"> AND `city`=#{city}</if>" +
            "           <if test=\"name!=null and name!=''\"> AND `name` like '%${name}%'</if>" +
            "   ) `customer_service_counter` " +
            "WHERE " +
            "   `distance` IS NOT NULL " +
            "ORDER BY " +
            "   `distance` ASC" +
            "</script>")
    List<Counter> getCounterBySearch(
            @Param("latitude") BigDecimal latitude,
            @Param("longitude") BigDecimal longitude,
            @Param("province") String province,
            @Param("city") String city,
            @Param("name") String name
    );


    /**
     * 跟据定位查找附近的门店
     *
     * @param latitude 经度
     * @param longitude 纬度
     * @return 门店
     */
    @Select("SELECT " +
            "   `id`,`province`,`city`,`district`,`name`,`address`,`distance`,`latitude`,`longitude` " +
            "FROM " +
            "   (" +
            "       SELECT " +
            "           `id`," +
            "           `province`," +
            "           `city`," +
            "           `district`," +
            "           `name`," +
            "           `address`," +
            "           `latitude`," +
            "           `longitude`," +
            "           ROUND(6378.138*2*ASIN(SQRT(POW(SIN((#{latitude}*PI()/180-`latitude`*PI()/180)/2),2)+COS(#{latitude}*PI()/180)*COS(`latitude`*PI()/180)*POW(SIN((#{longitude}*PI()/180-`longitude`*PI()/180)/2),2)))*1000) AS `distance` " +
            "       FROM " +
            "           `customer_service_counter` " +
            "       WHERE " +
            "           `status`=1 " +
            "   ) `customer_service_counter` " +
            "WHERE " +
            "   `distance` IS NOT NULL " +
            "ORDER BY " +
            "   `distance` ASC " +
            "LIMIT 1")
    Counter getNearestCounter(
            @Param("latitude") BigDecimal latitude,
            @Param("longitude") BigDecimal longitude
    );

    /**
     * 写入日志
     * @param counterLog 日志
     */
    @Insert("INSERT INTO `counter_log`(" +
            "   `type`, " +
            "   `wechat_official_openid`, " +
            "   `latitude`, " +
            "   `longitude`, " +
            "   `province`, " +
            "   `city`, " +
            "   `district`, " +
            "   `name`, " +
            "   `address`," +
            "   `distance`" +
            ") " +
            "VALUES (" +
            "   #{type}, " +
            "   #{wechatOfficialOpenid}, " +
            "   #{latitude}, " +
            "   #{longitude}, " +
            "   #{province}, " +
            "   #{city}, " +
            "   #{district}, " +
            "   #{name}, " +
            "   #{address}," +
            "   #{distance})")
    void insertCounterLog(CounterLog counterLog);

}
