package cn.slashsoft.clinique.service.mini.impl;

import org.springframework.stereotype.Service;

import cn.slashsoft.clinique.dao.mini.VerificationDao;
import cn.slashsoft.clinique.domain.mini.Account;
import cn.slashsoft.clinique.domain.mini.StoreExchange;
import cn.slashsoft.clinique.service.mini.VerificationService;

/**
 * 核销
 *
 * <AUTHOR>
 */
@Service
public class VerificationServiceImpl implements VerificationService {

    private final VerificationDao verificationDao;

    public VerificationServiceImpl(VerificationDao verificationDao) {
        this.verificationDao = verificationDao;
    }

    /**
     * 跟据用户名获取帐户
     *
     * @param username 用户名
     * @return 帐户
     */
    @Override
    public Account getAccountByUsername(String username) {
        return verificationDao.getAccountByUsername(username);
    }

    /**
     * 跟据订单编号查找兑换订单
     *
     * @param order 订单编号
     * @return 订单
     */
    @Override
    public StoreExchange getStoreExchangeByOrder(String order) {
        return verificationDao.getStoreExchangeByOrder(order);
    }

    /**
     * 更新订单状态
     *
     * @param order 订单编号
     * @return 处理结果
     */
    @Override
    public int updateStoreExchangeLogisticsByOrder(String order) {
        return verificationDao.updateStoreExchangeLogisticsByOrder(order);
    }
}
