package cn.slashsoft.clinique.service.mini;


import cn.slashsoft.clinique.domain.campaign.*;
import cn.slashsoft.clinique.domain.mini.TTestimonyBlindBoxMotLog;
import cn.slashsoft.clinique.domain.mini.TTestimonyFragment;
import cn.slashsoft.clinique.domain.mini.TTestimonyFragmentRank;
import cn.slashsoft.clinique.domain.mini.TTestimonyFragmentTop15;
import cn.slashsoft.clinique.domain.mini.TTestimonyHelp;
import cn.slashsoft.clinique.vo.mini.TestimonyFragmentRankVo;
import cn.slashsoft.clinique.vo.mini.TestimonyFragmentRewardVo;
import cn.slashsoft.clinique.vo.mini.TestimonyFragmentSumVo;

import java.util.List;

/**
* <p>
    * 记录获得碎片信息
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
public interface TTestimonyFragmentService {
    void insertTTestimonyFragment(TTestimonyFragment tTestimonyFragment);
    void updateTTestimonyFragment(TTestimonyFragment tTestimonyFragment);
    TTestimonyFragment getTTestimonyFragment(Long id);
    Integer getTTestimonyFragmentCountByCustomerId(Long customerId);
    TestimonyFragmentRankVo getTTestimonyFragmentRankByCustomerId(Long customerId);
    List<TestimonyFragmentRankVo> getTTestimonyFragmentRankListByCustomerId(Long customerId);
    void insertTTestimonyFragmentRank(TTestimonyFragmentRank tTestimonyFragmentRank);
    void addFragmentRankQty(TTestimonyFragment tTestimonyFragment);
    TTestimonyFragmentRank getFragmentRankByCustomerId(Long customerId);
    TTestimonyFragment getTTestimonyFragmentByCustomerId(Long customerId);
    Integer getFragmentCountByType(Long customerId, Integer fragType);

    /**
     * 根据类型添加碎片记录，并更新rank总计
     * @param customerId
     * @param fragType
     * @return
     */
    Integer addFragmentRecords(Long customerId, Integer fragType);

    /**
     * 获取碎片统计信息 用户策略界面
     * @param customerId
     * @return
     */
    List<TestimonyFragmentSumVo> getFragmentSumInfo(Long customerId);

    /**
     * 获取兑奖
     * @param customerId
     * @param storeId
     * @return
     */
    TestimonyFragmentRewardVo getFragmentRewards(long customerId, Integer storeId, String userName);

    /**
     * 通过id获取top15记录
     * @param customerId
     * @return
     */
    TTestimonyFragmentTop15 getFragmentTop15ByCustomerId(long customerId);

    /**
     * 更新top15礼品领取标识
     * @param customerId
     */
    void updateTop15Flag(long customerId);

    /**
     * 根据id，type查询是否存在记录
     * @param customerId
     * @param fragmentType
     * @return
     */
    TTestimonyFragment getFragmentByCustomerIdAndType(Long customerId, Integer fragmentType);

    /**
     * 是否是koc用户
     * @param customerId
     * @return
     */
    Boolean isKocUser(Long customerId);

    /**
     * 是否是Cust 302用户
     * @param customerId
     * @return
     */
    Boolean isCust302User(Long customerId);

    /**
     * 是否有发布笔记
     * @param customerId
     * @return
     */
    Boolean hasNote(long customerId);

    /**
     * 判断用户当天是否已领取打卡碎片
     * @param customerId
     * @return
     */
    Boolean hasAlreadyDrawed(long customerId);

    /**
     * 判断用户是否有领取笔记碎片权限
     * @param customerId
     * @return
     */
    Boolean getNotePrivilegeByCustomerId(long customerId);

    /**
     * 临时添加从小程序openid获取customerId
     * @param openId
     */
    String getCustomerIdbyOpenId(String openId);

    /**
     * 添加日志记录
     * @param customerId
     * @param source
     * @param page
     * @return
     */
    long insertTestimonyViewLog(long customerId, String source, String page);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    void setTestimonyViewLog(long id);

    /**
     * 添加助力的日志功能
     * @param tTestimonyHelp
     */
    void addTTestimonyHelp(TTestimonyHelp tTestimonyHelp);

    /**
     * 添加转发log
     * @param customerId
     */
    void addShareLog(long customerId);

    /**
     * 从店铺id获取店铺名称
     * @param storeId
     * @return
     */
    String getStoreNameByStoreId(Integer storeId);

    /**
     * 保存mot日志信息
     * @param tTestimonyBlindBoxMotLog
     */
    void saveTestimonyBlindBoxMotLog(TTestimonyBlindBoxMotLog tTestimonyBlindBoxMotLog);


    /**
     *
     * 保存TOP15客户对门店的选择
     *
     * @param customerChooser
     */
    void saveTop15CustomerChooseStore(TTestimonyFragmentTop15 customerChooser);
}
