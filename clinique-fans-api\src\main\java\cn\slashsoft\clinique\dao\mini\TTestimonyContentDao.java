package cn.slashsoft.clinique.dao.mini;

import cn.slashsoft.clinique.domain.mini.TTestimonyContent;
import cn.slashsoft.clinique.domain.mini.TTestimonyContentFilters;
import cn.slashsoft.clinique.domain.mini.TTestimonyHelp;
import cn.slashsoft.clinique.vo.mini.TestimonyContentVo;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface TTestimonyContentDao {

    @Insert("INSERT INTO `t_testimony_content`(" +
        "   `id`, " +
        "   `customer_id`, " +
        "   `content`, " +
        "   `create_time`, " +
        "   `update_time` " +
        ") " +
        "VALUES (" +
        "   #{id}, " +
        "   #{customerId}, " +
        "   #{content}, " +
        "   #{createTime}, " +
        "   #{updateTime} " +
        ")")
    void insertTTestimonyContent(TTestimonyContent tTestimonyContent);

    @Update("UPDATE " +
        "   `t_testimony_content` " +
        "SET " +
        "   `id` = #{id}, " +
        "   `customer_id` = #{customerId}, " +
        "   `content` = #{content}, " +
        "   `create_time` = #{createTime}, " +
        "   `update_time` = #{updateTime} " +
        "WHERE " +
        "   `id`=#{id} ")
    void updateTTestimonyContent(TTestimonyContent tTestimonyContent);

    @Select("SELECT " +
        "   `id`, " +
        "   `customer_id`, " +
        "   `content`, " +
        "   `create_time`, " +
        "   `update_time` " +
        "FROM " +
        "   `t_testimony_content` " +
        "WHERE " +
        "   `id`=#{id} " +
        "LIMIT 1 ")
    TTestimonyContent getTTestimonyContent(Long id);

//    @Select("select c.id, c.customer_id, w.avatar_url, c.content,  " +
//            "case IFNULL(l.id,FALSE) when false then false else true end liked\n" +
//            "from t_testimony_content c left join wechat w on c.customer_id = w.customer_id \n" +
//            "   left join t_testimony_like l on c.id = l.content_id  and l.customer_id = #{customerId}\n" +
//            "where c.customer_id <> #{customerId} \n" +
//            "order by c.id desc \n" +
//            "limit 50")
    @Select("select c.id, c.customer_id, w.avatar_url, c.content, \n" +
            "\tIFNULL(p.mobile, 0) cust302,  \n" +
            "\tIFNULL(k.customer_id,0) koc,\n" +
            "\tcase IFNULL(l.id,FALSE) when false then false else true end liked, \n" +
            "\tcase when c.customer_id = #{customerId} then true else false end selfContent \n" +
            "from t_testimony_content c \n" +
            "\tleft join wechat w on c.customer_id = w.customer_id \n" +
            "\tleft join t_testimony_like l on c.id = l.content_id and l.customer_id = #{customerId}\n" +
            "\tleft join customer cst on c.customer_id = cst.id \n" +
            "\tleft join t_testimony_302_phone_list p on cst.phone_number = p.mobile\n" +
            "\tleft join t_testimony_koc_user_list k on c.customer_id = k.customer_id\n" +
            "order by c.id desc \n" +
            "limit 15")
    List<TestimonyContentVo> getTestimonyContentVo(Long customerId);

    @Select("select c.id, c.customer_id, w.avatar_url, c.content, \n" +
            "\tIFNULL(p.mobile, 0) cust302,  \n" +
            "\tIFNULL(k.customer_id,0) koc,\n" +
            "\tcase IFNULL(l.id,FALSE) when false then false else true end liked, \n" +
            "\tcase when c.customer_id = #{customerId} then true else false end selfContent \n" +
            "from t_testimony_content c \n" +
            "\tleft join wechat w on c.customer_id = w.customer_id \n" +
            "\tleft join t_testimony_like l on c.id = l.content_id and l.customer_id = #{customerId}\n" +
            "\tleft join customer cst on c.customer_id = cst.id \n" +
            "\tleft join t_testimony_302_phone_list p on cst.phone_number = p.mobile\n" +
            "\tleft join t_testimony_koc_user_list k on c.customer_id = k.customer_id\n" +
            "order by c.id  \n" +
            "limit 15")
    List<TestimonyContentVo> getInnerTestimonyContent(Long customerId);

    @Select("select count(*) cnt\n" +
            "from t_testimony_content\n" +
            "where customer_id= #{customerId}")
    Integer getTTestimonyContentCountByCustomerId(long customerId);

    @Select("select count(*) cnt\n" +
            "from t_testimony_content\n" +
            "where content= #{content} AND  customer_id= #{customerId}")
    Integer getTTestimonyContentCountByCustomerIdContent(long customerId, String content);
    
    @Select("select count(*) cnt\n" +
            "from t_testimony_content\n" +
            "where TO_DAYS(now()) = TO_DAYS(create_time) AND  customer_id= #{customerId}")
	Integer getTTestimonyContentCountByCustomerIdTodady(long customerId);
    
    @Select("select customer_id\n" +
            "from t_testimony_content\n" +
            "where id= #{contentId}")
    Long getCustomerIdByContentId(long contentId);

    @Select("SELECT " +
            "   `id`, " +
            "   `customer_id`, " +
            "   `content_id`, " +
            "   `help_customer_id`, " +
            "   `create_time`, " +
            "   `update_time` " +
            "FROM " +
            "   `t_testimony_help` " +
            "WHERE " +
            "   customer_id= #{customerId} and help_customer_id = #{helpCustomerId} " +
            "LIMIT 1 ")
    TTestimonyHelp getHelpByCustomerIdHelpCustomerId(long customerId, long helpCustomerId);

    @Select("SELECT " +
            "   `content` " +
            "FROM " +
            "   `t_testimony_content` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "ORDER BY create_time desc " +
            "LIMIT 1 ")
    String getLastontent(long customerId);

    @Select("SELECT " +
            "   `id`, " +
            "   `key_word`, " +
            "   `create_time`, " +
            "   `update_time` " +
            "FROM " +
            "   `t_testimony_content_filters` " +
            "ORDER BY create_time desc ")
    List<TTestimonyContentFilters> getContentFilters();

    @Insert("INSERT INTO `t_testimony_content_filtered`(" +
            "   `id`, " +
            "   `customer_id`, " +
            "   `content`, " +
            "   `create_time`, " +
            "   `update_time` " +
            ") " +
            "VALUES (" +
            "   #{id}, " +
            "   #{customerId}, " +
            "   #{content}, " +
            "   #{createTime}, " +
            "   #{updateTime} " +
            ")")
    void insertFilteredTTestimonyContent(TTestimonyContent tTestimonyContent);

}
