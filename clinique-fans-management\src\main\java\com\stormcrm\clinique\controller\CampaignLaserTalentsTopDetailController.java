package com.stormcrm.clinique.controller;

import com.stormcrm.clinique.domain.CampaignLaserTalentsTop;
import com.stormcrm.clinique.domain.CampaignLaserTalentsTopDetail;
import com.stormcrm.clinique.service.CampaignLaserTalentsTopDetailService;
import com.stormcrm.clinique.service.CampaignLaserTalentsTopService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.util.VerifyUtil;
import com.stormcrm.clinique.vo.Result;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 新品体验官活动TOP5详情
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("campaign-laser-talents-top-detail")
public class CampaignLaserTalentsTopDetailController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    private final CampaignLaserTalentsTopService campaignLaserTalentsTopService;
    private final CampaignLaserTalentsTopDetailService campaignLaserTalentsTopDetailService;

    public CampaignLaserTalentsTopDetailController(CampaignLaserTalentsTopDetailService campaignLaserTalentsTopDetailService, CampaignLaserTalentsTopService campaignLaserTalentsTopService) {
        this.campaignLaserTalentsTopDetailService = campaignLaserTalentsTopDetailService;
        this.campaignLaserTalentsTopService = campaignLaserTalentsTopService;
    }

    /**
     * 页面模版
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_LASER_TALENTS_TOP')")
    @GetMapping("/{topId}")
    public String index(
            @PathVariable("topId") long topId,
            Model model) {
        model.addAttribute("staticDomain", staticDomain);
        CampaignLaserTalentsTop campaignLaserTalentsTop = campaignLaserTalentsTopService.getById(topId);
        if(null == campaignLaserTalentsTop){
            return "m/fans/common/empty";
        }

        model.addAttribute("topId", topId);
        model.addAttribute("campaignLaserTalentsTop", campaignLaserTalentsTop);
        return "m/fans/campaign-laser-talents-top-detail/index";
    }

    /**
     * 查询所有
     *
     * @return 列表
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_LASER_TALENTS_TOP')")
    @PostMapping("get-all/{topId}")
    @ResponseBody
    public String getAll(
            @PathVariable("topId") long topId
    ) {

        List<CampaignLaserTalentsTopDetail> campaignLaserTalentsTopDetailList = campaignLaserTalentsTopDetailService.getAll(topId);

        JSONObject mata = new JSONObject();
        mata.put("page", 1);
        mata.put("pages", 1);
        mata.put("perpage", -1);
        mata.put("total", campaignLaserTalentsTopDetailList.size());
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("mata", mata);
        result.put("data", campaignLaserTalentsTopDetailList);

        return JSONObject.toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }

    /**
     * 增加模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_LASER_TALENTS_TOP')")
    @RequestMapping("add/{topId}")
    public String add(
            @PathVariable("topId") long topId,
            Model model) {
        model.addAttribute("staticDomain", staticDomain);
        model.addAttribute("topId", topId);
        return "m/fans/campaign-laser-talents-top-detail/add";
    }

    /**
     * 新增
     *
     * @param topId     TOP5编号
     * @param id    达人官编号
     * @param sort 排序
     * @return 结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_LASER_TALENTS_TOP')")
    @PostMapping(value = "add-submit/{topId}", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String addSubmit(
            @PathVariable("topId") long topId,
            @RequestParam(value = "form-id", required = false) long id,
            @RequestParam(value = "form-sort", required = false) short sort
    ) {

        // 验证
        if (!VerifyUtil.required((int) id)) {
            return ResultUtil.verifyFailToJson("form-id", "这是必填字段");
        }

        // 验证
        if (!VerifyUtil.required((int) sort)) {
            return ResultUtil.verifyFailToJson("form-sort", "这是必填字段");
        }

        // 生成对象
        CampaignLaserTalentsTopDetail campaignLaserTalentsTopDetail = new CampaignLaserTalentsTopDetail();
        campaignLaserTalentsTopDetail.setTalentsTopId(topId);
        campaignLaserTalentsTopDetail.setTalentsId(id);
        campaignLaserTalentsTopDetail.setSort(sort);

        // 传到Service服务中保存
        Result result = campaignLaserTalentsTopDetailService.save(campaignLaserTalentsTopDetail);

        // 返回绍果给前端
        return JSONObject.toJSONString(result);
    }

    /**
     * 编辑模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_LASER_TALENTS_TOP')")
    @GetMapping("edit/{detailId}")
    public String edit(@PathVariable("detailId") long detailId, Model model) {
        model.addAttribute("staticDomain", staticDomain);
        // 读数据库
        CampaignLaserTalentsTopDetail campaignLaserTalentsTopDetail = campaignLaserTalentsTopDetailService.getById(detailId);
        if (null == campaignLaserTalentsTopDetail) {
            return "m/fans/common/empty";
        }
        model.addAttribute("detailId", detailId);
        model.addAttribute("campaignLaserTalentsTopDetail", campaignLaserTalentsTopDetail);
        return "m/fans/campaign-laser-talents-top-detail/edit";
    }

    /**
     * 编辑
     *
     * @param detailId     TOP5 DETAIL 编号
     * @param id    达人官编号
     * @param sort 排序
     * @return 结果
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_LASER_TALENTS_TOP')")
    @PostMapping(value = "edit-submit/{detailId}", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String editSubmit(
            @PathVariable("detailId") long detailId,
            @RequestParam(value = "form-id", required = false) long id,
            @RequestParam(value = "form-sort", required = false) short sort
    ) {

        // 验证
        if (!VerifyUtil.required((int) id)) {
            return ResultUtil.verifyFailToJson("form-id", "这是必填字段");
        }

        // 验证
        if (!VerifyUtil.required((int) sort)) {
            return ResultUtil.verifyFailToJson("form-sort", "这是必填字段");
        }

        // 生成对象
        CampaignLaserTalentsTopDetail campaignLaserTalentsTopDetail = new CampaignLaserTalentsTopDetail();
        campaignLaserTalentsTopDetail.setId(detailId);
        campaignLaserTalentsTopDetail.setTalentsId(id);
        campaignLaserTalentsTopDetail.setSort(sort);

        // 传到Service服务中保存
        Result result = campaignLaserTalentsTopDetailService.update(campaignLaserTalentsTopDetail);

        // 返回绍果给前端
        return JSONObject.toJSONString(result);
    }

    /**
     * 逻辑删除
     *
     * @param detailId 编号
     * @return 影响的行数
     */
    @PreAuthorize("hasAuthority('CAMPAIGN_DEL')")
    @GetMapping("del/{detailId}")
    @ResponseBody
    public String del(@PathVariable("detailId") long detailId) {

        if (0 == campaignLaserTalentsTopDetailService.delete(detailId)) {
            return ResultUtil.failToJson();
        }

        return ResultUtil.successToJson();

    }

}
