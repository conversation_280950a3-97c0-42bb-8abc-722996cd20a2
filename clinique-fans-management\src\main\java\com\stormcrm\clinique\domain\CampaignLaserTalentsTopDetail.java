package com.stormcrm.clinique.domain;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 新品体验官活动TOP5明细
 *
 * <AUTHOR>
 */
@Data
public class CampaignLaserTalentsTopDetail {

    private Long id;
    private String wechatMiniOpenid;
    private Long customerId;
    private String nickName;
    private String avatarUrl;
    private Short sort;
    private Short status;
    private Date uploadTime;
    private Date examineTime;
    private Date createTime;

    private Long talentsTopId;
    private Long talentsId;
    private List<CampaignLaserTalentsImage> imageList;

}
