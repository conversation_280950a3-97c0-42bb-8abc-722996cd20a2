package cn.slashsoft.clinique.util;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * XML工具类
 * <AUTHOR>
 */
public class XmlUtil {

    /**
     * XML字符串转集合
     * @param string XML字符
     * @return 集合
     * @throws Exception 读取异常
     */
    public static Map<String, Object> xmlToMap(String string) throws Exception {

        InputStream inputStream = new ByteArrayInputStream(string.getBytes(StandardCharsets.UTF_8));

        Map<String, Object> data = xmlToMap(inputStream);
        try {
            inputStream.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return data;

    }

    /**
     * XML输入流转集合
     * @param inputStream XML输入流
     * @return 集合
     * @throws Exception 读取异常
     */
    public static Map<String, Object> xmlToMap(InputStream inputStream) throws Exception {

        Map<String, Object> data = new HashMap<>();

        DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
        DocumentBuilder documentBuilder = documentBuilderFactory.newDocumentBuilder();

        Document doc = documentBuilder.parse(inputStream);
        doc.getDocumentElement().normalize();
        NodeList nodeList = doc.getDocumentElement().getChildNodes();
        for (int idx = 0; idx < nodeList.getLength(); ++idx) {
            Node node = nodeList.item(idx);
            if (node.getNodeType() == Node.ELEMENT_NODE) {
                Element element = (Element) node;
                data.put(element.getNodeName(), element.getTextContent());
            }
        }

        return data;
    }
}
