package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.dao.mini.CactiveDao;
import com.stormcrm.clinique.dao.mini.CustomerDao;
import com.stormcrm.clinique.domain.mini.PointTransaction;
import com.stormcrm.clinique.enums.CActiveTypeEnum;

import com.stormcrm.clinique.util.DateUtil;
import io.netty.util.internal.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
 * 与活动相关
 *
 * <AUTHOR> from api）
 */
@Service
@AllArgsConstructor
@Slf4j
public class CactiveService {

    private final CactiveDao cactiveDao;
    private final CustomerDao customerDao;


    /**
     * 未使用
     * @param customerId
     * @param cactiveType
     * @param masterId  noteId
     * @param detailId  nodeDis
     * @return
     */
    public int removePoints(
            Long customerId,
            CActiveTypeEnum cactiveType,
            Long masterId,
            Long detailId) {


        PointTransaction activeTransaction = new PointTransaction();

        activeTransaction.setPointTypeId(cactiveType.getId());
        activeTransaction.setCustomerId(customerId);
        activeTransaction.setForeignMasterId(masterId);
        activeTransaction.setForeignDetailId(detailId);

        // 查询到记录，执行删除
        int activeId = cactiveDao.getIdBy(activeTransaction);
        log.trace(" ===== cactiveDao.getIdBy ...... {}",activeId);
        if (activeId > 0) {
            int result = cactiveDao.deletePointTransactionBy(activeId);
            log.trace(" ===== cactiveDao.deletePointTransactionBy ...... {}",result);
            if (result > 0) {
                // 减分
                int resultR = customerDao.minusCustomerActivePoints(cactiveType.getPoints(), customerId);
                log.trace(" ===== customerDao.minusCustomerActivePoints ...... {}",resultR);
            }
            return result;
        }
        return 1;
    }
}
