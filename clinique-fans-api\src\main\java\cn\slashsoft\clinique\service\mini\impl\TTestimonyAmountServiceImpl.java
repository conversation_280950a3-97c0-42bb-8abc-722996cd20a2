package cn.slashsoft.clinique.service.mini.impl;

import cn.slashsoft.clinique.dao.mini.TTestimonyAmountDao;
import cn.slashsoft.clinique.domain.mini.TTestimonyAmount;
import cn.slashsoft.clinique.service.mini.TTestimonyAmountService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
* <p>
    * 为提高效率，应该放到redis当中
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
@Service
public class TTestimonyAmountServiceImpl implements TTestimonyAmountService {

    @Resource
    private TTestimonyAmountDao tTestimonyAmountDao;

    @Override
    public void insertTTestimonyAmount(TTestimonyAmount tTestimonyAmount) {
        tTestimonyAmountDao.insertTTestimonyAmount(tTestimonyAmount);
    }

    @Override
    public void updateTTestimonyAmount(TTestimonyAmount tTestimonyAmount) {
        tTestimonyAmountDao.updateTTestimonyAmount(tTestimonyAmount);
    }

    @Override
    public TTestimonyAmount getTTestimonyAmount(Long id) {
        return tTestimonyAmountDao.getTTestimonyAmount(id);
    }

    @Override
    public Integer getTTestimonyLikeCountByCustomerId(Long customerId) {
        return tTestimonyAmountDao.getTTestimonyLikeCountByCustomerId(customerId);
    }

    @Override
    public Integer getTTestimonyLikeRankByCustomerId(Long customerId){
        return tTestimonyAmountDao.getTTestimonyLikeRankByCustomerId(customerId);
    }


    @Override
    public void updateTTestimonyAmountByCustomerId(Long customerId) {
        tTestimonyAmountDao.updateTTestimonyAmountByCustomerId(customerId);
    }

    @Override
    public TTestimonyAmount getTTestimonyAmountByCustomerId(Long customerId) {
        return tTestimonyAmountDao.getTTestimonyAmountByCustomerId(customerId);
    }
}
