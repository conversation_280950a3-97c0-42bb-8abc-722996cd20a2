package cn.slashsoft.clinique.service.mini.impl;

import java.util.ArrayList;
import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import cn.slashsoft.clinique.dao.mini.CactiveDao;
import cn.slashsoft.clinique.dao.mini.NoteDao;
import cn.slashsoft.clinique.dao.mini.PointDao;
import cn.slashsoft.clinique.dao.mini.VideoDao;
import cn.slashsoft.clinique.dao.mini.WechatDao;
import cn.slashsoft.clinique.domain.mini.PointTransaction;
import cn.slashsoft.clinique.domain.mini.Video;
import cn.slashsoft.clinique.domain.mini.VideoLikeLog;
import cn.slashsoft.clinique.domain.mini.VideoPlayLog;
import cn.slashsoft.clinique.domain.mini.VideoShareLog;
import cn.slashsoft.clinique.domain.mini.VideoTag;
import cn.slashsoft.clinique.domain.mini.VideoTagDefine;
import cn.slashsoft.clinique.domain.mini.VideoTagSearchLog;
import cn.slashsoft.clinique.enums.CActiveTypeEnum;
import cn.slashsoft.clinique.enums.CBForeignEnum;
import cn.slashsoft.clinique.enums.CBTypeEnum;
import cn.slashsoft.clinique.service.mini.CactiveService;
import cn.slashsoft.clinique.service.mini.VideoService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.EncryptUtil;

/**
 * 视频
 *
 * <AUTHOR>
 */
@Service
public class VideoServiceImpl implements VideoService {

	@Resource
	private CactiveService cactiveService;
	
	private final VideoDao videoDao;
	private final PointDao pointDao;
	private final CactiveDao activeDao;
	private final WechatDao wechatDao;

	public VideoServiceImpl(VideoDao videoDao,PointDao pointDao,CactiveDao activeDao, WechatDao wechatDao) {
		this.videoDao = videoDao;
		this.pointDao = pointDao;
		this.activeDao = activeDao;
		this.wechatDao = wechatDao;
	}

	/**
	 * 获取top笔记/视频标签列表
	 *
	 */
	public ArrayList<VideoTagDefine> getTopTagList() {
		ArrayList<VideoTagDefine> videoTopTagList = this.videoDao.getTopTagList();
		int size = videoTopTagList.size();
		if(size < 10) {
			ArrayList<VideoTagDefine> videoHotTagList = this.videoDao.getHotTagList();
			for(VideoTagDefine hotTag : videoHotTagList) {
				boolean has = false;
				for(VideoTagDefine tag : videoTopTagList) {
					if(tag.getId() == hotTag.getId()) {
						has = true;
						break;
					}
				}
				if(!has) {
					videoTopTagList.add(hotTag);
				}
				if(videoTopTagList.size() == 10) {
					break;
				}
			}		
		}
		
		return videoTopTagList;
	}

	/**
	 * 搜索笔记标签
	 *
	 * @param key
	 * @param type
	 */
	public ArrayList<VideoTagDefine> getTagList(String key, int type) {
		return this.videoDao.getTagList(key, type);
	}
	/**
	 * 保存标签搜索、使用日志
	 *
	 * @param NoteTagSearchLog 日志
	 */
	public int insertTagSearchLog(VideoTagSearchLog log) {
		return  this.videoDao.insertTagSearchLog(log);
	}
	/**
	 * 保存阅读视频日志
	 *
	 * @param VideoReadLog 日志
	 */
	public int insertReadLog(VideoPlayLog log, String shareOpenId) {
		Long customerId = 0L;
		if(shareOpenId != null && shareOpenId.length() >0) {
			// 分享笔记阅读 给分享者加C粉值
			customerId = this.wechatDao.getCustomerIdByOpenId(shareOpenId);
			if(customerId == null ) {
				customerId = 0L;
			}
		}
		log.setShareCustomerId(customerId);
		int count = this.videoDao.insertPlayLog(log);
		if(count == 0) {
			return count;
		}
        Date now = new Date();
        
        /*
		if(customerId != null && customerId.intValue() > 0 && customerId != log.getCustomerId()) {
			// 分享视频播放 给分享者加C粉值
	    
	        cactiveService.addPoints(
	        		customerId, 
	        		CActiveTypeEnum.SHARE_READ, 
	        		log.getVideoId(), 
	        		log.getId(), 
	        		"拔草笔记");
		}
		*/
       // if(this.videoDao.getPlayLogCount(log) == 1) {
			// 发放C粉值
	        /*
	        cactiveService.addPoints(
	        		log.getCustomerId(), 
	        		CActiveTypeEnum.NOTE_VIDEO_READ, 
	        		log.getVideoId(), 
	        		log.getId(), 
	        		"");
	        		*/
			if(log.getType() == 2) {
				//客户端大于80%进度上传，避免float计算损失，这里启用0.6
				if(log.getPlayTime()/log.getDuration() >= 0.6) {

					//视频每月加CB 一次
					int monthCount = 
							pointDao.getCustomerMonthPointCountByType(CBTypeEnum.VIDEO_PLAY.getId(),log.getCustomerId());
					if(monthCount >= CBTypeEnum.VIDEO_PLAY.getMax()) {
						return count;
					}
					//查看本视频是否已经加过CB
					int giveCount = pointDao.getCustomerPointCountByDetailId(CBTypeEnum.VIDEO_PLAY.getId(),log.getCustomerId(),log.getVideoId());
					if(giveCount > 0) {
						return count;
					}		
		            // 发放C币
		            PointTransaction pointTransaction = new PointTransaction();
		            pointTransaction.setCustomerId(log.getCustomerId());
		            pointTransaction.setPointTypeId(CBTypeEnum.VIDEO_PLAY.getId());
		            pointTransaction.setPoints(CBTypeEnum.VIDEO_PLAY.getPoints());
		            pointTransaction.setRemainingPoints(CBTypeEnum.VIDEO_PLAY.getPoints());
		            pointTransaction.setStartTime(now);
		            pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
		            pointTransaction.setForeignId(CBForeignEnum.VIDEO_PLAY.getId());
		            pointTransaction.setForeignMasterId(log.getVideoId());
		            pointTransaction.setForeignDetailId(log.getId());
		            pointTransaction.setRemark(CBTypeEnum.VIDEO_PLAY.getName());
					//每个视频CB一次
			        if(pointDao.has(pointTransaction) == 0) {
			        	pointDao.insertPointTransaction(pointTransaction);
			        }
				}
			}
      //  }
		return count;
	}

	/**
	 * 拔草视频
	 *
	 * @param VideoLikeLog
	 */
	public int insertLikeLog(VideoLikeLog log) {
		int count = this.videoDao.insertLikeLog(log);
		if(count == 0) {
			return count;
		}

       // if(this.videoDao.getLikeLogCount(log) == 1) {
	        	// 发放C粉值
		
	        Date now = new Date();
	        /*
	        cactiveService.addPoints(
	        		log.getCustomerId(), 
	        		CActiveTypeEnum.NOTE_VIDEO_LIKE, 
	        		log.getVideoId(), 
	        		log.getId(), 
	        		"拔草视频");
	        
			//查看本类型加分是否已超过10次

			int giveCount = pointDao.getCustomerMonthPointCountByType(CBTypeEnum.NOTE_VIDEO_ACTION.getId(),log.getCustomerId());
			if(giveCount >= CBTypeEnum.NOTE_VIDEO_ACTION.getMax()) {
				return count;
			}	
			*/			
	        // 发放C币
	        PointTransaction pointTransaction = new PointTransaction();
	        pointTransaction.setCustomerId(log.getCustomerId());
	        pointTransaction.setPointTypeId(CBTypeEnum.NOTE_VIDEO_ACTION.getId());
	        pointTransaction.setPoints(CBTypeEnum.NOTE_VIDEO_ACTION.getPoints());
	        pointTransaction.setRemainingPoints(CBTypeEnum.NOTE_VIDEO_ACTION.getPoints());
	        pointTransaction.setStartTime(now);
	        pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
	        pointTransaction.setForeignId(CBForeignEnum.NOTE_VIDEO_ACTION.getId());
	        pointTransaction.setForeignMasterId(log.getVideoId());
	        pointTransaction.setForeignDetailId(log.getId());
	        pointTransaction.setRemark("拔草视频");
	        if(pointDao.has(pointTransaction) == 0) {
	        	pointDao.insertPointTransaction(pointTransaction);
	        }
        //}
		return count ;
	}

	/**
	 * 删除 拔草视频
	 *
	 * @param videoId
	 * @param customerId
	 */
	public int unlikeVideo(VideoLikeLog log) {
		return this.videoDao.delLikeLog(log);
	}

	/**
	 * 保存分享视频日志
	 *
	 * @param VideoShareLog
	 */
	public int insertShareLog(VideoShareLog log) {
		int count =  this.videoDao.insertShareLog(log);
		if(count == 0) {
			return count;
		}

		//查看本类型加分是否已超过10次
		int giveCount = pointDao.getCustomerMonthPointCountByType(CBTypeEnum.NOTE_VIDEO_SHARE.getId(),log.getCustomerId());
		if(giveCount >= CBTypeEnum.NOTE_VIDEO_SHARE.getMax()) {
			return count;
		}				
        // 发放C币
        Date now = new Date();
        PointTransaction pointTransaction = new PointTransaction();
        pointTransaction.setCustomerId(log.getCustomerId());
        pointTransaction.setPointTypeId(CBTypeEnum.NOTE_VIDEO_SHARE.getId());
        pointTransaction.setPoints(CBTypeEnum.NOTE_VIDEO_SHARE.getPoints());
        pointTransaction.setRemainingPoints(CBTypeEnum.NOTE_VIDEO_SHARE.getPoints());
        pointTransaction.setStartTime(now);
        pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
        pointTransaction.setForeignId(CBForeignEnum.NOTE_VIDEO_SHARE.getId());
        pointTransaction.setForeignMasterId(log.getVideoId());
        pointTransaction.setForeignDetailId(log.getId());
        pointTransaction.setRemark("分享视频");
        pointDao.insertPointTransaction(pointTransaction);
		return count ;

	}


	/**
	 * 获取用户拔草视频列表
	 *
	 * @param customerId
	 * @param start
	 */
	public ArrayList<Video> getCustomerLikeVideoList(Long customerId, int start) {
		ArrayList<Video> list = this.videoDao.getCustomerLikeVideoList(customerId, start);
		for(Video video : list) {
			video.setTags(this.videoDao.getVideoTagList(video.getId()));
		}
		return list;
	}



	/*
	 * 获取视频列表
	 */
	@Override
	public ArrayList<Video> getVideoList(Long customerId, Long tagId) {
		ArrayList<Video> list = null;
		if(tagId.intValue() == 0) {
			list = this.videoDao.getVideoList(customerId);
		}else {
			if(customerId.intValue() != 0) {
				VideoTagDefine tag = this.videoDao.getVideoTagDefine(tagId);
				VideoTagSearchLog log = new VideoTagSearchLog();
				log.setCustomerId(customerId);
				log.setTagType(tag.getType());
				log.setTagTitle(tag.getTitle());
				log.setTagId(tagId);
				this.videoDao.insertTagSearchLog(log);
			}
			list = this.videoDao.getVideoListByTag(customerId, tagId);
		}
		for(Video video : list) {
			video.setTags(this.videoDao.getVideoTagList(video.getId()));
		}
		
		return list;
	}

}
