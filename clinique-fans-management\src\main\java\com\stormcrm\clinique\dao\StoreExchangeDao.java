package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.StoreGift;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 商城兑礼管理
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface StoreExchangeDao {

    /**
     * 查询所有礼品-分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @return 礼品列表
     */
    @SelectProvider(type = StoreExchangeProvider.class, method = "getPage")
    List<StoreGift> getPage(
            @Param("pageIndex") int pageIndex,
            @Param("pageSize") int pageSize,
            @Param("generalSearch") String generalSearch
    );

    /**
     * 查询所有礼品带分页的记录数
     *
     * @param generalSearch 搜索
     * @return 记录数
     */
    @SelectProvider(type = StoreExchangeProvider.class, method = "getPageCount")
    int getPageCount(
            String generalSearch
    );


}
