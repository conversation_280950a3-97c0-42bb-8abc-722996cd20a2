package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.NotePointLog;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * C 币奖励 笔记相关日志
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface NotePointLogDao {

    /**
     * 保存笔记和标签相关的得分日志
     *
     * @param log 日志
     */
    @Insert("INSERT INTO `note_point_log`(" +
            "   `target_id`, " +
            "   `point_type`, " +
            "   `points`, " +
            "   `note_id`, " +
            "   `tag_id`, " +
            "   `customer_id` " +
            ") " +
            "VALUES (" +
            "   #{targetId}, " +
            "   #{pointType}, " +
            "   #{points}, " +
            "   #{noteId}, " +
            "   #{tagId}, " +
            "   #{customerId}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertPointLog(NotePointLog log);

    /**
     * 根据目标id和奖励类型查询历史记录
     *
     * @param targetId 根据得分类型，可能是笔记ID或标签ID
     * @param type     奖励类型
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `note_point_log` " +
            "WHERE " +
            "   `target_id`=#{targetId} AND " +
            "   `point_type`=#{type} ")
    List<NotePointLog> getPointLog(Long targetId, int type);


    /**
     * 根据 note tag 的 id 和 奖励类型 查询历史记录
     *
     * @param noteId 笔记ID
     * @param tagId  标签ID
     * @param type   奖励类型
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `note_point_log` " +
            "WHERE " +
            "   `note_id`=#{noteId} AND " +
            "   `tag_id`=#{tagId} AND " +
            "   `point_type`=#{type} ")
    List<NotePointLog> getPointLogs(Long noteId, Long tagId, int type);

    /**
     * 根据 客户id、奖励类型、日期、
     * <p>
     * 按月统计
     *
     * @param customerId 客户id
     * @param pointType  奖励类型
     * @param date       日期，完整日期 now 即可
     */
    @Select("SELECT " +
            "   count(*) " +
            "FROM " +
            "   `note_point_log` " +
            "WHERE " +
            "   `customer_id`=#{customerId} AND " +
            "   date_format(create_time,'%Y%m') = date_format(#{date},'%Y%m')  AND " +
            "   `point_type`=#{pointType} ")
    Integer getMaxTimesBy(Long customerId, Short pointType, String date);

    /**
     * 根据 客户id、奖励类型
     *
     * @param customerId 客户id
     * @param pointType  奖励类型
     */
    @Select("SELECT " +
            "   count(*) " +
            "FROM " +
            "   `note_point_log` " +
            "WHERE " +
            "   `customer_id`=#{customerId} AND " +
            "   `point_type`=#{pointType} ")
    Integer getMaxTimesWithoutDate(Long customerId, Short pointType);

    /**
     * 根据 客户id、奖励类型、日期、
     * <p>
     * 按月统计
     *
     * @param customerId 客户id
     * @param pointType  奖励类型
     * @param date       日期，完整日期 now 即可
     */
    @Select("SELECT " +
            "   count(*) " +
            "FROM " +
            "   `note_point_log` " +
            "WHERE " +
            "   `customer_id`=#{customerId} AND " +
            "   `tag_id`=#{tagId} AND " +
            "   date_format(create_time,'%Y%m') = date_format(#{date},'%Y%m')  AND " +
            "   `point_type`=#{pointType} ")
    Integer getMaxTimesForTagBy(Long customerId, Short pointType, String date, Long tagId);

    /**
     * 根据 客户id、奖励类型
     *
     * @param customerId 客户id
     * @param pointType  奖励类型
     */
    @Select("SELECT " +
            "   count(*) " +
            "FROM " +
            "   `note_point_log` " +
            "WHERE " +
            "   `customer_id`=#{customerId} AND " +
            "   `tag_id`=#{tagId} AND " +
            "   `point_type`=#{pointType} ")
    Integer getMaxTimesForTagWithoutDate(Long customerId, Short pointType, Long tagId);

}
