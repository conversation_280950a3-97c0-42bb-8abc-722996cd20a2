package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * EBCI 美白达人官
 *
 * <AUTHOR>
 */
public interface CampaignEbciTalentsService {

    /**
     * 获取美白达人官信息
     *
     * @param customerId 顾客编号
     * @return 美白达人官信息
     */
    CampaignEbciTalents getCampaignEbciTalents(long customerId);

    /**
     * 更新提醒状态
     *
     * @param customerId 顾客编号
     */
    void updateCampaignEbciTalentsAlert(long customerId);

    /**
     * 获取图片
     *
     * @param campaignEbciTalentsId 顾客活动信息编号
     * @return 图片地址
     */
    List<CampaignEbciTalentsImage> getCampaignEbciTalentsImageList(long campaignEbciTalentsId);

    /**
     * 获取当前TOP信息
     *
     * @return TOP信息
     */
    CampaignEbciTalentsTop getCampaignEbciTalentsTop();

    /**
     * 获取入选TOP的名单
     *
     * @param campaignEbciTalentsTopId TOP信息编号
     * @return 名单
     */
    List<CampaignEbciTalentsTopDetail> getCampaignEbciTalentsTopDetailList(long campaignEbciTalentsTopId);

    /**
     * 获取入选TOP的图片
     *
     * @param campaignEbciTalentsTopId TOP信息编号
     * @return 图片
     */
    List<CampaignEbciTalentsTopDetailImage> getCampaignEbciTalentsTopDetailImageList(long campaignEbciTalentsTopId);

    /**
     * 保存图片
     *
     * @param customerId 顾客编号
     * @param file       要保存的图片
     * @return 处理结果
     */
    boolean upload(long customerId, MultipartFile file);

    /**
     * 写入访问开始日志
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @param page       页面
     * @return 访问日志编号
     */
    long insertCampaignEbciTalentsViewLog(long customerId, String source, String page);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    void setCampaignEbciTalentsViewLog(long id);

}
