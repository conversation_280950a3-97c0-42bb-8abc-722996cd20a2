package com.stormcrm.clinique.tag.dao;

import java.util.List;

import com.stormcrm.clinique.domain.mini.CustomerLabel;
import com.stormcrm.clinique.domain.tag.LabelResult;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import com.stormcrm.clinique.domain.tag.Label;
import com.stormcrm.clinique.domain.tag.LabelOutput;
import com.stormcrm.clinique.vo.TargetBiVo;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
public interface LabelDao {

    @Select("SELECT * from label where status = 1 ")
    List<Label> getLabelList();
    
    @Select("SELECT * from label where id=#{id} and status = 1 ")
    Label getLabelById(int id);

    @Select("SELECT * from output where id=#{outputId} and status = 1 limit 1 ")
	LabelOutput getOutput(Label label);
    

    @Select("SELECT * from output where status = 1 ")
    List<LabelOutput> getOutputAll();

    @Select("SELECT id,name from output  where status = 1")
	List<TargetBiVo> getOutputAllForBI();

    @Insert("INSERT INTO label ("
    		+ "`name`,"
    		+ "`output_id`,"
    		+ "`type`,"
    		+ "`user_id`,"
    		+ "`status`,"
    		+ "`create_time`,"
    		+ "`update_time`"
    		+ ") VALUES ("
    		+ "#{name},"
    		+ "#{outputId},"
    		+ "1,"
    		+ "1,"
    		+ "1,"
    		+ "now(),"
    		+ "now()"
    		+ ") ")
	@Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
	int addLabel(Label label);
    
    @Update("UPDATE label SET "
    		+ " `name` = #{name},"
    		+ " `output_id` = #{outputId},"
    		+ " `type` = #{type},"
    		+ " `user_id` = #{userId},"
    		+ " `status` = #{status},"
    		+ " `update_time` = now()"
    		+ " WHERE id = #{id}")
	int updateLabel(Label label);


    @Delete("DELETE from label where id = #{labelId}")
	void delLabel(int labelId);

	@Insert("INSERT INTO label_result ("
			+ "`label_id`,"
			+ "`result`,"
			+ "`user_id`,"
			+ "`status`,"
			+ "`create_time`"
			+ ") VALUES ("
			+ "#{labelId},"
			+ "#{result},"
			+ "1,"
			+ "1,"
			+ "now()"
			+ ") ")
	@Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int addLabelResult(LabelResult result);

    @Select("SELECT * from label_result where label_id = #{labelId} order by create_time desc limit 1")
    LabelResult getLastResult(int labelId);

	@Select("${sql}")
	int getSqlResult(@Param(value="sql") String sql);

	@Select("${sql}")
    List<CustomerLabel> getSqlList(@Param(value="sql") String sql);
}
