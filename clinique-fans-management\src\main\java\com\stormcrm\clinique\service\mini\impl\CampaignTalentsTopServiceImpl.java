package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.domain.CampaignTalentsTop;
import com.stormcrm.clinique.service.CampaignTalentsTopService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.vo.Result;
import com.stormcrm.clinique.dao.CampaignTalentsTopDao;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 新品体验官活动
 *
 * <AUTHOR>
 */
@Service
public class CampaignTalentsTopServiceImpl implements CampaignTalentsTopService {

    private final CampaignTalentsTopDao campaignTalentsTopDao;

    public CampaignTalentsTopServiceImpl(CampaignTalentsTopDao campaignTalentsTopDao) {
        this.campaignTalentsTopDao = campaignTalentsTopDao;
    }

    /**
     * 查询所有活动
     *
     * @return 活动列表
     */
    @Override
    public List<CampaignTalentsTop> getAll() {
        return campaignTalentsTopDao.getAll();
    }

    /**
     * 查询信息
     *
     * @param id 自动编号
     * @return 信息
     */
    @Override
    public CampaignTalentsTop getById(long id) {
        return campaignTalentsTopDao.getById(id);
    }

    /**
     * 保存
     *
     * @param campaignTalentsTop 信息
     * @return 影响的行数
     */
    @Override
    public Result save(CampaignTalentsTop campaignTalentsTop) {
        campaignTalentsTopDao.save(campaignTalentsTop);
        return ResultUtil.success("增加成功!");
    }

    /**
     * 更新
     *
     * @param campaignTalentsTop 信息
     * @return 影响的行数
     */
    @Override
    public Result update(CampaignTalentsTop campaignTalentsTop) {
        campaignTalentsTopDao.update(campaignTalentsTop);
        return ResultUtil.success("编辑成功!");
    }
}
