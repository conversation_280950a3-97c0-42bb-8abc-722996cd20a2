package cn.slashsoft.clinique.controller.official;

import cn.slashsoft.clinique.domain.mini.Account;
import cn.slashsoft.clinique.domain.mini.StoreExchange;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.VerificationService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.util.StringUtil;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.util.Date;

/**
 * 核销
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/official/verification")
public class VerificationController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpSession session;

    private final VerificationService verificationService;

    public VerificationController(VerificationService verificationService) {
        this.verificationService = verificationService;
    }

    @GetMapping("/index")
    public String index(Model model) {
        model.addAttribute("staticDomain", staticDomain);

        String accountId = String.valueOf(session.getAttribute("verificationAccountId"));
        Date expiredTime = DateUtil.valueOf((String) session.getAttribute("verificationExpiredTime"));
        if (!StringUtil.isNullOrEmpty(accountId) && null != expiredTime && DateUtil.laterThanNow(expiredTime)) {
            return "redirect:/official/verification/search/" + accountId;
        }

        return "w/fans/official/verification/index";

    }

    @PostMapping("/login")
    @ResponseBody
    public String login(
            @RequestParam("username") String username,
            @RequestParam("password") String password
    ) {

        if (StringUtil.isNullOrEmpty(username)) {
            return ResultUtil.customer(ResultEnum.FAILED, "帐号不能为空");
        }

        if (StringUtil.isNullOrEmpty(password)) {
            return ResultUtil.customer(ResultEnum.FAILED, "密码不能为空");
        }

        Account account = verificationService.getAccountByUsername(username);
        if (null == account) {
            return ResultUtil.customer(ResultEnum.FAILED, "帐号不存在");
        }

        if (!account.getStatus()) {
            return ResultUtil.customer(ResultEnum.FAILED, "帐号已停用");
        }

        if (!password.equals(account.getPassword())) {
            return ResultUtil.customer(ResultEnum.FAILED, "密码不正确");
        }

        session.setAttribute("verificationAccountId", account.getId());
        session.setAttribute("verificationExpiredTime", DateUtil.parseString(DateUtil.addDay(new Date(),1)));

        return ResultUtil.customer(ResultEnum.SUCCESS, "成功", account.getId());

    }

    @GetMapping("/search/{accountId}")
    public String search(
            @PathVariable("accountId") String accountId,
            Model model) {
        model.addAttribute("staticDomain", staticDomain);
        
        String sessionAccountId = String.valueOf(session.getAttribute("verificationAccountId"));
        Date expiredTime = DateUtil.valueOf((String) session.getAttribute("verificationExpiredTime"));
        if (!accountId.equals(sessionAccountId) || null == expiredTime || DateUtil.earlierThanNow(expiredTime)) {
            return "redirect:/official/verification/index";
        }

        model.addAttribute("accountId", accountId);

        return "w/fans/official/verification/search";

    }

    @PostMapping("/order/{accountId}/{order}")
    @ResponseBody
    public String order(
            @PathVariable("accountId") String accountId,
            @PathVariable("order") String order
    ){

        String sessionAccountId = String.valueOf(session.getAttribute("verificationAccountId"));
        Date expiredTime = DateUtil.valueOf((String) session.getAttribute("verificationExpiredTime"));
        if (!accountId.equals(sessionAccountId) || null == expiredTime || DateUtil.earlierThanNow(expiredTime)) {
            return ResultUtil.customer(ResultEnum.UNAUTHORIZED, "未登陆");
        }

        StoreExchange storeExchange = verificationService.getStoreExchangeByOrder(order);
        if(null == storeExchange){
            return ResultUtil.customer(ResultEnum.FAILED, "没有此订单号");
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, "成功");

    }

    @GetMapping("/detail/{accountId}/{order}")
    public String detail(
            @PathVariable("accountId") String accountId,
            @PathVariable("order") String order,
            Model model) {
        model.addAttribute("staticDomain", staticDomain);

        String sessionAccountId = String.valueOf(session.getAttribute("verificationAccountId"));
        Date expiredTime = DateUtil.valueOf((String) session.getAttribute("verificationExpiredTime"));
        if (!accountId.equals(sessionAccountId) || null == expiredTime || DateUtil.earlierThanNow(expiredTime)) {
            return "redirect:/official/verification/index";
        }

        StoreExchange storeExchange = verificationService.getStoreExchangeByOrder(order);
        model.addAttribute("storeExchange", storeExchange);
        model.addAttribute("accountId", accountId);
        model.addAttribute("order", order);

        return "w/fans/official/verification/detail";

    }

    @PostMapping("/receive/{accountId}/{order}")
    @ResponseBody
    public String loginSubmit(
            @PathVariable("accountId") String accountId,
            @PathVariable("order") String order
    ) {

        String sessionAccountId = String.valueOf(session.getAttribute("verificationAccountId"));
        Date expiredTime = DateUtil.valueOf((String) session.getAttribute("verificationExpiredTime"));
        if (!accountId.equals(sessionAccountId) || null == expiredTime || DateUtil.earlierThanNow(expiredTime)) {
            return ResultUtil.customer(ResultEnum.UNAUTHORIZED, "未登陆");
        }

        if (0 == verificationService.updateStoreExchangeLogisticsByOrder(order)) {
            return ResultUtil.customer(ResultEnum.FAILED, "核销失败");
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, "成功");

    }

}
