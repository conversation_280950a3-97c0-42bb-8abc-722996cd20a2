package cn.slashsoft.clinique.dao.mini;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.mini.MiniWindow;

/**
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface MiniWindowDao {
	
	@Select("SELECT * FROM mini_window WHERE id = #{id} AND status=1")
	MiniWindow getById(Long id);
	
}
