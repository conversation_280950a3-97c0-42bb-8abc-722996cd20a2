package cn.slashsoft.clinique.controller.mini;


import cn.slashsoft.clinique.domain.mini.Customer;
import cn.slashsoft.clinique.domain.mini.RankingGift;
import cn.slashsoft.clinique.domain.mini.RankingLog;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.enums.StoreExchangeTypeEnum;
import cn.slashsoft.clinique.service.mini.*;
import cn.slashsoft.clinique.util.IntegerUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.vo.Result;
import cn.slashsoft.clinique.vo.mini.Exchange;
import cn.slashsoft.clinique.vo.mini.RankingExchangeExpressVo;
import cn.slashsoft.clinique.vo.mini.RankingExchangeVo;
import cn.slashsoft.clinique.vo.mini.RankingVo;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 积分排名
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class RankingController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final RankingService rankingService;
    private final StoreService storeService;
    private final PointService pointService;
    private final CustomerService customerService;
    private final SnsService snsService;
    private final StringRedisTemplate stringRedisTemplate;

    public RankingController(RankingService rankingService, StoreService storeService, PointService pointService, CustomerService customerService, SnsService snsService, StringRedisTemplate stringRedisTemplate) {
        this.rankingService = rankingService;
        this.storeService = storeService;
        this.pointService = pointService;
        this.customerService = customerService;
        this.snsService = snsService;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    @PostMapping("/ranking/get-data")
    public String getRankingData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        // 获取我的排名
        Customer customer = rankingService.getCustomerRankingById(customerId);

        RankingVo rankingVo = new RankingVo();

        rankingVo.setRankingStatus(IntegerUtil.parseInt(stringRedisTemplate.opsForValue().get("configRankingStatus")));
        rankingVo.setRanking(customer.getRankingAlways());
        rankingVo.setCustomerList(rankingService.getCustomerRankingTop());

        return ResultUtil.customer(ResultEnum.SUCCESS, rankingVo);

    }

    /**
     * 获取商城兑换页页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/ranking/get-exchange-data/{id}")
    public String getRankingExchangeData(
            @PathVariable("id") long id
    ) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 获取我的排名
        Customer customer = rankingService.getCustomerRankingById(customerId);
        Long ranking = customer.getRanking();

        RankingExchangeVo rankingExchangeVo = new RankingExchangeVo();
        rankingExchangeVo.setRanking(ranking);

        if (150 >= ranking) {
            RankingGift rankingGift = rankingService.getRankingGiftById(id, customerId);
            if(null != rankingGift && rankingGift.getRankingMin() <= ranking && ranking <= rankingGift.getRankingMax()){
                rankingExchangeVo.setPointTotal(pointService.getRemainingPointTotal(customerId));
                rankingExchangeVo.setStoreAreaList(storeService.getStoreAreaList());
                rankingExchangeVo.setStoreAreaPlaceList(storeService.getStoreAreaPlaceList());
                rankingExchangeVo.setRankingGift(rankingGift);
            }
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, rankingExchangeVo);

    }

    /**
     * 获取商城兑换页页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/ranking/get-exchange-express-data/{id}")
    public String getRankingExchangeExpressData(
            @PathVariable("id") long id
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 获取我的排名
        Customer customer = rankingService.getCustomerRankingById(customerId);
        Long ranking = customer.getRanking();

        RankingExchangeExpressVo rankingExchangeExpressVo = new RankingExchangeExpressVo();
        rankingExchangeExpressVo.setRanking(ranking);

        if (150 >= ranking) {
            RankingGift rankingGift = rankingService.getRankingGiftById(id, customerId);
            if(null != rankingGift && rankingGift.getRankingMin() <= ranking && ranking <= rankingGift.getRankingMax()){
                rankingExchangeExpressVo.setPointTotal(pointService.getRemainingPointTotal(customerId));
                rankingExchangeExpressVo.setCustomerAddressList(customerService.getCustomerAddressByCustomerId(customerId));
                rankingExchangeExpressVo.setRankingGift(rankingGift);
            }
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, rankingExchangeExpressVo);
    }

    /**
     * 立即兑换
     *
     * @return 页面数据
     */
    @PostMapping("/ranking/exchange-for-place")
    public String exchangeForPlace(
            @RequestBody Exchange exchange
    ) {

        if (0 >= exchange.getId() || StringUtil.isNullOrEmpty(exchange.getAreaId()) || 0 >= exchange.getAreaPlaceId()) {
            return ResultUtil.customer(ResultEnum.PARAM_ERROR, "参数错误");
        }

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        exchange.setCustomerId(customerId);

        // 1,成功； 2,礼品不存在或已下架; 3,库存不足; 4,系统繁忙;
        Result result = rankingService.exchange(exchange, StoreExchangeTypeEnum.PICKUP);
        if (1 != result.getCode()) {
            return ResultUtil.customer(ResultEnum.FAILED, result.getMessage());
        }

        // 发送验证码
        snsService.exchangePickupSend(customerId, exchange.getAreaPlaceId());

        return ResultUtil.customer(ResultEnum.SUCCESS);

    }

    /**
     * 立即兑换
     *
     * @return 页面数据
     */
    @PostMapping("/ranking/exchange-for-express")
    public String exchangeForExpress(
            @RequestBody Exchange exchange
    ) {

        if (0 >= exchange.getId() || 0 >= exchange.getCustomerAddressId()) {
            return ResultUtil.customer(ResultEnum.PARAM_ERROR, "参数错误");
        }

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        exchange.setCustomerId(customerId);

        // 1,成功； 2,礼品不存在或已下架; 3,库存不足; 4,系统繁忙;
        Result result = rankingService.exchange(exchange, StoreExchangeTypeEnum.EXPRESS);
        if (1 != result.getCode()) {
            return ResultUtil.customer(ResultEnum.FAILED, result.getMessage());
        }

        // 发送验证码
        snsService.exchangeExpress(customerId);

        return ResultUtil.customer(ResultEnum.SUCCESS);

    }

    /**
     * 写入日志
     *
     * @param rankingLog 日志
     * @return 处理结果
     */
    @PostMapping("/ranking/set-ranking-log")
    public String setRankingLog(
            @RequestBody RankingLog rankingLog
    ) {

        rankingLog.setOpenid((String) request.getAttribute("miniOpenid"));
        rankingService.setRankingLog(rankingLog);

        return ResultUtil.customer(ResultEnum.SUCCESS);

    }

}
