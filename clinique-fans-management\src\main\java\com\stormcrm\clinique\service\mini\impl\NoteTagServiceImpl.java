package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.dao.VideoTagDao;
import com.stormcrm.clinique.domain.NoteTag;

import com.stormcrm.clinique.dao.NoteDao;
import com.stormcrm.clinique.dao.PointDao;
import com.stormcrm.clinique.service.NoteTagService;
import com.stormcrm.clinique.service.SmotService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;

import java.util.ArrayList;
import java.util.List;


/**
 * 笔记
 *
 * <AUTHOR>
 */
@Service
public class NoteTagServiceImpl implements NoteTagService {
    @Value("${wechat.subscribe-template.examine-result}")
    private String templateExamineResult;

    private final NoteDao noteDao;
    private final VideoTagDao videoTagDao;
    private final PointDao pointDao;
    private final SmotService smotService;

    public NoteTagServiceImpl(
            NoteDao noteDao,
            VideoTagDao videoTagDao,
            PointDao pointDao,
            SmotService smotService) {
        this.noteDao = noteDao;
        this.videoTagDao = videoTagDao;
        this.pointDao = pointDao;
        this.smotService = smotService;
    }


    /**
     * 添加标签
     *
     * @param noteTag
     */
    public void insertTag(NoteTag noteTag) {
        this.noteDao.insertNoteTag(noteTag);
    }

    /**
     * 修改标签
     *
     * @param noteTag
     */
    public void updateTag(NoteTag noteTag, boolean withImage) {
        if (withImage) {
            this.noteDao.updateNoteTag(noteTag);
        } else {
            this.noteDao.updateNoteTagWithoutImage(noteTag);
        }

    }

    /**
     * 删除标签
     *
     * @param id
     */
    public int deleteTag(Long id) {
        return noteDao.deleteNoteTag(id);
    }


    /**
     * 搜索笔记标签
     *
     *@param key
     *@param type
     */
//    public ArrayList<NoteTag> getTagList(String key, int type) {
//    	return this.noteDao.getTagList(key, type);
//    }


    /**
     * 查询所有活动带分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索
     * @param top           状态
     * @return 活动列表分页
     */
    @Override
    public List<NoteTag> getPage(int page, int perpage, String generalSearch, Short top) {
        return noteDao.getTagPage((page - 1) * perpage, perpage, generalSearch, top);
    }

    /**
     * 查询所有活动带分页的记录数
     *
     * @param generalSearch 搜索
     * @param top           状态
     * @return 记录数
     */
    @Override
    public int getPageCount(String generalSearch, Short top) {
        return noteDao.getTagPageCount(generalSearch, top);
    }


    /**
     * 获取单个标签
     *
     * @param id 状态
     * @return 记录数
     */
    @Override
    public NoteTag getTag(Long id) {
        return this.noteDao.getTag(id);
    }
}
