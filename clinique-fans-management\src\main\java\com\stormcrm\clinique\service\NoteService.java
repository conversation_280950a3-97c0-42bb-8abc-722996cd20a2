package com.stormcrm.clinique.service;

import com.stormcrm.clinique.domain.Note;
import com.stormcrm.clinique.domain.NoteTag;
import com.stormcrm.clinique.domain.NoteDiscuss;
import com.stormcrm.clinique.domain.NoteLikeLog;
import com.stormcrm.clinique.domain.NoteReadLog;
import com.stormcrm.clinique.domain.NoteShareLog;
import com.stormcrm.clinique.domain.NotePhoto;
import com.stormcrm.clinique.domain.NotePhotoTag;

import java.util.ArrayList;
import java.util.List;

/**
 * 笔记
 *
 * <AUTHOR>
 */
public interface NoteService {
    /**
     * 保存笔记
     *
     * @param note
     */
    void insertNote(Note note);

    /**
     * 保存笔记评论
     *
     * @param noteDiscuss
     */
    void insertDiscuss(NoteDiscuss noteDiscuss);

    /**
     * 删除笔记
     *
     * @param note
     */
    void delNote(Note note);

    /**
     * 保存阅读笔记日志
     *
     * @param log 日志
     */
    void insertReadLog(NoteReadLog log);

    /**
     * 保存拔草笔记日志
     *
     * @param log
     */
    void insertLikeLog(NoteLikeLog log);

    /**
     * 保存分享笔记日志
     *
     * @param log
     */
    void insertShareLog(NoteShareLog log);

    /**
     * 笔记是否拔草
     *
     * @param noteId
     */
    int liked(Long noteId, Long customerId);


    /**
     * 搜索笔记标签
     *
     * @param noteId 笔记id
     * @return
     */
    List<Object> getTagList(Long noteId);


    /**
     * 根据笔记 id 获取全部标签列表
     *
     * @param noteId
     */
    ArrayList<NoteTag> getTagListByNoteId(Long noteId);


    /**
     * 获取用户笔记评论列表
     *
     * @param customerId
     * @param start
     */
    ArrayList<NoteDiscuss> getCustomerDiscussList(int customerId, int start);

    /**
     * 获取笔记列表
     *
     * @param start
     * @return
     */
    List<Note> getNoteList(int start);

    /**
     * 获取用户笔记列表
     *
     * @param customerId
     * @param start
     */
    ArrayList<Note> getCustomerNoteList(int customerId, int start);

    /**
     * 获取用户笔记
     *
     * @param id
     */
    Note getNote(Long id);

    /**
     * 审核通过
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    int accept(long id);

    /**
     * 审核通过
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    int accept(long id, int status);

    /**
     * 批量同意
     *
     * @param ids 自动编号
     * @return 影响的行数
     */
    int acceptBatch(String ids);

    /**
     * 批量同意
     *
     * @param ids    自动编号
     * @param status 2 3 4 来自 ReviewType
     * @return 影响的行数
     */
    int acceptBatch(String ids, int status);

    /**
     * 拒绝
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    int reject(long id);

    /**
     * 批量拒绝
     *
     * @param ids 自动编号
     * @return 影响的行数
     */
    int rejectBatch(String ids);

    /**
     * 查询所有活动带分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索
     * @param status        状态
     * @return 活动列表分页
     */

    List<Note> getPage(int page, int perpage, String generalSearch, Short status);

    /**
     * 查询所有活动带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    int getPageCount(String generalSearch, Short status);

    /**
     * 查询所有图，太多可能带分页
     *
     * @param page    页码
     * @param perpage 页面大小
     * @param noteId  note
     * @return 活动列表分页
     */

    List<NotePhoto> getPhotoPage(int page, int perpage, Long noteId);

    /**
     * 查询所有活动带分页的记录数
     *
     * @param nodeId 状态
     * @return 记录数
     */
    int getPhotoPageCount(Long nodeId);

    /**
     * 获取用户笔记 的图
     *
     * @param noteId
     * @return
     */
    List<NotePhoto> getPhotos(Long noteId);

    /**
     * 获取 图 的标签
     *
     * @param photoId
     */
    List<NotePhotoTag> getPhotoTags(Long noteId, Long photoId);

    /**
     * 根据图id删除
     *
     * @param photoId
     */
    int deletePhoto(long photoId);

    NotePhoto getPhoto(long id);

    /**
     * 根据图id删除
     *
     * @param noteId
     */
    List<NoteDiscuss> getDiscussPage(Integer page, int perpage, String generalSearch, Short status, Long noteId);

    /**
     * 根据图id删除
     *
     * @param noteId
     */
    int getDiscussPageCount(String generalSearch, Short status, Long noteId);

    int acceptDiscuss(long noteDiscussId);

    int acceptDiscussBatch(String noteDiscussIds);

    int rejectDiscuss(long noteDiscussId);

    int rejectDiscussBatch(String noteDiscussIds);

    ArrayList<NoteDiscuss> getDiscussByNoteId(long noteId);

    void appendNoteTagCBByHistory();

    int deleteDiscuss(long id);
}
