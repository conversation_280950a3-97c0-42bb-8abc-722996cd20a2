package cn.slashsoft.clinique.service.mini;

import cn.slashsoft.clinique.domain.mini.TTestimonyLike;
import cn.slashsoft.clinique.vo.mini.TestimonyHelpThumbupVo;

/**
* <p>
    *
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
public interface TTestimonyLikeService {
    void insertTTestimonyLike(TTestimonyLike tTestimonyLike);
    void updateTTestimonyLike(TTestimonyLike tTestimonyLike);
    TTestimonyLike getTTestimonyLike(Long id);
    TTestimonyLike getTTestimonyLikeByCustomerIdContentId(Long customerId, Long contentId);
    TestimonyHelpThumbupVo getHelpThumbupInfo(Long customerId);
}
