package cn.slashsoft.clinique.controller.mini;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.slashsoft.clinique.domain.mini.Customer;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.CustomerService;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.service.outside.PrivateRelishService;
import cn.slashsoft.clinique.util.ResultUtil;
/**
 *  美颜会 预约成功
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class PrivateRelishMiniController {

    @Resource
    private HttpServletRequest request;

    private final PrivateRelishService prService;
    private final CustomerService customerService;
    private final WechatService wechatService;

    public PrivateRelishMiniController(PrivateRelishService prService, WechatService wechatService, CustomerService customerService) {
        this.prService = prService;
        this.customerService = customerService;
        this.wechatService = wechatService;
    }

}
