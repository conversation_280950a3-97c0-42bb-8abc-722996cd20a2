package com.stormcrm.clinique.service;

import com.stormcrm.clinique.domain.RightLike;

import java.util.List;

/**
 * 点赞积分权益相关服务
 * <AUTHOR>
 */
public interface RightLikeService {

    /**
     * 获取记录总数
     * @return 记录总数
     */
    int getCount();

    /**
     * 按页读取点赞权益
     *
     * @param page 页码
     * @param size 分页大小
     * @return 点赞权益
     */
    List<RightLike> getPage(int page, int size);

}
