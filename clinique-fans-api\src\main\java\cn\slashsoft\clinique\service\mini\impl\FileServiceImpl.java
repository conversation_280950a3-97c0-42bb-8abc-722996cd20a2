package cn.slashsoft.clinique.service.mini.impl;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import cn.slashsoft.clinique.service.mini.FileService;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.util.StringUtil;

/**
 * 与活动相关
 *
 * <AUTHOR>
 */
@Service
public class FileServiceImpl implements FileService {
	    private final OutsideService outsideService;

	    public FileServiceImpl( OutsideService outsideService) {
	        this.outsideService = outsideService;
	    }

	@Override
	public String upload(Long customerId, MultipartFile file) {
		  // 保存图片
        String imageUrl = outsideService.uploadImageOss(file);
        if (StringUtil.isNullOrEmpty(imageUrl)) {
            return "";
        }

		return imageUrl;
	}

   
}
