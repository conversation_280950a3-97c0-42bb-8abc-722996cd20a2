package cn.slashsoft.clinique.service.campaign.impl;

import cn.slashsoft.clinique.dao.campaign.CampaignMsDao;
import cn.slashsoft.clinique.domain.campaign.CampaignMsDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignMsLog;
import cn.slashsoft.clinique.domain.campaign.CampaignMsStock;
import cn.slashsoft.clinique.service.campaign.CampaignMsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

/**
 * 水磁场申领
 *
 * <AUTHOR>
 */
@Service
public class CampaignMsServiceImpl implements CampaignMsService {

    private final CampaignMsDao campaignMsDao;

    public CampaignMsServiceImpl(CampaignMsDao campaignMsDao) {
        this.campaignMsDao = campaignMsDao;
    }

    /**
     * 保存日志
     *
     * @param campaignMsLog 日志
     */
    @Override
    public void insertLog(CampaignMsLog campaignMsLog) {
        campaignMsDao.insertLog(campaignMsLog);
    }

    /**
     * 获取手机号码
     *
     * @param unionid 开放平台唯一编号
     * @return 手机号码
     */
    @Override
    public String getPhoneNumberByUnionid(String unionid) {
        return campaignMsDao.getPhoneNumberByUnionid(unionid);
    }

    /**
     * 获取手机号码
     *
     * @param openid 公众号唯一编号
     * @return 手机号码
     */
    @Override
    public String getPhoneNumberByOpenid(String openid) {
        return campaignMsDao.getPhoneNumberByOpenid(openid);
    }

    /**
     * 获取是否已经审领
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    @Override
    public int hasDetail(String phoneNumber) {
        return campaignMsDao.hasDetail(phoneNumber);
    }

    /**
     * 获取是否已经审领
     *
     * @param openid 公众号唯一编号
     * @return 数量
     */
    @Override
    public int hasDetailByOpenid(String openid) {
        return campaignMsDao.hasDetailByOpenid(openid);
    }

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    @Override
    public CampaignMsDetail getDetail(String phoneNumber) {
        return campaignMsDao.getDetail(phoneNumber);
    }

    /**
     * 申领
     *
     * @param campaignMsDetail 资料
     * @return 0:成功，1：手机号码已经领过了，2：库存不足
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int submit(CampaignMsDetail campaignMsDetail) {

        // 扣库存
        if (0 == campaignMsDao.updateStock(campaignMsDetail)) {
            return 2;
        }

        // 写入记录
        try {
            campaignMsDao.insetDetail(campaignMsDetail);
            return 0;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return 1;
        }

    }

    /**
     * 更新是否关注
     *
     * @param campaignMsDetail 申领信息
     */
    @Override
    public void setFollow(CampaignMsDetail campaignMsDetail) {
        campaignMsDao.setFollow(campaignMsDetail);
    }

    /**
     * 获取审领信息
     *
     * @param openid 公众号唯一编号
     * @return 审领信息
     */
    @Override
    public CampaignMsDetail getDetailByOpenid(String openid) {
        return campaignMsDao.getDetailByOpenid(openid);
    }

    /**
     * 跟据场景值获取门店
     *
     * @param scene 场景值
     * @return 门店
     */
    @Override
    public CampaignMsStock getByScene(String scene) {
        return campaignMsDao.getByScene(scene);
    }

    /**
     * 更新核销信息
     *
     * @param campaignMsDetail 核销信息
     */
    @Override
    public void setReceive(CampaignMsDetail campaignMsDetail) {
        campaignMsDao.setReceive(campaignMsDetail);
    }
}
