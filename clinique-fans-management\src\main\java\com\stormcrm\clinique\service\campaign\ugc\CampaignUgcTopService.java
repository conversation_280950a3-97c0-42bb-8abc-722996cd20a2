package com.stormcrm.clinique.service.campaign.ugc;

import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgcTop;
import com.stormcrm.clinique.vo.Result;

import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
public interface CampaignUgcTopService {

    /**
     * 查询所有活动
     *
     * @param campaignId 活动编号
     * @return 活动列表
     */
    List<CampaignUgcTop> getAll(long campaignId);

    /**
     * 查询信息
     *
     * @param id 自动编号
     * @return 信息
     */
    CampaignUgcTop getById(long id);

    /**
     * 保存
     *
     * @param campaignUgcTop 信息
     * @return 影响的行数
     */
    Result save(CampaignUgcTop campaignUgcTop);

    /**
     * 更新
     *
     * @param campaignUgcTop 信息
     * @return 影响的行数
     */
    Result update(CampaignUgcTop campaignUgcTop);

}
