package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.VideoTagDefine;
import com.stormcrm.clinique.util.StringUtil;
import org.apache.ibatis.jdbc.SQL;

/**
 *
 * <AUTHOR> zhang
 */
public class VideoTagProvider {

    /**
     * 查询所有礼品-分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param type        状态
     * @return 礼品列表
     */
    public String getPage(int pageIndex, int pageSize, String generalSearch, Short type) {

        return new SQL() {{

            SELECT("`t`.* ");
            FROM("`video_tag_define` `t` ");
            WHERE("1=1");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`t`.`title` LIKE '%${generalSearch}%'");
            }
            if (null != type && 0 <= type) {
                AND();
                WHERE("`t`.`type`=#{type}");
            }
            ORDER_BY("`t`.`id` DESC LIMIT #{pageIndex},#{pageSize}");

        }}.toString();

    }

    /**
     * 查询所有礼品带分页的记录数
     *
     * @param generalSearch 搜索
     * @param type        状态
     * @return 记录数
     */
    public String getPageCount(String generalSearch, Short type) {

        return new SQL() {{

            SELECT("COUNT(*)");
            FROM("`video_tag_define` `t` ");
            WHERE("1=1");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`t`.`title` LIKE '%${generalSearch}%'");
            }
            if (null != type && 0 < type) {
                AND();
                WHERE("`t`.`type`=#{type}");
            }

        }}.toString();

    }

    /**
     * 更新
     *
     * @param videoTagDefine 信息
     */
    public String update(VideoTagDefine videoTagDefine) {

        return new SQL() {{

            UPDATE("`video_tag_define`");

            SET("`title`=#{title}");
            if (!StringUtil.isNullOrEmpty(videoTagDefine.getImage())) {
                SET("`image`=#{image}");
            }
            SET("`top`=#{top}");
            SET("`type`=#{type}");

            WHERE("`id`=#{id}");

        }}.toString();

    }
}
