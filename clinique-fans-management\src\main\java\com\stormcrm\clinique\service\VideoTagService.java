package com.stormcrm.clinique.service;

import com.stormcrm.clinique.domain.VideoTag;
import com.stormcrm.clinique.domain.VideoTagDefine;
import com.stormcrm.clinique.vo.EditVideoWithTagVo;

import java.util.ArrayList;
import java.util.List;

public interface VideoTagService {

    /**
     * 添加标签
     *
     * @param videoTagDefine 视频标签
     */
    void insert(VideoTagDefine videoTagDefine);

    /**
     * 修改标签
     *
     * @param videoTagDefine 视频标签
     */
    void update(VideoTagDefine videoTagDefine);

    /**
     * 修改标签
     *
     * @param id 视频标签 id
     */
    VideoTagDefine get(Long id);

    /**
     * 删除标签
     *
     * @param id 视频标签 id
     */
    int delete(Long id);

    /**
     * 查询所有活动带分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索
     * @param type           状态
     * @return 活动列表分页
     */

    List<VideoTagDefine> getPage(int page, int perpage, String generalSearch, Short type);

    /**
     * 查询所有活动带分页的记录数
     *
     * @param generalSearch 搜索
     * @param type           状态
     * @return 记录数
     */
    int getPageCount(String generalSearch, Short type);

    /**
     * 获取全部标签
     */
    ArrayList<VideoTagDefine> getTagDefineList();

    /**
     * 根据视频id 获取全部标签
     */
    ArrayList<VideoTag> getTagListByVideoId(Long videoId);

    /**
     * 复选框中被选中的标签和全部标签的集合
     *
     * @param videoId 视频id
     * @return 复选框中被选中的标签和全部标签的集合
     */
    ArrayList<EditVideoWithTagVo> getTagListSelected(long videoId);


}
