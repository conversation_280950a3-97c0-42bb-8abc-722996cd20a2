package com.stormcrm.clinique.service;

import com.stormcrm.clinique.domain.CampaignTalentsTop;
import com.stormcrm.clinique.vo.Result;

import java.util.List;

/**
 * 新品体验官活动TOP
 *
 * <AUTHOR>
 */
public interface CampaignTalentsTopService {

    /**
     * 查询所有活动
     *
     * @return 活动列表
     */
    List<CampaignTalentsTop> getAll();

    /**
     * 查询信息
     *
     * @param id 自动编号
     * @return 信息
     */
    CampaignTalentsTop getById(long id);

    /**
     * 保存
     *
     * @param campaignTalentsTop 信息
     * @return 影响的行数
     */
    Result save(CampaignTalentsTop campaignTalentsTop);

    /**
     * 更新
     *
     * @param campaignTalentsTop 信息
     * @return 影响的行数
     */
    Result update(CampaignTalentsTop campaignTalentsTop);

}
