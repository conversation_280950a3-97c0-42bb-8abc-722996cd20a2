package cn.slashsoft.clinique.dao.mini;

import cn.slashsoft.clinique.domain.mini.Customer;
import cn.slashsoft.clinique.domain.mini.RankingGift;
import cn.slashsoft.clinique.domain.mini.RankingLog;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 排名
 * <AUTHOR>
 */
@Repository
@Mapper
public interface RankingDao {

    /**
     * 更新排名
     */
    @Update("UPDATE " +
            "   `customer` `c` " +
            "       INNER JOIN " +
            "   (" +
            "       SELECT" +
            "           @RANK:=@RANK+1 `ranking`," +
            "           `p`.`customer_id`," +
            "           `p`.`points_total`," +
            "           `p`.`id`" +
            "       FROM " +
            "           (" +
            "               SELECT " +
            "                   `customer_id`," +
            "                   SUM(`points`) `points_total`," +
            "                   MAX(`id`) `id`" +
            "               FROM " +
            "                   `point_transaction`" +
            "               WHERE" +
            "                   `status`=1 AND " +
            "                   `points`>0" +
            "               GROUP BY" +
            "                   `customer_id`" +
            "               ORDER BY" +
            "                   `points_total` DESC, " +
            "                   `id`" +
            "           ) `p`, (SELECT @RANK:=0) `i`" +
            "   ) `p`" +
            "       on `p`.`customer_id` = `c`.`id` " +
            "SET " +
            "   `c`.`ranking`=`p`.`ranking`," +
            "   `c`.`points_total`=`p`.`points_total`")
    void updateRanking();

    /**
     * 获取我的排名
     * 排名页
     *
     * @param customerId 顾客编号
     * @return 顾客信息
     */
    @Select("SELECT " +
            "   `ranking_always`, " +
            "   `ranking_exchange` " +
            "FROM " +
            "   `customer` " +
            "WHERE " +
            "   `id`=#{customerId} " +
            "LIMIT 1")
    Customer getCustomerRankingById(long customerId);

    /**
     * 更新已兑换状态
     * @param customerId 顾客编号
     * @return 影响的行数
     */
    @Update("UPDATE" +
            "   `customer` " +
            "SET " +
            "   `ranking_exchange`=1 " +
            "WHERE " +
            "   `id`=#{customerId} " +
            "   AND `ranking_exchange`=0")
    int updateCustomerRankingExchangeById(long customerId);

    /**
     * 获取排名前20的顾客
     * @return 顾客信息
     */
    @Select("SELECT " +
            "   `c`.`name`," +
            "   `w`.`avatar_url`," +
            "   `c`.`points_total`, " +
            "   `c`.`ranking_always` " +
            "FROM " +
            "   `customer` `c` " +
            "       inner join " +
            "   `wechat` `w` " +
            "       on `w`.`customer_id`=`c`.`id` " +
            "WHERE " +
            "   `c`.`ranking_always`>0 " +
            "ORDER BY " +
            "   `c`.`ranking_always`, `c`.`id` " +
            "LIMIT 20")
    List<Customer> getCustomerRankingTop();

    /**
     * 获取有效的商城礼品列表，按更新时间倒序排列
     * 积分兑礼
     *
     * @param customerId 顾客编号
     * @return 商城礼品列表
     */
    @Select("SELECT " +
            "   `g`.`id`, " +
            "   `g`.`name`, " +
            "   `g`.`image_url`, " +
            "   `r`.`ranking_min`, " +
            "   `r`.`ranking_max`, " +
            "   (" +
            "       SELECT " +
            "           COUNT(*) " +
            "       FROM " +
            "           `store_exchange` " +
            "       WHERE " +
            "           `store_gift_id`=`g`.`id` " +
            "           AND `customer_id`=#{customerId} " +
            "           AND `status`=1 " +
            "           AND `recovery`=0 " +
            "   ) `exchange_total` " +
            "FROM " +
            "   `store_gift` `g` " +
            "       INNER JOIN " +
            "   `store_gift_ranking` `r` " +
            "       on `r`.`store_gift_id`=`g`.`id` " +
            "WHERE " +
            "   `status`=1 " +
            "   AND `recovery`=0 " +
            "   AND `store_gift_type_id`=2 " +
            "ORDER BY " +
            "   `create_time` DESC"
    )
    List<RankingGift> getRankingGiftList(long customerId);

    /**
     * 跟据礼品编号获取礼品资料，和已兑礼数量
     *
     * @param id         礼品编号
     * @param customerId 顾客编号
     * @return 礼品资料
     */
    @Select("SELECT " +
            "   `g`.`id`, " +
            "   `g`.`name`, " +
            "   `g`.`image_url`, " +
            "   `r`.`ranking_min`, " +
            "   `r`.`ranking_max`, " +
            "   (" +
            "       SELECT " +
            "           COUNT(*) " +
            "       FROM " +
            "           `store_exchange` " +
            "       WHERE " +
            "           `store_gift_id`=#{id} " +
            "           AND `customer_id`=#{customerId} " +
            "           AND `status`=1 " +
            "           AND `recovery`=0 " +
            "   ) `exchange_total` " +
            "FROM " +
            "   `store_gift` `g` " +
            "       inner join " +
            "   `store_gift_ranking` `r` " +
            "       on `r`.`store_gift_id`=`g`.`id` " +
            "WHERE " +
            "   `g`.`id`=#{id} " +
            "   AND `g`.`status`=1 " +
            "   AND `g`.`recovery`=0 " +
            "   AND `g`.`store_gift_type_id`=2 ")
    RankingGift getRankingGiftById(@Param("id") long id, @Param("customerId") long customerId);

    /**
     * 读取兑换的礼品信息
     *
     * @param id         礼品编号
     * @param customerId 顾客编号
     * @return 礼品信息
     */
    @Select("SELECT " +
            "   `g`.`id`, " +
            "   `g`.`name`, " +
            "   `r`.`ranking_min`, " +
            "   `r`.`ranking_max`, " +
            "   (" +
            "       SELECT " +
            "           COUNT(*) " +
            "       FROM " +
            "           `store_exchange` " +
            "       WHERE " +
            "           `store_gift_id`=#{id} " +
            "           AND `customer_id`=#{customerId} " +
            "           AND `status`=1 " +
            "           AND `recovery`=0 " +
            "   ) `exchange_total` " +
            "FROM " +
            "   `store_gift` `g` " +
            "       inner join " +
            "   `store_gift_ranking` `r` " +
            "       on `r`.`store_gift_id`=`g`.`id` " +
            "WHERE " +
            "   `g`.`id`=#{id} " +
            "   AND `g`.`status`=1 " +
            "   AND `g`.`recovery`=0 " +
            "   AND `g`.`store_gift_type_id`=2 ")
    RankingGift getRankingGiftForExchangeById(@Param("id") long id, @Param("customerId") long customerId);

    /**
     * 写入查看排行榜日志
     * @param rankingLog 查看排行榜日志
     */
    @Insert("INSERT INTO `ranking_log`(" +
            "   `wechat_mini_openid`, " +
            "   `ref`" +
            ") " +
            "VALUES (" +
            "   #{openid}," +
            "   #{ref}" +
            ")"
    )
    void insertRankingLog(RankingLog rankingLog);

}
