package com.stormcrm.clinique.util;

import org.springframework.web.multipart.MultipartFile;

import java.io.File;

/**
 * 图片工具
 *
 * <AUTHOR>
 */
public class ImageUtil {

    /**
     * 保存图片
     *
     * @param path      路径
     * @param imageFile 图片
     * @return 保存后的图片路径和名称
     */
    public static String save(String path, MultipartFile imageFile) {

        // 获取扩展名
        String imageFileName = imageFile.getOriginalFilename();
        String imageFileSuffix = imageFileName.substring(imageFileName.lastIndexOf("."));

        // 新文件的文件名
        String fileName = RandomUtil.getFileName() + imageFileSuffix;

        // 新文件
        File file = new File(System.getProperty("user.dir") + "/static" + path + fileName);

        // 判断文件父目录是否存在
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdir();
        }

        //保存文件
        try {
            imageFile.transferTo(file);
            return path + fileName;
        } catch (Exception e) {
            return null;
        }

    }

}
