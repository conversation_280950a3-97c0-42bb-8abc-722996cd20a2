package com.stormcrm.clinique.controller.tag;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.stormcrm.clinique.domain.mini.CustomerLabel;
import com.stormcrm.clinique.domain.tag.Label;
import com.stormcrm.clinique.domain.tag.LabelResult;
import com.stormcrm.clinique.enums.ResultEnum;
import com.stormcrm.clinique.service.tag.LabelService;
import com.stormcrm.clinique.service.tag.RuleService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.util.StringUtil;
import jxl.Workbook;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;

/**
 * 标签包管理
 *
 * <AUTHOR>
 */


@RestController
@RequestMapping("/tag/label")
public class LabelController {

	@Resource
	RuleService ruleService;
	@Resource 
	LabelService labelService;

	@RequestMapping("/get-all")
	public String getAll() {
		HashMap<String,Object> data = new HashMap<>();
		data.put("labels", labelService.getAll());
		data.put("target", labelService.getTargetAll());

		return ResultUtil.customer(ResultEnum.SUCCESS, "ok", data);
	}
	

	@RequestMapping("/get-filter-all-by-label/{id}")
	public String getFilterAll(
			@PathVariable("id") int labelId) {
		HashMap<String,Object> data = new HashMap<>();
		data.put("label", null);
		data.put("filterList", null);
		if(labelId != 0) {
			Label label = labelService.getById(labelId);
			data.put("label", label);
			data.put("filterList", ruleService.getFilterListByLabel(label));
		}
		data.put("ruleList",ruleService.getRuleAll());
		data.put("targetList", labelService.getTargetBiVoAll());
		data.put("templateList", labelService.getFilterTemplateAllForBI());

		return ResultUtil.customer(ResultEnum.SUCCESS, "ok", data);
	}

	@PostMapping("/save-label")
	public String saveLabel(
			@RequestBody Label label) {
		if(label.getId() == 0) {
			labelService.addLabel(label);
		}else {
			labelService.updateLabel(label);
		}
		
		return ResultUtil.customer(ResultEnum.SUCCESS, "ok", label.getId());
	}

	@PostMapping("/get-label-count")
	public String getLabelCount(
			@RequestBody Label label){

		return ResultUtil.customer(ResultEnum.SUCCESS, "ok", labelService.getLabelCount(label));
	}

	@PostMapping("/get-label-list")
	public String getLabelList(
			@RequestBody Label label){

		return ResultUtil.customer(ResultEnum.SUCCESS, "ok", labelService.getLabelList(label));
	}


	@GetMapping("/down-label-list/{id}")

	public String downLabelList(
			@PathVariable("id") int labelId,
			 HttpServletResponse response ){
		try {
			Label label = labelService.getById(labelId);
			List<CustomerLabel> all = labelService.getLabelList(label);
			response.setContentType("application/octet-stream");
			//默认Excel名称
			String fileName = URLEncoder.encode( label.getName(), "UTF-8") +  "-" + new Date().getTime() + ".xls";
			response.setHeader("Content-Disposition", "attachment;fileName="+ fileName);

			ByteArrayOutputStream os = new ByteArrayOutputStream();
			WritableWorkbook workbook = Workbook.createWorkbook(os);

			WritableSheet ws = workbook.createSheet(label.getName() , 0);

			ws.setColumnView(0,45);

			int rowIndex = 0;
			jxl.write.Label wlabel = new jxl.write.Label(0, rowIndex, "unionid");
			ws.addCell(wlabel);
			for(CustomerLabel l : all){
				if(StringUtil.isNullOrEmpty(l.getUnionid())){
					continue;
				}
				jxl.write.Label rowlabel = new jxl.write.Label(0, ++rowIndex, l.getUnionid());
				ws.addCell(rowlabel);
			}

			workbook.write();
			workbook.close();

			response.flushBuffer();

			response.getOutputStream().write(os.toByteArray());
		} catch (IOException | WriteException e) {
			e.printStackTrace();
		}

		return "";
	}

	@RequestMapping("/get-label-count-by-id/{id}")
	public String getLabelCountById(
			@PathVariable("id") int labelId) {
		int data = 0 ;
		if(labelId != 0) {
			Label label = labelService.getById(labelId);
			if(null != label){
				data = labelService.getLabelCount(label);
				LabelResult result = new LabelResult();
				result.setLabelId(labelId);
				result.setResult(""+data);
				labelService.addLabelResult(result);
			}
		}

		return ResultUtil.customer(ResultEnum.SUCCESS, "ok", data);
	}

	@RequestMapping("/del-label/{id}")
	public String delLabel(
			@PathVariable("id") int labelId) {

		if(labelId != 0) {
			labelService.delLabel(labelId);

		}

		return ResultUtil.customer(ResultEnum.SUCCESS, "ok");
	}
}
