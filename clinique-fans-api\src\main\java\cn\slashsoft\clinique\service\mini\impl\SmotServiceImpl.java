package cn.slashsoft.clinique.service.mini.impl;

import java.util.Date;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import cn.slashsoft.clinique.dao.campaign.CampaignC520Dao;
import cn.slashsoft.clinique.dao.mini.SmotDao;
import cn.slashsoft.clinique.domain.campaign.CampaignC520;
import cn.slashsoft.clinique.domain.mini.SmotAuthorize;
import cn.slashsoft.clinique.service.mini.SmotService;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.util.DateUtil;

/**
 * 一次性订阅消息服务
 *
 * <AUTHOR>
 */
@Service
public class SmotServiceImpl implements SmotService {

    @Value("${wechat.subscribe-template.gift-receive-success}")
    private String templateGiftReceiveSuccess;

    @Value("${wechat.subscribe-template.receive-success}")
    private String templateReceiveSuccess;

    private final CampaignC520Dao campaignC520Dao;
    private final OutsideService outsideService;
    private final SmotDao smotDao;

    public SmotServiceImpl(CampaignC520Dao campaignC520Dao, OutsideService outsideService, SmotDao smotDao) {
        this.campaignC520Dao = campaignC520Dao;
        this.outsideService = outsideService;
        this.smotDao = smotDao;
    }

    /**
     * 51活动发送礼品领取成功一次性消息
     *
     * @param openid     小程序唯一编号
     * @param customerId 顾客编号
     */
    @Override
    public void receiveCampaignC520(String openid, long customerId) {

        CampaignC520 campaignC520 = campaignC520Dao.getCampaignC520(customerId);
        if (null != campaignC520) {

            String data = "{" +
                    "\"thing1\":{\"value\":\"C粉圈新客专享好礼\"}," +
                    "\"name2\":{\"value\":\"" + campaignC520.getNickName() + "\"}," +
                    "\"date3\":{\"value\":\"" + DateUtil.parseDayString(campaignC520.getReceiveTime()) + "\"}" +
                    "}";

            outsideService.subscribeMessageSend((short) 1, openid, templateGiftReceiveSuccess, "c520/index/index?source=mot0429", data);
        }
    }

    /**
     * 520活动发送礼品领取成功一次性消息
     *
     * @param openid   小程序唯一编号
     * @param nickName 顾客昵称
     */
    @Override
    public void receiveCampaignG520(String openid, String nickName) {

        String data = "{" +
                "\"thing1\":{\"value\":\"520 花漾福利凭证\"}," +
                "\"date2\":{\"value\":\"" + DateUtil.parseDayString(new Date()) + "\"}," +
                "\"name3\":{\"value\":\"" + nickName + "\"}," +
                "\"thing5\":{\"value\":\"福利凭证请于活动期间至倩碧柜台购买时使用\"}" +
                "}";

        outsideService.subscribeMessageSend((short) 2, openid, templateReceiveSuccess, "g520/index/index?source=fuwutongzhi", data);

    }

    /**
     * 写入一次性订阅消息授权选择记录
     *
     * @param smotAuthorize 授权选择记录
     */
    @Override
    public void insertSmotAuthorize(SmotAuthorize smotAuthorize) {
        smotDao.insertSmotAuthorize(smotAuthorize);
    }

    /**
     * 获取是否授权
     *
     * @param type   类型
     * @param openid 小程序编号
     * @param choice 是否选择
     * @return 数量
     */
    @Override
    public int getSmotAuthorize(short type, String openid, boolean choice) {
        return smotDao.getSmotAuthorizeChoice(type, openid, choice);
    }

    /**
     * 获取是否授权
     *
     * @param type   类型
     * @param openid 小程序编号
     * @return 数量
     */
    @Override
    public int getSmotAuthorize(short type, String openid) {
        return smotDao.getSmotAuthorize(type, openid);
    }
}
