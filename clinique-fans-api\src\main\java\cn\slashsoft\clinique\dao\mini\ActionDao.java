package cn.slashsoft.clinique.dao.mini;

import cn.slashsoft.clinique.domain.mini.Action;
import cn.slashsoft.clinique.domain.mini.PageLog;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * 动作
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface ActionDao {

    /**
     * 写入动作
     *
     * @param action 动作
     */
    @Insert("insert into `action` ( " +
            "   `wechat_mini_openid`, " +
            "   `type` " +
            ") " +
            "VALUES ( " +
            "   #{wechatOfficialOpenid}, " +
            "   #{type} " +
            ")")
    void insertAction(Action action);
    

	@Select("SELECT count(*) FROM action WHERE type = #{actionId} and `wechat_mini_openid`=#{miniOpenid} ")
	int getActionCount(int actionId, String miniOpenid);
    
    /**
	 * 获取三日打卡任务 
	 *
	 * @param miniopenid
	 */
    @Select("SELECT DISTINCT type , a.* FROM `action` a WHERE `wechat_mini_openid`=#{miniOpenid} AND type in (53,54,55,56,57,58,59,60) ")
	List<Action> getDay3TaskActions(String miniOpenid);
    
    /**
  	 * 获取特权banner点击记录
  	 *
  	 * @param miniopenid
  	 */
      @Select("SELECT DISTINCT type FROM `action` WHERE `wechat_mini_openid`=#{miniOpenid} AND type in (select action_id from special_banner) ")
  	List<Action> getSpecialActions(String miniOpenid);
  /**
	 * 获取三日打卡任务记录计数
	 *
	 * @param miniopenid
	 */
    @Select("SELECT IFNULL(count(DISTINCT type),0) FROM `action` "
    		+ " WHERE `wechat_mini_openid`=#{miniOpenid} AND type in (53,54,55,56,57,58,59,60) ")
	int getDay3TaskActionCount(String miniOpenid);
    

    /**
	 * 获取三日打卡任务 第一个任务已过去天数
	 *
	 * @param miniopenid
	 */
    @Select("SELECT IFNULL(to_days(now())-to_days(create_time),0) FROM `action` "
    		+ " WHERE `wechat_mini_openid`=#{miniOpenid} "
    		+ " AND type in (53,54,55,56,57,58,59,60) "
    		+ " ORDER BY create_time  "
    		+ " limit 1 ")
    int getDay3TaskFistActionToDays(String miniOpenid);
}
