package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.CampaignTalentsTopDetail;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 新品体验官活动TOP
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignTalentsTopDetailDao {

    /**
     * 查询所有活动
     * @param id TOP5编号
     * @return 活动列表
     */
    @Select("SELECT " +
            "   `d`.`id`," +
            "   `t`.`customer_id`," +
            "   `w`.`nick_name`, " +
            "   `w`.`avatar_url`," +
            "   `d`.`campaign_ebci_talents_id`, " +
            "   `t`.`upload_time`, " +
            "   `t`.`examine_time`, " +
            "   `d`.`sort`, " +
            "   `t`.`status` " +
            "FROM " +
            "   `campaign_ebci_talents_top_detail` `d` " +
            "       INNER JOIN " +
            "   `campaign_ebci_talents` `t` " +
            "       ON `d`.`campaign_ebci_talents_id`=`t`.`id` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `t`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "   `d`.`campaign_ebci_talents_top_id`=#{id} " +
            "   AND `d`.`status`=1 " +
            "ORDER BY " +
            "   `d`.`sort`,`d`.`id`")
    List<CampaignTalentsTopDetail> getAll(long id);

    /**
     * 查询
     *
     * @param detailId 自动编号
     * @return 信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `campaign_ebci_talents_id`," +
            "   `sort` " +
            "FROM " +
            "   `campaign_ebci_talents_top_detail` " +
            "WHERE " +
            "   `id`=#{id} " +
            "LIMIT 1")
    CampaignTalentsTopDetail getById(long detailId);

    /**
     * 保存
     *
     * @param campaignTalentsTopDetail 信息
     */
    @Insert("INSERT INTO `campaign_ebci_talents_top_detail`(" +
            "   `campaign_ebci_talents_top_id`," +
            "   `campaign_ebci_talents_id`, " +
            "   `sort` " +
            ") " +
            "VALUES (" +
            "   #{campaignEbciTalentsTopId}, " +
            "   #{campaignEbciTalentsId}, " +
            "   #{sort} " +
            ")")
    void save(CampaignTalentsTopDetail campaignTalentsTopDetail);

    /**
     * 更新
     *
     * @param campaignTalentsTopDetail 信息
     */
    @Update("UPDATE " +
            "   `campaign_ebci_talents_top_detail` " +
            "SET " +
            "   `campaign_ebci_talents_id`=#{campaignEbciTalentsId}, " +
            "   `sort`=#{sort} " +
            "WHERE " +
            "   `id`=#{id}")
    void update(CampaignTalentsTopDetail campaignTalentsTopDetail);

    /**
     * 删除
     * @param id 编号
     * @return 影响的行
     */
    @Delete("UPDATE " +
            "   `campaign_ebci_talents_top_detail` " +
            "SET " +
            "   `status`=0 " +
            "WHERE " +
            "   `id`=#{id}")
    int delete(long id);

}
