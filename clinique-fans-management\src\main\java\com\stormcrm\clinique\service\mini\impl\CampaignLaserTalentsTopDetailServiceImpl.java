package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.dao.CampaignLaserTalentsTopDetailDao;
import com.stormcrm.clinique.domain.CampaignLaserTalentsTopDetail;
import com.stormcrm.clinique.service.CampaignLaserTalentsTopDetailService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.vo.Result;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 新品体验官活动
 *
 * <AUTHOR>
 */
@Service
public class CampaignLaserTalentsTopDetailServiceImpl implements CampaignLaserTalentsTopDetailService {

    private final CampaignLaserTalentsTopDetailDao campaignLaserTalentsTopDetailDao;

    public CampaignLaserTalentsTopDetailServiceImpl(CampaignLaserTalentsTopDetailDao campaignLaserTalentsTopDetailDao) {
        this.campaignLaserTalentsTopDetailDao = campaignLaserTalentsTopDetailDao;
    }

    /**
     * 查询所有活动
     *
     * @param id TOP5编号
     * @return 活动列表
     */
    @Override
    public List<CampaignLaserTalentsTopDetail> getAll(long id) {
        return campaignLaserTalentsTopDetailDao.getAll(id);
    }

    /**
     * 查询信息
     *
     * @param detailId 自动编号
     * @return 信息
     */
    @Override
    public CampaignLaserTalentsTopDetail getById(long detailId) {
        return campaignLaserTalentsTopDetailDao.getById(detailId);
    }

    /**
     * 保存
     *
     * @param campaignLaserTalentsTopDetail 信息
     * @return 影响的行数
     */
    @Override
    public Result save(CampaignLaserTalentsTopDetail campaignLaserTalentsTopDetail) {
        campaignLaserTalentsTopDetailDao.save(campaignLaserTalentsTopDetail);
        return ResultUtil.success("增加成功!");
    }

    /**
     * 更新
     *
     * @param campaignLaserTalentsTopDetail 信息
     * @return 影响的行数
     */
    @Override
    public Result update(CampaignLaserTalentsTopDetail campaignLaserTalentsTopDetail) {
        campaignLaserTalentsTopDetailDao.update(campaignLaserTalentsTopDetail);
        return ResultUtil.success("增加成功!");
    }

    /**
     * 更新
     *
     * @param id 编号
     * @return 影响的行数
     */
    @Override
    public int delete(long id) {
        return campaignLaserTalentsTopDetailDao.delete(id);
    }
}
