package cn.slashsoft.clinique.dao.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignViewLog;
import cn.slashsoft.clinique.domain.campaign.CampaignWechatWorkBinding;
import cn.slashsoft.clinique.domain.campaign.CampaignWechatWorkBindingCounter;
import cn.slashsoft.clinique.domain.campaign.CampaignWechatWorkBindingGift;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 企业微信绑定活动
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignWechatWorkBindingDao {

    /**
     * 获取门店
     *
     * @return 门店
     */
    @Select("select " +
            "   `city`," +
            "   GROUP_CONCAT(CONCAT('''',`name`,'''')) `counter` " +
            "from " +
            "   `customer_service_counter` " +
            "where " +
            "   `status`=1 " +
            "group by `city` " +
            "order by CONVERT(`city` USING GBK)")
    List<CampaignWechatWorkBindingCounter> getCampaignWechatWorkBindingCounter();

    /**
     * 中奖名单
     *
     * @return 中奖名单
     */
    @Select("select " +
            "   `b`.`nick_name`," +
            "   `b`.`avatar_url`," +
            "   `g`.`name` `gift_name` " +
            "from " +
            "   `campaign_wechat_work_binding` `b` " +
            "       inner join " +
            "   `campaign_wechat_work_binding_gift` `g` " +
            "       on `b`.`gift_id`=`g`.`id` " +
            "order by " +
            "   `b`.`draw_time` desc " +
            "limit 100")
    List<CampaignWechatWorkBinding> getCampaignWechatWorkBindingList();

    /**
     * 获取活动的全面信息
     *
     * @param officialOpenid 公众号唯一编号
     * @return 活动信息
     */
    @Select("select " +
            "   `b`.`id`," +
            "   `b`.`nick_name`," +
            "   `b`.`avatar_url`," +
            "   `b`.`is_draw`," +
            "   `b`.`draw_time`," +
            "   `g`.`gift_id`," +
            "   `g`.`type` `gift_type`," +
            "   `g`.`name` `gift_name`," +
            "   `b`.`is_form`," +
            "   `b`.`form_time`," +
            "   `p`.`counter` `pickup_counter`," +
            "   `p`.`is_receive` `pickup_is_receive`," +
            "   `p`.`receive_time` `pickup_receive_time`," +
            "   `e`.`is_receive` `express_is_receive` " +
            "from " +
            "   `campaign_wechat_work_binding` `b` " +
            "       left join " +
            "   `campaign_wechat_work_binding_gift` `g` " +
            "       on `b`.`gift_id`=`g`.`id` " +
            "       left join " +
            "   `campaign_wechat_work_binding_pickup` `p` " +
            "       on `b`.`id`=`p`.`campaign_wechat_work_binding_id` " +
            "       left join " +
            "   `campaign_wechat_work_binding_express` `e` " +
            "       on `b`.`id`=`e`.`campaign_wechat_work_binding_id` " +
            "where " +
            "   `b`.`official_openid`=#{officialOpenid}")
    CampaignWechatWorkBinding getCampaignWechatWorkBinding(String officialOpenid);

    /**
     * 写入活动信息
     *
     * @param campaignWechatWorkBinding 活动信息
     */
    @Insert("INSERT INTO `campaign_wechat_work_binding`(" +
            "   `official_openid`, " +
            "   `member_type`, " +
            "   `nick_name`, " +
            "   `avatar_url`" +
            ") " +
            "VALUES (" +
            "   #{officialOpenid}, " +
            "   #{memberType}, " +
            "   #{nickName}, " +
            "   #{avatarUrl}" +
            ")")
    void insertCampaignWechatWorkBinding(CampaignWechatWorkBinding campaignWechatWorkBinding);

    /**
     * 读取活动简单信息
     *
     * @param officialOpenid 公众号唯一编号
     * @return 活动信息
     */
    @Select("select " +
            "   `b`.`id`," +
            "   `b`.`member_type`," +
            "   `b`.`nick_name`," +
            "   `b`.`avatar_url`," +
            "   `b`.`is_draw`," +
            "   `b`.`draw_time`," +
            "   `b`.`is_form`," +
            "   `b`.`form_time` " +
            "from " +
            "   `campaign_wechat_work_binding` `b` " +
            "where " +
            "   `b`.`official_openid`=#{officialOpenid}")
    CampaignWechatWorkBinding getCampaignWechatWorkBindingOnly(String officialOpenid);

    /**
     * 更新中奖信息
     *
     * @param officialOpenid 公众号唯一编号
     * @param giftId         奖品编号
     * @return 影响的行
     */
    @Update("update " +
            "   `campaign_wechat_work_binding` " +
            "set " +
            "   `is_draw`=1," +
            "   `draw_time`=NOW()," +
            "   `gift_id`=#{giftId} " +
            "where " +
            "   `official_openid`=#{officialOpenid} " +
            "   and `is_draw`=0")
    int updateCampaignWechatWorkBindingForDraw(@Param("officialOpenid") String officialOpenid, @Param("giftId") short giftId);

    /**
     * 更新表单填写状态
     *
     * @param officialOpenid 公众号唯一编号
     * @return 影响的行
     */
    @Update("update " +
            "   `campaign_wechat_work_binding` " +
            "set " +
            "   `is_form`=1," +
            "   `form_time`=NOW() " +
            "where " +
            "   `official_openid`=#{officialOpenid} " +
            "   and `is_form`=0")
    int updateCampaignWechatWorkBindingForCounter(String officialOpenid);

    /**
     * 读取所有礼品
     *
     * @return 礼品列表
     */
    @Select("select " +
            "   `id`," +
            "   `gift_id`," +
            "   `type`," +
            "   `name`," +
            "   `ratio` " +
            "from " +
            "   `campaign_wechat_work_binding_gift` " +
            "where " +
            "   `member_type`=#{memberType} " +
            "   and `stocks`>0")
    List<CampaignWechatWorkBindingGift> getCampaignWechatWorkBindingGift(short memberType);

    /**
     * 扣除库存
     *
     * @param id 礼品编号
     * @return 影响的行
     */
    @Update("update " +
            "   `campaign_wechat_work_binding_gift` " +
            "set " +
            "   `stocks`=`stocks`-1, " +
            "   `exchange_total`=`exchange_total`+1 " +
            "where " +
            "   `id`=#{id} " +
            "   and `stocks`>0")
    int updateCampaignWechatWorkBindingGiftForDraw(short id);

    /**
     * 写入领取专柜信息
     *
     * @param officialOpenid 公众号维一编号
     * @param name           姓名
     * @param city           城市
     * @param counter        门店
     */
    @Insert("insert into `campaign_wechat_work_binding_pickup`(" +
            "   `campaign_wechat_work_binding_id`, " +
            "   `name`, " +
            "   `city`, " +
            "   `counter`" +
            ") " +
            "select " +
            "    `id`," +
            "    #{name}," +
            "    #{city}," +
            "    #{counter} " +
            "from " +
            "    `campaign_wechat_work_binding` " +
            "where " +
            "    `official_openid`=#{officialOpenid}")
    void insertCampaignWechatWorkBindingPickup(
            @Param("officialOpenid") String officialOpenid,
            @Param("name") String name,
            @Param("city") String city,
            @Param("counter") String counter
    );

    /**
     * 修改核销时间
     *
     * @param officialOpenid 公众号维一编号
     */
    @Update("update " +
            "   `campaign_wechat_work_binding_pickup` " +
            "set " +
            "   is_receive=1," +
            "   receive_time=NOW() " +
            "where " +
            "   `campaign_wechat_work_binding_id`=(" +
            "       select " +
            "           `id` " +
            "       from " +
            "           `campaign_wechat_work_binding` " +
            "       where " +
            "           `official_openid`=#{officialOpenid}" +
            "   ) " +
            "   and is_receive=0")
    void updateCampaignWechatWorkBindingPickup(String officialOpenid);

    /**
     * 写入快递信息
     *
     * @param officialOpenid 公众号唯一编号
     * @param name           姓名
     * @param phoneNumber    手机号码
     * @param province       省份
     * @param city           城市
     * @param district       区域
     * @param address        具体地址
     */
    @Insert("insert into `campaign_wechat_work_binding_express`(" +
            "   `campaign_wechat_work_binding_id`, " +
            "   `name`, " +
            "   `phone_number`, " +
            "   `province`, " +
            "   `city`, " +
            "   `district`, " +
            "   `address`" +
            ") " +
            "select " +
            "    `id`," +
            "   #{name}, " +
            "   #{phoneNumber}, " +
            "   #{province}, " +
            "   #{city}, " +
            "   #{district}, " +
            "   #{address} " +
            "from " +
            "    `campaign_wechat_work_binding` " +
            "where " +
            "    `official_openid`=#{officialOpenid}")
    void insertCampaignWechatWorkBindingExpress(
            @Param("officialOpenid") String officialOpenid,
            @Param("name") String name,
            @Param("phoneNumber") String phoneNumber,
            @Param("province") String province,
            @Param("city") String city,
            @Param("district") String district,
            @Param("address") String address
    );

    /**
     * 写入临时积分
     *
     * @param unionid 开放平台唯一编号
     */
    @Insert("insert into `campaign_wechat_work_binding_point`(" +
            "   `unionid` " +
            ") " +
            "VALUES (" +
            "   #{unionid} " +
            ")")
    void insertCampaignWechatWorkBindingPoint(
            @Param("unionid") String unionid
    );

    /**
     * 注册发放积分
     *
     * @param unionid     开放平台唯一编号
     * @param customerId  顾客编号
     * @param pointTypeId 积分类型
     * @param points      积分
     * @param startTime   开始时间
     * @param expiredTime 结束时间
     * @param foreignId   来源类型
     */
    @Insert("INSERT INTO `point_transaction`(" +
            "   `customer_id`, " +
            "   `point_type_id`, " +
            "   `points`, " +
            "   `remaining_points`, " +
            "   `start_time`, " +
            "   `expired_time`, " +
            "   `foreign_id`, " +
            "   `foreign_master_id`, " +
            "   `foreign_detail_id`" +
            ") " +
            "select " +
            "   #{customerId}," +
            "   #{pointTypeId}," +
            "   #{points}," +
            "   #{points}," +
            "   #{startTime}," +
            "   #{expiredTime}," +
            "   #{foreignId}," +
            "   0," +
            "   0 " +
            "from " +
            "   `campaign_wechat_work_binding_point` " +
            "where " +
            "   `unionid`=#{unionid} ")
    void insertPoint(
            @Param("unionid") String unionid,
            @Param("customerId") long customerId,
            @Param("pointTypeId") short pointTypeId,
            @Param("points") int points,
            @Param("startTime") Date startTime,
            @Param("expiredTime") Date expiredTime,
            @Param("foreignId") short foreignId
    );

    /**
     * 写入访问日志
     *
     * @param campaignViewLog 日志
     */
    @Insert("INSERT INTO `campaign_wechat_work_binding_view_log`(" +
            "   `official_openid`," +
            "   `source`," +
            "   `page`" +
            ") " +
            "VALUES (" +
            "   #{officialOpenid}," +
            "   #{source}," +
            "   #{page}" +
            ")")
    void insertViewLog(CampaignViewLog campaignViewLog);

}
