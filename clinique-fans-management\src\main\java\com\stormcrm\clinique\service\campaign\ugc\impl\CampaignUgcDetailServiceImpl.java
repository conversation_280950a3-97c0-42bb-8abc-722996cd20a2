package com.stormcrm.clinique.service.campaign.ugc.impl;

import com.stormcrm.clinique.dao.CampaignMsTalentsDao;
import com.stormcrm.clinique.dao.PointDao;
import com.stormcrm.clinique.dao.campaign.ugc.CampaignUgcDetailDao;
import com.stormcrm.clinique.domain.CampaignMsTalents;
import com.stormcrm.clinique.domain.CampaignMsTalentsImage;
import com.stormcrm.clinique.domain.PointTransaction;
import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgcDetail;
import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgcDetailPoint;
import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgcImage;
import com.stormcrm.clinique.enums.PointForeignEnum;
import com.stormcrm.clinique.enums.PointTypeEnum;
import com.stormcrm.clinique.service.CampaignMsTalentsService;
import com.stormcrm.clinique.service.SmotService;
import com.stormcrm.clinique.service.campaign.ugc.CampaignUgcDetailService;
import com.stormcrm.clinique.util.DateUtil;
import com.stormcrm.clinique.util.LongUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Service
public class CampaignUgcDetailServiceImpl implements CampaignUgcDetailService {

    @Value("${wechat.subscribe-template.examine-result}")
    private String templateExamineResult;

    private final CampaignUgcDetailDao campaignUgcDetailDao;
    private final PointDao pointDao;
    private final SmotService smotService;

    public CampaignUgcDetailServiceImpl(CampaignUgcDetailDao campaignUgcDetailDao, PointDao pointDao, SmotService smotService) {
        this.campaignUgcDetailDao = campaignUgcDetailDao;
        this.pointDao = pointDao;
        this.smotService = smotService;
    }

    /**
     * 查询所有活动带分页
     *
     * @param campaignId 活动编号
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索
     * @param status        状态
     * @return 活动列表分页
     */
    @Override
    public List<CampaignUgcDetail> getPage(long campaignId, int page, int perpage, String generalSearch, Short status) {
        return campaignUgcDetailDao.getPage(campaignId, (page - 1) * perpage, perpage, generalSearch, status);
    }

    /**
     * 查询所有活动带分页的记录数
     *
     * @param campaignId 活动编号
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    @Override
    public int getPageCount(long campaignId, String generalSearch, Short status) {
        return campaignUgcDetailDao.getPageCount(campaignId, generalSearch, status);
    }

    /**
     * 获取美白达人官信息
     *
     * @param id 编号
     * @return 美白达人官信息
     */
    @Override
    public CampaignUgcDetail getById(long id) {
        return campaignUgcDetailDao.getById(id);
    }

    /**
     * 获取图片
     *
     * @param id 顾客活动信息编号
     * @return 图片地址
     */
    @Override
    public List<CampaignUgcImage> getImageById(long id) {
        return campaignUgcDetailDao.getImageById(id);
    }

    /**
     * 审核通过
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Override
    public int accept(long id) {

        // 读取用户及活动信息
        CampaignUgcDetail campaignUgcDetail = campaignUgcDetailDao.getById(id);
        if (null == campaignUgcDetail) {
            return 0;
        }

        // 只在审核中和审核未通过才可以审核通过
        if (0 == campaignUgcDetailDao.accept(id)) {
            return 0;
        }

        // 发放积分
        Date now = new Date();
        PointTransaction pointTransaction = new PointTransaction();
        pointTransaction.setCustomerId(campaignUgcDetail.getCustomerId());
        pointTransaction.setPointTypeId(PointTypeEnum.UGC.getId());
        pointTransaction.setPoints(PointTypeEnum.UGC.getPoints());
        pointTransaction.setRemainingPoints(PointTypeEnum.UGC.getPoints());
        pointTransaction.setStartTime(now);
        pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
        pointTransaction.setForeignId(PointForeignEnum.UGC.getId());
        pointTransaction.setForeignMasterId(id);
        pointTransaction.setForeignDetailId(0L);
        pointDao.insertPointTransaction(pointTransaction);

        String data = "{" +
                "\"thing2\":{\"value\":\"审核通过\"}," +
                "\"thing8\":{\"value\":\"C币及抽奖机会已到帐，现在查看你的晒图！\"}" +
                "}";

        // 发送订阅消息
        smotService.send((short) 3, campaignUgcDetail.getWechatMiniOpenid(), templateExamineResult, "/ugc/index/index?id=" + campaignUgcDetail.getCampaignId() + "&source=SuccessUploadMot", data);

        return 1;
    }

    /**
     * 批量同意
     *
     * @param ids 自动编号
     * @return 影响的行数
     */
    @Override
    public int acceptBatch(String ids) {

        String[] idArray = ids.split(",");

        List<Long> idList = new ArrayList<>();
        for (String idString : idArray) {
            long id = LongUtil.parseInt(idString);
            if (0 < id) {
                idList.add(id);
            }
        }

        if (0 == idList.size()) {
            return 0;
        }

        List<CampaignUgcDetail> campaignUgcDetailList = campaignUgcDetailDao.getByIds(idList);
        if (null == campaignUgcDetailList || 0 == campaignUgcDetailList.size()) {
            return 0;
        }

        for (CampaignUgcDetail campaignUgcDetail : campaignUgcDetailList) {

            // 只在审核中和审核未通过才可以审核通过
            if (0 == campaignUgcDetailDao.accept(campaignUgcDetail.getId())) {
                continue;
            }

            // 发放积分
            Date now = new Date();
            PointTransaction pointTransaction = new PointTransaction();
            pointTransaction.setCustomerId(campaignUgcDetail.getCustomerId());
            pointTransaction.setPointTypeId(PointTypeEnum.UGC.getId());
            pointTransaction.setPoints(PointTypeEnum.UGC.getPoints());
            pointTransaction.setRemainingPoints(PointTypeEnum.UGC.getPoints());
            pointTransaction.setStartTime(now);
            pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
            pointTransaction.setForeignId(PointForeignEnum.UGC.getId());
            pointTransaction.setForeignMasterId(campaignUgcDetail.getId());
            pointTransaction.setForeignDetailId(0L);
            pointDao.insertPointTransaction(pointTransaction);

            String data = "{" +
                    "\"thing2\":{\"value\":\"审核通过\"}," +
                    "\"thing8\":{\"value\":\"C币及抽奖机会已到帐，现在查看你的晒图！\"}" +
                    "}";

            // 发送订阅消息
            smotService.send((short) 3, campaignUgcDetail.getWechatMiniOpenid(), templateExamineResult, "/ugc/index/index?id=" + campaignUgcDetail.getCampaignId() + "&source=SuccessUploadMot", data);

        }

        return 1;
    }

    /**
     * 审核失败
     *
     * @param id 自动编号
     * @return 影响的行数
     */
    @Override
    public int reject(long id) {

        // 读取用户及活动信息
        CampaignUgcDetail campaignUgcDetail = campaignUgcDetailDao.getById(id);
        if (null == campaignUgcDetail) {
            return 0;
        }

        // 改变状态
        if (0 == campaignUgcDetailDao.reject(id)) {
            return 0;
        }

        // 重置图片
        campaignUgcDetailDao.setImageStatus(id);

        String data = "{" +
                "\"thing2\":{\"value\":\"审核失败\"}," +
                "\"thing8\":{\"value\":\"检查截图是否符合审核条件，再重新上传哦！\"}" +
                "}";

        // 发送订阅消息
        smotService.send((short) 4, campaignUgcDetail.getWechatMiniOpenid(), templateExamineResult, "/ugc/index/index?id=" + campaignUgcDetail.getCampaignId() + "&source=FailedUploadMot", data);

        return 1;
    }

    /**
     * 批量拒绝
     *
     * @param ids 自动编号
     * @return 影响的行数
     */
    @Override
    public int rejectBatch(String ids) {

        String[] idArray = ids.split(",");

        List<Long> idList = new ArrayList<>();
        for (String idString : idArray) {
            long id = LongUtil.parseInt(idString);
            if (0 < id) {
                idList.add(id);
            }
        }

        if (0 == idList.size()) {
            return 0;
        }

        List<CampaignUgcDetail> campaignUgcDetailList = campaignUgcDetailDao.getByIds(idList);
        if (null == campaignUgcDetailList || 0 == campaignUgcDetailList.size()) {
            return 0;
        }

        for (CampaignUgcDetail campaignUgcDetail : campaignUgcDetailList) {

            // 改变状态
            if (0 == campaignUgcDetailDao.reject(campaignUgcDetail.getId())) {
                continue;
            }

            // 重置图片
            campaignUgcDetailDao.setImageStatus(campaignUgcDetail.getId());

            String data = "{" +
                    "\"thing2\":{\"value\":\"审核失败\"}," +
                    "\"thing8\":{\"value\":\"检查截图是否符合审核条件，再重新上传哦！\"}" +
                    "}";

            // 发送订阅消息
            smotService.send((short) 4, campaignUgcDetail.getWechatMiniOpenid(), templateExamineResult, "/ugc/index/index?id=" + campaignUgcDetail.getCampaignId() + "&source=FailedUploadMot", data);

        }

        return 1;
    }

    @Override
    public CampaignUgcDetailPoint getCampaignUgcPoint(long id) {
        return campaignUgcDetailDao.getDetailPoint(id);
    }

    @Override
    public int savePoint(CampaignUgcDetailPoint point) {
        CampaignUgcDetailPoint p = campaignUgcDetailDao.getDetailPoint(point.getDetailId());
        if(null == p){
            return campaignUgcDetailDao.addDetailPoint(point);
        }else{
            return campaignUgcDetailDao.updateDetailPoint(point);
        }

    }
}
