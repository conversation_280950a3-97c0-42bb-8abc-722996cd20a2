package cn.slashsoft.clinique.enums;

import lombok.Getter;

/**
 * 积分类别
 *
 * <AUTHOR>  
 */
public enum PointTypeEnum {

    // 积分类别
    BINDING          ((short) 1, "绑定会员中心", 100),
    INFO             ((short) 2, "完善个人资料", 50),
    COMMENT          ((short) 3, "公众号文章精选评论", 10),
    LIKE             ((short) 4, "公众号文章点赞Top3评论", 20),
    LINK             ((short) 5, "公众号文章链接进入小程序", 10),
    TMALL            ((short) 6, "天猫会员额外赠分", 100),
    EXCHANGE         ((short) 7, "礼品兑换", 0),
    EXPIRED          ((short) 8, "积分过期", 0),
    CNY              ((short) 9, "“鼠你最红”奖励积分", 50),
    VIDEO_SIGN       ((short) 10, "视频签到活动", 100),
    EBCI_SIGN        ((short) 11, "EBCI签到活动", 100),
    TMALL_REGISTER   ((short) 12, "天猫专享积分", 100),
    EBCI_TALENTS     ((short) 13, "美白新品体验官", 200),
    MS_TALENTS       ((short) 14, "黄油达人榜", 200),
    LASER_TALENTS    ((short) 15, "夏日抚纹作战", 200),
    NOTE_PUBLISH    ((short) 16, "发布个人笔记", 20),
    NOTE_READ    	((short) 17, "分享转发笔记/视频", 5),
    NOTE_ACTION    	((short) 18, "社群内阅读/评论/收藏", 1),
    NOTE_TAG    	((short) 19, "参与品牌话题互动", 5),
    NOTE_FAVORITE    ((short) 20, "个人笔记加精", 50),
    KOC_JOIN    	((short) 21, "参与KOC争夺战", 200),
    VIDEO_WATCH    	((short) 22, "观看视频/直播", 50),
    WECHAT_WORK_BINDING    ((short) 25, "王牌见面礼", 100),
    CHALLENGE72            ((short) 108, "72小时极限保湿挑战", 200)
    ;
	
    @Getter
    private final short id;

    @Getter
    private final String name;

    @Getter
    private final int points;

    PointTypeEnum(short id, String name, int points) {
        this.id = id;
        this.name = name;
        this.points = points;
    }
}
