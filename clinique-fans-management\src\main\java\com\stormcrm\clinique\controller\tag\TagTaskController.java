package com.stormcrm.clinique.controller.tag;

import java.util.HashMap;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.stormcrm.clinique.domain.tag.Label;
import com.stormcrm.clinique.domain.tag.TagTask;
import com.stormcrm.clinique.enums.ResultEnum;
import com.stormcrm.clinique.service.tag.LabelService;
import com.stormcrm.clinique.service.tag.TagTaskService;
import com.stormcrm.clinique.util.ResultUtil;


/**
 * 任务接口
 *
 * <AUTHOR>
 */


@RestController
@RequestMapping("/tagtask")
public class TagTaskController {

	@Resource
	TagTaskService tagTaskService;
	@Resource
	LabelService labelService;

    @PreAuthorize("hasAuthority('TAGS_TASK')")
	@RequestMapping("/get-list/{target}/{progress}")
	public String index(
			@PathVariable int target,
			@PathVariable int progress) {

		HashMap<String,Object> data = new HashMap<>();

		List<TagTask> all = tagTaskService.getTagTaskBy(progress, target);
		for(TagTask task : all) {
			Label label = labelService.getById(task.getLabelId());
			task.setLabelName(label.getName());
			task.setLabelCount(labelService.getLabelCount(label));
		}
		
		data.put("tasks", all);

		return ResultUtil.customer(ResultEnum.SUCCESS, "ok", data);
	}
	

}
