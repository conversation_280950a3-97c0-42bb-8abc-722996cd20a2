package com.stormcrm.clinique.controller.tag;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import com.stormcrm.clinique.service.tag.RuleService;
import com.stormcrm.clinique.service.tag.LabelService;

/**
 * 筛选条件管理
 *
 * <AUTHOR>
 */

@Controller
@RequestMapping("/tag")
public class FilterManageController {

	@Resource
    RuleService filterService;

	@Resource 
	LabelService labelService;

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    

    @PreAuthorize("hasAuthority('TAGS')")
	@RequestMapping("/index/{id}")
	public String index(Model model,
			@PathVariable Long id) {
        model.addAttribute("staticDomain", staticDomain);
        model.addAttribute("tagId", id);
        
        return "m/fans/bi/tag";
	}


}
