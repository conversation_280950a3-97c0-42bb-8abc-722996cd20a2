package com.stormcrm.clinique.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */
public enum ResultEnum {

    // HTTP状态码
    SUCCESS(200, "成功"),

    FAILED(400, "失败"),
    UNAUTHORIZED(401, "未授权"),
    UNREGISTER(402, "未注册"),

    ERROR(511, "调用接口错误"),
    EXCEPTION(516, "程序异常"),
    PROCESS_BUSY(517, "业务繁忙"),
    PARAM_ERROR(518, "参数错误"),
    OTHER(519, "未知错误");

    @Getter
    private int status;

    @Getter
    private String message;

    ResultEnum(int status, String message) {
        this.status = status;
        this.message = message;
    }

}
