package com.stormcrm.clinique.controller;

import static com.alibaba.fastjson.JSON.toJSONStringWithDateFormat;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import com.stormcrm.clinique.domain.BSmotCampaign;
import com.stormcrm.clinique.domain.BSmotTemplate;
import com.stormcrm.clinique.service.BSmotService;
import com.stormcrm.clinique.util.ResultUtil;

/**
 * 笔记
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("template-message")
public class MessageController {
    public static final String VALID_MUST_HAVE_VAR = "这是必填字段";

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    Logger logger = LoggerFactory.getLogger(MessageController.class);

    @Resource
    private BSmotService bsmotService;

    public MessageController() {
   
    }

    /**
     * 模板消息
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('TEMPLATE_MESSAGE')")
    @RequestMapping("index")
    public String index(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/message/index";
    }

    /**
     * 查看信息
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('TEMPLATE_MESSAGE_VIEW')")
    @GetMapping("view/{id}")
    public String detail(@PathVariable long id,Model model) {
        model.addAttribute("staticDomain", staticDomain);
        // 读数据库
        BSmotCampaign bc = this.bsmotService.getCampaign(id);
        if (null == bc) {
            return "m/fans/common/empty";
        }        

    	List<BSmotTemplate> btList =  this.bsmotService.getBSMotTemplateList();
        model.addAttribute("id", id);
        model.addAttribute("bc", bc);
        model.addAttribute("templates", btList);
        return "m/fans/message/view";
    }

    /**
     * 查询所有-分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 列表
     */
    @PreAuthorize("hasAuthority('TEMPLATE_MESSAGE')")
    @PostMapping("get-page")
    @ResponseBody
    public String getPage(
            @RequestParam(value = "pagination[page]", required = false) Integer page,
            @RequestParam(value = "pagination[perpage]", required = false) Integer perpage
    ) {

        List<BSmotCampaign> bcList = this.bsmotService.getCampaignList();

        JSONObject result = new JSONObject();
        result.put("data", bcList);

        return toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }

    /**
     * 编辑模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('TEMPLATE_MESSAGE_ADD')")
    @GetMapping("edit/{id}")
    public String edit(@PathVariable(value = "id", required = false) long id, Model model) {
        model.addAttribute("staticDomain", staticDomain);

      	BSmotCampaign bc = new BSmotCampaign();
    	if(id != 0) {
            // 读数据库
    		bc = this.bsmotService.getCampaign(id);
            if (null == bc) {
                return "m/fans/common/empty";
            }   
    	}
    	List<BSmotTemplate> btList =  this.bsmotService.getBSMotTemplateList();
        model.addAttribute("id", id);
        model.addAttribute("bc", bc);
        model.addAttribute("templates", btList);

        return "m/fans/message/edit";
    }
    /**
     * 新增
     *
     * 
     */
    @PreAuthorize("hasAuthority('TEMPLATE_MESSAGE_ADD')")
    @PostMapping(value = "edit-submit", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String editSubmit(
            @RequestParam(value = "form-id", required = false) Long id,
            @RequestParam(value = "form-desc", required = false) String title,
            @RequestParam(value = "form-param", required = false) String param,
            @RequestParam(value = "form-template", required = false) Long templateId,
            @RequestParam(value = "form-source", required = false) String source,
            @RequestParam(value = "form-miniid", required = false) String mini_id,
            @RequestParam(value = "form-pagepath", required = false) String pagepath
    ) {

      
        BSmotCampaign bc = new BSmotCampaign();
        bc.setId(id);
        bc.setDesc(title);
        bc.setMiniappId(mini_id);
        bc.setPagePath(pagepath);
        bc.setParam(param);
        bc.setSource(source);
        bc.setTemplateId(templateId);

        if(id.intValue() != 0) {
        	this.bsmotService.updateCampaign(bc);
        }else {
        	this.bsmotService.addCampaign(bc);
        }
        

        // 返回结果给前端
        return ResultUtil.successToJson();
    }
  
    /**
     * 发送
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('TEMPLATE_MESSAGE_SEND')")
    @PostMapping("send/{id}")
    @ResponseBody
    public String send(@PathVariable long id, Model model) {
    	this.bsmotService.sendCampaign(id);
     
        return ResultUtil.successToJson();
    }

    /**
     * 发送
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('TEMPLATE_MESSAGE_SEND')")
    @PostMapping("testsend/{type}")
    @ResponseBody
    public String testSend( 
    		@PathVariable(value = "type", required = true) Short type,
    		@RequestParam(value = "id", required = true) Long campaignId
    ) {
    	this.bsmotService.testCampaign(campaignId, type);
     
        return ResultUtil.successToJson();
    }



}
