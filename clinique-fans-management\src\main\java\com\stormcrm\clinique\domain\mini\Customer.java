package com.stormcrm.clinique.domain.mini;

import lombok.Data;

import java.util.Date;

/**
 * 顾客表
 *
 * <AUTHOR>
 */
@Data
public class Customer {

    private Long id;

    private String phoneNumber;
    private String name;
    private String avatarUrl;
    private Date birthday;
    private Short birthdayUpdateTimes;
    private String xiaohongshuAccount;

    private Boolean updateReward;

    private Integer pointsTotal;
    private Long ranking;
    private Long rankingAlways;
    private Boolean rankingExchange;

    private String source;

    private Boolean status;

    private Date updateTime;
    private Date createTime;
    
    /* 给用户加一个小绿色V */
    private Integer kocV;

}
