package cn.slashsoft.clinique.dao.mini;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.mini.TTestimonyKocUserList;

@Repository
@Mapper
public interface TTestimonyKocUserListDao {

    @Insert("INSERT INTO `t_testimony_koc_user_list`(" +
        "   `id`, " +
        "   `customer_id`, " +
        "   `open_id`, " +
        "   `create_time`, " +
        "   `update_time` " +
        ") " +
        "VALUES (" +
        "   #{id}, " +
        "   #{customerId}, " +
        "   #{openId}, " +
        "   #{createTime}, " +
        "   #{updateTime} " +
        ")")
    void insertTTestimonyKocUserList(TTestimonyKocUserList tTestimonyKocUserList);

    @Update("UPDATE " +
        "   `t_testimony_koc_user_list` " +
        "SET " +
        "   `id` = #{id}, " +
        "   `customer_id` = #{customerId}, " +
        "   `open_id` = #{openId}, " +
        "   `create_time` = #{createTime}, " +
        "   `update_time` = #{updateTime} " +
        "WHERE " +
        "   `id`=#{id} ")
    void updateTTestimonyKocUserList(TTestimonyKocUserList tTestimonyKocUserList);

    @Select("SELECT " +
        "   `id`, " +
        "   `customer_id`, " +
        "   `open_id`, " +
        "   `create_time`, " +
        "   `update_time` " +
        "FROM " +
        "   `t_testimony_koc_user_list` " +
        "WHERE " +
        "   `id`=#{id} " +
        "LIMIT 1 ")
    TTestimonyKocUserList getTTestimonyKocUserList(Long id);

    /**
     * 获取用户是否在名单
     *
     * @param customer customer id
     * @return 数量
     */
    @Select("SELECT count(*) " +
            "FROM " +
            "   `t_testimony_koc_user_list`  " +
            "WHERE " +
            "   `customer_id`=#{customer} ")
	Integer getKocByCustomerId(String customer);
}
