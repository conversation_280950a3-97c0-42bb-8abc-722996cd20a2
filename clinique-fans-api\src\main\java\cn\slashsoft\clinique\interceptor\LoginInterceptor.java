package cn.slashsoft.clinique.interceptor;

import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.util.DuplicateUtil;
import cn.slashsoft.clinique.util.StringUtil;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;

/**
 * 小程序接口拦截器: 未登录返回401
 *
 * <AUTHOR>
 */
@Component
public class LoginInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        HttpSession httpSession = request.getSession();

        // 读取Session中的openid
        String openid = (String) httpSession.getAttribute("miniOpenid");
        String unionid = (String) httpSession.getAttribute("unionid");

        ArrayList<String> whiteUrlList = new ArrayList<>();
        whiteUrlList.add("/api/note/get-banner");
        whiteUrlList.add("/api/note/get-data");
        whiteUrlList.add("/api/note/tag/get-data");
        whiteUrlList.add("/api/note/detail");
        whiteUrlList.add("/api/video/get-data");
        whiteUrlList.add("/api/note/discuss-data");
        whiteUrlList.add("/api/set-nologin-log");
        whiteUrlList.add("/api/private-relish/check");
        whiteUrlList.add("/api/kocbeta/check");
        whiteUrlList.add("/api/campaign/2408/set/tracking");

        boolean needLogin = true;
        for(String url : whiteUrlList) {
            if(request.getRequestURI().startsWith(url)) {
                needLogin = false;
                break;
            }
        }

        // 当缓存中的openid为空时，返回未登录，CODE: 401
        if (StringUtil.isNullOrEmpty(openid) && needLogin) {
            DuplicateUtil.interceptor(request, response, ResultEnum.UNAUTHORIZED);
            return false;
        }

        Long customerId = (Long) httpSession.getAttribute("customerId");

        // 请求中传递unionid、openid和customerId
        request.setAttribute("unionid", unionid);
        request.setAttribute("miniOpenid", openid);
        request.setAttribute("customerId", customerId);

        return true;
    }

}
