package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.util.StringUtil;
import org.apache.ibatis.jdbc.SQL;

/**
 * 新品体验官活动
 *
 * <AUTHOR>
 */
public class CampaignTalentsProvider {

    /**
     * 查询所有礼品-分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 礼品列表
     */
    public String getPage(int pageIndex, int pageSize, String generalSearch, Short status) {

        return new SQL() {{

            SELECT("`t`.`id`,`t`.`customer_id`,`w`.`nick_name`,`w`.`avatar_url`,`t`.`status`,`t`.`upload_time`,`t`.`examine_time`,`t`.`create_time`");
            FROM("`campaign_ebci_talents` `t` INNER JOIN `wechat` `w` ON `t`.`customer_id`=`w`.`customer_id`");
            WHERE("1=1");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`w`.`nick_name` LIKE '%${generalSearch}%'");
            }
            if (null != status && 0 < status) {
                AND();
                WHERE("`t`.`status`=#{status}");
            }
            ORDER_BY("`t`.`id` DESC LIMIT #{pageIndex},#{pageSize}");

        }}.toString();

    }

    /**
     * 查询所有礼品带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    public String getPageCount(String generalSearch, Short status) {

        return new SQL() {{

            SELECT("COUNT(*)");
            FROM("`campaign_ebci_talents` `t` INNER JOIN `wechat` `w` ON `t`.`customer_id`=`w`.`customer_id`");
            WHERE("1=1");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`w`.`nick_name` LIKE '%${generalSearch}%'");
            }
            if (null != status && 0 < status) {
                AND();
                WHERE("`t`.`status`=#{status}");
            }

        }}.toString();

    }

}
