package com.stormcrm.clinique.service.campaign.ugc.impl;

import com.stormcrm.clinique.dao.campaign.CampaignH5Dao;
import com.stormcrm.clinique.domain.campaign.CampaignH5;
import com.stormcrm.clinique.service.campaign.ugc.CampaignH5Service;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Campaign H5
 *
 * <AUTHOR>
 */
@Service
public class CampaignH5ServiceImpl implements CampaignH5Service {
	@Resource
    private CampaignH5Dao campaignH5Dao;

    @Override
    public CampaignH5 getValidCampaign(String campaignCode) {
        return campaignH5Dao.getValidCampaign(campaignCode);
    }

}
