package cn.slashsoft.clinique.service.campaign.impl;

import cn.slashsoft.clinique.dao.campaign.CampaignWechatWorkBindingDao;
import cn.slashsoft.clinique.dao.mini.PointDao;
import cn.slashsoft.clinique.dao.mini.WechatDao;
import cn.slashsoft.clinique.domain.campaign.*;
import cn.slashsoft.clinique.domain.mini.Member;
import cn.slashsoft.clinique.domain.mini.PointTransaction;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.enums.CBTypeEnum;
import cn.slashsoft.clinique.enums.PointForeignEnum;
import cn.slashsoft.clinique.enums.PointTypeEnum;
import cn.slashsoft.clinique.service.campaign.CampaignWechatWorkBindingService;
import cn.slashsoft.clinique.service.outside.impl.OutsideServiceImpl;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.HttpUtil;
import cn.slashsoft.clinique.util.RandomUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.vo.Result;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.io.ByteArrayInputStream;
import java.util.Date;
import java.util.List;
import java.util.logging.Logger;

/**
 * 企业微信绑定活动
 *
 * <AUTHOR>
 */
@Service
public class CampaignWechatWorkBindingServiceImpl implements CampaignWechatWorkBindingService {

    private final Logger logger = Logger.getLogger(CampaignWechatWorkBindingServiceImpl.class.getName());

    @Value("${wechat.cdp.select-customer-info-by-openid}")
    private String urlCdpSelectCustomerInfoByOpenid;

    private final WechatDao wechatDao;
    private final CampaignWechatWorkBindingDao campaignWechatWorkBindingDao;
    private final PointDao pointDao;

    public CampaignWechatWorkBindingServiceImpl(CampaignWechatWorkBindingDao campaignWechatWorkBindingDao, PointDao pointDao, WechatDao wechatDao) {
        this.campaignWechatWorkBindingDao = campaignWechatWorkBindingDao;
        this.pointDao = pointDao;
        this.wechatDao = wechatDao;
    }

    /**
     * 获取门店
     *
     * @return 门店
     */
    @Override
    public List<CampaignWechatWorkBindingCounter> getCampaignWechatWorkBindingCounter() {
        return campaignWechatWorkBindingDao.getCampaignWechatWorkBindingCounter();
    }

    /**
     * 中奖名单
     *
     * @return 中奖名单
     */
    @Override
    public List<CampaignWechatWorkBinding> getCampaignWechatWorkBindingList() {
        return campaignWechatWorkBindingDao.getCampaignWechatWorkBindingList();
    }

    /**
     * 获取活动信息
     *
     * @param officialOpenid 公众号唯一编号
     * @return 活动信息
     */
    @Override
    public CampaignWechatWorkBinding getCampaignWechatWorkBinding(
            String officialOpenid,
            String nickName,
            String avatarUrl
    ) {

        CampaignWechatWorkBinding campaignWechatWorkBinding = campaignWechatWorkBindingDao.getCampaignWechatWorkBinding(officialOpenid);

        if (null == campaignWechatWorkBinding) {

            // CDP里有，并绑定了美容顾客，才创建活动信息
            Member member = getCdpMemberByOfficialOpenid(officialOpenid);
            if(null != member && member.getIsBindingCustomerService()){
                campaignWechatWorkBinding = new CampaignWechatWorkBinding();
                campaignWechatWorkBinding.setOfficialOpenid(officialOpenid);
                campaignWechatWorkBinding.setMemberType(member.getMemberType());
                campaignWechatWorkBinding.setNickName(nickName);
                campaignWechatWorkBinding.setAvatarUrl(avatarUrl);
                campaignWechatWorkBinding.setIsDraw(false);
                campaignWechatWorkBindingDao.insertCampaignWechatWorkBinding(campaignWechatWorkBinding);
            }

        }

        return campaignWechatWorkBinding;
    }

    /**
     * 获取CDP用户信息
     *
     * @param officialOpenid 公众号唯一编号
     * @return 用户信息
     */
    private Member getCdpMemberByOfficialOpenid(String officialOpenid) {
        try {

            logger.warning(urlCdpSelectCustomerInfoByOpenid + officialOpenid);

            String response = HttpUtil.get(urlCdpSelectCustomerInfoByOpenid + officialOpenid);

            logger.warning(response);

            Document document = new SAXReader().read(new ByteArrayInputStream(response.getBytes("UTF-8")));
            Element root = document.getRootElement();
            if (!"True".equals(root.elementText("Code"))) {
                return null;
            }

            Element result = root.element("ResultList");
            Member member = new Member();

            String firstChannelPurchase = result.elementText("FirstChannelPurchase");
            member.setMemberType((short) (StringUtil.isNullOrEmpty(firstChannelPurchase)?2:1));

            String baName = result.elementText("BAName");
            String baCardNo = result.elementText("BACardNo");
            member.setIsBindingCustomerService(!StringUtil.isNull(baName) || !StringUtil.isNullOrEmpty(baCardNo));

            return member;

        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 抽奖
     *
     * @param unionid 开放平台唯一编号
     * @param officialOpenid 公众号唯一编号
     * @return 奖品结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result draw(String unionid, String officialOpenid) {

        CampaignWechatWorkBinding campaignWechatWorkBinding = campaignWechatWorkBindingDao.getCampaignWechatWorkBindingOnly(officialOpenid);
        if(null == campaignWechatWorkBinding){
            return new Result(1, "未找到活动信息，请刷新重试");
        }

        if(campaignWechatWorkBinding.getIsDraw()){
            return new Result(1, "您已抽过奖了");
        }

        //读取奖池
        List<CampaignWechatWorkBindingGift> campaignWechatWorkBindingGiftList = campaignWechatWorkBindingDao.getCampaignWechatWorkBindingGift(campaignWechatWorkBinding.getMemberType());
        if (null == campaignWechatWorkBindingGiftList || 0 == campaignWechatWorkBindingGiftList.size()) {
            return new Result(1, "未配置奖品");
        }

        //抽取奖品
        CampaignWechatWorkBindingGift campaignWechatWorkBindingGift = null;

        if (1 == campaignWechatWorkBindingGiftList.size()) {
            campaignWechatWorkBindingGift = campaignWechatWorkBindingGiftList.get(0);
        } else {
            campaignWechatWorkBindingGift = getCampaignWechatWorkBindingGiftByRatio(campaignWechatWorkBindingGiftList);
        }

        if (null == campaignWechatWorkBindingGift) {
            return new Result(1, "系统出小差了，请稍后再试");
        }

        //写入中奖信息
        if (0 == campaignWechatWorkBindingDao.updateCampaignWechatWorkBindingForDraw(officialOpenid, campaignWechatWorkBindingGift.getId())) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new Result(1, "系统出小差了，请稍后再试");
        }

        //扣库存
        if (0 == campaignWechatWorkBindingDao.updateCampaignWechatWorkBindingGiftForDraw(campaignWechatWorkBindingGift.getId())) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new Result(1, "系统出小差了，请稍后再试");
        }

        //发放积分
        if(3==campaignWechatWorkBindingGift.getType()){

            Wechat wechat = wechatDao.getWechatByOpenId(officialOpenid);
            if(null !=wechat && null != wechat.getCustomerId() && 0 < wechat.getCustomerId()){
                // 发放积分
                Date now = new Date();
                PointTransaction pointTransaction = new PointTransaction();
                pointTransaction.setCustomerId(wechat.getCustomerId());
                pointTransaction.setPointTypeId(CBTypeEnum.WECHAT_WORK_BINDING.getId());
                pointTransaction.setPoints(CBTypeEnum.WECHAT_WORK_BINDING.getPoints());
                pointTransaction.setRemainingPoints(CBTypeEnum.WECHAT_WORK_BINDING.getPoints());
                pointTransaction.setStartTime(now);
                pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
                pointTransaction.setForeignId(CBTypeEnum.WECHAT_WORK_BINDING.getId());
                pointTransaction.setForeignMasterId(campaignWechatWorkBinding.getId());
                pointTransaction.setForeignDetailId(0L);
                pointDao.insertPointTransaction(pointTransaction);
            }
            else{
                campaignWechatWorkBindingDao.insertCampaignWechatWorkBindingPoint(unionid);
            }

        }

        return new Result(0, "{\"giftId\":" + campaignWechatWorkBindingGift.getGiftId() + ",\"giftType\":" + campaignWechatWorkBindingGift.getType() + ",\"giftName\":\"" + campaignWechatWorkBindingGift.getName() + "\"}");
    }

    /**
     * 按比率抽奖一个奖项
     *
     * @param campaignWechatWorkBindingGiftList 奖池
     * @return 中奖
     */
    private CampaignWechatWorkBindingGift getCampaignWechatWorkBindingGiftByRatio(List<CampaignWechatWorkBindingGift> campaignWechatWorkBindingGiftList) {

        // 中奖率
        int ratio = campaignWechatWorkBindingGiftList.parallelStream().mapToInt(CampaignWechatWorkBindingGift::getRatio).sum();

        if (0 == ratio) {
            return null;
        }

        int random = RandomUtil.getNumberBetween(1, ratio);

        for (CampaignWechatWorkBindingGift campaignWechatWorkBindingGift : campaignWechatWorkBindingGiftList) {
            if (random <= campaignWechatWorkBindingGift.getRatio()) {
                return campaignWechatWorkBindingGift;
            }
            random -= campaignWechatWorkBindingGift.getRatio();
        }

        return null;
    }

    /**
     * 领取专柜
     *
     * @param officialOpenid 公众号唯一编号
     * @param name           姓名
     * @param city           城市
     * @param counter        门店
     * @return 处理结果
     */
    @Override
    public Result counter(String officialOpenid, String name, String city, String counter) {

        // 更新表单写入状态
        if (0 == campaignWechatWorkBindingDao.updateCampaignWechatWorkBindingForCounter(officialOpenid)){
            return new Result(1, "您已经填写过了");
        }

        try {
            campaignWechatWorkBindingDao.insertCampaignWechatWorkBindingPickup(officialOpenid, name, city, counter);
        }
        catch (Exception e){
            return new Result(1, "系统出小差了，请稍后再试");
        }

        return new Result(0, "成功");
    }

    /**
     * 核销码
     *
     * @param officialOpenid 公众号唯一编号
     */
    @Override
    public void pickup(String officialOpenid) {
        campaignWechatWorkBindingDao.updateCampaignWechatWorkBindingPickup(officialOpenid);
    }

    /**
     * 快递信息
     *
     * @param officialOpenid 公众号唯一编号
     * @param name           姓名
     * @param phoneNumber    手机号码
     * @param province       省份
     * @param city           城市
     * @param district       区域
     * @param address        具体地址
     */
    @Override
    public Result address(String officialOpenid, String name, String phoneNumber, String province, String city, String district, String address) {

        // 更新表单写入状态
        if (0 == campaignWechatWorkBindingDao.updateCampaignWechatWorkBindingForCounter(officialOpenid)){
            return new Result(1, "您已经填写过了");
        }

        try{
            campaignWechatWorkBindingDao.insertCampaignWechatWorkBindingExpress(officialOpenid, name, phoneNumber, province, city, district, address);
        }
        catch (Exception e){
            return new Result(1, "系统出小差了，请稍后再试");
        }

        return new Result(0, "成功");
    }

    /**
     * 写入访问日志
     *
     * @param campaignViewLog 日志
     * @return 日志编号
     */
    @Override
    public void setCampaignViewLog(CampaignViewLog campaignViewLog) {
        campaignWechatWorkBindingDao.insertViewLog(campaignViewLog);
    }
}
