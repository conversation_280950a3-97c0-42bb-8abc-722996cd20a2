package cn.slashsoft.clinique.service.mini.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import cn.slashsoft.clinique.dao.mini.ActionDao;
import cn.slashsoft.clinique.dao.mini.SpecialBannerDao;
import cn.slashsoft.clinique.domain.mini.Customer;
import cn.slashsoft.clinique.domain.mini.Day3Task;
import cn.slashsoft.clinique.domain.mini.SpecialBanner;
import cn.slashsoft.clinique.service.campaign.CampaignNtfService;
import cn.slashsoft.clinique.service.mini.CustomerService;
import cn.slashsoft.clinique.service.mini.SpecialService;
import cn.slashsoft.clinique.service.mini.TaskService;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.service.outside.PrivateRelishService;
import cn.slashsoft.clinique.util.DateUtil;

/**
 * 用户特权banner
 *
 * <AUTHOR>
 */
@Service
public class SpecialServiceImpl implements SpecialService {
	@Resource
	private SpecialBannerDao specialBannerDao;

	@Resource
	private ActionDao actionDao;
	@Resource
	private CustomerService customerService;

	@Resource
	private TaskService taskService;
	
	@Resource
	private OutsideService outsideService;
	
	@Resource
	private PrivateRelishService prService;
	
	@Resource 
	private CampaignNtfService campaignNtfService;
	/**
	 * banner 是否可以看到koc beta
	 *
	 * 1 返回SpecialBanner
	 */
	@Override
	public SpecialBanner koccheck(Long customerId) {
		boolean finished = true;
		if (!finished) {
			if (this.prService.koccanSee(customerId + "")) {
				return specialBannerDao.getSpecialBannerById(21L);
			}
		}

		return null;
	}

	/**
	 * banner 新老客
	 *
	 * 1 返回SpecialBanner
	 */
	@Override
	public SpecialBanner oldnewcheck(String unionid, Long customerId) {
		boolean finished = false;
		if (!finished) {
			//System.out.println("2021-01-06 23:59:59");
			Date endTime = DateUtil.valueOf("2021-01-06 23:59:59");
			if(!DateUtil.earlierThanNow(endTime)) {
				//System.out.println("get");
				boolean isNewCustomer = campaignNtfService.isNewCustomer(unionid, customerId);
				
				SpecialBanner oldNewSpecialBanner = null;
				if(isNewCustomer){
					//System.out.println("22L");
					oldNewSpecialBanner = specialBannerDao.getSpecialBannerById(22L);;
				}else {
					//System.out.println("23L");
					oldNewSpecialBanner = specialBannerDao.getSpecialBannerById(23L);;
				}				

				return oldNewSpecialBanner;
			}
		}

		return null;
	}

	@Override
	public SpecialBanner days3signCheck(String unionid, String miniOpenid,Long customerId) {
		boolean finished = false;
		if (!finished) {
				boolean days3SignCustomer = customerService.days3signCheck(unionid, customerId);
				
				SpecialBanner  days3signSpecialBanner = null;
				if(days3SignCustomer){
					//System.out.println("24L");

					Day3Task task = taskService.getTask(unionid, customerId);
					if(null == task) {
						days3signSpecialBanner = specialBannerDao.getSpecialBannerById(24L);
					}else {
						int all = actionDao.getDay3TaskActionCount(miniOpenid);
						if(all > 0) {
							int days = actionDao.getDay3TaskFistActionToDays(miniOpenid);
							if(days < 3) {
								days3signSpecialBanner = specialBannerDao.getSpecialBannerById(24L);
							}
						}else {
							days3signSpecialBanner = specialBannerDao.getSpecialBannerById(24L);
						}
					}
				}
				return  days3signSpecialBanner;
		}		

		return null;
	}

	@Override
	public List<SpecialBanner> getNormalSpecial() {
		
		return specialBannerDao.getNormal();
	}

	@Override
	public SpecialBanner onlineDownCheck(String unionid, String openid, long customerId) {
		SpecialBanner  onlineDownSpecialBanner = null;
		Customer customer = customerService.getCustomerById(customerId);
		if(outsideService.isOnlineCustomer(customer.getPhoneNumber())) {
			onlineDownSpecialBanner = specialBannerDao.getSpecialBannerById(25L);
		}
		return  onlineDownSpecialBanner;
	}

 
}
