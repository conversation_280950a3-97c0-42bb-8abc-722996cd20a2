package cn.slashsoft.clinique.dao.mini;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.mini.TTestimonyFragmentType;

@Repository
@Mapper
public interface TTestimonyFragmentTypeDao {

    @Insert("INSERT INTO `t_testimony_fragment_type`(" +
        "   `id`, " +
        "   `fragment_type`, " +
        "   `fragment_qty`, " +
        "   `fragment_limit`, " +
        "   `create_time`, " +
        "   `update_time` " +
        ") " +
        "VALUES (" +
        "   #{id}, " +
        "   #{fragmentType}, " +
        "   #{fragmentQty}, " +
        "   #{fragmentLimit}, " +
        "   #{createTime}, " +
        "   #{updateTime} " +
        ")")
    void insertTTestimonyFragmentType(TTestimonyFragmentType tTestimonyFragmentType);

    @Update("UPDATE " +
        "   `t_testimony_fragment_type` " +
        "SET " +
        "   `id` = #{id}, " +
        "   `fragment_type` = #{fragmentType}, " +
        "   `fragment_qty` = #{fragmentQty}, " +
        "   `fragment_limit` = #{fragmentLimit}, " +
        "   `create_time` = #{createTime}, " +
        "   `update_time` = #{updateTime} " +
        "WHERE " +
        "   `id`=#{id} ")
    void updateTTestimonyFragmentType(TTestimonyFragmentType tTestimonyFragmentType);

    @Select("SELECT " +
        "   `id`, " +
        "   `fragment_type`, " +
        "   `fragment_qty`, " +
        "   `fragment_limit`, " +
        "   `create_time`, " +
        "   `update_time` " +
        "FROM " +
        "   `t_testimony_fragment_type` " +
        "WHERE " +
        "   `id`=#{id} " +
        "LIMIT 1 ")
    TTestimonyFragmentType getTTestimonyFragmentType(Long id);
}
