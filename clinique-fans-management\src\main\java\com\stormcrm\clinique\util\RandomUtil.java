package com.stormcrm.clinique.util;

import java.util.Date;
import java.util.Random;

/**
 * 随机数工具类
 *
 * <AUTHOR>
 */
public class RandomUtil {

    /**
     * 获取随机数
     *
     * @param min 最小
     * @param max 最大
     * @return 数字
     */
    public static int getNumberBetween(int min, int max) {
        Random random = new Random();
        return random.nextInt(max - min) + min;
    }

    /**
     * 生成16位随机数，由yyMMddHHmmss+4位随机数
     * @return 随机数
     */
    public static String getFileName(){
        return DateUtil.parseString(new Date(), "yyMMddHHmmssSSSS") + getNumberBetween(100000, 999999);
    }

    /**
     * 生成4位随机数的短信验证码
     *
     * @return 验证码
     */
    public static String getVerifyCode() {
        return "" + getNumberBetween(0, 9) + getNumberBetween(0, 9) + getNumberBetween(0, 9) + getNumberBetween(0, 9);
    }
}
