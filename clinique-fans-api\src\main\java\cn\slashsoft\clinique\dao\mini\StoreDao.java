package cn.slashsoft.clinique.dao.mini;

import cn.slashsoft.clinique.domain.mini.*;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 商城相关的数据库操作
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface StoreDao {

    /**
     * 获取有效的商城礼品列表，按更新时间倒序排列
     * 积分兑礼
     *
     * @param customerId 顾客编号
     * @return 商城礼品列表
     */
    @Select("SELECT " +
            "   `g`.`id`, " +
            "   `g`.`name`, " +
            "   `g`.`image_url`, " +
            "   `g`.`description`, " +
            "   `g`.`points`, " +
            "   `g`.`stocks`, " +
            "   (SELECT price FROM store_gift_discount WHERE `start_time` <= now() AND `end_time` >= now() AND store_gift_id = g.id) "
            + " `halfPrice`, " +
            "   (SELECT `desc` FROM store_gift_discount WHERE `start_time` <= now() AND `end_time` >= now() AND store_gift_id = g.id)  " +
            "    `halfDesc`, " +
            "   (" +
            "       SELECT " +
            "           COUNT(*) " +
            "       FROM " +
            "           `store_exchange` " +
            "       WHERE " +
            "           `store_gift_id`=`g`.`id` " +
            "           AND `customer_id`=#{customerId} " +
            "           AND `status`=1 " +
            "           AND `recovery`=0 " +
            "   ) `exchange_total` " +
            "FROM " +
            "   `store_gift` `g` " +
            "WHERE " +
            //"   g.`status`=1 AND " +
            " 	 g.`start_time` <= now() AND g.`end_time` >= now() " +
            "   AND g.`recovery`=0 " +
            "   AND g.`store_gift_type_id`=1 " +
            "ORDER BY " +
            "   g.`create_time` DESC"
    )
    List<StoreGift> getStoreGiftList(long customerId);

    /**
     * 读取所有门店区域
     *
     * @return 区域
     */
    @Select("SELECT " +
            "   DISTINCT `city` as `id`," +
            "   `city` as `name` " +
            "FROM " +
            "   `customer_service_counter` " +
            "WHERE " +
            "   `status`=1  "+
            "   ORDER BY `code` "
    )
    List<StoreArea> getStoreAreaList();

    /**
     * 读取所有门店区域提货点
     *
     * @return 提货点
     */
    @Select("SELECT " +
            "   `id`," +
            "   `city` as `store_area_id`," +
            "   `code`, " +
            "   `name` " +
            "FROM " +
            "   `customer_service_counter` " +
            "WHERE " +
            "   `status`=1 "+
            "   ORDER BY `code` "
    )
    List<StoreAreaPlace> getStoreAreaPlaceList();

    /**
     * 读取门店信息
     *
     * @param id 门店编号
     * @return 门店信息
     */
    @Select("SELECT " +
            "    `id`," +
            "   `city` as `store_area_id`, " 
            + " `code`, " +
            "   `name` " +
            "FROM " +
            "   `customer_service_counter` " +
            "WHERE " +
            "   `id`=#{id} " +
            "LIMIT 1")
    StoreAreaPlace getStoreAreaPlace(Long id);

    /**
     * 跟据礼品编号获取礼品资料，和已兑礼数量
     *
     * @param id         礼品编号
     * @param customerId 顾客编号
     * @return 礼品资料
     */
    @Select("SELECT " +
            "   `g`.`id`, " +
            "   `g`.`name`, " +
            "   `g`.`image_url`, " +
            "   `g`.`points`, " +
            "   `g`.`coupon_id`, " +
            "   (SELECT price FROM store_gift_discount WHERE `start_time` <= now() AND `end_time` >= now() AND store_gift_id = g.id) "
            + " `halfPrice`, " +
            "   (SELECT `desc` FROM store_gift_discount WHERE `start_time` <= now() AND `end_time` >= now() AND store_gift_id = g.id)  " +
            "    `halfDesc`, " +
            "   (" +
            "       SELECT " +
            "           COUNT(*) " +
            "       FROM " +
            "           `store_exchange` " +
            "       WHERE " +
            "           `store_gift_id`=#{id} " +
            "           AND `customer_id`=#{customerId} " +
            "           AND `status`=1 " +
            "           AND `recovery`=0 " +
            "   ) `exchange_total` " +
            "FROM " +
            "   `store_gift` `g` " +
            "WHERE " +
            "   `g`.`id`=#{id} " +
            //"   AND `g`.`status`=1 " +
            " 	AND g.`start_time` <= now() AND g.`end_time` >= now() " +
            "   AND `g`.`recovery`=0 " +
            "   AND `g`.`store_gift_type_id`=1 ")
    StoreGift getStoreGiftById(@Param("id") long id, @Param("customerId") long customerId);

    /**
     * 读取兑换的礼品信息
     *
     * @param id         礼品编号
     * @param customerId 顾客编号
     * @return 礼品信息
     */
    @Select("SELECT " +
            "   `g`.`id`, " +
            "   `g`.`name`, " +
            "   `g`.`points`, " +
            "   `g`.`stocks`, " +
            "   `g`.`coupon_id`, " +
            "   (SELECT price FROM store_gift_discount WHERE `start_time` <= now() AND `end_time` >= now() AND store_gift_id = g.id) "
            + " `halfPrice`, " +
            "   (SELECT `desc` FROM store_gift_discount WHERE `start_time` <= now() AND `end_time` >= now() AND store_gift_id = g.id)  " +
            "    `halfDesc`, " +
            "   (" +
            "       SELECT " +
            "           COUNT(*) " +
            "       FROM " +
            "           `store_exchange` " +
            "       WHERE " +
            "           `store_gift_id`=#{id} " +
            "           AND `customer_id`=#{customerId} " +
            "           AND `status`=1 " +
            "           AND `recovery`=0 " +
            "   ) `exchange_total` " +
            "FROM " +
            "   `store_gift` `g` " +
            "WHERE " +
            "   `g`.`id`=#{id} " +
            //"   AND `g`.`status`=1 " +
            " 	AND g.`start_time` <= now() AND g.`end_time` >= now() " +
            "   AND `g`.`recovery`=0 " +
            "   AND `g`.`store_gift_type_id`=1 ")
    StoreGift getStoreGiftForExchangeById(@Param("id") long id, @Param("customerId") long customerId);

    /**
     * 扣除库存
     *
     * @param id 礼品编号
     * @return 影响的行数
     */
    @Update("UPDATE " +
            "   `store_gift` " +
            "SET " +
            "   `stocks`=`stocks`-1," +
            "   `exchange_total`=`exchange_total`+1 " +
            "WHERE " +
            "   `id`=#{id} " +
            "   AND `stocks`>0"
    )
    int updateStoreGiftStocksById(long id);

    /**
     * 写入兑换表
     *
     * @param storeExchange 兑换信息
     */
    @Insert("INSERT INTO `store_exchange`(" +
            "   `exchange_order`, " +
            "   `customer_id`, " +
            "   `store_gift_id`, " +
            "   `store_exchange_type_id`, " +
            "   `store_exchange_logistics_id`, " +
            "   `points` " +
            ") " +
            "VALUES (" +
            "   #{exchangeOrder}, " +
            "   #{customerId}, " +
            "   #{storeGiftId}, " +
            "   #{storeExchangeTypeId}, " +
            "   #{storeExchangeLogisticsId}, " +
            "   #{points} " +
            ")"
    )
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertStoreExchange(StoreExchange storeExchange);

    /**
     * 写入自提类型的兑换
     *
     * @param storeExchangePickup 自提类型的兑换
     */
    @Insert("INSERT INTO `store_exchange_pickup`(" +
            "   `store_exchange_id`, " +
            "   `store_area_id`, " +
            "   `store_area_place_id`" +
            ") " +
            "VALUES (" +
            "   #{storeExchangeId}, " +
            "   #{storeAreaId}, " +
            "   #{storeAreaPlaceId}" +
            ")"
    )
    void insertStoreExchangePickup(StoreExchangePickup storeExchangePickup);

    /**
     * 写入快递类型的兑换
     *
     * @param storeExchangeExpress 快递类型的兑换
     */
    @Insert("INSERT INTO `store_exchange_express`(" +
            "   `store_exchange_id`, " +
            "   `name`, " +
            "   `phone_number`, " +
            "   `province`, " +
            "   `city`, " +
            "   `district`, " +
            "   `address` " +
            ") " +
            "SELECT " +
            "   #{storeExchangeId}, " +
            "   `name`, " +
            "   `phone_number`, " +
            "   `province`, " +
            "   `city`, " +
            "   `district`, " +
            "   `address` " +
            "FROM " +
            "   `customer_address` " +
            "WHERE " +
            "   `id`=#{customerAddressId}"
    )
    void insertStoreExchangeExpress(StoreExchangeExpress storeExchangeExpress);

    /**
     * 获取兑换记录
     *
     * @param customerId 顾客编号
     * @return 兑换记录
     */
    @Select("select " +
            "   `e`.`exchange_order`, " +
            "   `g`.`name`, " +
            "   `g`.`image_url`, " +
            "   `e`.`store_exchange_type_id`, " +
            "   `e`.`store_exchange_logistics_id`, " +
            "   `l`.`name` `store_exchange_logistics_name`, " +
            "   `s`.`name` `store_area_place_name`, " +
            "   `s`.`city` `store_area_name`, " +
            "   `e`.`points`, " +
            "   `e`.`create_time` " +
            "from " +
            "   `store_exchange` `e` " +
            "       INNER JOIN " +
            "   `store_gift` `g` " +
            "       ON `e`.`store_gift_id`=`g`.`id` " +
            "       INNER JOIN " +
            "   `store_exchange_logistics` `l` " +
            "       ON `e`.`store_exchange_logistics_id`=`l`.`id` " +
            "       LEFT JOIN " +
            "   `store_exchange_pickup` `p` " +
            "       ON `p`.`store_exchange_id`=`e`.`id` " +
            "       LEFT JOIN " +
            "   `customer_service_counter` `s` " +
            "       ON `p`.`store_area_place_id`=`s`.`id` " +
            "where " +
            "   `e`.`customer_id`=#{customerId} " +
            "   AND `e`.`status`=1 " +
            "   AND `e`.`recovery`=0 " +
            "ORDER BY" +
            "   `e`.`id` DESC ")
    List<StoreExchange> getStoreExchangeByCustomer(long customerId);

    /**
     * 写入兑换日志
     *
     * @param storeExchangeLog 兑换日志
     */
    @Insert("INSERT INTO `store_exchange_log`(" +
            "   `wechat_mini_openid`, " +
            "   `store_gift_id`," +
            "   `step`" +
            ") " +
            "VALUES (" +
            "   #{openid}," +
            "   #{storeGiftId}," +
            "   #{step}" +
            ")"
    )
    void insertStoreExchangeLog(StoreExchangeLog storeExchangeLog);

    /**
     * 跟据编号查找礼品名称
     *
     * @param id 礼品编号
     * @return 礼品名称
     */
    @Select("SELECT " +
            "   `name` " +
            "from " +
            "   `store_gift` " +
            "WHERE " +
            "   `id`=#{id}")
    String getStoreGiftNameById(long id);

    /**
     * 跟据编号查找店铺名称
     *
     * @param id 店铺编号
     * @return 店铺名称
     */
    @Select("SELECT " +
            "   `name` " +
            "from " +
            "   `customer_service_counter` " +
            "WHERE " +
            "   `id`=#{id}")
    String getStoreAreaPlaceNameById(long id);

    /**
     * 写入商城列表页访问来源的日志
     *
     * @param viewLog 小程序首页的来源
     */
    @Insert("INSERT INTO `store_view_log`(" +
            "   `wechat_mini_openid`, " +
            "   `source`" +
            ") " +
            "VALUES (" +
            "   #{wechatMiniOpenid}, " +
            "   #{source}" +
            ")")
    void insertViewLog(ViewLog viewLog);
}
