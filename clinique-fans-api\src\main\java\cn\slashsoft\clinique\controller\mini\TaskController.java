package cn.slashsoft.clinique.controller.mini;

import cn.slashsoft.clinique.domain.mini.Action;
import cn.slashsoft.clinique.domain.mini.CustomerTask;
import cn.slashsoft.clinique.domain.mini.Day3Task;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.ActionService;
import cn.slashsoft.clinique.service.mini.TaskService;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.mini.Day3TaskDetailVo;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 小程序内任务
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/task")
public class TaskController {

    @Resource
    private HttpServletRequest request;

    @Resource
    private TaskService taskService;


    /**
     * 获取三日打卡任务详情
     *
     * @param 
     * @return 成功
     */
    @PostMapping("/day3-detail")
    public String day3Detail() {
        // 获取缓存中的顾客编号
        Long customerId = (long)request.getAttribute("customerId");

        return ResultUtil.customer(ResultEnum.SUCCESS, taskService.getDay3TaskDetail( customerId));
    }

    /**
     * 三日打卡任务完成发券
     *
     * @param 
     * @return 成功
     */
    @PostMapping("/day3-finished")
    public String day3Finish() {
        // 获取缓存中的顾客编号
        Long customerId = (long)request.getAttribute("customerId");

        int finish = taskService.day3TaskFinish( customerId);
        if(finish > 0) {
        	return ResultUtil.customer(ResultEnum.SUCCESS, 1 );
        }
        return ResultUtil.customer(ResultEnum.FAILED, "发券失败" );
    }

    /**
     * 三日打卡提醒
     *
     * @param 
     * @return 成功
     */
    @PostMapping("/day3-notice-set")
    public String day3NoticeSet() {
        // 获取缓存中的顾客编号
        Long customerId = (long)request.getAttribute("customerId");

        return ResultUtil.customer(ResultEnum.SUCCESS, taskService.day3TaskNotice( customerId, 1));
    }
    /**
     * 取消三日打卡提醒 
     *
     * @param 
     * @return 成功
     */
    @PostMapping("/day3-notice-cancel")
    public String day3NoticeCancel() {
        // 获取缓存中的顾客编号
        Long customerId = (long)request.getAttribute("customerId");

        return ResultUtil.customer(ResultEnum.SUCCESS, taskService.day3TaskNotice( customerId, 0));
    }
}
