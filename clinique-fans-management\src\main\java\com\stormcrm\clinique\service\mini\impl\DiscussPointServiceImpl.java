package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.domain.Wechat;
import com.stormcrm.clinique.service.DiscussPointService;
import com.stormcrm.clinique.dao.WechatDao;

import com.alibaba.druid.util.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 点赞积分权益相关服务
 *
 * <AUTHOR>
 */
@Service
public class DiscussPointServiceImpl implements DiscussPointService {

    private final WechatDao wechatDao;

    public DiscussPointServiceImpl(WechatDao  wechatDao) {
        this.wechatDao = wechatDao;
    }

 

    /**
     * 获取用户列表
     *
     * @param nickName 用户昵称
     * @return 用户列表
     */
    @Override
    public List<Wechat> getAll(String NickName) {
    	if(StringUtils.isEmpty(NickName)) {
    		return wechatDao.getAll();
    	}
        return wechatDao.getByNickName(NickName);
    }
    
    /**
     * 获取用户
     *
     * @param customerId 
     * @return 用户
     */
    @Override
    public  Wechat getOne(String customerId) {
    	return wechatDao.getByCustomerId(customerId);
    }
    
}
