package cn.slashsoft.clinique.vo.mini;

import cn.slashsoft.clinique.domain.mini.StoreArea;
import cn.slashsoft.clinique.domain.mini.StoreAreaPlace;
import cn.slashsoft.clinique.domain.mini.StoreGift;
import lombok.Data;

import java.util.List;

/**
 * 商城兑换明细页面返回数据
 * <AUTHOR>
 */
@Data
public class StoreExchangeVo {

    private Integer pointTotal;
    private List<StoreArea> storeAreaList;
    private List<StoreAreaPlace> storeAreaPlaceList;
    private StoreGift storeGift;

}
