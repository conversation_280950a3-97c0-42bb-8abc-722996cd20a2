package cn.slashsoft.clinique.dao.campaign;

import cn.slashsoft.clinique.domain.campaign.*;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 视频签到活动
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignVideoSignDao {

    /**
     * 获取签到信息
     *
     * @param customerId 顾客编号
     * @return 签到信息
     */
    @Select("SELECT " +
            "   `level`, " +
            "   `level_time` " +
            "FROM " +
            "   `campaign_video_sign` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1 ")
    CampaignVideoSign getCampaignVideoSign(long customerId);

    /**
     * 写入签到信息
     *
     * @param campaignVideoSign 签到信息
     */
    @Insert("INSERT INTO `campaign_video_sign`(" +
            "   `customer_id` " +
            ") " +
            "VALUES (" +
            "   #{customerId}" +
            ")")
    void insertCampaignVideoSign(CampaignVideoSign campaignVideoSign);

    /**
     * 更新签到信息
     *
     * @param customerId 顾客编号
     */
    @Update("UPDATE " +
            "   `campaign_video_sign` " +
            "SET " +
            "   `level`=(SELECT COUNT(*) FROM `campaign_video_sign_detail` WHERE `customer_id`=#{customerId}), " +
            "   `level_time`=NOW() " +
            "where " +
            "   `customer_id`=#{customerId}")
    void updateCampaignVideoSign(long customerId);

    /**
     * 写入签到明细
     *
     * @param campaignVideoSignDetail 签到明细
     */
    @Insert("INSERT `campaign_video_sign_detail` (" +
            "   `customer_id`," +
            "   `level`" +
            ")" +
            "SELECT " +
            "   #{customerId}, " +
            "   COUNT(*)+1 " +
            "FROM " +
            "   `campaign_video_sign_detail` " +
            "WHERE `customer_id`=#{customerId}")
    void insertCampaignVideoSignDetail(CampaignVideoSignDetail campaignVideoSignDetail);

    /**
     * 获取视频列表
     *
     * @return 视频列表
     */
    @Select("SELECT " +
            "   `id`," +
            "   `type`," +
            "   `name`," +
            "   `image_url`," +
            "   `video_url` " +
            "FROM " +
            "   `campaign_video_sign_config`")
    List<CampaignVideoSignConfig> getCampaignVideoSignConfig();

    /**
     * 写入访问日志
     *
     * @param campaignVideoSignViewLog 日志
     */
    @Insert("INSERT INTO `campaign_video_sign_view_log`(" +
            "   `customer_id`, " +
            "   `source`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{source}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertCampaignVideoSignViewLog(CampaignVideoSignViewLog campaignVideoSignViewLog);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Update("UPDATE " +
            "   `campaign_video_sign_view_log` " +
            "SET " +
            "   `end_time`=NOW()," +
            "   `diff_second`=TimeStampDiff(SECOND,`start_time`,NOW()) " +
            "WHERE " +
            "   `id`=#{id}")
    void setCampaignVideoSignViewLog(long id);

    /**
     * 写入播放记录
     *
     * @param campaignVideoSignPlayLog 播放记录
     */
    @Insert("INSERT INTO `campaign_video_sign_play_log`(" +
            "   `customer_id`, " +
            "   `config_id`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{configId}" +
            ")")
    void insertCampaignVideoSignPlayLog(CampaignVideoSignPlayLog campaignVideoSignPlayLog);

    /**
     * 写入跳转记录
     *
     * @param campaignVideoSignJumpLog 跳转记录
     */
    @Insert("INSERT INTO `campaign_video_sign_jump_log`(" +
            "   `customer_id`, " +
            "   `config_id`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{configId}" +
            ")")
    void insertCampaignVideoSignJumpLog(CampaignVideoSignJumpLog campaignVideoSignJumpLog);


}
