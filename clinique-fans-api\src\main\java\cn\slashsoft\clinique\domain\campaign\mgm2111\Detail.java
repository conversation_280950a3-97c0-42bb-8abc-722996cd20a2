package cn.slashsoft.clinique.domain.campaign.mgm2111;

import lombok.Data;

import java.util.Date;

/**
 * 活动信息
 *
 * <AUTHOR>
 */
@Data
public class Detail {

    private Long id;
    private Long customerId;
    private String avatarUrl;
    private Boolean sign;
    private Integer signTotal;
    private Date lastSignTime;
    private Boolean signCoupon5;
    private String signCoupon5Code;
    private Date signCoupon5Time;
    private Boolean signCoupon8;
    private String signCoupon8Code;
    private Date signCoupon8Time;
    private Boolean invite;
    private Integer inviteRank;
    private Integer inviteTotal;
    private Date lastInviteTime;
    private Boolean inviteCoupon3;
    private String inviteCoupon3Code;
    private Date inviteCoupon3Time;
    private Boolean inviteCoupon8;
    private String inviteCoupon8Code;
    private Date inviteCoupon8Time;
    private String source;
    private Date updateTime;
    private Date createTime;

}
