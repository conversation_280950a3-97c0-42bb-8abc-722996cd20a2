package cn.slashsoft.clinique.enums;

import lombok.Getter;

/**
 * 商城订单类别
 *
 * <AUTHOR>
 */
public enum StoreExchangeTypeEnum {

    // 兑换类别
    PICKUP              ((short) 1, "门店自提"),
    EXPRESS             ((short) 2, "快递");

    @Getter
    private final short id;

    @Getter
    private final String name;

    StoreExchangeTypeEnum(short id, String name) {
        this.id = id;
        this.name = name;
    }
}
