package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignC520;

/**
 * 520活动
 *
 * <AUTHOR>
 */
public interface CampaignC520Service {

    /**
     * 获取排除名单
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    int getCampaignC520Exclude(String phoneNumber);

    /**
     * 获取520活动信息
     *
     * @param customerId 顾客编号
     * @return 520活动信息
     */
    CampaignC520 getCampaignC520(long customerId);

    /**
     * 申领
     *
     * @param customerId 顾客编号
     * @return 处理结果
     */
    boolean applyCampaignC520(long customerId);

    /**
     * 核销
     *
     * @param customerId 顾客编号
     * @return 处理结果
     */
    boolean receiveCampaignC520(long customerId);

    /**
     * 写入访问开始日志
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @param page       页面
     * @return 访问日志编号
     */
    long insertCampaignC520ViewLog(long customerId, String source, String page);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    void setCampaignC520ViewLog(long id);

}
