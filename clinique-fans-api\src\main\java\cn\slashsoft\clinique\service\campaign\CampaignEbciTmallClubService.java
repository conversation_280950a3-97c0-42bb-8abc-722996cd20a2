package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignEbciDetail;

/**
 * 302美白镭射瓶
 *
 * <AUTHOR>
 */
public interface CampaignEbciTmallClubService {

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    CampaignEbciDetail getDetail(String phoneNumber);

    /**
     * 申领
     *
     * @param campaignEbciDetail 资料
     * @return 0:成功，1，帐户已经领过了，2：手机号码已经领过了，3：库存不足
     */
    int submit(CampaignEbciDetail campaignEbciDetail);

}
