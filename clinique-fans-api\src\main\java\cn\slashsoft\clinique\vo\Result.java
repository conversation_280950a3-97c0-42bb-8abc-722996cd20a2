package cn.slashsoft.clinique.vo;

import lombok.Data;

/**
 * 向页面传递的Data数据
 *
 * <AUTHOR>
 */
@Data
public class Result {

    private int code;
    private String message;
    private String field;
    private Object data;

    public Result() {
    }

    public Result(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public Result(int code, String message, Object data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public Result(int code, String field, String message) {
        this.code = code;
        this.field = field;
        this.message = message;
    }
}
