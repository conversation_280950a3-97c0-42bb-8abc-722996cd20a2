package cn.slashsoft.clinique.domain.mini;

import lombok.Data;

import java.util.Date;

/**
 * 积分变化表
 *
 * <AUTHOR>
 */
@Data
public class PointTransaction {

    private Long id;
    private Long customerId;
    private Short pointTypeId;
    private String pointTypeName;
    private String pointTypeImageUrl;
    private Integer points;
    private Integer remainingPoints;
    private String remark;
    private Date startTime;
    private Date expiredTime;
    private Short foreignId;
    private Long foreignMasterId;
    private Long foreignDetailId;
    private Boolean status;
    private Boolean recovery;
    private Date updateTime;
    private Date createTime;

}
