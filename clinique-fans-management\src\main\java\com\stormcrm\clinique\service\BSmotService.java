package com.stormcrm.clinique.service;

import java.util.List;

import com.stormcrm.clinique.domain.BSmotCampaign;
import com.stormcrm.clinique.domain.BSmotLog;
import com.stormcrm.clinique.domain.BSmotTemplate;

/**
 * 模板消息
 * <AUTHOR>
 */
public interface BSmotService {
    //增加 发送任务
    void addCampaign(BSmotCampaign bmotCampaign);
    //修改 发送任务
    void updateCampaign(BSmotCampaign bmotCampaign);
    
    //获取 全部发送任务
    List<BSmotCampaign> getCampaignList();
    //获取 发送任务
    BSmotCampaign getCampaign(Long id);
    

    //增加 发送任务记录
    void addCampaignLog(BSmotLog bmotCampaignLog);
    //修改 发送任务记录
    void updateCampaignLog(BSmotLog bmotCampaignLog);
    
    //获取 全部发送任务记录
	List<BSmotLog> getCampaignLogList(Long id);
    //获取 发送任务记录
    BSmotLog getCampaignLog(Long id);

    //发送任务 测试 -- 1 开发测试 2 内测 3 公测
    void testCampaign(Long id, Short type);
    //发送任务 执行
    void sendCampaign(Long id);
    
	List<BSmotTemplate> getBSMotTemplateList();
}
