package cn.slashsoft.clinique.service.campaign.impl;

import cn.slashsoft.clinique.dao.campaign.CampaignEbciTalentsDao;
import cn.slashsoft.clinique.domain.campaign.*;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.service.campaign.CampaignEbciTalentsService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.RandomUtil;
import cn.slashsoft.clinique.util.ServerUtil;
import cn.slashsoft.clinique.util.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.Date;
import java.util.List;

/**
 * EBCI 美白达人官
 *
 * <AUTHOR>
 */
@Service
public class CampaignEbciTalentsServiceImpl implements CampaignEbciTalentsService {

    private final CampaignEbciTalentsDao campaignEbciTalentsDao;
    private final OutsideService outsideService;

    public CampaignEbciTalentsServiceImpl(CampaignEbciTalentsDao campaignEbciTalentsDao, OutsideService outsideService) {
        this.campaignEbciTalentsDao = campaignEbciTalentsDao;
        this.outsideService = outsideService;
    }

    /**
     * 获取美白达人官信息
     *
     * @param customerId 顾客编号
     * @return 美白达人官信息
     */
    @Override
    public CampaignEbciTalents getCampaignEbciTalents(long customerId) {

        CampaignEbciTalents campaignEbciTalents = campaignEbciTalentsDao.getCampaignEbciTalentsWithImageCount(customerId);

        if (null == campaignEbciTalents) {
            // 生成活动信息
            campaignEbciTalents = new CampaignEbciTalents();
            campaignEbciTalents.setCustomerId(customerId);
            campaignEbciTalents.setStatus((short) 1);

            campaignEbciTalentsDao.insertCampaignEbciTalents(campaignEbciTalents);

            // 写入日志
            CampaignEbciTalentsLog campaignEbciTalentsLog = new CampaignEbciTalentsLog();
            campaignEbciTalentsLog.setCustomerId(customerId);
            campaignEbciTalentsLog.setStatus((short) 1);
            campaignEbciTalentsLog.setContent("生成活动信息");
            campaignEbciTalentsDao.insertCampaignEbciTalentsLog(campaignEbciTalentsLog);
        }
        else{
            // 状态为未上传时，将所有图片改为无效
            if(1 == campaignEbciTalents.getStatus() && 0 < campaignEbciTalents.getImageCount()){
                campaignEbciTalentsDao.updateCampaignEbciTalentsImageStatus(customerId);
            }
        }

        return campaignEbciTalents;
    }

    /**
     * 更新提醒状态
     *
     * @param customerId 顾客编号
     */
    @Override
    public void updateCampaignEbciTalentsAlert(long customerId) {
        campaignEbciTalentsDao.updateCampaignEbciTalentsAlert(customerId);
    }

    /**
     * 获取图片
     *
     * @param campaignEbciTalentsId 顾客活动信息编号
     * @return 图片地址
     */
    @Override
    public List<CampaignEbciTalentsImage> getCampaignEbciTalentsImageList(long campaignEbciTalentsId) {
        return campaignEbciTalentsDao.getCampaignEbciTalentsImageList(campaignEbciTalentsId);
    }

    /**
     * 获取当前TOP信息
     *
     * @return TOP信息
     */
    @Override
    public CampaignEbciTalentsTop getCampaignEbciTalentsTop() {
        return campaignEbciTalentsDao.getCampaignEbciTalentsTop();
    }

    /**
     * 获取入选TOP的名单
     *
     * @param campaignEbciTalentsTopId TOP信息编号
     * @return 名单
     */
    @Override
    public List<CampaignEbciTalentsTopDetail> getCampaignEbciTalentsTopDetailList(long campaignEbciTalentsTopId) {
        return campaignEbciTalentsDao.getCampaignEbciTalentsTopDetailList(campaignEbciTalentsTopId);
    }

    /**
     * 获取入选TOP的图片
     *
     * @param campaignEbciTalentsTopId TOP信息编号
     * @return 图片
     */
    @Override
    public List<CampaignEbciTalentsTopDetailImage> getCampaignEbciTalentsTopDetailImageList(long campaignEbciTalentsTopId) {
        return campaignEbciTalentsDao.getCampaignEbciTalentsTopDetailImageList(campaignEbciTalentsTopId);
    }

    /**
     * 保存图片
     *
     * @param customerId 顾客编号
     * @param file       要保存的图片
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean upload(long customerId, MultipartFile file) {

        // 读取活动信息
        CampaignEbciTalents campaignEbciTalents = campaignEbciTalentsDao.getCampaignEbciTalentsWithImageCount(customerId);
        if (null == campaignEbciTalents) {
            return false;
        }

        CampaignEbciTalentsLog campaignEbciTalentsLog;
        switch (campaignEbciTalents.getStatus()) {
            // 未上传
            case 1:
                // 变更状态为2审核中
                if (0 == campaignEbciTalentsDao.updateCampaignEbciTalentsWithUploadTime(customerId, (short) 1, (short) 2)) {
                    return false;
                }
                // 写入日志
                campaignEbciTalentsLog = new CampaignEbciTalentsLog();
                campaignEbciTalentsLog.setCustomerId(customerId);
                campaignEbciTalentsLog.setStatus((short) 2);
                campaignEbciTalentsLog.setContent("状态从未上传变更为审核中");
                campaignEbciTalentsDao.insertCampaignEbciTalentsLog(campaignEbciTalentsLog);
                break;
            // 审核中
            case 2:
                // 检查当前相片的数量，超过2则不再保存
                if (2 <= campaignEbciTalents.getImageCount()) {
                    return false;
                }
                break;
            // 审核失败
            case 3:
                // 变更状态为2审核中
                if (0 == campaignEbciTalentsDao.updateCampaignEbciTalents(customerId, (short) 3, (short) 2)) {
                    return false;
                }
                // 原上传图片全部设为无效
                campaignEbciTalentsDao.updateCampaignEbciTalentsImageStatus(campaignEbciTalents.getId());
                // 写入日志
                campaignEbciTalentsLog = new CampaignEbciTalentsLog();
                campaignEbciTalentsLog.setCustomerId(customerId);
                campaignEbciTalentsLog.setStatus((short) 2);
                campaignEbciTalentsLog.setContent("状态从审核未通过变更为审核中");
                campaignEbciTalentsDao.insertCampaignEbciTalentsLog(campaignEbciTalentsLog);
                break;
            default:
                return false;
        }

        // 保存图片
        String imageUrl = outsideService.uploadImageOss(file);
        if(StringUtil.isNullOrEmpty(imageUrl)){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }

        // 保存数据库
        CampaignEbciTalentsImage campaignEbciTalentsImage = new CampaignEbciTalentsImage();
        campaignEbciTalentsImage.setCampaignEbciTalentsId(campaignEbciTalents.getId());
        campaignEbciTalentsImage.setImageUrl(imageUrl);
        campaignEbciTalentsDao.insertCampaignEbciTalentsImage(campaignEbciTalentsImage);

        // 写入日志
        campaignEbciTalentsLog = new CampaignEbciTalentsLog();
        campaignEbciTalentsLog.setCustomerId(customerId);
        campaignEbciTalentsLog.setStatus((short) 2);
        campaignEbciTalentsLog.setContent("上传图片");
        campaignEbciTalentsDao.insertCampaignEbciTalentsLog(campaignEbciTalentsLog);

        return true;
    }

    /**
     * 保存图片
     *
     * @param path 目录
     * @param originalFile 要保存的图片
     * @return 保存成功的文件地址
     */
    private String saveImageFile(String path, MultipartFile originalFile) {

        try {
            Date now = new Date();

            // 获取静态资源文件的目录地址
            String staticPath = ServerUtil.getStaticPath();

            // 保存位置
            path = path + DateUtil.parseString(now, "yyyyMMdd");
            // 如果目录不存在，创建目录
            File folder = new File(staticPath + path);
            if (!folder.exists()) {
                if (folder.mkdirs()) {
                    throw new Exception("创建目录失败");
                }
            }

            // 获取上传文件名的后缀名
            String originalFileName = originalFile.getOriginalFilename();
            if (StringUtil.isNullOrEmpty(originalFileName)) {
                throw new Exception("上传的文件名为空");
            }
            // 生成文件名
            String fileName = path + "/" + DateUtil.parseString(now, "HHmmssSS") + RandomUtil.getNumberBetween(1000, 9999) + originalFileName.substring(originalFileName.lastIndexOf("."));

            File imageFile = new File(staticPath + fileName);

            originalFile.transferTo(imageFile);

            return fileName;
        } catch (Exception e) {
            return null;
        }

    }

    /**
     * 写入访问开始日志
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @param page       页面
     * @return 访问日志编号
     */
    @Override
    public long insertCampaignEbciTalentsViewLog(long customerId, String source, String page) {
        CampaignEbciTalentsViewLog campaignEbciTalentsViewLog = new CampaignEbciTalentsViewLog();
        campaignEbciTalentsViewLog.setCustomerId(customerId);
        campaignEbciTalentsViewLog.setSource(source);
        campaignEbciTalentsViewLog.setPage(page);
        campaignEbciTalentsDao.insertCampaignEbciTalentsViewLog(campaignEbciTalentsViewLog);
        return campaignEbciTalentsViewLog.getId();
    }

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Override
    public void setCampaignEbciTalentsViewLog(long id) {
        campaignEbciTalentsDao.setCampaignEbciTalentsViewLog(id);
    }
}
