package com.stormcrm.clinique.service;

import com.stormcrm.clinique.domain.CampaignLaserTalentsTop;
import com.stormcrm.clinique.vo.Result;

import java.util.List;

/**
 * 新品体验官活动TOP
 *
 * <AUTHOR>
 */
public interface CampaignLaserTalentsTopService {

    /**
     * 查询所有活动
     *
     * @return 活动列表
     */
    List<CampaignLaserTalentsTop> getAll();

    /**
     * 查询信息
     *
     * @param id 自动编号
     * @return 信息
     */
    CampaignLaserTalentsTop getById(long id);

    /**
     * 保存
     *
     * @param campaignLaserTalentsTop 信息
     * @return 影响的行数
     */
    Result save(CampaignLaserTalentsTop campaignLaserTalentsTop);

    /**
     * 更新
     *
     * @param campaignLaserTalentsTop 信息
     * @return 影响的行数
     */
    Result update(CampaignLaserTalentsTop campaignLaserTalentsTop);

}
