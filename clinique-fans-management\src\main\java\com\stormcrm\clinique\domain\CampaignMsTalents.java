package com.stormcrm.clinique.domain;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 新品体验官活动
 *
 * <AUTHOR>
 */
@Data
public class CampaignMsTalents {

    private Long id;
    private String wechatMiniOpenid;
    private Long customerId;
    private String nickName;
    private String avatarUrl;
    private Short status;
    private Date uploadTime;
    private Date examineTime;
    private Date createTime;

    private List<CampaignMsTalentsImage> imageList;

}
