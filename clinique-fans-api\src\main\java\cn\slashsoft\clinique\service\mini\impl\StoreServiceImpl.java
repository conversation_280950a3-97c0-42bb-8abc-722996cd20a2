package cn.slashsoft.clinique.service.mini.impl;

import java.util.Date;
import java.util.List;

import cn.slashsoft.clinique.client.MemberClient;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import cn.slashsoft.clinique.dao.mini.PointDao;
import cn.slashsoft.clinique.dao.mini.StoreDao;
import cn.slashsoft.clinique.domain.mini.PointTransaction;
import cn.slashsoft.clinique.domain.mini.PointTransactionDeduct;
import cn.slashsoft.clinique.domain.mini.StoreArea;
import cn.slashsoft.clinique.domain.mini.StoreAreaPlace;
import cn.slashsoft.clinique.domain.mini.StoreExchange;
import cn.slashsoft.clinique.domain.mini.StoreExchangeExpress;
import cn.slashsoft.clinique.domain.mini.StoreExchangeLog;
import cn.slashsoft.clinique.domain.mini.StoreExchangePickup;
import cn.slashsoft.clinique.domain.mini.StoreGift;
import cn.slashsoft.clinique.domain.mini.ViewLog;
import cn.slashsoft.clinique.enums.PointForeignEnum;
import cn.slashsoft.clinique.enums.PointTypeEnum;
import cn.slashsoft.clinique.enums.StoreExchangeLogisticsEnum;
import cn.slashsoft.clinique.enums.StoreExchangeTypeEnum;
import cn.slashsoft.clinique.service.mini.StoreService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.RandomUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.Result;
import cn.slashsoft.clinique.vo.mini.Exchange;

import javax.annotation.Resource;

/**
 * 商城相关的服务
 *
 * <AUTHOR>
 */
@Service
public class StoreServiceImpl implements StoreService {

    @Resource
    private MemberClient memberClient;

    @Resource
    private StoreDao storeDao;

    @Resource
    private PointDao pointDao;

    /**
     * 获取有效的商城礼品列表，按更新时间倒序排列
     *
     * @param customerId 顾客编号
     * @return 商城礼品列表
     */
    @Override
    public List<StoreGift> getStoreGiftList(long customerId) {
    	List<StoreGift> storeGiftList = storeDao.getStoreGiftList(customerId);
    	for(StoreGift storeGift : storeGiftList) {
    		this.checkDiscount(storeGift);
    	}
        return storeGiftList;
    }

    /**
     * 读取所有门店区域
     *
     * @return 区域
     */
    @Override
    public List<StoreArea> getStoreAreaList() {
        return storeDao.getStoreAreaList();
    }

    /**
     * 读取所有门店区域提货点
     *
     * @return 提货点
     */
    @Override
    public List<StoreAreaPlace> getStoreAreaPlaceList() {
        return storeDao.getStoreAreaPlaceList();
    }

    /**
     * 跟据礼品编号获取礼品资料，和已兑礼数量
     *
     * @param id         礼品编号
     * @param customerId 顾客编号
     * @return 礼品资料
     */
    @Override
    public StoreGift getStoreGiftById(long id, long customerId) {
    	StoreGift gift = storeDao.getStoreGiftById(id, customerId);
    	this.checkDiscount(gift);
        return gift;
    }

    /**
     * 立即兑换
     *
     * @param unionid 开放平台唯一编号
     * @param exchange              兑换信息
     * @param storeExchangeTypeEnum 兑换类型
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result exchange(String unionid, Exchange exchange, StoreExchangeTypeEnum storeExchangeTypeEnum) {

        // 读取兑换礼口的详情
        StoreGift storeGift = storeDao.getStoreGiftForExchangeById(exchange.getId(), exchange.getCustomerId());

        // 礼品不存在或已下架
        if (null == storeGift) {
            return ResultUtil.customer(2, "礼品不存在或已下架");
        }

        // 判断礼品库存是否够
        if (1 > storeGift.getStocks()) {
            return ResultUtil.customer(2, "库存不足");
        }

        // 同一礼品兑换不能超过3次
        if (3 <= storeGift.getExchangeTotal()) {
            return ResultUtil.customer(3, "每件礼品至多兑换3件");
        }

        if(null == storeGift.getCouponId() || 0 >= storeGift.getCouponId()){
            return ResultUtil.customer(3, "礼品信息配置有误");
        }

        this.checkDiscount(storeGift);

        // 兑换的积分
        int points = storeGift.getPoints();

        // 读取有效的可用积分列表
        List<PointTransaction> pointTransactionList = pointDao.getRemainingPointTransaction(exchange.getCustomerId());

        // 统计合计
        int pointsTotal = pointTransactionList.parallelStream().mapToInt(PointTransaction::getRemainingPoints).sum();

        // 验证积分数量
        if (points > pointsTotal) {
            return ResultUtil.customer(4, "积分不足");
        }

        // 扣除库存
        if (0 == storeDao.updateStoreGiftStocksById(exchange.getId())) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResultUtil.customer(2, "库存不足");
        }

        // 写入兑换表
        StoreExchange storeExchange = new StoreExchange();
        storeExchange.setExchangeOrder(RandomUtil.getOrderNumber());
        storeExchange.setCustomerId(exchange.getCustomerId());
        storeExchange.setStoreGiftId(exchange.getId());
        storeExchange.setStoreExchangeTypeId(storeExchangeTypeEnum.getId());
        switch (storeExchangeTypeEnum) {
            case PICKUP:
                storeExchange.setStoreExchangeLogisticsId(StoreExchangeLogisticsEnum.WAITING_RECEIVE.getId());
                break;
            case EXPRESS:
                storeExchange.setStoreExchangeLogisticsId(StoreExchangeLogisticsEnum.WAITING_EXPRESS.getId());
                break;
            default:
                break;
        }
        storeExchange.setPoints(points);
        storeDao.insertStoreExchange(storeExchange);

        switch (storeExchangeTypeEnum) {
            case PICKUP:
                // 门店自提
                StoreExchangePickup storeExchangePickup = new StoreExchangePickup();
                storeExchangePickup.setStoreExchangeId(storeExchange.getId());
                storeExchangePickup.setStoreAreaId(exchange.getAreaId());
                storeExchangePickup.setStoreAreaPlaceId(exchange.getAreaPlaceId());
                storeDao.insertStoreExchangePickup(storeExchangePickup);
                break;
            case EXPRESS:
                // 快递
                StoreExchangeExpress storeExchangeExpress = new StoreExchangeExpress();
                storeExchangeExpress.setStoreExchangeId(storeExchange.getId());
                storeExchangeExpress.setCustomerAddressId(exchange.getCustomerAddressId());
                storeDao.insertStoreExchangeExpress(storeExchangeExpress);
                break;
            default:
                break;
        }

        Date now = new Date();

        // 积分扣除记录
        PointTransaction pointTransaction = new PointTransaction();
        pointTransaction.setCustomerId(exchange.getCustomerId());
        pointTransaction.setPointTypeId(PointTypeEnum.EXCHANGE.getId());
        pointTransaction.setPoints(-points);
        pointTransaction.setRemainingPoints(PointTypeEnum.EXCHANGE.getPoints());
        pointTransaction.setStartTime(now);
        pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
        pointTransaction.setForeignId(PointForeignEnum.EXCHANGE.getId());
        pointTransaction.setForeignMasterId(0L);
        pointTransaction.setForeignDetailId(storeExchange.getId());
        pointDao.insertPointTransaction(pointTransaction);

        // 扣除积分
        for (PointTransaction p : pointTransactionList) {
            // 当笔积分不够，则扣除当笔所有剩余积分
            // 当笔积分够，则扣除总积分的剩余积分
            int deduct = points >= p.getRemainingPoints() ? p.getRemainingPoints() : points;
            // 更新积分明细中的剩余积分，当笔剩余积分-扣除积分
            // 失败则回滚
            if (0 == pointDao.updateRemainingPoints(p.getId(), p.getRemainingPoints(), p.getRemainingPoints() - deduct)) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return ResultUtil.customer(5, "系统繁忙，请稍后再试");
            }

            // 写入积分使用明细表
            PointTransactionDeduct pointTransactionDeduct = new PointTransactionDeduct();
            pointTransactionDeduct.setPointTransactionId(pointTransaction.getId());
            pointTransactionDeduct.setDeductPointTransactionId(p.getId());
            pointTransactionDeduct.setPoints(deduct);
            pointDao.insertPointTransactionDeduct(pointTransactionDeduct);

            // 总积分扣除已扣除的积分
            points -= deduct;

            // 总积分为0时，扣除完成
            if (0 == points) {
                break;
            }

        }

        switch (storeExchangeTypeEnum) {
            case PICKUP:
                // 门店自提
                JSONObject response = JSON.parseObject(memberClient.receiveWithStoreForC(unionid, storeGift.getCouponId(), exchange.getAreaPlaceCode()));
                if(null == response || 200 != response.getIntValue("code")){
                    return ResultUtil.customer(3, "发放礼品优惠券失败");
                }
                break;
            case EXPRESS:
                // 快递
                break;
            default:
                break;
        }

        return ResultUtil.customer(1, "兑换成功");
    }

    /**
     * 获取兑换记录
     *
     * @param customerId 顾客编号
     * @return 兑换记录
     */
    @Override
    public List<StoreExchange> getStoreExchangeByCustomer(long customerId) {
        return storeDao.getStoreExchangeByCustomer(customerId);
    }

    /**
     * 写入日志
     *
     * @param storeExchangeLog 日志
     */
    @Override
    public void setStoreExchangeLog(StoreExchangeLog storeExchangeLog) {
        storeDao.insertStoreExchangeLog(storeExchangeLog);
    }

    /**
     * 写入商城列表页访问来源的日志
     *
     * @param source 来源
     * @param openid 微信小程序唯一编号
     */
    @Override
    public void setViewLog(String source, String openid) {
        ViewLog viewLog = new ViewLog();
        viewLog.setWechatMiniOpenid(openid);
        viewLog.setSource(source);
        storeDao.insertViewLog(viewLog);
    }

    private void checkDiscount(StoreGift storeGift) {
		if(storeGift.getHalfPrice() != null && storeGift.getHalfPrice() > 0) {
			storeGift.setWholePoints(storeGift.getPoints());
			storeGift.setPoints(storeGift.getHalfPrice());
		}
	}
}
