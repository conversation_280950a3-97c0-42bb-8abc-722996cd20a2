package cn.slashsoft.cliniqueapi;

import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.EncryptUtil;

import cn.slashsoft.clinique.util.HttpUtil;
import cn.slashsoft.clinique.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class UtilsTests {

    @Test
    public void getLastSecondOfYearTest(){

        System.out.println(DateUtil.parseString(DateUtil.getLastSecondOfYear(new Date())));

    }

    @Test
    public void getLastDayOfMonthTest(){

        Date january = DateUtil.valueOf("2019-01-03 17:24:31");
        Date february = DateUtil.valueOf("2019-02-03 17:24:31");
        Date march = DateUtil.valueOf("2019-03-03 17:24:31");
        Date april = DateUtil.valueOf("2019-04-03 17:24:31");
        Date may = DateUtil.valueOf("2019-05-03 17:24:31");
        Date june = DateUtil.valueOf("2019-06-03 17:24:31");
        Date july = DateUtil.valueOf("2019-07-03 17:24:31");
        Date august = DateUtil.valueOf("2019-08-03 17:24:31");
        Date september = DateUtil.valueOf("2019-09-03 17:24:31");
        Date october = DateUtil.valueOf("2019-10-03 17:24:31");
        Date november = DateUtil.valueOf("2019-11-03 17:24:31");
        Date december = DateUtil.valueOf("2019-12-03 17:24:31");

        System.out.println(DateUtil.parseString(DateUtil.getLastDayOfMonth(january)));
        System.out.println(DateUtil.parseString(DateUtil.getLastDayOfMonth(february)));
        System.out.println(DateUtil.parseString(DateUtil.getLastDayOfMonth(march)));
        System.out.println(DateUtil.parseString(DateUtil.getLastDayOfMonth(april)));
        System.out.println(DateUtil.parseString(DateUtil.getLastDayOfMonth(may)));
        System.out.println(DateUtil.parseString(DateUtil.getLastDayOfMonth(june)));
        System.out.println(DateUtil.parseString(DateUtil.getLastDayOfMonth(july)));
        System.out.println(DateUtil.parseString(DateUtil.getLastDayOfMonth(august)));
        System.out.println(DateUtil.parseString(DateUtil.getLastDayOfMonth(september)));
        System.out.println(DateUtil.parseString(DateUtil.getLastDayOfMonth(october)));
        System.out.println(DateUtil.parseString(DateUtil.getLastDayOfMonth(november)));
        System.out.println(DateUtil.parseString(DateUtil.getLastDayOfMonth(december)));

    }

    @Test
    public void getLastSecondOfMonthAndNextYear(){

        Date january = DateUtil.valueOf("2019-01-03 17:24:31");
        Date february = DateUtil.valueOf("2019-02-03 17:24:31");
        Date march = DateUtil.valueOf("2019-03-03 17:24:31");
        Date april = DateUtil.valueOf("2019-04-03 17:24:31");
        Date may = DateUtil.valueOf("2019-05-03 17:24:31");
        Date june = DateUtil.valueOf("2019-06-03 17:24:31");
        Date july = DateUtil.valueOf("2019-07-03 17:24:31");
        Date august = DateUtil.valueOf("2019-08-03 17:24:31");
        Date september = DateUtil.valueOf("2019-09-03 17:24:31");
        Date october = DateUtil.valueOf("2019-10-03 17:24:31");
        Date november = DateUtil.valueOf("2019-11-03 17:24:31");
        Date december = DateUtil.valueOf("2019-12-03 17:24:31");

        System.out.println(DateUtil.parseString(DateUtil.getLastSecondOfMonthAndNextYear(january)));
        System.out.println(DateUtil.parseString(DateUtil.getLastSecondOfMonthAndNextYear(february)));
        System.out.println(DateUtil.parseString(DateUtil.getLastSecondOfMonthAndNextYear(march)));
        System.out.println(DateUtil.parseString(DateUtil.getLastSecondOfMonthAndNextYear(april)));
        System.out.println(DateUtil.parseString(DateUtil.getLastSecondOfMonthAndNextYear(may)));
        System.out.println(DateUtil.parseString(DateUtil.getLastSecondOfMonthAndNextYear(june)));
        System.out.println(DateUtil.parseString(DateUtil.getLastSecondOfMonthAndNextYear(july)));
        System.out.println(DateUtil.parseString(DateUtil.getLastSecondOfMonthAndNextYear(august)));
        System.out.println(DateUtil.parseString(DateUtil.getLastSecondOfMonthAndNextYear(september)));
        System.out.println(DateUtil.parseString(DateUtil.getLastSecondOfMonthAndNextYear(october)));
        System.out.println(DateUtil.parseString(DateUtil.getLastSecondOfMonthAndNextYear(november)));
        System.out.println(DateUtil.parseString(DateUtil.getLastSecondOfMonthAndNextYear(december)));

    }

    @Test
    public void getOrderNumberTest(){

        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());
        System.out.println(RandomUtil.getOrderNumber());

    }

    @Test
    public void unionidEncodeTest(){

        String unionid = "oXzhX4yestRG9MC_WjXeMADET0DM";
        System.out.println("原始：" + unionid);
        String unionidEncode = EncryptUtil.unionidEncode(unionid);
        System.out.println("加密：" + unionidEncode);
        String unionidDecode = EncryptUtil.unionidDecode(unionidEncode);
        System.out.println("解密：" + unionidDecode);
        assert unionid.equals(unionidDecode);

    }

    @Test
    public void randomText(){
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
        System.out.println(RandomUtil.getNumberBetween(0,4));
    }

    @Test
    public void couponTest() throws IOException {

        String urlGetCoupon = "https://cliniquetest.elcapp.cn/member/open-api/coupon/customer-get?";
        String couponSecret = "fs34$UJ7MV4vKYw45afert5$^O$JI4Ok3DLoFdsFUYFKM564KS";

        Map<String, String> params = new HashMap<>();
        params.put("unionid", "olSYu1cPL9VAUIlWwHzhnKvXu7V8");
        params.put("id", "2");
        params.put("time", DateUtil.parseString(new Date()));
        params.put("secret", RandomUtil.getVerifyCode());
        params.put("source", "slash");

        String param = EncryptUtil.couponSign(params, couponSecret);

        System.out.println("调用发券接口请求地址：" + urlGetCoupon + param);

        JSONObject response = JSON.parseObject(HttpUtil.get(urlGetCoupon + param));

        System.out.println("调用发券接口返回值：" + response.toJSONString());

    }



}
