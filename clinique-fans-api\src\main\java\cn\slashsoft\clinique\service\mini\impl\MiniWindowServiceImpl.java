package cn.slashsoft.clinique.service.mini.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import cn.slashsoft.clinique.dao.mini.ActionDao;
import cn.slashsoft.clinique.dao.mini.MiniWindowDao;
import cn.slashsoft.clinique.domain.mini.MiniWindow;
import cn.slashsoft.clinique.service.mini.MiniWindowService;

/**
 *
 * <AUTHOR>
 */
@Service
public class MiniWindowServiceImpl implements MiniWindowService {

	@Resource
	MiniWindowDao miniWindowDao;
	
	@Resource
	ActionDao actionDao;	
	
	@Override
	public MiniWindow getById(Long id) {
		return miniWindowDao.getById(id);
	}
	
	@Override
	public boolean actionedShow(MiniWindow window, String miniOpenid) {
		return actionDao.getActionCount(window.getShowActionId() , miniOpenid)>0;
	}

	@Override
	public boolean actionedClose(MiniWindow window, String miniOpenid) {
		return actionDao.getActionCount(window.getCloseActionId(), miniOpenid)>0;
	}

	@Override
	public boolean actionedGo(MiniWindow window, String miniOpenid) {
		return actionDao.getActionCount(window.getButtonActionId(), miniOpenid)>0;
	}

   
}
