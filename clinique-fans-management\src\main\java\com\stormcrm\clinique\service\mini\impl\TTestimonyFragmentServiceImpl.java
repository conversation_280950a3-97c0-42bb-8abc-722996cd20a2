package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.dao.TTestimonyFragmentDao;

import com.stormcrm.clinique.service.TTestimonyFragmentService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
* <p>
    * 记录获得碎片信息
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
@Service
public class TTestimonyFragmentServiceImpl implements TTestimonyFragmentService {

    @Resource
    private TTestimonyFragmentDao tTestimonyFragmentDao;

    /**
     * 是否是koc用户
     * @param customerId
     * @return
     */
    public Boolean isKocUser(Long customerId) {
        Integer cnt = tTestimonyFragmentDao.isKocUser(customerId);
        return  cnt>0 ? true: false;
    }
}
