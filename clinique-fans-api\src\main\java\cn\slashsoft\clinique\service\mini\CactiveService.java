package cn.slashsoft.clinique.service.mini;

import java.util.HashMap;
import java.util.List;

import cn.slashsoft.clinique.domain.mini.CactiveRank;
import cn.slashsoft.clinique.enums.CActiveTypeEnum;

/**
 * 笔记
 *
 * <AUTHOR>
 */
public interface CactiveService {
 
	List<CactiveRank> getRank(int length);
	int getCustomerRank(Long customerId);
	int getRankCount();
	int getCustomerAll(Long customerId);
	
	int addPoints(Long customerId, CActiveTypeEnum cactiveType, Long masterId, Long detailId, String remark);
	
	HashMap<String, String> getRankDetail(Long customerId);
	
}
