package com.stormcrm.clinique.oauth.service;

import java.util.ArrayList;
import java.util.List;

import org.springframework.core.env.Environment;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import com.stormcrm.clinique.dao.oauth.OauthPermissionDao;
import com.stormcrm.clinique.dao.oauth.OauthPhoneCodeDao;
import com.stormcrm.clinique.dao.oauth.OauthUserDao;
import com.stormcrm.clinique.oauth.domain.OauthPermission;
import com.stormcrm.clinique.oauth.domain.OauthPhoneCode;
import com.stormcrm.clinique.oauth.domain.OauthUser;
import com.stormcrm.clinique.oauth.phone.PhoneNumberNotFoundException;
import com.stormcrm.clinique.oauth.phone.PhoneUserDetailsService;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
//@Service
//@Slf4j
public class OauthPhoneService implements PhoneUserDetailsService {

    private final Environment environment;

    private final OauthPermissionDao oauthPermissionDao;

    private final OauthUserDao oauthUserDao;

    private final OauthPhoneCodeDao oauthPhoneCodeDao;

    public OauthPhoneService(Environment environment, OauthPermissionDao oauthPermissionDao, OauthUserDao oauthUserDao, OauthPhoneCodeDao oauthPhoneCodeDao) {
        this.environment = environment;
        this.oauthPermissionDao = oauthPermissionDao;
        this.oauthUserDao = oauthUserDao;
        this.oauthPhoneCodeDao = oauthPhoneCodeDao;
    }


    @Override
    public boolean consumeCode(String number, String code) {
        return forTestingAndDevEnv(code) || isCorrect(number, code);
    }

    @Override
    public UserDetails loadUserByNumber(String number) {
        // 跟据number查找用户信息
        OauthUser oauthUser = oauthUserDao.getByPhone(number);

        // 未找到用户，抛出用户number未找到异常
        if (null == oauthUser) {
            throw new PhoneNumberNotFoundException(number);
        }

        // 权限集合
        List<GrantedAuthority> grantedAuthorities = new ArrayList<>();
        List<OauthPermission> oauthPermissions = oauthPermissionDao.getByUserId(oauthUser.getId());
        oauthPermissions.forEach(oauthPermission -> {
            GrantedAuthority grantedAuthority = new SimpleGrantedAuthority(oauthPermission.getMark());
            grantedAuthorities.add(grantedAuthority);
        });

        // 返回
        return new User(oauthUser.getUsername(), oauthUser.getPassword(), oauthUser.getStatus(), true, true, !oauthUser.getLocked(), grantedAuthorities);

    }

    /**
     * @param number 手机号
     * @param code   验证码
     * @return 是否正确
     */
    private boolean isCorrect(String number, String code) {
        // 【未完成】 系统配置表，如果配置为不使用短信，
        OauthPhoneCode userCode = this.oauthPhoneCodeDao.getPhoneValidCode(number, PhoneSmsCodeService.CODE_EXPIRED);
        if (userCode != null) {
            // times 减1
            this.oauthPhoneCodeDao.countDownTimes(userCode);
            // 比对code
            return userCode.getCode().equals(code);
        } else {
            return false;
        }
    }

    /**
     * 默认一个超级code 如果不是生产环境，直接返回 true
     *
     * @param code 验证码
     * @return 返回匹配结果
     */
    private boolean forTestingAndDevEnv(String code) {
        return notProductEnv() && useSuperCode(code);
    }

    private boolean notProductEnv() {
        return !this.environment.getActiveProfiles()[0].equals("prod");
    }

    private boolean useSuperCode(String code) {
        return "123456".equals(code);
    }


}
