package cn.slashsoft.clinique.controller.mini;

import cn.slashsoft.clinique.domain.mini.SmotAuthorize;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.SmotService;
import cn.slashsoft.clinique.util.ResultUtil;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 一次性订阅消息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class SmotController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final SmotService smotService;

    public SmotController(SmotService smotService) {
        this.smotService = smotService;
    }

    /**
     * 写入一次性订阅消息
     *
     * @param type 类型
     * @param choice 选择
     * @return 成功
     */
    @PostMapping("/set-smot-authorize/{type}/{choice}")
    public String setSmotAuthorize(
            @PathVariable("type") short type,
            @PathVariable("choice") boolean choice
    ) {
        // 获取缓存中的顾客编号
        String openid = (String) request.getAttribute("miniOpenid");

        SmotAuthorize smotAuthorize = new SmotAuthorize();
        smotAuthorize.setType(type);
        smotAuthorize.setWechatMiniOpenid(openid);
        smotAuthorize.setChoice(choice);

        smotService.insertSmotAuthorize(smotAuthorize);

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 获取一次性订阅消息
     *
     * @param type 类型
     * @param choice 选择
     * @return 成功
     */
    @PostMapping("/get-smot-authorize/{type}/{choice}")
    public String getSmotAuthorize(
            @PathVariable("type") short type,
            @PathVariable("choice") boolean choice
    ) {
        // 获取缓存中的顾客编号
        String openid = (String) request.getAttribute("miniOpenid");
        int count = smotService.getSmotAuthorize(type, openid, choice);

        return ResultUtil.customer(ResultEnum.SUCCESS, count);
    }

    /**
     * 获取一次性订阅消息
     *
     * @param type 类型
     * @return 成功
     */
    @PostMapping("/get-smot-authorize/{type}")
    public String getSmotAuthorize(
            @PathVariable("type") short type
    ) {
        // 获取缓存中的顾客编号
        String openid = (String) request.getAttribute("miniOpenid");
        int count = smotService.getSmotAuthorize(type, openid);

        return ResultUtil.customer(ResultEnum.SUCCESS, count);
    }

}
