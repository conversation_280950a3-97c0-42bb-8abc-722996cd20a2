package com.stormcrm.clinique.controller.tag;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.stormcrm.clinique.domain.tag.TagTask;
import com.stormcrm.clinique.enums.ResultEnum;
import com.stormcrm.clinique.service.tag.LabelService;
import com.stormcrm.clinique.service.tag.RuleService;
import com.stormcrm.clinique.service.tag.TagTaskService;
import com.stormcrm.clinique.util.ResultUtil;


/**
 * 标签包任务管理
 *
 * <AUTHOR>
 */


@Controller
@RequestMapping("/tagtask")
public class TagTaskManageController {

	@Resource
	RuleService filterService;

	@Resource
	LabelService labelService;
	
	@Resource
	TagTaskService tagTaskService;

	@Value("${wechat.resources.static-domain}")
	private String staticDomain;
    

    @PreAuthorize("hasAuthority('TAGS_TASK')")
	@RequestMapping("/index/{type}/{taskId}")
	public String index(Model model,
			@PathVariable String type,
			@PathVariable Long taskId) {
        model.addAttribute("staticDomain", staticDomain);
        model.addAttribute("tagId", taskId);
    	model.addAttribute("title", "任务");
    	model.addAttribute("name", "TASK");
        switch(type) {
        case "mot":
        	model.addAttribute("name", "MOT");
        	model.addAttribute("title", "微信模板消息任务");
        	break;
		case "smot":
			model.addAttribute("name", "SMOT");
			model.addAttribute("title", "微信服务通知任务");
			break;
        case "sms":
        	model.addAttribute("name", "SMS");
        	model.addAttribute("title", "短信发送任务");
        	break;
        case "coupon":
        	model.addAttribute("name", "COUPON");
        	model.addAttribute("title", "卡券推送任务");
        	break;
        }
        return "m/fans/bi/task";
	}
	@PreAuthorize("hasAuthority('TAGS_TASK')")
	@RequestMapping("/add/{type}/{labelId}")
	public String add(Model model,
						@PathVariable String type,
						@PathVariable Long labelId) {
		model.addAttribute("staticDomain", staticDomain);
		model.addAttribute("labelId", labelId);
		model.addAttribute("title", "新增任务");
		model.addAttribute("name", "TASK");
		model.addAttribute("tagId", 1);
		switch (type) {
			case "MOT":
				model.addAttribute("name", "MOT");
				model.addAttribute("tagId", 3);
				model.addAttribute("title", "新增微信模板消息任务");
				break;
			case "SMOT":
				model.addAttribute("name", "SMOT");
				model.addAttribute("tagId", 4);
				model.addAttribute("title", "新增微信服务通知任务");
				break;
			case "SMS":
				model.addAttribute("name", "SMS");
				model.addAttribute("tagId", 2);
				model.addAttribute("title", "新增短信发送任务");
				break;
			case "COUPON":
				model.addAttribute("name", "COUPON");
				model.addAttribute("tagId", 1);
				model.addAttribute("title", "新增卡券推送任务");
				break;
		}
		return "m/fans/bi/task_add";
	}

	@PreAuthorize("hasAuthority('TAGS_TASK')")
	@PostMapping("/edit-submit")
	@ResponseBody
	public String editSubmit(TagTask tagTask) {
		return ResultUtil.customer(ResultEnum.SUCCESS, "ok", tagTaskService.addTagTask(tagTask));
	}
	

}
