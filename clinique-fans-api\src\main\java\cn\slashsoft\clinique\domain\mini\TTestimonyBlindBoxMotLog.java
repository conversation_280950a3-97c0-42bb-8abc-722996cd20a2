package cn.slashsoft.clinique.domain.mini;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 盲盒mot消息log
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TTestimonyBlindBoxMotLog implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;
    private Long customerId;

    private Integer storeId;
    private String storeName;

    private Integer productId;
    private String productName;
    private String productPwd;
    private String motText;

    private Date createTime;
    private Date updateTime;
}
