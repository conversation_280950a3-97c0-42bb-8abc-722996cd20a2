eureka:
  client:
    service-url:
      # 注册中心地址
      defaultZone: http://127.0.0.1:9111/eureka

spring:
  datasource:
    # DRUID 数据库连接池
    druid:
      url: ********************************************************************************************************************************************************************************************************************************************
      username: root
      password: storm

  # REDIS
  redis:
    database: 14
    host: ************
    port: 6379
    password: '@slAsHsOft&'

mybatis:
  configuration:
    # 日志中打印SQL语句
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  file: log/${spring.application.name}.log
  level:
    # 显示日志的级别
    root: info
    org.mybatis: debug
    java.sql: debug
    org.springframework.web: trace

member:
  get-customer-by-openid: http://127.0.0.1/member/api/customer/get-customer-by-openid?openid=
  get-customer-by-unionid: http://127.0.0.1/member/api/customer/get-customer-by-unionid?unionid=
  get-customer-by-phone: http://127.0.0.1/member/api/customer/get-customer-by-phone?phone=
  
wechat:
  # 静态资源域名
  resources:
    static-domain: /
    oss-domain: https://cl-wechat-mini.oss-cn-shanghai.aliyuncs.com/
  mini:
    appid: wx93d4fc2221a8408d
    appsecret: f69563b2193c6e87f5d6a961a927ef0f
    # 由于小程序的AccessToken每获取一次，原Token就失效了，增加是否启动获取AccessToken选项
    get-access-token: false

  official:
    authorize: https://cliniquewechat.elcapp.cn/wechat-api/app/oauthV2/authorize?appid=82&redirect_uri=
    follow: https://cliniquewechat.elcapp.cn/wechat-api/app/fans/info?appid=82&openid=
    access-token: https://cliniquewechat.elcapp.cn/wechat-api/app/service/wechatbasetoken?appid=82
    jssdk: https://cliniquewechat.elcapp.cn/wechat-api/app/service/jssignature?appid=82&url=
    template-send: https://cliniquewechat.elcapp.cn/wechat-api/app/templatemsg/send?appid=82&
    template-send-with-mp: https://cliniquewechat.elcapp.cn/wechat-api/app/templatemsg/SendMiniprogram?appid=82&
    message-send: https://cliniquewechat.elcapp.cn/wechat-api/app/groupmessage/sendtextmessage?appid=82&content=CONTENT&openid=OPENID
    member-bind: https://cliniquewechat.elcapp.cn/wechat-api/app/member/minibind?appid=82&openid=
    unionid-to-openid: https://cliniquewechat.elcapp.cn/wechat-api/app/member/unionid2openid?appid=82&unionid= 
    authorize-pro: https://cliniquewechat.elcapp.cn/wechat-api/app/oauthV2/authorize?appid=82&type=normal&redirect_uri=
    custom-send: https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=

  mall:
    member: https://miniappapi.clinique.com.cn/api/5e3a6e7f4a611.html

  template:

    get-points: Rh8N5M3DV1F65hyB1o3eeuK_-g2wtd3oEDYhaJjWEJM
    get-coupon: oBAY1-fjuUZpHwl1CK7T_Q0teSGm1mvCV3q6AzeH8YA
    store-exchange-pickup-success: X0l2-MwkZzogpbqL5PTytidyNem5VqbXWjUYhOAfLhE
    store-exchange-express-success: X0l2-MwkZzogpbqL5PTytidyNem5VqbXWjUYhOAfLhE
    ebci-sign-success: AJQp3Y9L8dpqc_DEPt-CmeVlXvFfyslXPrRVSpc_lAs
    ebci-sign-apply-success: X0l2-MwkZzogpbqL5PTytidyNem5VqbXWjUYhOAfLhE
    campaign-receive: X0l2-MwkZzogpbqL5PTytidyNem5VqbXWjUYhOAfLhE
    campaign-enroll-success: y0avMH0RjqqqqUC7VAA7U4QrqHZmpsFvD5WGUo8BJQQ
    testimony-draw-blindbox-success: BqY489Oof2A_azPBSLk8OAcRzHuC9aBEtPhPLQ8HfK0
    day3task-finish-notice: BqY489Oof2A_azPBSLk8OAcRzHuC9aBEtPhPLQ8HfK0

  subscribe-template:
    gift-receive-success: GfGg7uRvGmOa2V-fRYyV0R3bCOWFssg_sAeal7vQdDI
    campaign-winner: WOSQ6AKr15jTG0FHmKYzgSCYsaH-ZCaXRBQUhpkNJh8
    examine-result: M5rE6Y5zAfAk7OrL2jIJv7LYgrnC5KajjyW4gq-8FNs
    receive-success: rBVzkj0L-5IJqr_dCljuQfM6q4FVxmqw-iZ8UpOj9hw

  ad:
    user-actions: https://api.weixin.qq.com/marketing/user_actions/add?version=v1.0&access_token=

  oss:
    url: https://cli-wechat-test.oss-cn-shanghai.aliyuncs.com/
    end-point: https://oss-cn-shanghai.aliyuncs.com
    bucket: cli-wechat-test
    access-key-id: LTAI5tRUnPzf2yXB7NekdCE2
    access-key-secret: ******************************

  cdp:
    enhancement-campaign-sync-customer: http://47.103.190.7:12119/api/elc-data/enhancementCampaign/syncCustomer
    select-customer-info-by-openid: http://47.103.190.7:12119/api/elc-data/service.asmx/SelectCustomerInfo?brand=CL&source=WeChat&account=
    select-customer-info-by-unionid: http://47.103.190.7:12119/api/elc-data/newservice.asmx/SelectCustomerInfo?brand=CL&appId=wxc0099da8e25e2948&source=WeChat&unionId=
    customer-base-url: http://47.103.190.7:12119/api/elc-data/newservice.asmx/
    get-record-by-mobile: CustomerSearchByMobile?brand=CL&appId=wx93d4fc2221a8408d&account=
  coupon:
    secret: fs34$UJ7MV4vKYw45afert5$^O$JI4Ok3DLoFdsFUYFKM564KS
    get: https://cliniquetest.elcapp.cn/member/open-api/coupon/customer-get?
    get-by-mobile: https://cliniquetest.elcapp.cn/member/open-api/coupon/customer-get-by-mobile?
    coupon-67-detail : https://cliniquetest.elcapp.cn/member/open-api/coupon67/detail?

cdp:
  url: http://47.103.190.7:12119/api/elc-data/newservice.asmx/
  get-customer-by-unionid: SelectCustomerInfo?brand=CL&appId=wxc0099da8e25e2948&source=WeChat&unionId=
