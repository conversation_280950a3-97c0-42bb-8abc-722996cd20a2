package cn.slashsoft.clinique.service.mini.impl;

import cn.slashsoft.clinique.dao.mini.*;
import cn.slashsoft.clinique.domain.campaign.CampaignH5;
import cn.slashsoft.clinique.domain.mini.*;
import cn.slashsoft.clinique.enums.CBForeignEnum;
import cn.slashsoft.clinique.enums.CBTypeEnum;
import cn.slashsoft.clinique.service.campaign.CampaignH5Service;
import cn.slashsoft.clinique.service.mini.CactiveService;
import cn.slashsoft.clinique.service.mini.NoteService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.IntegerUtil;
import cn.slashsoft.clinique.util.LongUtil;
import io.netty.util.internal.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 笔记
 *
 * <AUTHOR>
 */
@Service
public class NoteServiceImpl implements NoteService {

    @Resource
    private CactiveService cactiveService;
    @Resource
    private CampaignH5Service campaignH5Service;

    private final NoteDao noteDao;
    private final PointDao pointDao;
    private final WechatDao wechatDao;

    @Resource
    KocDao kocDao;

    @Resource
    NoteDisscussFilterDao noteDiscussFilterDao;

    public NoteServiceImpl(NoteDao noteDao, PointDao pointDao, CactiveDao activeDao, WechatDao wechatDao) {
        this.noteDao = noteDao;
        this.pointDao = pointDao;
        this.wechatDao = wechatDao;
    }

    /**
     * 获取点赞排名，从高到底
     *
     * @param tagIds 标签编号
     * @param size 数量
     * @return 排名列表
     */
    @Override
    public List<NoteRanking> getRankingList(String tagIds, int size) {

        List<Integer> idList = new ArrayList<>();

        String[] idArray = tagIds.split(",");
        for (String idString : idArray) {
            int id = IntegerUtil.parseInt(idString);
            if(0 < id){
                idList.add(id);
            }
        }

        return noteDao.getRankingList(idList, size);

    }

    /**
     * 获取点赞排名的缓存
     *
     * @param tagIds 标签编号
     * @param size 数量
     * @return 排名列表
     */
    @Override
    public List<NoteRanking> getRankingListFromCache(String tagIds, int size) {
        return noteDao.getRankingListFromCache(tagIds, size);
    }

    /**
     * 保存笔记
     */
    @Override
    public Long insertNote(Note note) {
        return this.noteDao.insertNote(note);
    }

    /**
     * 保存笔记
     */
    @Override
    public void insertCamNote(Long customerId,Object title,Object content,Long id,Integer status) {
        //发布内容
        CampaignH5 camp = campaignH5Service.getValidCampaign("campaign2408");
        if (!ObjectUtils.isEmpty(camp)){
            this.noteDao.insertCamNote(customerId,title,content,id,status,"campaign2408");
        }
    }

    /**
     * 保存图片
     */
    @Override
    public Long insertPhoto(NotePhoto notePhoto) {
        return this.noteDao.insertNotePhoto(notePhoto);
    }

    /**
     * 保存图片标签
     */
    @Override
    public void insertNotePhotoTag(NotePhotoTag tag) {
        this.noteDao.insertNotePhotoTag(tag);
    }

    /**
     * 保存笔记评论
     */
    public int insertDiscuss(NoteDiscuss noteDiscuss) {
        Note no = this.noteDao.noteStatus(noteDiscuss.getNoteId());
        if (no == null || no.getStatus() <= 1) {
            return 99;
        }

        int count = this.noteDao.insertDiscuss(noteDiscuss);
        if (count == 0) {
            return count;
        }

        //活动评论
        CampaignH5 camp = campaignH5Service.getValidCampaign("campaign2408");
        if (!ObjectUtils.isEmpty(camp)){
            this.noteDao.insCamDiscussg(noteDiscuss.getNoteId(),noteDiscuss.getId(),noteDiscuss.getCustomerId(),noteDiscuss.getContent(),noteDiscuss.getStatus(),"campaign2408");
        }

        // 发放C币
        givePoint(noteDiscuss.getCustomerId(), noteDiscuss.getNoteId(), noteDiscuss.getId());

        return count;
    }

    /**
     * 删除笔记
     */
    @Override
    public int delNote(Long noteId, Long customerId) {
        //删除
        CampaignH5 camp = campaignH5Service.getValidCampaign("campaign2408");
        if (!ObjectUtils.isEmpty(camp)){
            this.noteDao.delCamDiscussg(noteId,customerId,"campaign2408");
        }

        return this.noteDao.delNote(noteId, customerId);
    }

    /**
     * 保存阅读笔记日志
     */
    @Override
    public int insertReadLog(NoteReadLog log, String shareOpenId) {
        Long customerId = 0L;
        if (shareOpenId != null && shareOpenId.length() > 0) {
            // 分享笔记阅读 给分享者加C粉值
            customerId = this.wechatDao.getCustomerIdByOpenId(shareOpenId);
            if (customerId == null) {
                customerId = 0L;
            }
        }

        log.setShareCustomerId(customerId);
        int count = this.noteDao.insertReadLog(log);
        if (count == 0) {
            return count;
        }

        Note note = this.noteDao.getNote(log.getNoteId(), log.getCustomerId());
        if (null != note && note.getCustomerId().equals(log.getCustomerId())) {
            return count;
        }

        // 发放C币
        givePoint(log.getCustomerId(), log.getNoteId(), log.getId());

        return count;
    }

    /**
     * 拔草笔记
     *
     * @param NoteLikeLog
     */
    @Override
    public int insertLikeLog(NoteLikeLog log) {
        if (this.noteDao.getLikeLogCount(log) > 0) {
            return 1;
        }
        //活动拔草
        CampaignH5 camp = campaignH5Service.getValidCampaign("campaign2408");
        if (!ObjectUtils.isEmpty(camp)){
            this.noteDao.insCamLikeLog(log.getNoteId(),log.getCustomerId(),"campaign2408");
        }
        int count = this.noteDao.insertLikeLog(log);
        if (count == 0) {
            return count;
        }

        this.addLikeCBPoint(log, "拔草笔记");

        // 重新统计
        noteDao.updateNoteLikeCount(log.getNoteId());

        return count;
    }

    private void addLikeCBPoint(NoteLikeLog log, String desc) {
        // 发放C币
        givePoint(log.getCustomerId(), log.getNoteId(), log.getId());
    }

    /**
     * 删除 拔草笔记
     */
    @Override
    public int unlikeNote(NoteLikeLog log) {
        //删除活动拔草
        CampaignH5 camp = campaignH5Service.getValidCampaign("campaign2408");
        if (!ObjectUtils.isEmpty(camp)){
            this.noteDao.delCamLikeLog(log.getNoteId(),log.getCustomerId(),"campaign2408");
        }
        return this.noteDao.delLikeLog(log);
    }

    /**
     * 保存分享笔记日志
     */
    @Override
    public int insertShareLog(NoteShareLog log) {
        int count = this.noteDao.insertShareLog(log);
        if (count == 0) {
            return count;
        }

        // if(this.noteDao.getShareLogCount(log) == 1) {
        // 查看本类型加分是否已超过2次

        int giveCount = pointDao.getCustomerMonthPointCountByType(CBTypeEnum.NOTE_VIDEO_SHARE.getId(), log.getCustomerId());
        if (giveCount >= CBTypeEnum.NOTE_VIDEO_SHARE.getMax()) {
            return count;
        }
        // 发放C币
        Date now = new Date();
        PointTransaction pointTransaction = new PointTransaction();
        pointTransaction.setCustomerId(log.getCustomerId());
        pointTransaction.setPointTypeId(CBTypeEnum.NOTE_VIDEO_SHARE.getId());
        pointTransaction.setPoints(CBTypeEnum.NOTE_VIDEO_SHARE.getPoints());
        pointTransaction.setRemainingPoints(CBTypeEnum.NOTE_VIDEO_SHARE.getPoints());
        pointTransaction.setStartTime(now);
        pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
        pointTransaction.setForeignId(CBForeignEnum.NOTE_VIDEO_SHARE.getId());
        pointTransaction.setForeignMasterId(log.getNoteId());
        pointTransaction.setForeignDetailId(log.getId());
        pointTransaction.setRemark("分享笔记");
        pointDao.insertPointTransaction(pointTransaction);
        return count;
    }

    /**
     * 笔记是否拔草
     */
    @Override
    public int liked(Long noteId, Long customerId) {
        Note no = this.noteDao.noteStatus(noteId);
        if (no == null || no.getStatus() <= 1) {
            return 99;
        }
        NoteLikeLog log = this.noteDao.liked(noteId, customerId);
        if (log == null) {
            return 0;
        }
        return 1;
    }

    /**
     * 获取top笔记/视频标签列表
     */
    public ArrayList<NoteTag> getTopTagList() {
        ArrayList<NoteTag> noteTopTagList = this.noteDao.getTopTagList();

        return noteTopTagList;
    }

    /**
     * 获取top笔记/视频标签列表
     */
    public ArrayList<NoteTag> getIndexTopTagList(int type) {
        ArrayList<NoteTag> noteTopTagList = this.noteDao.getIndexTopTagList();
        int size = noteTopTagList.size();
        if (size < 10) {
            ArrayList<NoteTag> noteHotTagList = this.noteDao.getHotTagList(type);
            for (NoteTag hotTag : noteHotTagList) {
                boolean has = false;
                for (NoteTag tag : noteTopTagList) {
                    if (tag.getId() == hotTag.getId()) {
                        has = true;
                        break;
                    }
                }
                if (!has) {
                    noteTopTagList.add(hotTag);
                }
                if (noteTopTagList.size() == 10) {
                    break;
                }
            }
        }

        return noteTopTagList;
    }

    /**
     * 获取top笔记/视频标签列表
     */
    public ArrayList<NoteTag> getSearchTopTagList() {
        ArrayList<NoteTag> noteTopTagList = this.noteDao.getSearchTopTagList();

        return noteTopTagList;
    }

    /**
     * 获取标签分组列表
     *
     * @return 分组列表
     */
    @Override
    public List<NoteTagGroup> getNoteTagGroupList() {
        return noteDao.getNoteTagGroupList();
    }

    /**
     * 搜索笔记标签
     *
     * @param key
     * @param type
     */
    public ArrayList<NoteTag> getTagList(String key, int type) {
        return this.noteDao.getTagList(key, type);
    }

    /**
     * 保存标签搜索、使用日志
     *
     * @param NoteTagSearchLog 日志
     */
    public int insertTagSearchLog(NoteTagSearchLog log) {
        return this.noteDao.insertTagSearchLog(log);
    }

    /**
     * 保存位置信息日志
     *
     * @param NoteAddressLog 日志
     */
    public int insertTagAddressLog(NoteTagAddressLog log) {
        return this.noteDao.insertTagAddressLog(log);
    }

    /**
     * 获取用户笔记评论列表
     *
     * @param customerId
     * @param start
     */
    public ArrayList<NoteDiscussMy> getCustomerDiscussList(Long customerId) {
        return this.noteDao.getCustomerDiscussList(customerId);
    }

    /**
     * 获取用户笔记评论列表
     *
     * @param customerId
     */
    /**
     * 获取笔记评论列表
     *
     * @param customerId
     */
    public ArrayList<NoteDiscuss> getNoteDiscussList(Long noteId, int start, int pageSize) {
        start = start - 1 <= 0 ? 0 : start - 1;
        return this.noteDao.getNoteDiscussList(noteId, start * pageSize, pageSize);
    }

    /**
     * 获取笔记评论数目表
     *
     * @param noteId
     */
    public int getNoteDiscussCount(Long noteId) {
        return this.noteDao.getNoteDiscussCount(noteId);
    }

    /**
     * 获取用户笔记列表
     *
     * @param customerId
     * @param start
     */
    public ArrayList<Note> getCustomerNoteList(Long customerId, int start) {
        return this.noteDao.getCustomerNoteList(customerId, start);
    }

    /**
     * 获取用户拔草笔记列表
     *
     * @param customerId
     * @param start
     */
    public ArrayList<Note> getCustomerLikeNoteList(Long customerId, int start) {
        return this.noteDao.getCustomerLikeNoteList(customerId, start);
    }

    /**
     * 获取用户笔记
     *
     * @param noteId
     */
    public Note getNote(Long id, Long CustomerId) {
        Note note = this.noteDao.getNote(id, CustomerId);

        if (note == null) {
            note = new Note();
            note.setId(0L);
        } else if (note.getId() != null && note.getId() != 0) {
            ArrayList<NotePhoto> photos = this.noteDao.getNotePhotos(id);
            for (NotePhoto photo : photos) {
                photo.setTags(this.noteDao.getNotePhotoTags(photo.getId()));
                photo.setId((long) 0);
            }
            note.setPhotos(photos);

            note.setDiscuss(this.noteDao.getNoteDiscuss(id));
        }
        return note;
    }

    /**
     * 获取笔记列表
     *
     * @param tagId
     * @param customerId
     * @param start
     * @param pageSize
     * @param sortType
     * @param sign
     */
    @Override
    public ArrayList<Note> getNoteListByGroup(Long tagId, Long customerId, int start, int pageSize, String sortType, int sign) {

        if (sortType.equals("hot")) {
            return noteDao.getHotNoteListByGroup(tagId, customerId, (start-1) * pageSize, pageSize);
        } else if (sortType.equals("new")) {
            return noteDao.getNewNoteListByGroup(tagId, customerId, (start-1) * pageSize, pageSize);
        }

        return null;

    }

    /**
     * 获取笔记列表
     *
     * @param tagId
     * @param start
     * @param pageSize
     */
    @Override
    public ArrayList<Note> getNoteList(Long tagId, Long customerId, int page, int pageSize, String sortType, int sign) {

        page = Math.max(page - 1, 0);

        if (tagId.intValue() != 0) {
            if (customerId.intValue() != 0) {
                NotePhotoTag tag = this.noteDao.getNoteTag(tagId);
                NoteTagSearchLog log = new NoteTagSearchLog();
                log.setCustomerId(customerId);
                log.setTagType(tag.getType());
                log.setTagTitle(tag.getTitle());
                log.setTagId(tagId);
                this.insertTagSearchLog(log);
            }
            if ("hot".equals(sortType)) {
                return this.noteDao.getNoteListByTag(tagId, customerId, page * pageSize, pageSize);
            } else if ("new".equals(sortType)) {
                return this.noteDao.getNewNoteListByTag(tagId, customerId, page * pageSize, pageSize);
            }
            else {
                return this.noteDao.getNoteListNormal(customerId, page * pageSize, pageSize);
            }
        }

        ArrayList<Note> stepList = new ArrayList<Note>();

        if ("hot".equals(sortType) && page == 0) {
            ArrayList<Note> topList = this.noteDao.getNoteListTop(customerId);
            stepList.addAll(topList);
            ArrayList<Note> specialList = this.noteDao.getNoteListSpecial(customerId, page, 2);
            stepList.addAll(specialList);
            pageSize -= specialList.size();
            ArrayList<Note> threeList = this.noteDao.getNoteListThree(customerId, page);
            stepList.addAll(threeList);
            pageSize -= threeList.size();
        }

        if ("hot".equals(sortType)) {
            stepList.addAll(this.noteDao.getNoteList(customerId, page * pageSize, pageSize));
        } else if ("new".equals(sortType)) {
            stepList.addAll(this.noteDao.getNoteListByNew(customerId, page * pageSize, pageSize));
        } else {
            stepList.addAll(this.noteDao.getNoteListNormal(customerId, page * pageSize, pageSize));
        }
        return stepList;

    }

    /**
     * 获取笔记数目
     *
     * @param tagId
     */
    @Override
    public int getNoteCount(Long tagId) {
        if (tagId != 0) {
            return this.noteDao.getNoteCountByTag(tagId);
        }
        return this.noteDao.getNoteCount();
    }

    /**
     * 获取笔记数目
     *
     * @param groupId 组编号
     * @return 笔记数目
     */
    @Override
    public int getNoteCountByGroup(long groupId) {
        return noteDao.getNoteCountByGroup(groupId);
    }

    @Override
    public Long updateNote(Note note) {
        return this.noteDao.updateNote(note);
    }

    @Override
    public void delPhotosByNote(Long id) {
        this.noteDao.delPhotosByNote(id);
    }

    @Override
    public void delPhotoTagsByNote(Long id) {
        this.noteDao.delPhotoTagsByNote(id);
    }

    @Override
    public List<CIndexBanner> GetCindexBanner() {
        return this.noteDao.getCindexBanner();
    }

    @Override
    public CIndexBanner GetCindexBannerById(Long id) {
        return this.noteDao.getCindexBannerById(id);
    }

    @Override
    public List<String> filterDiscuss(NoteDiscuss noteDiscuss) {
        ArrayList<String> keys = new ArrayList<String>();
        List<String> all = noteDiscussFilterDao.getFilters();
        for (String key : all) {
            if (noteDiscuss.getContent().contains(key)) {
                keys.add(key);
            }
        }
        List<String> fullAll = noteDiscussFilterDao.getFullFilters();
        for (String key1 : fullAll) {
            if (key1.equals(noteDiscuss.getContent())) {
                keys.add(key1);
            }
        }
        return keys;
    }

    @Override
    public List<Note> getKocList(Long customerId, int page, int pageSize) {
        page = page - 1 <= 0 ? 0 : page - 1;
        return kocDao.getKocListForNote(customerId, page * pageSize, pageSize);
    }

    @Override
    public int getKocCount() {
        return kocDao.getKocCountForNote();
    }

    @Override
    public Note getKoc(Long id, Long customerId) {
        Note note = this.kocDao.getNote(id, customerId);

        if (note == null) {
            note = new Note();
            note.setId(0L);
        } else if (note.getId() != null && note.getId() != 0) {
            ArrayList<NotePhoto> photos = this.kocDao.getNotePhotos(id);
            note.setPhotos(photos);
        }
        return note;
    }

    @Override
    public int unlikeKoc(NoteLikeLog log) {
        return this.kocDao.delLikeLog(log);
    }

    @Override
    public int insertKocLikeLog(NoteLikeLog log) {
        if (this.kocDao.getLikeLogCount(log) > 0) {
            return 1;
        }
        int count = this.kocDao.insertLikeLog(log);
        if (count == 0) {
            return count;
        }

        this.addLikeCBPoint(log, "拔草KOC");

        return 1;
    }

    @Override
    public void updateKocTitle(Long detailId, String title, Long customerId) {
        kocDao.updateTitle(detailId, title, customerId);
    }

    @Override
    public Note getKocByCampaign(long campaignId, long customerId) {
        Note note = this.kocDao.getNoteByCampaign(campaignId, customerId);

        if (note == null) {
            note = new Note();
            note.setId(0L);
        } else if (note.getId() != null && note.getId() != 0) {
            ArrayList<NotePhoto> photos = this.kocDao.getNotePhotos(note.getId());
            note.setPhotos(photos);
        }
        return note;
    }

    @Override
    public Note getNoteById(Long id, long customerId) {
        return noteDao.getNoteById(id,customerId);
    }

    /**
     * 阅读、分享、评论增加积分，每月最多增加10次
     *
     * @param customerId 顾客编号
     */
    private void givePoint(long customerId, long masterId, long detailId) {

        Date now = new Date();

        NotePointGiveLimit notePointGiveLimit = noteDao.getNotePointGiveLimit(customerId);
        if (null == notePointGiveLimit) {
            notePointGiveLimit = new NotePointGiveLimit();
            notePointGiveLimit.setCustomerId(customerId);
            notePointGiveLimit.setCount(0);
            notePointGiveLimit.setLastMonth(now);
            noteDao.insertNotePointGiveLimit(notePointGiveLimit);
        } else if (!DateUtil.isSameMonth(notePointGiveLimit.getLastMonth(), now)) {
            notePointGiveLimit.setCount(0);
            notePointGiveLimit.setLastMonth(now);
        } else if (10 <= notePointGiveLimit.getCount()) {
            return;
        }

        // 发放C币
        PointTransaction pointTransaction = new PointTransaction();
        pointTransaction.setCustomerId(customerId);
        pointTransaction.setPointTypeId(CBTypeEnum.NOTE_VIDEO_ACTION.getId());
        pointTransaction.setPoints(CBTypeEnum.NOTE_VIDEO_ACTION.getPoints());
        pointTransaction.setRemainingPoints(CBTypeEnum.NOTE_VIDEO_ACTION.getPoints());
        pointTransaction.setStartTime(now);
        pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
        pointTransaction.setForeignId(CBForeignEnum.NOTE_VIDEO_ACTION.getId());
        pointTransaction.setForeignMasterId(masterId);
        pointTransaction.setForeignDetailId(detailId);
        pointTransaction.setRemark("笔记评论");
        if (pointDao.has(pointTransaction) == 0) {
            pointDao.insertPointTransaction(pointTransaction);
            notePointGiveLimit.setCount(notePointGiveLimit.getCount() + 1);
            noteDao.updateNotePointGiveLimit(notePointGiveLimit);
        }
    }

}
