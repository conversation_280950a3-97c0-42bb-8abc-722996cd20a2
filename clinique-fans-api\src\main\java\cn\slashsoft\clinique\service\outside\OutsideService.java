package cn.slashsoft.clinique.service.outside;

import cn.slashsoft.clinique.domain.campaign.CampaignH5Detail;
import cn.slashsoft.clinique.domain.mini.Follow;
import cn.slashsoft.clinique.domain.mini.OutCustomer;
import cn.slashsoft.clinique.domain.mini.WechatJssdk;
import cn.slashsoft.clinique.domain.official.WechatUserInfo;


import org.springframework.web.multipart.MultipartFile;

import cn.slashsoft.clinique.vo.Result;
import org.springframework.web.multipart.MultipartFile;

/**
 * 外部接口
 *
 * <AUTHOR>
 */
public interface OutsideService {

    /**
     * 调用微信接口获取用户信息
     *
     * @param openid      公众号唯一编号
     * @param accessToken * @param accessToken
     * @return 用户信息
     */
    WechatUserInfo getUserInfo(String openid, String accessToken);

    /**
     * 用unionid查找总库是否有顾客信息
     *
     * @param unionid 开放平台唯一编号
     * @return 外部顾客信息
     */
    OutCustomer getMainMemberWithUnionid(String unionid);

    /**
     * 保存openid
     *
     * @param customerId 顾客编号
     * @return openid
     */
    String getOpenidByUnionid(long customerId);

    /**
     * 用unionid查找商城是否有顾客信息
     *
     * @param unionid 开放平台唯一编号
     * @return 外部顾客信息
     */
    OutCustomer getMallMemberWithUnionid(String unionid);

    /**
     * 小程序会员绑定
     *
     * @param unionid 开放平台唯一编号
     * @param openid  公众号唯一编号
     */
    void bindMember(String unionid, String openid);

    /**
     * 获取AccessToken
     */
    void getAccessToken();

    /**
     * 获取JSSDK
     *
     * @param url 地址
     * @param api 接口
     * @return JSSDK
     */
    WechatJssdk getJssdk(String url, String[] api);

    /**
     * 是否关注
     *
     * @param openid 公众号唯一编号
     * @return 是否关注
     */
    Follow isFollow(String openid);

    /**
     * 发送模版消息
     *
     * @param type       类型
     * @param openid     公众号唯一编号
     * @param templateId 模版编号
     * @param data       数据
     * @return 是否成功
     */
    boolean templateSend(short type, String openid, String templateId, String data);

    /**
     * 发送一次性订阅消息
     *
     * @param type       类型
     * @param openid     小程序唯一编号
     * @param templateId 模版编号
     * @param page       页面，如果为空不跳转
     * @param data       数据
     * @return 是否成功
     */
    boolean subscribeMessageSend(short type, String openid, String templateId, String page, String data);

    /**
     * 发送模版消息
     *
     * @param type       类型
     * @param openid     公众号唯一编号
     * @param templateId 模版编号
     * @param data       数据
     * @param appKey     小程序App Key
     * @param pagePath   小程序链接地址
     * @return 是否成功
     */
    boolean templateSendWithMp(short type, String openid, String templateId, String data, String appKey, String pagePath);

    /**
     * 发送客服消息
     *
     * @param type    类型
     * @param openid  公众号唯一编号
     * @param content 发送内容
     * @return 是否成功
     */
    boolean customerServiceMessageSend(short type, String openid, String content);

    /**
     * 腾讯广告上报
     *
     * @param url         地址
     * @param clickId     点击
     * @param name        姓名
     * @param phoneNumber 手机号码
     * @param city        城市
     * @param store       门店
     */
    void qqMarketing(String url, String clickId, String name, String phoneNumber, String city, String store);

    /**
     * 上传图片到OSS上
     *
     * @param file 文件
     * @return 返回的路径
     */
    String uploadImageOss(MultipartFile file);

    /**
     * 领取优惠券
     *
     * @param unionid  开放平台唯一编号
     * @param couponId 优惠编号
     * @return 领取结果
     */
    Result getCoupon(String unionid, String couponId);

    /**
     * 是否线下消费
     *
     * @param phone 手机号
     * @return boolean
     */
    boolean isOnlineCustomer(String phone);

    boolean getRecord(String phone);

	void getCouponByPhone(CampaignH5Detail camp, Long couponCampaignId);

    /**
     * 内容审查
     * @param openid 小程序唯一编号
     * @param content 内容
     * @return 是否通过
     */
    boolean securityMsgSecCheck(String openid, String content);

}
