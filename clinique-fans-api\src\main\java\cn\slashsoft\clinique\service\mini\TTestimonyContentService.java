package cn.slashsoft.clinique.service.mini;

import cn.slashsoft.clinique.domain.mini.TTestimonyContent;
import cn.slashsoft.clinique.domain.mini.TTestimonyHelp;
import cn.slashsoft.clinique.vo.mini.TestimonyContentVo;

import java.util.List;

/**
* <p>
    *
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
public interface TTestimonyContentService {
    void insertTTestimonyContent(TTestimonyContent tTestimonyContent);
    void updateTTestimonyContent(TTestimonyContent tTestimonyContent);
    TTestimonyContent getTTestimonyContent(Long id);
    List<TestimonyContentVo> getTestimonyContentVo(Long customerId);

    /**
     * 获取证言条数
     * @param customerId
     */
    Integer getTTestimonyContentCountByCustomerId(long customerId);

    /**
     * 通过contentId获取用户id
     * @param contentId
     * @return
     */
    Long getCustomerIdByContentId(long contentId);

    /**
     * 根据customerid contentid 获取记录
     * @param customerId
     * @param helpCustomerId
     * @return
     */
    TTestimonyHelp getHelpByCustomerIdHelpCustomerId(long customerId, long helpCustomerId);

    /**
     * 获取最后一条证言
     * @param customerId
     * @return
     */
    String getLastontent(long customerId);

    /**
     * 判断证言中是否包含需过滤的词汇
     * @param content
     * @return
     */
    Boolean containsFiltersWord(String content);

    /**
     * 添加过滤的记录
     * @param tTestimonyContent
     */
    void insertFilteredTTestimonyContent(TTestimonyContent tTestimonyContent);
	/**
	 * 获取证言条数
	 * @param customerId
	 */
	Integer getTTestimonyContentCountByCustomerIdContent(long customerId, String content);
	Integer getTTestimonyContentCountByCustomerIdToday(long customerId);
}
