package cn.slashsoft.cliniqueapi;

import cn.slashsoft.clinique.util.HttpUtil;
import org.junit.Test;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

public class SnsTests {

    @Test
    public void sendTest() throws IOException {

        String phoneNumber = "18701917525";

        String message = "您在C粉圈兑换的礼品已经离你越来越近了，请注意查收快递哦！/回TD退订";

        String messageEncode = encodeHexStr(message.getBytes(StandardCharsets.UTF_16BE));

        System.out.println(messageEncode);

        String url="http://43.240.124.37:3308/sms/mt?command=MT_REQUEST&spid=CL00J1&sppassword=61423e&da=" + phoneNumber + "&dc=8&sm=" + messageEncode;

        System.out.println(url);

        String result = HttpUtil.get(url);

        System.out.println(result);

    }

    private String encodeHexStr(byte[] src) {
        String strHex = "";
        StringBuilder sb = new StringBuilder("");
        for (byte b : src) {
            strHex = Integer.toHexString(b & 0xFF);
            sb.append((strHex.length() == 1) ? "0" + strHex : strHex); // 每个字节由两个字符表示，位数不够，高位补0
        }
        return sb.toString().trim();
    }


}
