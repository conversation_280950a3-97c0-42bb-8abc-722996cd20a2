package cn.slashsoft.clinique.dao.mini;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.mini.BSmotTemplate;
import cn.slashsoft.clinique.domain.mini.Smot;
import cn.slashsoft.clinique.domain.mini.SmotAuthorize;

/**
 * 模版消息
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface SmotDao {

    /**
     * 写入一次性订阅消息
     *
     * @param smot 一次性订阅消息
     */
    @Insert("INSERT INTO `smot`(" +
            "   `wechat_mini_openid`, " +
            "   `type`, " +
            "   `param`, " +
            "   `result`, " +
            "   `status`" +
            ") " +
            "VALUES (" +
            "   #{wechatMiniOpenid}, " +
            "   #{type}, " +
            "   #{param}, " +
            "   #{result}, " +
            "   #{status}" +
            ")")
    void insertSmot(Smot smot);

    /**
     * 写入一次性订阅消息授权选择记录
     *
     * @param smotAuthorize 授权选择记录
     */
    @Insert("INSERT INTO `smot_authorize`(" +
            "   `type`, " +
            "   `wechat_mini_openid`, " +
            "   `choice` " +
            ") " +
            "VALUES (" +
            "   #{type}, " +
            "   #{wechatMiniOpenid}, " +
            "   #{choice} " +
            ")")
    void insertSmotAuthorize(SmotAuthorize smotAuthorize);

    /**
     * 获取是否授权
     *
     * @param type   类型
     * @param openid 小程序编号
     * @param choice 是否选择
     * @return 数量
     */
    @Select("SELECT " +
            "   COUNT(`id`) " +
            "FROM " +
            "   `smot_authorize` " +
            "WHERE " +
            "   `wechat_mini_openid`=#{openid} " +
            "   AND `type`=#{type} " +
            "   AND `choice`=#{choice} " +
            "LIMIT 1")
    int getSmotAuthorizeChoice(@Param("type") short type, @Param("openid") String openid, @Param("choice") boolean choice);

    /**
     * 获取是否授权
     *
     * @param type   类型
     * @param openid 小程序编号
     * @return 数量
     */
    @Select("SELECT " +
            "   COUNT(`id`) " +
            "FROM " +
            "   `smot_authorize` " +
            "WHERE " +
            "   `wechat_mini_openid`=#{openid} " +
            "   AND `type`=#{type} " +
            "LIMIT 1")
    int getSmotAuthorize(@Param("type") short type, @Param("openid") String openid);

    @Select(" SELECT * FROM `smot_template` where id=#{snsId} limit 1 ")
	BSmotTemplate getSmot(Long snsId);

}
