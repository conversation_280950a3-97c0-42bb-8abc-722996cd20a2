package com.stormcrm.clinique.service;

import com.stormcrm.clinique.domain.*;
import com.stormcrm.clinique.vo.Result;

import java.util.List;

/**
 * 视频
 *
 * <AUTHOR>
 */
public interface VideoService {


    /**
     * 查询所有活动带分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索
     * @param type          状态
     * @return 活动列表分页
     */

    List<Video> getPage(int page, int perpage, String generalSearch, Short type);

    /**
     * 查询所有活动带分页的记录数
     *
     * @param generalSearch 搜索
     * @param type          状态
     * @return 记录数
     */
    int getPageCount(String generalSearch, Short type);

    /**
     * 根据id 获取 video
     *
     * @param id video id
     * @return 视频对象
     */
    Video getById(long id);

    /**
     * 删除
     *
     * @param id video id
     * @return 删除标记
     */
    int del(long id);

    /**
     * 保存
     *
     * @param video 视频
     * @return 页面结果
     */
    Result save(Video video, List<Long> tags);

    /**
     * 更新
     *
     * @param video 视频
     * @return 页面结果
     */
    Result update(Video video, List<Long> tags);
}
