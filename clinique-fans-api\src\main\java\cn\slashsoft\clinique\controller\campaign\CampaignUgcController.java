package cn.slashsoft.clinique.controller.campaign;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.slashsoft.clinique.domain.campaign.ugc.Koc;
import cn.slashsoft.clinique.domain.campaign.ugc.KocDetail;
import cn.slashsoft.clinique.domain.campaign.ugc.KocDetailImage;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTop;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTopDetailImageLog;
import cn.slashsoft.clinique.domain.campaign.ugc.KocTopDetailLog;
import cn.slashsoft.clinique.domain.mini.Note;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.campaign.CampaignUgcService;
import cn.slashsoft.clinique.service.mini.NoteService;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.campaign.ugc.CampaignUgcIndexVo;
import cn.slashsoft.clinique.vo.campaign.ugc.CampaignUgcMineVo;
import cn.slashsoft.clinique.vo.campaign.ugc.CampaignUgcTopVo;
import cn.slashsoft.clinique.vo.campaign.ugc.CampaignUgcUploadVo;

/**
 * UGC
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/campaign-ugc")
public class CampaignUgcController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final CampaignUgcService campaignUgcService;
    private final StringRedisTemplate stringRedisTemplate;
    private final WechatService wechatService;
    

    public CampaignUgcController(CampaignUgcService campaignUgcService, StringRedisTemplate stringRedisTemplate, WechatService wechatService) {
        this.campaignUgcService = campaignUgcService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.wechatService = wechatService;
    }

//    /**
//     * 配置活动信息
//     *
//     * @param verification 验证
//     * @param startTime    开始时间
//     * @param endTime      结束时间
//     * @return 处理结果
//     * https://clinique.stormcrm.com/api/campaign-config/laser-talents/20-23-04-00-28/2020-06-01-00-00-00/2020-06-30-23-59-59
//     * http://127.0.0.1:9121/api/campaign-config/laser-talents/20-15-07-07-31/2020-07-01-00-00-00/2020-08-31-23-59-59
//     */
//    @GetMapping("/campaign-config/laser-talents/{verification}/{startTime}/{endTime}")
//    public String setConfig(
//            @PathVariable("verification") String verification,
//            @PathVariable("startTime") String startTime,
//            @PathVariable("endTime") String endTime
//    ) {
//        if (DateUtil.parseVerification(new Date()).equals(verification)) {
//            stringRedisTemplate.opsForValue().set("campaignUgcStartTime", DateUtil.parseString(DateUtil.valueOf(startTime, "yyyy-MM-dd-HH-mm-ss")));
//            stringRedisTemplate.opsForValue().set("campaignUgcEndTime", DateUtil.parseString(DateUtil.valueOf(endTime, "yyyy-MM-dd-HH-mm-ss")));
//            return "success";
//        }
//        return "fail";
//    }

    /**
     * 获取页面数据
     *
     * @param campaignId 活动编号
     * @return 页面数据
     */
    @PostMapping("/get-data/{campaignId}")
    public String getData(
            @PathVariable("campaignId") long campaignId
    ) {

        Koc campaignUgc = campaignUgcService.getCampaignUgc(campaignId);
        if (null == campaignUgc){
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 获取美白达人官活动参与信息
        KocDetail campaignUgcDetail = campaignUgcService.getCampaignUgcDetail(campaignId, customerId);

        CampaignUgcIndexVo campaignUgcIndexVo = new CampaignUgcIndexVo();

        campaignUgcIndexVo.setStartTime(campaignUgc.getStartTime());
        campaignUgcIndexVo.setEndTime(campaignUgc.getEndTime());

        campaignUgcIndexVo.setIndexBannerImageUrl(campaignUgc.getIndexBannerImageUrl());
        campaignUgcIndexVo.setRuleImageUrl(campaignUgc.getRuleImageUrl());
        campaignUgcIndexVo.setFailImageUrl(campaignUgc.getFailImageUrl());

        campaignUgcIndexVo.setTitle(campaignUgcDetail.getTitle());
        campaignUgcIndexVo.setShareTitle(campaignUgc.getShareTitle());
        campaignUgcIndexVo.setShareImageUrl(campaignUgc.getShareImageUrl());

        campaignUgcIndexVo.setStatus(campaignUgcDetail.getStatus());

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignUgcIndexVo);

    }

    /**
     * 获取页面数据
     *
     * @param campaignId 活动编号
     * @return 页面数据
     */
    @PostMapping("/alert-get-data/{campaignId}")
    public String getAlertData(
            @PathVariable("campaignId") long campaignId
    ) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 获取美白达人官活动参与信息
        KocDetail campaignUgcDetail = campaignUgcService.getCampaignUgcDetailAlert(campaignId, customerId);
        if (null == campaignUgcDetail){
            return ResultUtil.customer(ResultEnum.SUCCESS);
        }

        CampaignUgcIndexVo campaignUgcIndexVo = new CampaignUgcIndexVo();
        campaignUgcIndexVo.setAlert(campaignUgcDetail.getAlert());
        campaignUgcIndexVo.setStatus(campaignUgcDetail.getStatus());

        // 首次变更状态时，设置提醒状态
        if (campaignUgcDetail.getAlert()) {
            campaignUgcService.updateCampaignUgcDetailAlert(campaignId, customerId);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignUgcIndexVo);

    }
 
    /**
     * 图片上传
     *
     * @param campaignId 活动编号
     * @param file 图片
     * @param index 索引
     * @return 处理结果
     */
    @PostMapping("/upload/{campaignId}")
    public String upload(
            @PathVariable("campaignId") long campaignId,
            @RequestParam("file") MultipartFile file,
            @RequestParam("index") int index
    ) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        if (!campaignUgcService.upload(campaignId, customerId, file)) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, index);

    }

    /**
     * 获取页面数据
     *
     * @param campaignId 活动编号
     * @return 页面数据
     */
    @PostMapping("/upload-get-data/{campaignId}")
    public String getUploadData(
            @PathVariable("campaignId") long campaignId
    ) {

        Koc campaignUgc = campaignUgcService.getCampaignUgc(campaignId);
        if (null == campaignUgc){
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        CampaignUgcUploadVo campaignUgcUploadVo = new CampaignUgcUploadVo();

        campaignUgcUploadVo.setStartTime(campaignUgc.getStartTime());
        campaignUgcUploadVo.setEndTime(campaignUgc.getEndTime());

        campaignUgcUploadVo.setUploadBannerImageUrl(campaignUgc.getUploadBannerImageUrl());
        
        campaignUgcUploadVo.setShareTitle(campaignUgc.getShareTitle());
        campaignUgcUploadVo.setShareImageUrl(campaignUgc.getShareImageUrl());

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignUgcUploadVo);

    }

    /**
     * 获取页面数据
     *
     * @param campaignId 活动编号
     * @return 页面数据
     */
    @PostMapping("/mine-get-data/{campaignId}")
    public String getMineData(
            @PathVariable("campaignId") long campaignId
    ) {

        Koc campaignUgc = campaignUgcService.getCampaignUgc(campaignId);
        if (null == campaignUgc){
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 获取美白达人官活动参与信息
        KocDetail campaignUgcDetail = campaignUgcService.getCampaignUgcDetail(campaignId, customerId);
        if(campaignUgcDetail == null) {
        	return ResultUtil.customer(ResultEnum.SUCCESS, 0);
        }

        CampaignUgcMineVo campaignUgcMineVo = new CampaignUgcMineVo();

        campaignUgcMineVo.setStartTime(campaignUgc.getStartTime());
        campaignUgcMineVo.setEndTime(campaignUgc.getEndTime());
        
        campaignUgcMineVo.setTitle(campaignUgcDetail.getTitle());

        campaignUgcMineVo.setShareTitle(campaignUgc.getShareTitle());
        campaignUgcMineVo.setShareImageUrl(campaignUgc.getShareImageUrl());

        campaignUgcMineVo.setStatus(campaignUgcDetail.getStatus());
        campaignUgcMineVo.setUploadTime(campaignUgcDetail.getUploadTime());
        campaignUgcMineVo.setExamineTime(campaignUgcDetail.getExamineTime());

        List<KocDetailImage> campaignUgcImageList = campaignUgcService.getCampaignUgcImageList(campaignUgcDetail.getId());
        campaignUgcMineVo.setImageList(campaignUgcImageList);

        Wechat wechat = wechatService.getWechatInfoByCustomerId(customerId);
        campaignUgcMineVo.setNickName(wechat.getNickName());
        campaignUgcMineVo.setAvatarUrl(wechat.getAvatarUrl());

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignUgcMineVo);

    }

    /**
     * 获取页面数据
     *
     * @param campaignId 活动编号
     * @return 页面数据
     */
    @PostMapping("/top-get-data/{campaignId}")
    public String getTopData(
            @PathVariable("campaignId") long campaignId
    ) {

        Koc campaignUgc = campaignUgcService.getCampaignUgc(campaignId);
        if (null == campaignUgc){
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        // 获取美白达人官活动TOP信息
        KocTop campaignUgcTop = campaignUgcService.getCampaignUgcTop(campaignId);
        if (null == campaignUgcTop) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        CampaignUgcTopVo campaignUgcTopVo = new CampaignUgcTopVo();

        campaignUgcTopVo.setStartTime(campaignUgc.getStartTime());
        campaignUgcTopVo.setEndTime(campaignUgc.getEndTime());

        campaignUgcTopVo.setShareTitle(campaignUgc.getShareTitle());
        campaignUgcTopVo.setShareImageUrl(campaignUgc.getShareImageUrl());

        campaignUgcTopVo.setStage(campaignUgcTop.getStage());
        campaignUgcTopVo.setTopDetailList(campaignUgcService.getCampaignUgcTopDetailList(campaignUgcTop.getId()));
        campaignUgcTopVo.setTopDetailImageList(campaignUgcService.getCampaignUgcTopDetailImageList(campaignUgcTop.getId()));

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignUgcTopVo);

    }

    /**
     * 写入皮肤信息
     * @param skinType 皮肤信息
     * @return 成功
     */
    @PostMapping("/set-skin-type/{campaignId}/{skinType}")
    public String setCampaignUgcSkinType(
            @PathVariable("campaignId") long campaignId,
            @PathVariable("skinType") short skinType
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        // 写入皮肤信息
        campaignUgcService.setCampaignUgcSkinType(campaignId, customerId, skinType);
        // 返回
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 写入访问开始日志
     *
     * @param source 来源
     * @return 成功
     */
    @PostMapping("/view-log-set-start/{campaignId}/{source}/{page}")
    public String setStartViewLog(
            @PathVariable("campaignId") long campaignId,
            @PathVariable("source") String source,
            @PathVariable("page") String page
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        // 写入开始访问
        long id = campaignUgcService.insertCampaignUgcViewLog(campaignId, customerId, source, page);
        // 返回
        return ResultUtil.customer(ResultEnum.SUCCESS, id);
    }

    /**
     * 写入访问结束日志
     *
     * @param id 来源
     * @return 成功
     */
    @PostMapping("/view-log-set-end/{id}")
    public String setEndViewLog(
            @PathVariable("id") long id
    ) {
        campaignUgcService.setCampaignUgcViewLog(id);
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 写入达人傍访问日志
     *
     * @param stage       达人榜编号
     * @param topDetailId 达人榜明细编号
     * @return 成功
     */
    @PostMapping("/top-detail-set-log/{campaignId}/{stage}/{topDetailId}")
    public String setTopDetailLog(
            @PathVariable("campaignId") long campaignId,
            @PathVariable("stage") short stage,
            @PathVariable("topDetailId") long topDetailId
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        KocTopDetailLog campaignUgcTopDetailLog = new KocTopDetailLog();
        campaignUgcTopDetailLog.setCustomerId(customerId);
        campaignUgcTopDetailLog.setCampaignId(campaignId);
        campaignUgcTopDetailLog.setStage(stage);
        campaignUgcTopDetailLog.setTopDetailId(topDetailId);
        campaignUgcService.insertCampaignUgcTopDetailLog(campaignUgcTopDetailLog);

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 写入达人傍访问日志
     *
     * @param stage       达人榜编号
     * @param topDetailId 达人榜明细编号
     * @return 成功
     */
    @PostMapping("/top-detail-image-set-log/{campaignId}/{stage}/{topDetailId}/{imageId}/{type}")
    public String setTopDetailImageLog(
            @PathVariable("campaignId") long campaignId,
            @PathVariable("stage") short stage,
            @PathVariable("topDetailId") long topDetailId,
            @PathVariable("imageId") long imageId,
            @PathVariable("type") short type
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        KocTopDetailImageLog campaignUgcTopDetailImageLog = new KocTopDetailImageLog();
        campaignUgcTopDetailImageLog.setCustomerId(customerId);
        campaignUgcTopDetailImageLog.setCampaignId(campaignId);
        campaignUgcTopDetailImageLog.setStage(stage);
        campaignUgcTopDetailImageLog.setTopDetailId(topDetailId);
        campaignUgcTopDetailImageLog.setImageId(imageId);
        campaignUgcTopDetailImageLog.setType(type);
        campaignUgcService.insertCampaignUgcTopDetailImageLog(campaignUgcTopDetailImageLog);

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

}
