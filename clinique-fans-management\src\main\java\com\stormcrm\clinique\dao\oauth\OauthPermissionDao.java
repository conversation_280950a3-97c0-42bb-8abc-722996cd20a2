package com.stormcrm.clinique.dao.oauth;

import com.stormcrm.clinique.oauth.domain.OauthPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
public interface OauthPermissionDao {

    /**
     * 获取权限
     * @param userId 用户编号
     * @return 权限
     */
    @Select("SELECT " +
            "   `p`.`mark` " +
            "FROM " +
            "   `oauth_user` `u` " +
            "       INNER JOIN " +
            "   `oauth_user_role` `ur` " +
            "       ON `u`.`id`=`ur`.`user_id` " +
            "       INNER JOIN " +
            "   `oauth_role` `r` " +
            "       ON `ur`.`role_id`=`r`.`id` " +
            "       INNER JOIN " +
            "   `oauth_role_permission` `rp` " +
            "       ON `r`.`id`=`rp`.`role_id` " +
            "       INNER JOIN " +
            "   `oauth_permission` `p` " +
            "       ON `rp`.`permission_id`=`p`.`id` " +
            "WHERE " +
            "   `u`.`status`=1 AND " +
            "   `r`.`status`=1 AND " +
            "   `p`.`status`=1 AND " +
            "   `u`.`id`=#{userId}")
    List<OauthPermission> getByUserId(long userId);

}
