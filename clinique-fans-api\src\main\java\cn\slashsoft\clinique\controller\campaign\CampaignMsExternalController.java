package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignMsDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignMsLog;
import cn.slashsoft.clinique.service.mini.SnsService;
import cn.slashsoft.clinique.service.campaign.CampaignMsService;
import cn.slashsoft.clinique.util.CookieUtil;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.util.VerifyUtil;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * 黄油变粉安瓶级保湿补光申领
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/external")
public class CampaignMsExternalController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    @Resource
    private HttpServletResponse response;

    private final CampaignMsService campaignMsService;
    private final SnsService snsService;
    private final StringRedisTemplate stringRedisTemplate;

    public CampaignMsExternalController(CampaignMsService campaignMsService, SnsService snsService, StringRedisTemplate stringRedisTemplate) {
        this.campaignMsService = campaignMsService;
        this.snsService = snsService;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 配置活动信息
     *
     * @param verification 验证
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 处理结果
     * https://clinique.stormcrm.com/external/campaign-config/ms/20-23-04-00-28/2020-04-30-00-00-00/2020-05-15-23-59-59
     */
    @GetMapping("/campaign-config/ms/{verification}/{startTime}/{endTime}")
    @ResponseBody
    public String setConfig(
            @PathVariable("verification") String verification,
            @PathVariable("startTime") String startTime,
            @PathVariable("endTime") String endTime
    ) {
        if (DateUtil.parseVerification(new Date()).equals(verification)) {
            stringRedisTemplate.opsForValue().set("campaignMsStartTime", DateUtil.parseString(DateUtil.valueOf(startTime, "yyyy-MM-dd-HH-mm-ss")));
            stringRedisTemplate.opsForValue().set("campaignMsEndTime", DateUtil.parseString(DateUtil.valueOf(endTime, "yyyy-MM-dd-HH-mm-ss")));
            return "success";
        }
        return "fail";
    }

    /**
     * 不带跳转的页面
     *
     * @param source 来源
     * @param model  模型
     * @return 页面
     */
    @GetMapping("/campaign-ms/{source}.html")
    public String index(
            @PathVariable("source") String source,
            Model model
    ) {

        Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignMsStartTime"));
        Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignMsEndTime"));

        model.addAttribute("notStart", DateUtil.laterThanNow(startTime));
        model.addAttribute("isEnd", DateUtil.earlierThanNow(endTime));

        String phoneNumber = CookieUtil.getCookie("phoneNumber", request);

        CampaignMsDetail campaignMsDetail = null;
        // 读取是否填写过
        if (!StringUtil.isNullOrEmpty(phoneNumber)) {
            return "redirect:/external/campaign-ms-result/" + source + ".html";
        }

        String sessionId = request.getSession().getId().replace("-", "");

        // 保存日志
        CampaignMsLog campaignMsLog = new CampaignMsLog();
        campaignMsLog.setType((short) 3);
        campaignMsLog.setUniqueId(sessionId);
        campaignMsLog.setSource(source);
        campaignMsLog.setPage("index");
        campaignMsService.insertLog(campaignMsLog);

        model.addAttribute("campaignMsDetail", campaignMsDetail);
        model.addAttribute("serviceName", "external");
        model.addAttribute("source", source);
        
            model.addAttribute("staticDomain", staticDomain);
        return "w/fans/official/ms/external/index";
    }

    /**
     * 带跳转到天猫裸链链的页面
     *
     * @param source 来源
     * @param model  模型
     * @return 页面
     */
    @GetMapping("/campaign-ms-tmall/{source}.html")
    public String indexTmall(
            @PathVariable("source") String source,
            Model model
    ) {

        Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignMsStartTime"));
        Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignMsEndTime"));

        model.addAttribute("notStart", DateUtil.laterThanNow(startTime));
        model.addAttribute("isEnd", DateUtil.earlierThanNow(endTime));

        String phoneNumber = CookieUtil.getCookie("phoneNumber", request);

        CampaignMsDetail campaignMsDetail = null;
        // 读取是否填写过
        if (!StringUtil.isNullOrEmpty(phoneNumber)) {
            return "redirect:/external/campaign-ms-result/" + source + ".html";
        }

        String sessionId = request.getSession().getId().replace("-", "");

        // 保存日志
        CampaignMsLog campaignMsLog = new CampaignMsLog();
        campaignMsLog.setType((short) 3);
        campaignMsLog.setUniqueId(sessionId);
        campaignMsLog.setSource(source);
        campaignMsLog.setPage("index");
        campaignMsService.insertLog(campaignMsLog);

        model.addAttribute("campaignMsDetail", campaignMsDetail);
        model.addAttribute("serviceName", "external");
        model.addAttribute("source", source);
        
            model.addAttribute("staticDomain", staticDomain);
        return "w/fans/official/ms/external/index-tmall";
    }

    /**
     * 带跳转到天猫含流量宝链链的页面
     *
     * @param source 来源
     * @param model  模型
     * @return 页面
     */
    @GetMapping("/campaign-ms-tmall-spm/{source}.html")
    public String indexTmallSpm(
            @PathVariable("source") String source,
            Model model
    ) {

        Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignMsStartTime"));
        Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignMsEndTime"));

        model.addAttribute("notStart", DateUtil.laterThanNow(startTime));
        model.addAttribute("isEnd", DateUtil.earlierThanNow(endTime));

        String phoneNumber = CookieUtil.getCookie("phoneNumber", request);

        CampaignMsDetail campaignMsDetail = null;
        // 读取是否填写过
        if (!StringUtil.isNullOrEmpty(phoneNumber)) {
            return "redirect:/external/campaign-ms-result/" + source + ".html";
        }

        String sessionId = request.getSession().getId().replace("-", "");

        // 保存日志
        CampaignMsLog campaignMsLog = new CampaignMsLog();
        campaignMsLog.setType((short) 3);
        campaignMsLog.setUniqueId(sessionId);
        campaignMsLog.setSource(source);
        campaignMsLog.setPage("index");
        campaignMsService.insertLog(campaignMsLog);

        model.addAttribute("campaignMsDetail", campaignMsDetail);
        model.addAttribute("serviceName", "external");
        model.addAttribute("source", source);
        
            model.addAttribute("staticDomain", staticDomain);
        return "w/fans/official/ms/external/index-tmall-spm";
    }

    /**
     * 获取手机验证码
     *
     * @param phoneNumber 手机号码
     * @return 处理结果
     */
    @GetMapping("/campaign-ms-verify-code/{phoneNumber}")
    @ResponseBody
    public String getVerifyCode(
            @PathVariable("phoneNumber") String phoneNumber
    ) {
        return "{\"code\":" + snsService.sendVerifyCode(phoneNumber) + "}";
    }

    /**
     * 提交审领
     *
     * @param source      来源
     * @param name        姓名
     * @param phoneNumber 手机号码
     * @param verifyCode  验证码
     * @param city        城市
     * @param store       门店
     * @return 审领结果
     */
    @PostMapping("/campaign-ms-submit/{source}.html")
    @ResponseBody
    public String submit(
            @PathVariable("source") String source,
            @RequestParam("formName") String name,
            @RequestParam("formPhoneNumber") String phoneNumber,
            @RequestParam("formVerifyCode") String verifyCode,
            @RequestParam("formCity") String city,
            @RequestParam("formStore") String store
    ) {

        Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignMsStartTime"));
        Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignMsEndTime"));

        if (DateUtil.laterThanNow(startTime)) {
            return "{\"code\":9,\"message\":\"活动未开始\"}";
        }

        if (DateUtil.earlierThanNow(endTime)) {
            return "{\"code\":9,\"message\":\"活动已结束\"}";
        }

        if (!VerifyUtil.required(name)) {
            return "{\"code\":9,\"message\":\"请输入姓名\"}";
        }

        if (!VerifyUtil.required(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.isPhoneNumber(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.required(verifyCode)) {
            return "{\"code\":9,\"message\":\"请输入验证码\"}";
        }

        if (!VerifyUtil.isVerifyCode(verifyCode)) {
            return "{\"code\":9,\"message\":\"验证码格式错误\"}";
        }

        if (!VerifyUtil.required(city)) {
            return "{\"code\":9,\"message\":\"请选择城市\"}";
        }

        if (!VerifyUtil.required(store)) {
            return "{\"code\":9,\"message\":\"请选择柜台\"}";
        }

        // 验证手机是否一致
        String sessionPhoneNumber = (String) request.getSession().getAttribute("verifyPhoneNumber");
        if (!phoneNumber.equals(sessionPhoneNumber)) {
            return "{\"code\":9,\"message\":\"请重新获取验证码\"}";
        }

        // 验证是否正确
        String sessionVerifyCode = (String) request.getSession().getAttribute("verifyCode");
        if (!verifyCode.equals(sessionVerifyCode)) {
            return "{\"code\":9,\"message\":\"验证码错误\"}";
        }

        // 验证码是否过期
        Date sessionVerifyTime = DateUtil.valueOf((String) request.getSession().getAttribute("verifyTime"));
        if (DateUtil.earlierThanNow(sessionVerifyTime)) {
            return "{\"code\":9,\"message\":\"验证码已过期\"}";
        }

        String sessionId = request.getSession().getId().replace("-", "");

        // 申领
        CampaignMsDetail campaignMsDetail = new CampaignMsDetail();
        campaignMsDetail.setType((short) 3);
        campaignMsDetail.setUniqueId(sessionId);
        campaignMsDetail.setName(name);
        campaignMsDetail.setPhoneNumber(phoneNumber);
        campaignMsDetail.setCity(city);
        campaignMsDetail.setStore(store);
        campaignMsDetail.setSource(source);

        // 0:成功，1：手机号码已经领过了，2：库存不足
        switch (campaignMsService.submit(campaignMsDetail)) {
            case 0:
                snsService.msExternal(phoneNumber, store);
                CookieUtil.addCookie("phoneNumber", phoneNumber, response);
                return "{\"code\":0}";
            case 1:
                return "{\"code\":1}";
            case 2:
                return "{\"code\":2}";
            default:
                return "{\"code\":3}";
        }

    }

    /**
     * 审领结果页
     *
     * @param source 来源
     * @param model  模型
     * @return 页面
     */
    @GetMapping("/campaign-ms-result/{source}.html")
    public String result(
            @PathVariable("source") String source,
            Model model
    ) {

        String phoneNumber = CookieUtil.getCookie("phoneNumber", request);

        if (StringUtil.isNullOrEmpty(phoneNumber)) {
            return "redirect:/external/campaign-ms/" + source + ".html";
        }

        // 读取申领信息
        CampaignMsDetail campaignMsDetail = campaignMsService.getDetail(phoneNumber);
        if (null == campaignMsDetail) {
            CookieUtil.removeCookie("phoneNumber", response);
            return "redirect:/external/campaign-ms/" + source + ".html";
        }

        String sessionId = request.getSession().getId().replace("-", "");

        // 保存日志
        CampaignMsLog campaignMsLog = new CampaignMsLog();
        campaignMsLog.setType((short) 3);
        campaignMsLog.setUniqueId(sessionId);
        campaignMsLog.setSource(source);
        campaignMsLog.setPage("result");
        campaignMsService.insertLog(campaignMsLog);
       
            model.addAttribute("staticDomain", staticDomain);
        model.addAttribute("campaignMsDetail", campaignMsDetail);
        return "w/fans/official/ms/external/success";

    }

}
