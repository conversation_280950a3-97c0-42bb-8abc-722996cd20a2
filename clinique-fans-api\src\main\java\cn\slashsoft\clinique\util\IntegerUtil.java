package cn.slashsoft.clinique.util;

/**
 * 整型工具类
 * <AUTHOR>
 */
public class IntegerUtil {

    /**
     * 字符串转整型
     * @param s 字符串
     * @return 整型
     */
    public static int parseInt(String s){
        try {
            if(null == s || s.isEmpty()){
                return 0;
            }
            return Integer.parseInt(s);
        }
        catch (Exception e){
            return 0;
        }
    }

}
