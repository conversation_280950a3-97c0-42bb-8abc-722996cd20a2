package cn.slashsoft.clinique.controller.mini;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.CactiveService;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.mini.CactiveRankVo;

/**
 * 动作
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class CactiveController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final CactiveService caService;

    public CactiveController(CactiveService caService) {
        this.caService = caService;
    }

    /**
     *  获取排行     
     * @return 实时排行
     */
    @PostMapping("/cactive/rank/get-data")
    public String getAll( ) {

		
		// 获取缓存中的顾客编号
		Long customerId = (Long) request.getAttribute("customerId");

		if (customerId == null || customerId.intValue() == 0) {
			return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
		}

		CactiveRankVo crVo = new CactiveRankVo();
		crVo.setCactiveRankList(this.caService.getRank(20));
		crVo.setMyRank(this.caService.getCustomerRank(customerId));
		
        return ResultUtil.customer(ResultEnum.SUCCESS, crVo);
    }
    
    /**
     * 获取当前参与人数
     */

    @PostMapping("/cactive/rank/get-count")
    public String getCount( ) {
        return ResultUtil.customer(ResultEnum.SUCCESS, this.caService.getRankCount());
    }

    
    /**
     * 获取当前详情
     * from season 2， 2021.5.10
     */

    @PostMapping("/cactive/rank/get-detail")
    public String getDetail( ) {
		// 获取缓存中的顾客编号
		Long customerId = (Long) request.getAttribute("customerId");
		if (customerId == null || customerId.intValue() == 0) {
			return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
		}
        return ResultUtil.customer(ResultEnum.SUCCESS, this.caService.getRankDetail(customerId));
    }
  
}
