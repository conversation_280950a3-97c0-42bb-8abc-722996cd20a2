package com.stormcrm.clinique.service.tag.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.stormcrm.clinique.domain.tag.TagTask;
import com.stormcrm.clinique.service.tag.TagTaskService;
import com.stormcrm.clinique.tag.dao.TagTaskDao;
/**
 * 
 *
 * <AUTHOR>
 */

@Service
public class TagTaskServiceImpl implements TagTaskService {

    @Resource
    TagTaskDao tagTaskDao;

    @Override
    public TagTask getTagTask(int taskId) {
        return tagTaskDao.getTagTask(taskId);
    }

    @Override
    public List<TagTask> getTagTaskList() {
        return tagTaskDao.getTagTaskAll();
    }

    @Override
    public List<TagTask> getTagTaskBy(int progress, int target) {
    	if(progress > 0) {
    		if(target > 0) {
    			return tagTaskDao.getTagTaskBy(progress, target);
    		}else {
    			 return tagTaskDao.getTagTaskByProgress(progress);
    		}
    	}else {
    		if(target > 0) {
    			return tagTaskDao.getTagTaskByTarget(target);
    		}else {
    			return tagTaskDao.getTagTaskAll();
    		}    		
    	}        
    }
    
    @Override
    public int addTagTask(TagTask task) {
       return tagTaskDao.addTagTask(task);
    }

    @Override
    public int editTagTask(TagTask task) {
        return tagTaskDao.updateTagTask(task);
    }
    
    @Override
    public int delTagTask(int taskId) {
        return tagTaskDao.removeTagTask(taskId);
    }
    
}
