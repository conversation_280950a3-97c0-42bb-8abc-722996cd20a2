package cn.slashsoft.clinique.service.mini;

import java.util.List;

import cn.slashsoft.clinique.domain.mini.SpecialBanner;

/**
 * 用户特权banner
 *
 * <AUTHOR>
 */
public interface SpecialService {

	/**
	 * banner 是否可以看到koc beta
	 *
	 * 1 返回SpecialBanner
	 */
	SpecialBanner koccheck(Long customerId) ;

	/**
	 * banner 新老客
	 *
	 * 1 返回SpecialBanner
	 */
	SpecialBanner oldnewcheck(String unionid, Long customerId) ;
	

	SpecialBanner days3signCheck(String unionid, String miniOpenid, Long customerId);

	List<SpecialBanner> getNormalSpecial();

	SpecialBanner onlineDownCheck(String unionid, String openid, long customerId);

}
