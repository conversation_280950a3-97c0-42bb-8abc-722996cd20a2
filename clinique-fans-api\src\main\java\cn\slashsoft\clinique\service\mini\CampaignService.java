package cn.slashsoft.clinique.service.mini;

import cn.slashsoft.clinique.domain.mini.Campaign;
import cn.slashsoft.clinique.domain.mini.CampaignLog;

import java.util.List;

/**
 * 与活动相关
 *
 * <AUTHOR>
 */
public interface CampaignService {
    /**
     * 获取新客活动排除名单
     * @param openId 小程序openId
     * @return 数量
     */
    int getCampaignNewFreeExclude(String openId);

    /**
     * 获取排除名单
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    int getCampaignC520Exclude(String phoneNumber);

    /**
     * 获取有效活动信息，按更新时间倒序排列
     * 活动资讯
     *
     * @return 活动信息
     */
    List<Campaign> getCampaign();

    /**
     * 写入日志
     *
     * @param campaignLog 日志
     */
    void setCampaignLog(CampaignLog campaignLog);

}
