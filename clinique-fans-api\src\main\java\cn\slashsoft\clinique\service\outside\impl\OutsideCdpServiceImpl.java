package cn.slashsoft.clinique.service.outside.impl;

import cn.slashsoft.clinique.domain.cdp.Member;
import cn.slashsoft.clinique.service.outside.OutsideCdpService;
import cn.slashsoft.clinique.util.HttpUtil;
import cn.slashsoft.clinique.util.IntegerUtil;
import cn.slashsoft.clinique.util.StringUtil;
import lombok.extern.java.Log;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;

/**
 * CDP接口
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Log
@Service
public class OutsideCdpServiceImpl implements OutsideCdpService {

    @Value("${cdp.url}")
    private String baseUrl;

    @Value("${cdp.get-customer-by-unionid}")
    private String urlCdpSelectCustomerInfoByOpenid;

    /**
     * 获取CDP用户信息
     *
     * @param miniOpenid 小程序唯一编号
     * @param unionid    开放平台唯一编号
     * @return 用户信息
     * @since 2023-12-04
     */
    @Override
    public Member getCdpCustomerByUnionid(String unionid, String miniOpenid) {

        try {

            String url = baseUrl + urlCdpSelectCustomerInfoByOpenid + unionid;
            String response = HttpUtil.get(url);

            log.warning("CDP-获取用户信息-" + unionid + ": " + response);

            Document document = new SAXReader().read(new ByteArrayInputStream(response.getBytes(StandardCharsets.UTF_8)));
            Element root = document.getRootElement();
            if (!"True".equals(root.elementText("Code"))) {
                return null;
            }

            Element result = root.element("ResultList");
            Member member = new Member();

            // 变量名不同，转换一下
            member.setUnionid(unionid);
            member.setOfficialOpenid(result.elementText("OfficialOpenID"));
            member.setMiniOpenid(miniOpenid);

            // 姓名、手机、性别、生日
            member.setName(result.elementText("Name"));
            member.setPhoneNumber(result.elementText("Mobile"));
            member.setGender(result.elementText("Gender"));
            member.setBirthday(result.elementText("Birthday"));

            // 会员唯一编号
            member.setCustomerSid(result.elementText("ShortNumber"));
            member.setUnionid(unionid);

            String regChannel = result.elementText("RegChannel");
            String firstChannelPurchase = result.elementText("FirstChannelPurchase");

            // 类型：1线上，2线下
            member.setCustomerType(memberType(regChannel, firstChannelPurchase));

            // 购买渠道
            member.setFirstPurchaseChannel(firstChannelPurchase);
            member.setFirstPurchaseDate(result.elementText("FirstPurchaseDate"));
            member.setNewCustomer(result.elementText("IsNewCustomer"));
            member.setFourMonthsPurchaseCustomer(result.elementText("IsFourMonthsPosMultiplePurchaseCustomer"));

            // 三个月内购买
            member.setThreeMonthsPurchaseCustomer(result.elementText("IsThreeMonthsPurchaseCustomer"));
            member.setThreeMonthsMultiplePurchaseCustomer(result.elementText("IsThreeMonthsMultiplePurchaseCustomer"));

            // 绑定的美容顾问
            member.setBaCardNo(result.elementText("BACardNo"));
            member.setBindTime(result.elementText("BindTime"));

            // 隐私条款
            member.setAgreePrivacyAgreement("true".equals(result.elementText("Latest_access")));

            // 会员等级
            member.setLevel(result.elementText("CustomerType"));
            member.setLevelExpiredDate(result.elementText("VIPDueDate"));

            // 可用积分、三个月即将到期积分
            member.setPoints(IntegerUtil.parseInt(result.elementText("TotalPoints")));
            member.setDuePoints(IntegerUtil.parseInt(result.elementText("DuePoints")));

            // 肤质
            member.setSkinType(result.elementText("SkinType"));
            member.setSkincareStep(result.elementText("SkinCareStep"));
            member.setSkincareEffect(result.elementText("SkinProblem"));
            member.setCosmeticsEffect(result.elementText("MakeUpFocus"));

            return member;

        } catch (Exception e) {
            log.warning("CDP-获取用户信息-" + unionid + "  Exception: " + e.getMessage());
            return null;
        }

    }

    /**
     * 2022-10-28
     * 先判断首次购买（老客）不为空的，且是Pos的，则为线下会员身份，否则为线上会员身份
     * 如果首次购买为空，再判断注册渠道，是TmallCL、VIPCL、DouYinCL、JDCL、E-commerce、call center为线上会员身份，否则为线下会员身份
     * 2020-07-22
     * 判断会员类型 先判断首次购买（老客）不为空的，是E-commerce或者Tmall的为线上，否则为线下，
     * 如果首次购买为空，再判断注册渠道，是E-commerce或者Tmall 或才空的为线上，否则为线下
     *
     * @param firstChannelPurchase 首单
     * @param regChannel           注册渠道
     * @return 类型
     */
    private short memberType(String regChannel, String firstChannelPurchase) {
        if (!StringUtil.isNullOrEmpty(firstChannelPurchase)) {
            if ("POS".equalsIgnoreCase(firstChannelPurchase)) {
                return (short) 2;
            } else {
                return (short) 1;
            }
        } else {
            if (!StringUtil.isNullOrEmpty(regChannel) && ("TMALLCL".equalsIgnoreCase(regChannel) || "VIPCL".equals(regChannel) || "DouYinCL".equals(regChannel) || "JDCL".equals(regChannel) || "E-COMMERCE".equals(regChannel) || "CALL CENTER".equals(regChannel))) {
                return (short) 1;
            } else {
                return (short) 2;
            }
        }
    }

}
