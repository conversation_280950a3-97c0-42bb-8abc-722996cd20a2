package cn.slashsoft.clinique.service.mini;


import cn.slashsoft.clinique.domain.mini.TTestimonyAmount;

/**
* <p>
    * 为提高效率，应该放到redis当中
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
public interface TTestimonyAmountService {
    void insertTTestimonyAmount(TTestimonyAmount tTestimonyAmount);
    void updateTTestimonyAmount(TTestimonyAmount tTestimonyAmount);
    TTestimonyAmount getTTestimonyAmount(Long id);
    Integer getTTestimonyLikeCountByCustomerId(Long customerId);
    Integer getTTestimonyLikeRankByCustomerId(Long customerId);
    void updateTTestimonyAmountByCustomerId(Long customerId);
    TTestimonyAmount getTTestimonyAmountByCustomerId(Long customerId);
}
