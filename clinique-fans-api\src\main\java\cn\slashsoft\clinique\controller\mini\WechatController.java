package cn.slashsoft.clinique.controller.mini;

import cn.slashsoft.clinique.domain.mini.OutCustomer;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.domain.mini.WechatAuthorize;
import cn.slashsoft.clinique.domain.mini.WechatRegisterStepLog;
import cn.slashsoft.clinique.domain.official.WechatFriend;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.util.EncryptUtil;
import cn.slashsoft.clinique.util.HttpUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.vo.*;
import cn.slashsoft.clinique.vo.mini.*;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Objects;
import java.util.logging.Logger;

/**
 * 登陆相关控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class WechatController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    private final Logger logger = Logger.getLogger(WechatController.class.getName());

    @Resource
    private HttpServletRequest request;

    @Resource
    private HttpSession session;

    private final WechatService wechatService;
    private final OutsideService outsideService;
    @Value("${wechat.mini.appid}")
    private String appId;

    @Value("${wechat.mini.appsecret}")
    private String appSecret;

    @Value("${wechat.mini.url.code-2-session}")
    private String urlCode2Session;


    public WechatController(WechatService wechatService, OutsideService outsideService) {
        this.wechatService = wechatService;
        this.outsideService = outsideService;
    }
    /**
     * 登录接口
     *
     * @param wxLogin 微信小程序中的wx.login参数
     * @return 登录及注册信息
     */
    @PostMapping("/login")
    public String login(
            @RequestBody WxLogin wxLogin
    ) {

        logger.info("01 - 开始登陆");

        // 用Code换取unionid
        Result result = wechatService.code2Unionid(wxLogin.getCode(), wxLogin.getScene());

        logger.info("08 - 登陆结果（1:成功, 2:openid存在, 3:异常）：" + result.getCode());

        // 1:成功, 2:openid存在, 3:异常
        switch (result.getCode()) {
            case 1:
                RegisterVo registerVo = new RegisterVo();

                logger.info("09 - 登陆成功：判断是否已绑定");

                Wechat wechat = (Wechat) result.getData();
                registerVo.setRegister(false);
                if (0L == wechat.getCustomerId()) {
                    registerVo.setRegister(false);
                    // 写入注册日志
                    WechatRegisterStepLog wechatRegisterStepLog = new WechatRegisterStepLog();
                    wechatRegisterStepLog.setOpenid(wechat.getWechatMiniOpenid());
                    wechatRegisterStepLog.setStep((short) 1);
                    wechatService.setRegisterStepLog(wechatRegisterStepLog);

                    registerVo.setVerifyOfficialOpenid(false);
                    registerVo.setVerifyPhoneNumber(false);
                    registerVo.setKocV(wechat.getKocV());
                } else {
                    registerVo.setRegister(true);

                    registerVo.setVerifyOfficialOpenid(false);
                    registerVo.setVerifyPhoneNumber(StringUtil.isNullOrEmpty(wechat.getPhoneNumber()));
                    registerVo.setKocV(wechat.getKocV());
                }

                logger.info("10 - 开始写入SESSION");

                session.setAttribute("openid", wechat.getWechatMiniOpenid());
                session.setAttribute("miniOpenid", wechat.getWechatMiniOpenid());
                session.setAttribute("unionid", wechat.getUnionid());
                session.setAttribute("customerId", wechat.getCustomerId());

                logger.info("11 - 写入SESSION成功");

                registerVo.setOpenid(wechat.getWechatMiniOpenid());
                registerVo.setUnionid(EncryptUtil.unionidEncode(wechat.getUnionid()));
                registerVo.setTrueunionid(wechat.getUnionid());

                return ResultUtil.customer(ResultEnum.SUCCESS, registerVo);
            case 2:

                return ResultUtil.customer(ResultEnum.PROCESS_BUSY, result.getMessage());
            default:
                return ResultUtil.customer(ResultEnum.EXCEPTION, result.getMessage());
        }

    }

    /**
     * 登录接口
     *
     * @param wxLogin 微信小程序中的wx.login参数
     * @return 登录及注册信息
     */
    @PostMapping("/check")
    public String check(@RequestBody WxLogin wxLogin){

        String unionid = (String) session.getAttribute("unionid");
        String miniOpenid = (String) session.getAttribute("miniOpenid");

        // 用Code换取unionid
        Wechat wechat = wechatService.checkRegister(unionid, miniOpenid, wxLogin.getScene());

        RegisterVo registerVo = new RegisterVo();
        if (0L == wechat.getCustomerId()) {
            registerVo.setRegister(false);

            registerVo.setVerifyOfficialOpenid(false);
            registerVo.setVerifyPhoneNumber(false);
            registerVo.setKocV(wechat.getKocV());
        }
        else{
            registerVo.setRegister(true);
            registerVo.setVerifyOfficialOpenid(false);
            registerVo.setVerifyPhoneNumber(StringUtil.isNullOrEmpty(wechat.getPhoneNumber()));
            registerVo.setKocV(wechat.getKocV());

            session.setAttribute("openid", wechat.getWechatMiniOpenid());
            session.setAttribute("miniOpenid", wechat.getWechatMiniOpenid());
            session.setAttribute("unionid", wechat.getUnionid());
            session.setAttribute("customerId", wechat.getCustomerId());
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, registerVo);

    }

    @PostMapping("/is-friend")
    public String isFriend() {
        String unionid = (String) session.getAttribute("unionid");
        WechatFriend wf = wechatService.getWechatFriend(unionid);
        if(null != wf && wf.getStatus()==1) {
            return ResultUtil.customer(ResultEnum.SUCCESS);
        }
        return ResultUtil.customer(ResultEnum.FAILED);
    }

    /**
     * 是否已注册接口
     *
     * @return 登录及注册信息
     */
    @PostMapping("/get-is-register")
    public String isRegister() {

        // 读取缓存中的unionid
        String openid = (String) request.getAttribute("miniOpenid");

        Wechat wechat = wechatService.getWechatByOpenid(openid);

        RegisterVo registerVo = new RegisterVo();

        if (null == wechat || 0L == wechat.getCustomerId()) {
            registerVo.setRegister(false);
        } else {
            registerVo.setRegister(true);
        }

        // 返回
        return ResultUtil.customer(ResultEnum.SUCCESS, registerVo);

    }

    /**
     * 更新微信信息
     * @param authorize 微信小程序传入的授权信息
     * @return 处理结果
     */
    @PostMapping("/set-wechat")
    public String setWechat(@RequestBody Authorize authorize){

        String sessionKey = (String) session.getAttribute("sessionKey");
        if (StringUtil.isNullOrEmpty(sessionKey)) {
            return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
        }

        authorize.setSessionKey(sessionKey);

        // 解密并保存启动记录
        Result result = wechatService.setWechat(authorize);

        // 解密失败
        if (1 != result.getCode()) {
            return ResultUtil.customer(ResultEnum.FAILED, result.getMessage());
        }

        // 返回
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 授权接口
     *
     * @param authorize 微信小程序传入的授权信息
     * @return 处理结果
     */
    @PostMapping("/authorize")
    public String authorize(
            @RequestBody Authorize authorize
    ) {

        String sessionKey = (String) session.getAttribute("sessionKey");
        if (StringUtil.isNullOrEmpty(sessionKey)) {
            return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
        }

        // 写入注册日志
        WechatRegisterStepLog wechatRegisterStepLog = new WechatRegisterStepLog();
        wechatRegisterStepLog.setOpenid((String) request.getAttribute("miniOpenid"));
        wechatRegisterStepLog.setStep((short) 2);
        wechatService.setRegisterStepLog(wechatRegisterStepLog);

        authorize.setSessionKey(sessionKey);

        // 解密并保存启动记录
        Result result = wechatService.authorize(authorize);
        // 解密失败
        if (1 != result.getCode()) {
            return ResultUtil.customer(ResultEnum.FAILED, result.getMessage());
        }
        // 解密信息
        WechatAuthorize wechatAuthorize = (WechatAuthorize) result.getData();

        // 返回值
        AuthorizeVo authorizeVo = new AuthorizeVo();

        // 用unionid去外部平台查找是否已注册
        OutCustomer outCustomer = outsideService.getMainMemberWithUnionid(wechatAuthorize.getUnionid());
        // 未注册用户
        if (null == outCustomer || StringUtil.isNullOrEmpty(outCustomer.getPhoneNumber()) || StringUtil.isNullOrEmpty(outCustomer.getOpenid())){
            // TODO: 用unionid去商城平台查找是否已注册
            authorizeVo.setRegister(false);
            return ResultUtil.customer(ResultEnum.SUCCESS, authorizeVo);
        }

        wechatAuthorize.setPhoneNumber(outCustomer.getPhoneNumber());
        wechatAuthorize.setWechatOfficialOpenid(outCustomer.getOpenid());
        // 分享者的openid
        wechatAuthorize.setOpenid(authorize.getOpenid());
        wechatAuthorize.setCampaign(authorize.getCampaign());
        wechatAuthorize.setSource(authorize.getSource());

        // 结果: 1,成功; 4 手机号码已存在, 5，开放平台或小程序的唯一编号冲突
        result = wechatService.registerWithOutside(wechatAuthorize);
        if (1 != result.getCode()) {
            authorizeVo.setRegister(false);
            return ResultUtil.customer(ResultEnum.SUCCESS, authorizeVo);
        }

        Wechat wechat = (Wechat) result.getData();

        // 写入缓存
        session.setAttribute("unionid", wechat.getUnionid());
        session.setAttribute("customerId", wechat.getCustomerId());
        authorizeVo.setRegister(true);

        // 更新注册步骤
        wechatRegisterStepLog.setStep((short) 4);
        wechatService.setRegisterStepLog(wechatRegisterStepLog);

        // 传给公众号
        //outsideService.bindMember(wechatAuthorize.getUnionid(), wechatAuthorize.getWechatOfficialOpenid());

        // 返回
        return ResultUtil.customer(ResultEnum.SUCCESS, authorizeVo);

    }

    /**
     * 解密手机号码
     *
     * @param phoneNumber 微信小程序传入的信息
     * @return 手机号码
     */
    @PostMapping("/get-phone-number")
    public String getPhoneNumber(
            @RequestBody PhoneNumber phoneNumber
    ) {

        String sessionKey = (String) session.getAttribute("sessionKey");
        if (StringUtil.isNullOrEmpty(sessionKey)) {
            return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
        }

        // 写入注册日志
        WechatRegisterStepLog wechatRegisterStepLog = new WechatRegisterStepLog();
        wechatRegisterStepLog.setOpenid((String) request.getAttribute("miniOpenid"));
        wechatRegisterStepLog.setStep((short) 3);
        wechatService.setRegisterStepLog(wechatRegisterStepLog);

        phoneNumber.setSessionKey(sessionKey);

        // 1,成功; 2，控糖密码不存在或已失效
        Result result = wechatService.getPhoneNumber(phoneNumber);
        if (1 != result.getCode()) {
            return ResultUtil.customer(ResultEnum.FAILED, result.getMessage());
        }

        // 返回
        return ResultUtil.customer(ResultEnum.SUCCESS, result.getData());

    }

    /**
     * 设置手机号码
     *
     * @param phoneNumber 微信小程序传入的信息
     * @return 手机号码
     */
    @PostMapping("/set-phone-number")
    public String setPhoneNumber(
            @RequestBody PhoneNumber phoneNumber
    ) {

        String sessionKey = (String) session.getAttribute("sessionKey");
        if (StringUtil.isNullOrEmpty(sessionKey)) {
            return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
        }

        String openid = (String) request.getAttribute("miniOpenid");

        phoneNumber.setOpenid(openid);
        phoneNumber.setSessionKey(sessionKey);

        // 1,成功; 2，控糖密码不存在或已失效
        Result result = wechatService.setPhoneNumber(phoneNumber);
        if (1 != result.getCode()) {
            return ResultUtil.customer(ResultEnum.FAILED, result.getMessage());
        }

        // 返回
        return ResultUtil.customer(ResultEnum.SUCCESS, result.getData());

    }

    /**
     * 注册接口
     *
     * @param register 微信小程序传入的信息
     * @return 注册信息
     */
    @PostMapping("/register")
    public String register(
            @RequestBody Register register
    ) {

        String sessionKey = (String) session.getAttribute("sessionKey");
        if (StringUtil.isNullOrEmpty(sessionKey)) {
            return ResultUtil.customer(ResultEnum.UNAUTHORIZED);
        }

        // 写入注册日志
        WechatRegisterStepLog wechatRegisterStepLog = new WechatRegisterStepLog();
        wechatRegisterStepLog.setOpenid((String) request.getAttribute("miniOpenid"));
        wechatRegisterStepLog.setStep((short) 4);
        wechatService.setRegisterStepLog(wechatRegisterStepLog);

        register.setSessionKey(sessionKey);

        // 1,成功; 2，手机号码解密失败; 3,用户数据解密失败; 4,手机号码已被注册; 5:开放平台或小程序的唯一编号冲突
        Result result = wechatService.register(register,(String)request.getAttribute("miniOpenid") );
        if (1 != result.getCode()) {
            return ResultUtil.customer(ResultEnum.FAILED, result.getMessage());
        }

        Wechat wechat = (Wechat) result.getData();

        // 写入缓存
        session.setAttribute("unionid", wechat.getUnionid());
        session.setAttribute("customerId", wechat.getCustomerId());

        // 返回页面的数据
        RegisterVo registerVo = new RegisterVo();
        registerVo.setUnionid(EncryptUtil.unionidEncode(wechat.getUnionid()));
        registerVo.setRegister(true);

        // 返回
        return ResultUtil.customer(ResultEnum.SUCCESS, registerVo);

    }

    /**
     * 获取微信信息
     * @return 微信信息
     */
    @PostMapping("/get-wechat")
    public String getWechat(){

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        Wechat wechat = wechatService.getWechatInfoByCustomerId(customerId);

        return ResultUtil.customer(ResultEnum.SUCCESS, wechat);

    }

    /**
     * 打开小程序首页的来源
     * @param source 来源
     * @return 处理结果
     */
    @PostMapping("/set-originate/{source}")
    @ResponseBody
    public String setOriginate(
            @PathVariable("source") String source
    ){

        wechatService.setOriginate(source, (String) request.getAttribute("miniOpenid"));
        return ResultUtil.customer(ResultEnum.SUCCESS);

    }

    /**
     * 发放场景值福利
     * @param wxLogin 微信登录信息
     * @return 处理结果
     */
    @PostMapping("/set-scene-reward")
    public String setSceneReward(
            @RequestBody WxLogin wxLogin
    ){

        wechatService.setSceneReward(wxLogin.getScene(), (String) request.getAttribute("miniOpenid"));
        return ResultUtil.customer(ResultEnum.SUCCESS);

    }
    /**
     * 保存未登录用户访问记录
     * @param wxLogin 微信登录信息
     * @return 处理结果
     */
    @PostMapping("/set-nologin-log")
    public String setNoLoginLog(
            @RequestBody WxLogin wxLogin
    ){

    	try {
            JSONObject json = (JSONObject) JSON.parse(HttpUtil.get(
            		urlCode2Session.replace("APPID", appId).replace("SECRET", appSecret).replace("JSCODE", wxLogin.getCode())));
            String openid = json.getString("openid");
            wechatService.setNoLoginLog(wxLogin.getScene(), openid);
    	}catch(Exception e){
    		
    	}
        return ResultUtil.customer(ResultEnum.SUCCESS);

    }

    /**
     * 更新小程序文案配置
     *
     * @return 处理结果
     */
    @GetMapping("/updateConfig")
    public String updateConfig() {
        wechatService.updateConfig();
        return "success";
    }

}
