package cn.slashsoft.clinique.util;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期工具类
 *
 * <AUTHOR>
 */
public class DateUtil {

    /**
     * 按指定格式将字符串转换成日期
     *
     * @param s 日期字符串
     * @param p 格式
     * @return 日期
     */
    public static Date valueOf(String s, String p) {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(p);
            return simpleDateFormat.parse(s);
        } catch (Exception e) {
            return null;
        }
    }
    /**
     * 格式成 yyyy-MM-dd 样式的日期字符串
     *
     * @param d 日期
     * @return 日期字符串
     */
    public static String parseCnDayString(Date d) {
        return parseString(d, "yyyy年MM月dd日");
    }

    /**
     * 按 yyyy-MM-dd 格式将字符串转换成日期
     *
     * @param s 日期字符串
     * @return 日期
     */
    public static Date valueOfDay(String s) {
        return valueOf(s, "yyyy-MM-dd");
    }

    /**
     * 按 yyyy-MM-dd HH:mm:ss 格式将字符串转换成日期
     *
     * @param s 日期字符串
     * @return 日期
     */
    public static Date valueOf(String s) {
        return valueOf(s, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 跟据指定格式格式化日期
     *
     * @param d 日期
     * @param p 格式
     * @return 日期字符串
     */
    public static String parseString(Date d, String p) {
        return new SimpleDateFormat(p).format(d);
    }

    /**
     * 格式成 yyyy-MM-dd HH:mm:ss 样式的日期字符串
     *
     * @param d 日期
     * @return 日期字符串
     */
    public static String parseString(Date d) {
        return parseString(d, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 格式成 yyyy-MM-dd 样式的日期字符串
     *
     * @param d 日期
     * @return 日期字符串
     */
    public static String parseDayString(Date d) {
        return parseString(d, "yyyy-MM-dd");
    }

    /**
     * 格式成 yyyyHHMMmmdd 样式的日期字符串
     *
     * @param d 日期
     * @return 日期字符串
     */
    public static String parseVerification(Date d) {
        return parseString(d, "yy-HH-MM-mm-dd");
    }

    /**
     * 判断 D1 是否早于 D2
     *
     * @param d1 日期
     * @param d2 日期
     * @return 是，否
     */
    public static boolean earlierThan(Date d1, Date d2) {
        try {
        	Long s = d1.getTime();
        	Long e = d2.getTime();
            return s.compareTo( e) < 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断 日期 是否早于 现在
     *
     * @param date 日期
     * @return 是，否
     */
    public static boolean earlierThanNow(Date date) {
        return earlierThan(date, new Date());
    }

    /**
     * 判断 D1 是否晚于 D2
     *
     * @param d1 日期
     * @param d2 日期
     * @return 是，否
     */
    public static boolean laterThan(Date d1, Date d2) {
        try {
        	Long s = d1.getTime();
        	Long e = d2.getTime();
            return s.compareTo( e) > 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断 日期 是否晚于 现在
     *
     * @param date 日期
     * @return 是，否
     */
    public static boolean laterThanNow(Date date) {
        return laterThan(date, new Date());
    }

    /**
     * 判断二个日期是否是同一天
     *
     * @param o 日期
     * @param n 日期
     * @return 是，否
     */
    public static boolean isSameDay(Date o, Date n) {
        return null != o && null != n && getYear(o) == getYear(n) && getMonth(o) == getMonth(n) && getDay(o) == getDay(n);
    }

    /**
     * 判断二个日期是否是同一周
     *
     * @param o 日期
     * @param n 日期
     * @return 是，否
     */
    public static boolean isSameWeek(Date o, Date n) {
        return laterThan(n, getFirstSecondOfWeek(o)) && earlierThan(n, getLastSecondOfWeek(o));
    }

    /**
     * 判断日期是否是今天
     *
     * @param d 日期
     * @return 是，否
     */
    public static boolean isToday(Date d) {
        return isSameDay(d, new Date());
    }

    /**
     * 判断日期是否是昨天
     *
     * @param d 日期
     * @return 是，否
     */
    public static boolean isYesterday(Date d) {
        return isSameDay(d, addDay(new Date(), -1));
    }

    /**
     * 获取指定日期的月份的第一天
     *
     * @param d 日期
     * @return 处理后的日期
     */
    public static Date getFirstDayOfMonth(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.set(Calendar.DAY_OF_MONTH, 1);
        return c.getTime();
    }

    /**
     * 获取指定日期的月份的最后一天
     *
     * @param d 日期
     * @return 处理后的日期
     */
    public static Date getLastDayOfMonth(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);

        c.add(Calendar.MONTH, 1);
        c.set(Calendar.DAY_OF_MONTH, 1);
        c.add(Calendar.DAY_OF_MONTH, -1);
        return c.getTime();
    }

    /**
     * 获取指定日期的周的第一天
     *
     * @param d 日期
     * @return 处理后的日期
     */
    public static Date getFirstDayOfWeek(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        int dayOfWeek = c.get(Calendar.DAY_OF_WEEK);
        dayOfWeek = 1 == dayOfWeek ? 7 : dayOfWeek - 1;
        c.add(Calendar.DAY_OF_MONTH, -dayOfWeek + 1);
        return c.getTime();
    }

    /**
     * 获取指定日期的周的最后一天
     *
     * @param d 日期
     * @return 处理后的日期
     */
    public static Date getLastDayOfWeek(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        int dayOfWeek = c.get(Calendar.DAY_OF_WEEK);
        dayOfWeek = 1 == dayOfWeek ? 7 : dayOfWeek - 1;
        c.add(Calendar.DAY_OF_MONTH, 7 - dayOfWeek);
        return c.getTime();
    }

    /**
     * 获取指定日期的周的第一天的第1秒
     *
     * @param d 日期
     * @return 处理后的日期
     */
    public static Date getFirstSecondOfWeek(Date d) {
        return getFirstDayOfWeek(getFirstSecondOfDay(d));
    }

    /**
     * 获取指定日期的周的最后一天的最后一秒
     *
     * @param d 日期
     * @return 处理后的日期
     */
    public static Date getLastSecondOfWeek(Date d) {
        return getLastDayOfWeek(getLastSecondOfDay(d));
    }

    /**
     * 获取指定日期的天的第一秒
     *
     * @param d 日期
     * @return 处理后的日期
     */
    public static Date getFirstSecondOfDay(Date d) {
        return valueOf(parseDayString(d) + " 00:00:00");
    }

    /**
     * 获取指定日期的天的最后一秒
     *
     * @param d 日期
     * @return 处理后的日期
     */
    public static Date getLastSecondOfDay(Date d) {
        return valueOf(parseDayString(d) + " 23:59:59");
    }

    /**
     * 获取指定日期的月的第一秒
     *
     * @param d 日期
     * @return 处理后的日期
     */
    public static Date getFirstSecondOfMonth(Date d) {
        return getFirstSecondOfDay(getFirstDayOfMonth(d));
    }

    /**
     * 获取指定日期的月的最后一秒
     *
     * @param d 日期
     * @return 处理后的日期
     */
    public static Date getLastSecondOfMonth(Date d) {
        return getLastSecondOfDay(getLastDayOfMonth(d));
    }

    /**
     * 获取指定日期的年的最后一秒
     *
     * @param d 日期
     * @return 处理后的日期
     */
    public static Date getLastSecondOfYear(Date d){

        return getLastSecondOfDay(getLastDayOfMonth(setMonth(d, 11)));

    }

    /**
     * 获取指定日期的明年的本月的最后一秒
     *
     * @param d 日期
     * @return 处理后的日期
     */
    public static Date getLastSecondOfMonthAndNextYear(Date d){
        return getLastSecondOfDay(getLastDayOfMonth(addYear(d, 1)));
    }

    /**
     * 获取日期的年份
     *
     * @param d 日期
     * @return 年份
     */
    public static int getYear(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        return c.get(Calendar.YEAR);
    }

    /**
     * 设置日期的年份
     *
     * @param d 日期
     * @return 设置后的日期
     */
    public static Date setYear(Date d, int s) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.set(Calendar.YEAR, s);
        return c.getTime();
    }

    /**
     * 日期的年份增加指定数字
     *
     * @param d 日期
     * @param s 数字，负数代表减去
     * @return 增加后日期
     */
    public static Date addYear(Date d, int s) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.add(Calendar.YEAR, s);
        return c.getTime();
    }

    /**
     * 获取日期的月份
     *
     * @param d 日期
     * @return 月份
     */
    public static int getMonth(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        return c.get(Calendar.MONTH) + 1;
    }

    /**
     * 设置日期的月份
     *
     * @param d 日期
     * @return 设置后的日期
     */
    public static Date setMonth(Date d, int s) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.set(Calendar.MONTH, s);
        return c.getTime();
    }

    /**
     * 日期的月份增加指定数字
     *
     * @param d 日期
     * @param s 数字，负数代表减去
     * @return 增加后日期
     */
    public static Date addMonth(Date d, int s) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.add(Calendar.MONTH, s);
        return c.getTime();
    }

    /**
     * 获取日期的日
     *
     * @param d 日期
     * @return 日
     */
    public static int getDay(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        return c.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 设置日期的日
     *
     * @param d 日期
     * @return 设置后的日期
     */
    public static Date setDay(Date d, int s) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.set(Calendar.DAY_OF_MONTH, s);
        return c.getTime();
    }

    /**
     * 日期的日增加指定数字
     *
     * @param d 日期
     * @param s 数字，负数代表减去
     * @return 增加后日期
     */
    public static Date addDay(Date d, int s) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.add(Calendar.DAY_OF_MONTH, s);
        return c.getTime();
    }

    /**
     * 获取日期的小时
     *
     * @param d 日期
     * @return 小时
     */
    public static int getHour(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        return c.get(Calendar.HOUR_OF_DAY);
    }

    /**
     * 设置日期的小时
     *
     * @param d 日期
     * @return 设置后的日期
     */
    public static Date setHour(Date d, int s) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.set(Calendar.HOUR_OF_DAY, s);
        return c.getTime();
    }

    /**
     * 日期的小时增加指定数字
     *
     * @param d 日期
     * @param s 数字，负数代表减去
     * @return 增加后日期
     */
    public static Date addHour(Date d, int s) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.add(Calendar.HOUR_OF_DAY, s);
        return c.getTime();
    }

    /**
     * 获取日期的分钟
     *
     * @param d 日期
     * @return 分钟
     */
    public static int getMinute(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        return c.get(Calendar.MINUTE);
    }

    /**
     * 设置日期的分钟
     *
     * @param d 日期
     * @return 设置后的日期
     */
    public static Date setMinute(Date d, int s) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.set(Calendar.MINUTE, s);
        return c.getTime();
    }

    /**
     * 日期的分钟增加指定数字
     *
     * @param d 日期
     * @param s 数字，负数代表减去
     * @return 增加后日期
     */
    public static Date addMinute(Date d, int s) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.add(Calendar.MINUTE, s);
        return c.getTime();
    }

    /**
     * 获取日期的秒
     *
     * @param d 日期
     * @return 秒
     */
    public static int getSecond(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        return c.get(Calendar.SECOND);
    }

    /**
     * 设置日期的秒
     *
     * @param d 日期
     * @return 设置后的日期
     */
    public static Date setSecond(Date d, int s) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.set(Calendar.SECOND, s);
        return c.getTime();
    }

    /**
     * 日期的秒增加指定数字
     *
     * @param d 日期
     * @param s 数字，负数代表减去
     * @return 增加后日期
     */
    public static Date addSecond(Date d, int s) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.add(Calendar.SECOND, s);
        return c.getTime();
    }

    /**
     * 获取日期的星期
     *
     * @param d 日期
     * @return 星期
     */
    public static int getDayOfWeek(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        int result = c.get(Calendar.DAY_OF_WEEK);
        return 1 == result ? 7 : result - 1;
    }

    /**
     * 通过时间秒毫秒数判断两个时间的间隔天数
     *
     * @param d1 早一点的时间
     * @param d2 晚一点的时间
     * @return 天数
     */
    public static int differentDays(Date d1, Date d2) {
    	long dis = d2.getTime() - d1.getTime();
    	int da = 1000 * 3600 * 24;
    	int plus = 0;
    	if(dis%da > 0) {
    		plus = dis>0?1:-1;
    	}
    	int diff = (int) (dis/ da);
        return  diff+ plus;
    }

    /**
     * 通过时间秒毫秒数判断和当前时间的间隔天数
     *
     * @param d 早一点的时间
     * @return 天数
     */
    public static int differentDaysNow(Date d) {
        return differentDays(new Date(), d);
    }

    /**
     * 判断二个日期是否是同年同月
     *
     * @param o 日期
     * @param n 日期
     * @return 是，否
     */
    public static boolean isSameMonth(Date o, Date n) {
        return null != o && null != n && getYear(o) == getYear(n) && getMonth(o) == getMonth(n) ;
    }


    public static int differentDayToNow(Date d) {
    	Date now = new Date();
        if(!isSameMonth(now, d)) {
        	return -9999;
        }
        return getDay(d) - getDay(now);
    }

}
