package cn.slashsoft.clinique.service.mini.impl;


import org.springframework.stereotype.Service;

import cn.slashsoft.clinique.dao.mini.PageLogDao;
import cn.slashsoft.clinique.domain.mini.PageLog;
import cn.slashsoft.clinique.service.mini.PageLogService;

/**
 * 与活动相关
 *
 * <AUTHOR>
 */
@Service
public class PageLogServiceImpl implements PageLogService {

    private final PageLogDao actionDao;

    public PageLogServiceImpl(PageLogDao actionDao) {
        this.actionDao = actionDao;
    }

	@Override
	public void insertPageLog(PageLog log) {
		actionDao.insertPageLog(log);		
	}

}
