package com.stormcrm.clinique.dao.mini;

import com.stormcrm.clinique.domain.mini.Customer;
import com.stormcrm.clinique.domain.mini.CustomerAddress;
import com.stormcrm.clinique.vo.mini.Info;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 与顾客相关的操作
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CustomerDao {

    /**
     * 获取登陆时需要的信息
     * 登录
     *
     * @param customerId 顾客编号
     * @return 顾客信息
     */
    @Select("SELECT " +
            "   `phone_number`," +
            "   `name`," +
            "   `birthday`," +
            "   `birthday_update_times`," +
            "   `xiaohongshu_account` " +
            "FROM " +
            "   `customer` " +
            "WHERE " +
            "   `id`=#{customerId} " +
            "LIMIT 1")
    Customer getCustomerById(long customerId);

    /**
     * 获取顾客注册的手机号码
     * 增加资料页
     *
     * @param customerId 顾客编号
     * @return 顾客信息
     */
    @Select("SELECT " +
            "   IFNULL(`phone_number`,'') " +
            "FROM " +
            "   `customer` " +
            "WHERE " +
            "   `id`=#{customerId} " +
            "LIMIT 1")
    String getCustomerPhoneNumberById(long customerId);

    /**

     * 获取姓名
     * MOT
     *
     * @param customerId 顾客编号
     * @return 顾客信息
     */
    @Select("SELECT " +
            "   `name` " +
            "FROM " +
            "   `customer` " +
            "WHERE " +
            "   `id`=#{customerId} " +
            "LIMIT 1")
    String getNameById(long customerId);

    /**
     * 保存顾客信息
     * 注册
     *
     * @param customer 顾客信息
     * @return 影响的行
     */
    @Insert("INSERT INTO `customer` (" +
            "   `phone_number`, " +
            "   `name`," +
            "   `source`," +
            "   `ranking`, " +
            "   `ranking_always` " +
            ") " +
            "SELECT " +
            "   #{phoneNumber}, " +
            "   #{name}," +
            "   #{source}," +
            "   COUNT(*), " +
            "   COUNT(*) " +
            "FROM " +
            "   `customer` "
    )
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertCustomer(Customer customer);

    /**
     * 获取首次更新资料奖励是否发放
     * 完善资料页
     *
     * @param customerId 顾客编号
     * @return 顾客资料
     */
    @Select("SELECT " +
            "   `birthday`," +
            "   `birthday_update_times`," +
            "   `is_update_reward` `update_reward` " +
            "FROM " +
            "   `customer` " +
            "WHERE " +
            "   `id`=#{customerId} " +
            "LIMIT 1")
    Customer getCustomerForUpdate(long customerId);

    /**
     * 更新已发放首次更新资料奖励
     * 完善资料页
     *
     * @param customerId 顾客编号
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `customer` " +
            "SET " +
            "   `is_update_reward`=1 " +
            "WHERE " +
            "   `id`=#{customerId} " +
            "   AND `is_update_reward`=0"
    )
    int updateCustomerUpdateRewardById(long customerId);

    /**
     * 更新资料
     * 完善资料页
     *
     * @param info 资料
     */
    @Update("UPDATE " +
            "   `customer` " +
            "SET " +
            "   `name`=#{name}, " +
            "   `birthday`=#{birthday}, " +
            "   `xiaohongshu_account`=#{xiaohongshuAccount}, " +
            "   `update_source`=#{updateSource}, " +
            "   `update_source_time`=NOW() " +
            "WHERE " +
            "   `id`=#{customerId}"
    )
    void updateCustomerWithBirthday(Info info);

    /**
     * 更新资料
     * 完善资料页
     *
     * @param info 资料
     */
    @Update("UPDATE " +
            "   `customer` " +
            "SET " +
            "   `name`=#{name}, " +
            "   `birthday`=#{birthday}, " +
            "   `birthday_update_times`=1, " +
            "   `xiaohongshu_account`=#{xiaohongshuAccount}, " +
            "   `update_source`=#{updateSource}, " +
            "   `update_source_time`=NOW() " +
            "WHERE " +
            "   `id`=#{customerId} AND " +
            "   `birthday_update_times`=0"
    )
    void updateCustomerWithBirthdayAndTimes(Info info);

    /**
     * 更新资料
     * 完善资料页
     *
     * @param info 资料
     */
    @Update("UPDATE " +
            "   `customer` " +
            "SET " +
            "   `name`=#{name}, " +
            "   `xiaohongshu_account`=#{xiaohongshuAccount}, " +
            "   `update_source`=#{updateSource}, " +
            "   `update_source_time`=NOW() " +
            "WHERE " +
            "   `id`=#{customerId}"
    )
    void updateCustomerWithoutBirthday(Info info);

    /**
     * 获取首选地址
     * 完善资料页
     *
     * @param customerId 顾客编号
     * @return 地址
     */
    @Select("SELECT " +
            "   `name`, " +
            "   `phone_number`, " +
            "   `province`, " +
            "   `city`, " +
            "   `district`, " +
            "   `address` " +
            "FROM " +
            "   `customer_address` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "   AND `status`=1 " +
            "ORDER BY " +
            "   `preferred` DESC, `id` DESC " +
            "LIMIT 1"
    )
    CustomerAddress getPreferredAddressByCustomerId(long customerId);

    /**
     * 获取邮寄地址
     * 收货地址页
     *
     * @param customerId 顾客编号
     * @return 邮寄地址
     */
    @Select("SELECT " +
            "   `id`, " +
            "   `name`, " +
            "   `phone_number`, " +
            "   `province`, " +
            "   `city`, " +
            "   `district`, " +
            "   `address`, " +
            "   `preferred` " +
            "FROM " +
            "   `customer_address` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "   AND `status`=1 "
    )
    List<CustomerAddress> getCustomerAddressByCustomerId(long customerId);

    /**
     * 获取邮寄地址
     * 编辑地址页
     *
     * @param customerId        顾客编号
     * @param customerAddressId 邮寄地址编号
     * @return 邮寄地址
     */
    @Select("SELECT " +
            "   `id`, " +
            "   `name`, " +
            "   `phone_number`, " +
            "   `province`, " +
            "   `city`, " +
            "   `district`, " +
            "   `address`, " +
            "   `preferred` " +
            "FROM " +
            "   `customer_address` " +
            "WHERE " +
            "   `id`=#{customerAddressId} " +
            "   AND `customer_id`=#{customerId} " +
            "   AND `status`=1 "
    )
    CustomerAddress getCustomerAddressById(@Param("customerId") long customerId, @Param("customerAddressId") long customerAddressId);

    /**
     * 取消所有地址的默认装态
     * 增加地址页
     * 编辑地址页
     *
     * @param customerId 顾客编号
     */
    @Update("UPDATE " +
            "   `customer_address` " +
            "SET " +
            "   `preferred`=0 " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "   AND `preferred`=1"
    )
    void updatePreferredByCustomerId(long customerId);

    /**
     * 新增邮寄地址
     * 增加地址页
     *
     * @param customerAddress 邮寄地址
     */
    @Insert("INSERT INTO `customer_address` (" +
            "   `customer_id`, " +
            "   `name`, " +
            "   `phone_number`, " +
            "   `province`, " +
            "   `city`, " +
            "   `district`, " +
            "   `address`, " +
            "   `preferred` " +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{name}, " +
            "   #{phoneNumber}, " +
            "   #{province}, " +
            "   #{city}, " +
            "   #{district}, " +
            "   #{address}, " +
            "   #{preferred} " +
            ")"
    )
    void insertCustomerAddress(CustomerAddress customerAddress);

    /**
     * 修改邮寄地址
     * 编辑地址页
     *
     * @param customerAddress 邮寄地址
     */
    @Insert("UPDATE " +
            "   `customer_address` " +
            "SET " +
            "   `name`=#{name}, " +
            "   `phone_number`=#{phoneNumber}, " +
            "   `province`=#{province}, " +
            "   `city`=#{city}, " +
            "   `district`=#{district}, " +
            "   `address`=#{address}, " +
            "   `preferred`=#{preferred} " +
            "WHERE " +
            "   `id`=#{id}"
    )
    void updateCustomerAddress(CustomerAddress customerAddress);

    /**
     * 更新邮寄地址状态
     * 收货地址页
     *
     * @param customerAddressId 邮寄地址
     */
    @Update("UPDATE " +
            "   `customer_address` " +
            "SET " +
            "   `status`=0 " +
            "WHERE " +
            "   `id`=#{customerAddressId}"
    )
    void updateCustomerAddressStatus(long customerAddressId);

    /**
     * 写入手机号码
     *
     * @param unionid     公众号唯一编号
     * @param phoneNumber 手机号码
     */
    @Update("UPDATE " +
            "   `customer` `c` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       on `w`.`customer_id`=`c`.`id` " +
            "SET " +
            "   `c`.`phone_number`=#{phoneNumber} " +
            "WHERE " +
            "   `w`.`unionid`=#{unionid}")
    void setPhoneNumberByUnionid(@Param("unionid") String unionid, @Param("phoneNumber") String phoneNumber);

    /**
     * 写入手机号码
     *
     * @param openid     公众号唯一编号
     * @param phoneNumber 手机号码
     */
    @Update("UPDATE " +
            "   `customer` `c` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       on `w`.`customer_id`=`c`.`id` " +
            "SET " +
            "   `c`.`phone_number`=#{phoneNumber} " +
            "WHERE " +
            "   `w`.`wechat_official_openid`=#{openid}")
    void setPhoneNumberByOfficialOpenid(@Param("openid") String openid, @Param("phoneNumber") String phoneNumber);

    /**
     * 写入手机号码
     *
     * @param openid     公众号唯一编号
     * @param phoneNumber 手机号码
     */
    @Update("UPDATE " +
            "   `customer` `c` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       on `w`.`customer_id`=`c`.`id` " +
            "SET " +
            "   `c`.`phone_number`=#{phoneNumber} " +
            "WHERE " +
            "   `w`.`wechat_mini_openid`=#{openid}")
    void setPhoneNumberByMiniOpenid(@Param("openid") String openid, @Param("phoneNumber") String phoneNumber);


    /**
     * 获取天猫权益
     * @param phoneNumber 手机号码
     * @return 数量
     */
    @Select("SELECT " +
            "   COUNT(*) " +
            "FROM " +
            "   `right_tmall_register` " +
            "WHERE " +
            "   `phone_number`=#{phoneNumber}")
    int getRightTmall(String phoneNumber);

    /**
     * 获取顾客信息
     * 
     *
     * @param openid 顾客openid
     * @return 顾客信息
     */
    @Select("SELECT " +
            "   a.`phone_number`," +
            "   a.`name`," +
            "   a.`birthday`," +
            "   a.`birthday_update_times`," +
            "   a.`xiaohongshu_account` " +
            "FROM " +
            "   `customer` a " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       on `w`.`customer_id`=`a`.`id` " +
            "WHERE " +
            "   w.`wechat_official_openid` = #{openid} " +
            "LIMIT 1")
	Customer getCustomerByOfficialId(String openid);

    /**
     * 获取顾客信息
     * 
     *
     * @param openid 顾客openid
     * @return 顾客信息
     */
    @Select("SELECT " +
            "   a.`id`," +
            "   a.`name`," +
            "   a.`birthday`," +
            "   a.`birthday_update_times`," +
            "   a.`xiaohongshu_account` " +
            "FROM " +
            "   `customer` a " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       on `w`.`customer_id`=`a`.`id` " +
            "WHERE " +
            "   w.`wechat_mini_openid` = #{openid} " +
            "LIMIT 1")
	Customer getCustomerByMiniOpenId(String openid);
    
    @Update("UPDATE " +
            "   `customer` " +
            "SET " +
            "   `cactive_points`= ifnull(`cactive_points`,0) + ${points} " +
            "WHERE " +
            "   `id`=#{customerId}")
	int updateCustomerActivePoints(int points, Long customerId);

    /*
     * 获取用户c粉值排名 去掉已删除用户
     * @param count
     */
    @Select("SELECT count(*) FROM `customer`  " +
    		 " where `cactive_points` >  ${count}  ")
    int getPointRank(@Param("count")  int count);
    /*
     * 获取用户c粉值
     * @param customerId
     */
    @Select("select ifnull(`cactive_points`,0) from `customer` WHERE `id`=#{customerId}")
    int getCustomerAll(Long customerId);
    /*
     * 获取参与用户
     */
    @Select("select count(DISTINCT customer_id) from cactive_transaction ")
    int getAllCount();

    /**
     * 简单的积分扣减，没有幂等，可能会存在问题
     *
     * @param points
     * @param customerId
     * @return
     */
    @Update("UPDATE " +
            "   `customer` " +
            "SET " +
            "   `cactive_points`= ifnull(`cactive_points`,0) - ${points} " +
            "WHERE " +
            "   `id`=#{customerId}")
    int minusCustomerActivePoints(int points, Long customerId);

}
