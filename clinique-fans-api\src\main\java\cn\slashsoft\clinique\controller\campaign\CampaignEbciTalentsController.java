package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciTalents;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciTalentsImage;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciTalentsTop;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.service.campaign.CampaignEbciTalentsService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.campaign.CampaignEbciTalentsTopVo;
import cn.slashsoft.clinique.vo.campaign.CampaignEbciTalentsVo;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * EBCI 美白达人官
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class CampaignEbciTalentsController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final CampaignEbciTalentsService campaignEbciTalentsService;
    private final StringRedisTemplate stringRedisTemplate;
    private final WechatService wechatService;

    public CampaignEbciTalentsController(CampaignEbciTalentsService campaignEbciTalentsService, StringRedisTemplate stringRedisTemplate, WechatService wechatService) {
        this.campaignEbciTalentsService = campaignEbciTalentsService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.wechatService = wechatService;
    }

    /**
     * 配置活动信息
     *
     * @param verification 验证
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 处理结果
     * https://clinique.stormcrm.com/api/campaign-config/ebci-talents/20-23-04-00-28/2020-04-30-00-00-00/2020-05-15-23-59-59
     */
    @GetMapping("/campaign-config/ebci-talents/{verification}/{startTime}/{endTime}")
    public String setConfig(
            @PathVariable("verification") String verification,
            @PathVariable("startTime") String startTime,
            @PathVariable("endTime") String endTime
    ) {
        if (DateUtil.parseVerification(new Date()).equals(verification)) {
            stringRedisTemplate.opsForValue().set("campaignEbciTalentsStartTime", DateUtil.parseString(DateUtil.valueOf(startTime, "yyyy-MM-dd-HH-mm-ss")));
            stringRedisTemplate.opsForValue().set("campaignEbciTalentsEndTime", DateUtil.parseString(DateUtil.valueOf(endTime, "yyyy-MM-dd-HH-mm-ss")));
            return "success";
        }
        return "fail";
    }

    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-ebci-talents/get-data")
    public String getData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 获取美白达人官活动参与信息
        CampaignEbciTalents campaignEbciTalents = campaignEbciTalentsService.getCampaignEbciTalents(customerId);

        CampaignEbciTalentsVo campaignEbciTalentsVo = new CampaignEbciTalentsVo();

        campaignEbciTalentsVo.setStartTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciTalentsStartTime")));
        campaignEbciTalentsVo.setEndTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciTalentsEndTime")));

        campaignEbciTalentsVo.setAlert(campaignEbciTalents.getAlert());
        campaignEbciTalentsVo.setStatus(campaignEbciTalents.getStatus());

        // 首次变更状态时，设置提醒状态
        if(campaignEbciTalents.getAlert()){
            campaignEbciTalentsService.updateCampaignEbciTalentsAlert(customerId);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignEbciTalentsVo);

    }

    /**
     * 图片上传
     *
     * @param file 图片
     * @return 处理结果
     */
    @PostMapping("/campaign-ebci-talents/upload")
    public String upload(@RequestParam("file") MultipartFile file, @RequestParam("index") int index) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        if (!campaignEbciTalentsService.upload(customerId, file)) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, index);

    }

    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-ebci-talents-mine/get-data")
    public String getMineData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 获取美白达人官活动参与信息
        CampaignEbciTalents campaignEbciTalents = campaignEbciTalentsService.getCampaignEbciTalents(customerId);

        CampaignEbciTalentsVo campaignEbciTalentsVo = new CampaignEbciTalentsVo();

        campaignEbciTalentsVo.setStartTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciTalentsStartTime")));
        campaignEbciTalentsVo.setEndTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciTalentsEndTime")));

        campaignEbciTalentsVo.setStatus(campaignEbciTalents.getStatus());
        campaignEbciTalentsVo.setUploadTime(campaignEbciTalents.getUploadTime());
        campaignEbciTalentsVo.setExamineTime(campaignEbciTalents.getExamineTime());

        List<CampaignEbciTalentsImage> campaignEbciTalentsImageList = campaignEbciTalentsService.getCampaignEbciTalentsImageList(campaignEbciTalents.getId());
        campaignEbciTalentsVo.setCampaignEbciTalentsImageList(campaignEbciTalentsImageList);

        Wechat wechat = wechatService.getWechatInfoByCustomerId(customerId);
        campaignEbciTalentsVo.setNickName(wechat.getNickName());
        campaignEbciTalentsVo.setAvatarUrl(wechat.getAvatarUrl());

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignEbciTalentsVo);

    }

    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-ebci-talents-top/get-data")
    public String getTopData() {

        // 获取美白达人官活动TOP信息
        CampaignEbciTalentsTop campaignEbciTalentsTop = campaignEbciTalentsService.getCampaignEbciTalentsTop();
        if(null == campaignEbciTalentsTop){
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        CampaignEbciTalentsTopVo campaignEbciTalentsTopVo = new CampaignEbciTalentsTopVo();
        campaignEbciTalentsTopVo.setStage(campaignEbciTalentsTop.getStage());
        campaignEbciTalentsTopVo.setTalentsTopDetailList(campaignEbciTalentsService.getCampaignEbciTalentsTopDetailList(campaignEbciTalentsTop.getId()));
        campaignEbciTalentsTopVo.setTalentsTopDetailImageList(campaignEbciTalentsService.getCampaignEbciTalentsTopDetailImageList(campaignEbciTalentsTop.getId()));

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignEbciTalentsTopVo);

    }

    /**
     * 写入访问开始日志
     *
     * @param source 来源
     * @return 成功
     */
    @PostMapping("/campaign-ebci-talents/start-view-log/{source}/{page}")
    public String setStartViewLog(
            @PathVariable("source") String source,
            @PathVariable("page") String page
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        long id = campaignEbciTalentsService.insertCampaignEbciTalentsViewLog(customerId, source, page);

        return ResultUtil.customer(ResultEnum.SUCCESS, id);
    }

    /**
     * 写入访问结束日志
     *
     * @param id 来源
     * @return 成功
     */
    @PostMapping("/campaign-ebci-talents/end-view-log/{id}")
    public String setEndViewLog(
            @PathVariable("id") long id
    ) {
        campaignEbciTalentsService.setCampaignEbciTalentsViewLog(id);
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }



    @GetMapping("/campaign-config/stopConfig")
    public String stopConfig() {
        String startTime = stringRedisTemplate.opsForValue().get("C_STOP_START_TIME");
        String endTime = stringRedisTemplate.opsForValue().get("C_STOP_END_TIME");
        Date start = DateUtil.valueOf(startTime);
        Date end = DateUtil.valueOf(endTime);
        // 活动有效期
        if (DateUtil.laterThanNow(start)) {
            return  ResultUtil.customer(ResultEnum.SUCCESS,false);
        } else if ( DateUtil.earlierThanNow(end)) {
            return ResultUtil.customer(ResultEnum.SUCCESS,false);
        } else {
            return ResultUtil.customer(ResultEnum.SUCCESS,true);
        }

    }

}
