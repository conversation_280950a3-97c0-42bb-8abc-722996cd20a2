package com.stormcrm.clinique.controller;

import static com.alibaba.fastjson.JSON.toJSONString;
import static com.alibaba.fastjson.JSON.toJSONStringWithDateFormat;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import com.stormcrm.clinique.domain.PointType;
import com.stormcrm.clinique.service.mini.impl.PointTypeServiceImpl;
import com.stormcrm.clinique.util.DateUtil;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.util.VerifyUtil;
import com.stormcrm.clinique.vo.Result;
import lombok.extern.slf4j.Slf4j;

/**
 * C 币奖励类型管理
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("point/type")
@Slf4j
public class PointTypeController {

    private final PointTypeServiceImpl pointTypeService;

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;

    public PointTypeController(PointTypeServiceImpl pointTypeService) {
        this.pointTypeService = pointTypeService;
    }

    /**
     * 查询所有-分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索关键字
     * @param top           状态
     * @return 列表
     */
    @PreAuthorize("hasAuthority('POINT_TYPE')")
    @PostMapping("get-page")
    @ResponseBody
    public String getPage(
            @RequestParam(value = "pagination[page]", required = false) Integer page,
            @RequestParam(value = "pagination[perpage]", required = false) Integer perpage,
            @RequestParam(value = "query[generalSearch]", required = false) String generalSearch,
            @RequestParam(value = "query[top]", required = false) Short top
    ) {

        if (null == page) {
            page = 1;
        }

        if (null == perpage) {
            perpage = 10;
        }

        List<PointType> pointTypes = pointTypeService.getPage(page, perpage, generalSearch, top);
        int count = pointTypeService.getPageCount(generalSearch, top);

        JSONObject mata = new JSONObject();
        mata.put("page", page);
        mata.put("pages", (count + perpage - 1) / perpage);
        mata.put("perpage", perpage);
        mata.put("total", count);
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("meta", mata);
        result.put("data", pointTypes);

        return toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }


    /**
     * 笔记标签列表
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('POINT_TYPE')")
    @RequestMapping("")
    public String index(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/point-type/index";
    }

    /**
     * 增加模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('POINT_TYPE_EDIT')")
    @RequestMapping("add")
    public String add(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/point-type/add";
    }

    /**
     * 新增
     *
     * @return 结果
     */
    @PreAuthorize("hasAuthority('POINT_TYPE_EDIT')")
    @PostMapping(value = "add-submit", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String addSubmit(
            @RequestParam(value = "form-name", required = false) String name,
            // image_url 这个写死 不需要编辑
//            @RequestParam(value = "form-status", required = false) Integer status,
            @RequestParam(value = "form-for-note-tag", required = false) Integer forNoteTag,
            @RequestParam(value = "form-reward-point", required = false) Integer rewardPoint,
            @RequestParam(value = "form-reward-start", required = false) String rewardStart,
            @RequestParam(value = "form-reward-end", required = false) String rewardEnd,
            @RequestParam(value = "form-reward-max", required = false) Integer rewardMax,
            @RequestParam(value = "form-reward-max-forever", required = false) Integer rewardMaxForever
    ) {

        // 验证
        if (!VerifyUtil.required(name)) {
            return ResultUtil.verifyFailToJson("form-name", "这是必填字段");
        }

        // 生成对象
        PointType pointType = new PointType();
        pointType.setName(name);
        pointType.setForNoteTag(forNoteTag == 1);

        if (rewardPoint != null) {
            pointType.setRewardPoint(rewardPoint);
        } else {
            pointType.setRewardPoint(0);
        }


        Date startTimeDate = DateUtil.valueOf(rewardStart);
        pointType.setRewardActiveStart(startTimeDate);

        Date endTimeDate = DateUtil.valueOf(rewardEnd);
        pointType.setRewardActiveEnd(endTimeDate);

        if (rewardMax != null) {
            pointType.setRewardMax(rewardMax);
        } else {
            pointType.setRewardMax(0);
        }

        if (rewardMaxForever != null) {
            pointType.setRewardMaxForever(rewardMaxForever);
        } else {
            pointType.setRewardMaxForever(0);
        }


        // 传到Service服务中保存
        pointTypeService.insert(pointType);

        Result result = ResultUtil.customer(1, "成功", pointType);
        // 返回绍果给前端
        return toJSONString(result);
    }

    /**
     * 逻辑删除
     *
     * @param id 胸章的自动编号
     * @return 影响的行数
     */
    @PreAuthorize("hasAuthority('POINT_TYPE_DEL')")
    @GetMapping("del/{id}")
    @ResponseBody
    public String del(@PathVariable("id") long id) {

        if (0 == pointTypeService.delete(id)) {
            return ResultUtil.failToJson();
        }

        return ResultUtil.successToJson();

    }

    /**
     * 编辑模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('POINT_TYPE_EDIT')")
    @GetMapping("edit/{id}")
    public String edit(@PathVariable Short id,Model model) {
        model.addAttribute("staticDomain", staticDomain);
        // 读数据库
        PointType tag = pointTypeService.getById(id);
        if (null == tag) {
            return "m/fans/common/empty";
        }
        model.addAttribute("id", id);
        model.addAttribute("pointType", tag);

        return "m/fans/point-type/edit";
    }

    /**
     * 编辑
     *
     * @param id 编号
     * @return 结果
     */
    @PreAuthorize("hasAuthority('POINT_TYPE_EDIT')")
    @PostMapping(value = "edit-submit/{id}", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String editSubmit(
            @PathVariable("id") short id,
            @RequestParam(value = "form-name", required = false) String name,
            // image_url 这个写死 不需要编辑
//            @RequestParam(value = "form-status", required = false) Integer status,
            @RequestParam(value = "form-for-note-tag", required = false) Integer forNoteTag,
            @RequestParam(value = "form-reward-point", required = false) Integer rewardPoint,
            @RequestParam(value = "form-reward-start", required = false) String rewardStart,
            @RequestParam(value = "form-reward-end", required = false) String rewardEnd,
            @RequestParam(value = "form-reward-max", required = false) Integer rewardMax,
            @RequestParam(value = "form-reward-max-forever", required = false) Integer rewardMaxForever
    ) {

        // 验证
        if (!VerifyUtil.required(name)) {
            return ResultUtil.verifyFailToJson("form-name", "这是必填字段");
        }

        // 生成对象
        PointType pointType = new PointType();
        pointType.setId(id);
        pointType.setName(name);
        pointType.setForNoteTag(forNoteTag == 1);

        if (rewardPoint != null) {
            pointType.setRewardPoint(rewardPoint);
        } else {
            pointType.setRewardPoint(0);
        }


        Date startTimeDate = DateUtil.valueOf(rewardStart);
        pointType.setRewardActiveStart(startTimeDate);

        Date endTimeDate = DateUtil.valueOf(rewardEnd);
        pointType.setRewardActiveEnd(endTimeDate);

        if (rewardMax != null) {
            pointType.setRewardMax(rewardMax);
        } else {
            pointType.setRewardMax(0);
        }

        if (rewardMaxForever != null) {
            pointType.setRewardMaxForever(rewardMaxForever);
        } else {
            pointType.setRewardMaxForever(0);
        }

        // 传到Service服务中保存
        pointTypeService.update(pointType);

        Result result = ResultUtil.customer(1, "成功", pointType);
        // 返回绍果给前端
        return toJSONString(result);
    }

}
