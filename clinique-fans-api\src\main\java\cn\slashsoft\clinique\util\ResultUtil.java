package cn.slashsoft.clinique.util;

import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.vo.Result;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

/**
 * 处理结果工具类
 *
 * <AUTHOR>
 */
public class ResultUtil {

    /**
     * 输出Json格式字符串，日期格式化，空字符，空对象，空数组
     *
     * @param object 对象
     * @return Json格式字符串
     */
    public static String toJsonString(Object object) {
        return JSONObject.toJSONString(object, SerializerFeature.WriteDateUseDateFormat, SerializerFeature.WriteNullListAsEmpty);
    }

    public static <T> T toObject(String json, Class<T> clazz) {
        return JSONObject.parseObject(json, clazz);
    }

    /**
     * Service服务向Web服务传递成功结果
     *
     * @return 结果
     */
    public static Result success() {
        return success("");
    }

    /**
     * 向View传递成功
     *
     * @return 结果
     */
    public static String successToJson() {
        return toJsonString(success());
    }

    /**
     * 向View传递成功
     *
     * @param message 消息
     * @return 结果
     */
    public static String successToJson(String message) {
        return toJsonString(success(message));
    }

    /**
     * Service服务向Web服务传递成功结果
     *
     * @param message 消息
     * @return 结果
     */
    public static Result success(String message) {
        return new Result(1, message);
    }

    /**
     * Service服务向Web服务传递失败结果
     *
     * @return 结果
     */
    public static Result fail() {
        return fail("");
    }

    /**
     * 向View传递失败
     *
     * @return 结果
     */
    public static String failToJson() {
        return toJsonString(fail());
    }

    /**
     * 向View传递失败
     *
     * @param message 消息
     * @return 结果
     */
    public static String failToJson(String message) {
        return toJsonString(fail(message));
    }

    /**
     * Service服务向Web服务传递失败结果
     *
     * @param message 消息
     * @return 结果
     */
    public static Result fail(String message) {
        return new Result(0, message);
    }

    /**
     * Service服务向Web服务传递表单验证失败结果
     *
     * @param field   字段名
     * @param message 消息
     * @return 结果
     */
    public static Result verifyFail(String field, String message) {
        return new Result(2, field, message);
    }

    /**
     * 向View传递表单验证失败结果
     *
     * @param field   字段名
     * @param message 消息
     * @return 结果
     */
    public static String verifyFailToJson(String field, String message) {
        return toJsonString(new Result(2, field, message));
    }

    /**
     * Service服务向Web服务传递定制结果
     *
     * @param code    返回码
     * @param message 消息
     * @return 结果
     */
    public static Result customer(int code, String message) {
        return new Result(code, message);
    }

    /**
     * Service服务向Web服务传递定制结果
     *
     * @param code    返回码
     * @param message 消息
     * @return 结果
     */
    public static String customerToJson(int code, String message) {
        return toJsonString(new Result(code, message));
    }

    /**
     * Web服务向小程序传递定制结果
     *
     * @param code    状态码
     * @param message 自定义消息
     * @param data    自定义数据
     * @return 结果
     */
    public static Result customer(int code, String message, Object data) {
        return new Result(code, message, data);
    }

    /**
     * Web服务向小程序传递定制结果
     *
     * @param resultEnum 状态码
     * @return 结果
     */
    public static String customer(ResultEnum resultEnum) {
        return toJsonString(new Result(resultEnum.getStatus(), resultEnum.getMessage()));
    }

    /**
     * Web服务向小程序传递定制结果
     *
     * @param resultEnum 状态码
     * @param message    自定义消息
     * @return 结果
     */
    public static String customer(ResultEnum resultEnum, String message) {
        return toJsonString(new Result(resultEnum.getStatus(), message));
    }

    /**
     * Web服务向小程序传递定制结果
     *
     * @param resultEnum 状态码
     * @param data       自定义数据
     * @return 结果
     */
    public static String customer(ResultEnum resultEnum, Object data) {
        return toJsonString(new Result(resultEnum.getStatus(), resultEnum.getMessage(), data));
    }

    /**
     * Web服务向小程序传递定制结果
     *
     * @param resultEnum 状态码
     * @param message    自定义消息
     * @param data       自定义数据
     * @return 结果
     */
    public static String customer(ResultEnum resultEnum, String message, Object data) {
        return toJsonString(new Result(resultEnum.getStatus(), message, data));
    }

}
