package cn.slashsoft.clinique.dao.mini;

import cn.slashsoft.clinique.domain.campaign.*;
import cn.slashsoft.clinique.domain.mini.TTestimonyBlindBoxMotLog;
import cn.slashsoft.clinique.domain.mini.TTestimonyFragment;
import cn.slashsoft.clinique.domain.mini.TTestimonyFragmentRank;
import cn.slashsoft.clinique.domain.mini.TTestimonyFragmentTop15;
import cn.slashsoft.clinique.domain.mini.TTestimonyHelp;
import cn.slashsoft.clinique.domain.mini.TTestimonyViewLog;
import cn.slashsoft.clinique.vo.mini.TestimonyFragmentRankVo;
import cn.slashsoft.clinique.vo.mini.TestimonyFragmentRewardVo;
import cn.slashsoft.clinique.vo.mini.TestimonyFragmentSumVo;

import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface TTestimonyFragmentDao {

    @Insert("INSERT INTO `t_testimony_fragment`(" +
        "   `id`, " +
        "   `customer_id`, " +
        "   `fragment_qty`, " +
        "   `fragment_type`, " +
        "   `create_time`, " +
        "   `update_time` " +
        ") " +
        "VALUES (" +
        "   #{id}, " +
        "   #{customerId}, " +
        "   #{fragmentQty}, " +
        "   #{fragmentType}, " +
        "   #{createTime}, " +
        "   #{updateTime} " +
        ")")
    void insertTTestimonyFragment(TTestimonyFragment tTestimonyFragment);

    @Update("UPDATE " +
        "   `t_testimony_fragment` " +
        "SET " +
        "   `id` = #{id}, " +
        "   `customer_id` = #{customerId}, " +
        "   `fragment_qty` = #{fragmentQty}, " +
        "   `fragment_type` = #{fragmentType}, " +
        "   `create_time` = #{createTime}, " +
        "   `update_time` = #{updateTime} " +
        "WHERE " +
        "   `id`=#{id} ")
    void updateTTestimonyFragment(TTestimonyFragment tTestimonyFragment);

    @Select("SELECT " +
        "   `id`, " +
        "   `customer_id`, " +
        "   `fragment_qty`, " +
        "   `fragment_type`, " +
        "   `create_time`, " +
        "   `update_time` " +
        "FROM " +
        "   `t_testimony_fragment` " +
        "WHERE " +
        "   `id`=#{id} " +
        "LIMIT 1 ")
    TTestimonyFragment getTTestimonyFragment(Long id);

    @Select("SELECT " +
            "   `id`, " +
            "   `customer_id`, " +
            "   `fragment_qty`, " +
            "   `fragment_type`, " +
            "   `create_time`, " +
            "   `update_time` " +
            "FROM " +
            "   `t_testimony_fragment` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1 ")
    TTestimonyFragment getTTestimonyFragmentByCustomerId(Long customerId);

    @Select("SELECT " +
            "   IFNULL(sum(fragment_qty),0) " +
            "FROM " +
            "   `t_testimony_fragment` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1 ")
    Integer getTTestimonyFragmentCountByCustomerId(Long customerId);

    /**
     * 获取个人碎片排名
     * @param customerId
     * @return
     */
//    @Select("select t.customer_id, t.rank, t.fragment_qty, c.avatar_url, c.nick_name\n" +
//            "from ( select customer_id, t.fragment_qty, (@ranknum:=@ranknum+1) as rank    \n" +
//            "\tfrom t_testimony_fragment_rank t, (select (@ranknum :=0) ) b  \n" +
//            "\torder by fragment_qty desc ) t inner join wechat c  on t.customer_id = c.customer_id\n" +
//            "where t.`customer_id`=#{customerId}\n" +
//            "limit 1")
    @Select("select c.customer_id, IFNULL(t.rank, 0) rank, IFNULL(t.fragment_qty,0) fragment_qty, c.avatar_url, c.nick_name, \n" +
            "\tIFNULL(p.mobile, 0) cust302,  \n" +
            "\tIFNULL(k.customer_id,0) koc, \n" +
            "\t (select IFNULL(sum(fragment_qty),0) from t_testimony_fragment " +
            "   where customer_id = #{customerId} and fragment_type in (6,7) ) likedCount \n" +
            " from wechat c left join ( select customer_id, t.fragment_qty, (@ranknum:=@ranknum+1) as rank     \n" +
            "\tfrom t_testimony_fragment_rank t, (select (@ranknum :=0) ) b   \n" +
            "\torder by fragment_qty desc ) t on t.customer_id = c.customer_id \n" +
            "\tleft join customer cst on c.customer_id = cst.id \n"+
            "\tleft join t_testimony_302_phone_list p on cst.phone_number = p.mobile \n"+
            "\tleft join t_testimony_koc_user_list k on c.customer_id = k.customer_id\n" +
            "where c.`customer_id`= #{customerId}\n" +
            "limit 1")
    TestimonyFragmentRankVo getTTestimonyFragmentRankByCustomerId(Long customerId);

    /**
     * 获取前15碎片排名
     * @param customerId
     * @return
     */
//    @Select("select t.rank, t.fragment_qty, c.avatar_url, c.nick_name\n" +
//            "from ( select customer_id, t.fragment_qty, (@ranknum:=@ranknum+1) as rank    \n" +
//            "\tfrom t_testimony_fragment_rank t, (select (@ranknum :=0) ) b  \n" +
//            "\torder by fragment_qty desc ) t inner join wechat c  on t.customer_id = c.customer_id\n" +
//            "where t.`customer_id` <> #{customerId}\n" +
//            "limit 19")
    @Select("select t.rank, t.fragment_qty, c.avatar_url, c.nick_name,\n" +
            "\tIFNULL(p.mobile, 0) cust302,  \n" +
            "\tIFNULL(k.customer_id,0) koc\n" +
            "\tfrom ( select customer_id, t.fragment_qty, (@ranknum:=@ranknum+1) as rank     \n" +
            "\t\t\tfrom t_testimony_fragment_rank t, (select (@ranknum :=0) ) b   \n" +
            "\t\t\torder by fragment_qty desc ) t \n" +
            "\tleft join wechat c  on t.customer_id = c.customer_id \n" +
            "\tleft join customer cst on c.customer_id = cst.id \n" +
            "\tleft join t_testimony_302_phone_list p on cst.phone_number = p.mobile \n" +
            "\tleft join t_testimony_koc_user_list k on c.customer_id = k.customer_id\n" +
            "limit 15")
    List<TestimonyFragmentRankVo> getTTestimonyFragmentRankListByCustomerId(Long customerId);

    @Insert("INSERT INTO `t_testimony_fragment_rank`(" +
            "   `id`, " +
            "   `customer_id`, " +
            "   `fragment_qty`, " +
            "   `create_time`, " +
            "   `update_time` " +
            ") " +
            "VALUES (" +
            "   #{id}, " +
            "   #{customerId}, " +
            "   #{fragmentQty}, " +
            "   #{createTime}, " +
            "   #{updateTime} " +
            ")")
    void insertTTestimonyFragmentRank(TTestimonyFragmentRank tTestimonyFragmentRank);

    @Update("UPDATE " +
            "   `t_testimony_fragment_rank` " +
            "SET " +
            "   `fragment_qty` = `fragment_qty` + #{fragmentQty}, " +
            "   `update_time` = Now() " +
            "WHERE " +
            "   `customer_id`=#{customerId} ")
    void addFragmentRankQty(TTestimonyFragment tTestimonyFragment);

    @Select("SELECT " +
            "   `id`, " +
            "   `customer_id`, " +
            "   `fragment_qty`, " +
            "   `rank`, " +
            "   `create_time`, " +
            "   `update_time` " +
            "FROM " +
            "   `t_testimony_fragment_rank` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1 ")
    TTestimonyFragmentRank getFragmentRankByCustomerId(Long customerId);

    @Select("SELECT " +
            "   IFNULL(sum(fragment_qty),0) cnt " +
            "FROM " +
            "   `t_testimony_fragment` " +
            "WHERE " +
            "   `customer_id`=#{customerId} and `fragment_type`=#{fragType} ")
    Integer getFragmentCountByType(Long customerId, Integer fragType);


    @Select("select t.fragment_type, IFNULL(f.qty, 0) qty\n" +
            "from t_testimony_fragment_type t  left join \n" +
            "\t( select fragment_type, sum(fragment_qty) qty\n" +
            "\tfrom  t_testimony_fragment \n" +
            "\twhere customer_id = #{customerId} AND fragment_qty > 0  \n" +
            "\tGROUP BY fragment_type ) f \n" +
            "\ton t.fragment_type= f.fragment_type \n" +
            "ORDER BY t.fragment_type ")
    List<TestimonyFragmentSumVo> getFragmentSumInfo(Long customerId);

    @Select ("SELECT " +
            "   `id`, " +
            "   `customer_id`, " +
            "   `fragment_qty`, " +
            "   `fragment_type`, " +
            "   `create_time`, " +
            "   `update_time` " +
            "FROM " +
            "   `t_testimony_fragment` " +
            "WHERE " +
            "   `customer_id`=#{customerId} AND " +
            "   `fragment_type`=#{fragmentType} " +
            "LIMIT 1 ")
    TTestimonyFragment getFragmentByCustomerIdAndType(Long customerId, Integer fragmentType);

    @Select("select s.product_id, p.product_pwd, p.product_name\n" +
            "from t_testimony_blind_store s inner join t_testimony_product p on s.product_id = p.id\n" +
            "where store_id = #{storeId} and qty > 0\n" +
            "order by RAND() limit 1")
    TestimonyFragmentRewardVo getFragmentRewards(long customerId, Integer storeId);

    @Select("SELECT " +
            "   `id`, " +
            "   `customer_id`, " +
            "   `store_id`, " +
            "   `product_id`, " +
            "   `drawed`, " +
            "   `create_time`, " +
            "   `update_time` " +
            "FROM " +
            "   `t_testimony_fragment_top15` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1 ")
    TTestimonyFragmentTop15 getFragmentTop15ByCustomerId(long customerId);


    @Update("UPDATE " +
            "   `t_testimony_fragment_top15` " +
            "SET " +
            "   `drawed` = 1, " +
            "   `update_time` = Now() " +
            "WHERE " +
            "   `customer_id`=#{customerId} ")
    void updateTop15Flag(long customerId);

    /**
     * 保存 TOP15 客户对门店的选择
     *
     * @param tTestimonyFragmentTop15 客户对门店的选择
     * @return
     */
    @Update("UPDATE " +
            "   `t_testimony_fragment_top15` " +
            "SET " +
            "   `store_id` = #{storeId}, " +
            "   `user_name` = #{userName}, " +
            "   `update_time` = Now() " +
            "WHERE " +
            "   `id`=#{id} ")
    Integer saveTop15CustomerChooseStore(TTestimonyFragmentTop15 tTestimonyFragmentTop15);

    @Select("select count(*)\n" +
            "from t_testimony_koc_user_list\n" +
            "where customer_id = #{customerId}")
    Integer isKocUser(Long customerId);

    @Select("select count(*)\n" +
            "\tfrom t_testimony_302_phone_list p inner join customer c\n" +
            "\t\ton p.mobile = c.phone_number\n" +
            "where c.id = #{customerId}")
    Integer isCust302User(Long customerId);

    @Select("select count(*)\n" +
            "from note\n" +
            "where customer_id = #{customerId}")
    Integer hasNote(long customerId);

    /**
     * 判断用户当天是否已领取打卡碎片
     * @param customerId
     * @return
     */
    @Select("select count(*)\n" +
            "from t_testimony_fragment\n" +
            "where customer_id = #{customerId}\n" +
            " and fragment_type = 5 " +
            " and date_format(create_time, '%y-%m-%d' ) = curdate() ")
    Integer hasAlreadyDrawed(long customerId);

    /**
     * 获得是否有领取笔记积分权限
     * @param customerId
     * @return
     */
    @Select("select count(*)\n" +
            "from note \n" +
            "where customer_id = #{customerId}\n")
    Integer hasNotePrivilege(long customerId);

    /**
     * 临时添加从小程序openid获取customerId
     * @param openId
     */
    @Select("select customer_id from wechat\n" +
            "where wechat_mini_openid = #{openId}\n")
    String getCustomerIdbyOpenId(String openId);


    /**
     * 写入访问日志
     *
     * @param tTestimonyViewLog 日志
     */
    @Insert("INSERT INTO `t_testimony_view_log`(" +
            "   `customer_id`," +
            "   `source`," +
            "   `page`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{source}," +
            "   #{page}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertTestmonyViewLog(TTestimonyViewLog tTestimonyViewLog);


    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Update("UPDATE " +
            "   `t_testimony_view_log` " +
            "SET " +
            "   `end_time`=NOW()," +
            "   `diff_second`=TimeStampDiff(SECOND,`start_time`,NOW()) " +
            "WHERE " +
            "   `id`=#{id}")
    void setTestimonyViewLog(long id);

    @Insert("INSERT INTO `t_testimony_help`(" +
            "   `id`, " +
            "   `customer_id`, " +
            "   `content_id`, " +
            "   `help_customer_id`, " +
            "   `create_time`, " +
            "   `update_time` " +
            ") " +
            "VALUES (" +
            "   #{id}, " +
            "   #{customerId}, " +
            "   #{contentId}, " +
            "   #{helpCustomerId}, " +
            "   #{createTime}, " +
            "   #{updateTime} " +
            ")")
    void addTTestimonyHelp(TTestimonyHelp tTestimonyHelp);

    @Insert("INSERT INTO `t_testimony_share_log`(" +
            "   `customer_id`, " +
            "   `create_time` " +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   NOW() " +
            ")")
    void addShareLog(long customerId);

    @Select("select store_name\n" +
            "from t_testimony_blind_store\n" +
            "where store_id = #{storeId}\n" +
            "limit 1")
    String getStoreNameByStoreId(Integer storeId);

    @Insert("INSERT INTO `t_testimony_blind_box_mot_log`(" +
            "   `id`, " +
            "   `customer_id`, " +
            "   `store_id`, " +
            "   `store_name`, " +
            "   `product_id`, " +
            "   `product_name`, " +
            "   `product_pwd`, " +
            "   `create_time`, " +
            "   `update_time` " +
            ") " +
            "VALUES (" +
            "   #{id}, " +
            "   #{customerId}, " +
            "   #{storeId}, " +
            "   #{storeName}, " +
            "   #{productId}, " +
            "   #{productName}, " +
            "   #{productPwd}, " +
            "   #{createTime}, " +
            "   #{updateTime} " +
            ")")
    void saveTestimonyBlindBoxMotLog(TTestimonyBlindBoxMotLog tTestimonyBlindBoxMotLog);
}
