package cn.slashsoft.clinique.service.campaign.impl;

import cn.slashsoft.clinique.dao.campaign.CampaignEbcSignApplyDao;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciLog;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciStock;
import cn.slashsoft.clinique.service.campaign.CampaignEbciSignApplyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

/**
 * 黄油变粉安瓶级保湿补光申领
 *
 * <AUTHOR>
 */
@Service
public class CampaignEbciSignApplyServiceImpl implements CampaignEbciSignApplyService {

    private final CampaignEbcSignApplyDao campaignEbcSignApplyDao;

    public CampaignEbciSignApplyServiceImpl(CampaignEbcSignApplyDao campaignEbcSignApplyDao) {
        this.campaignEbcSignApplyDao = campaignEbcSignApplyDao;
    }

    /**
     * 保存日志
     *
     * @param campaignEbciLog 日志
     */
    @Override
    public void insertLog(CampaignEbciLog campaignEbciLog) {
        campaignEbcSignApplyDao.insertLog(campaignEbciLog);
    }

    /**
     * 获取手机号码
     *
     * @param unionid 开放平台唯一编号
     * @return 手机号码
     */
    @Override
    public String getPhoneNumberByUnionid(String unionid) {
        return campaignEbcSignApplyDao.getPhoneNumberByUnionid(unionid);
    }

    /**
     * 获取手机号码
     *
     * @param openid 公众号唯一编号
     * @return 手机号码
     */
    @Override
    public String getPhoneNumberByOpenid(String openid) {
        return campaignEbcSignApplyDao.getPhoneNumberByOpenid(openid);
    }

    /**
     * 获取是否已经审领
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    @Override
    public int hasDetail(String phoneNumber) {
        return campaignEbcSignApplyDao.hasDetail(phoneNumber);
    }

    /**
     * 获取是否已经审领
     *
     * @param openid 公众号唯一编号
     * @return 数量
     */
    @Override
    public int hasDetailByOpenid(String openid) {
        return campaignEbcSignApplyDao.hasDetailByOpenid(openid);
    }

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    @Override
    public CampaignEbciDetail getDetail(String phoneNumber) {
        return campaignEbcSignApplyDao.getDetail(phoneNumber);
    }

    /**
     * 申领
     *
     * @param campaignEbciDetail 资料
     * @return 0:成功，1：手机号码已经领过了，2：库存不足
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int submit(CampaignEbciDetail campaignEbciDetail) {

        // 扣库存
        if (0 == campaignEbcSignApplyDao.updateStock(campaignEbciDetail)) {
            return 2;
        }

        // 写入记录
        try {
            campaignEbcSignApplyDao.insetDetail(campaignEbciDetail);
            return 0;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return 1;
        }

    }

    /**
     * 更新是否关注
     *
     * @param campaignEbciDetail 申领信息
     */
    @Override
    public void setFollow(CampaignEbciDetail campaignEbciDetail) {
        campaignEbcSignApplyDao.setFollow(campaignEbciDetail);
    }

    /**
     * 获取审领信息
     *
     * @param openid 公众号唯一编号
     * @return 审领信息
     */
    @Override
    public CampaignEbciDetail getDetailByOpenid(String openid) {
        return campaignEbcSignApplyDao.getDetailByOpenid(openid);
    }

    /**
     * 跟据场景值获取门店
     *
     * @param scene 场景值
     * @return 门店
     */
    @Override
    public CampaignEbciStock getByScene(String scene) {
        return campaignEbcSignApplyDao.getByScene(scene);
    }

    /**
     * 更新核销信息
     *
     * @param campaignEbciDetail 核销信息
     */
    @Override
    public void setReceive(CampaignEbciDetail campaignEbciDetail) {
        campaignEbcSignApplyDao.setReceive(campaignEbciDetail);
    }

    /**
     * 是否打卡满7天
     *
     * @param openid 公众号openid
     * @return 统计
     */
    @Override
    public int hadSigned(String openid) {
        return campaignEbcSignApplyDao.hadSigned(openid);
    }
}
