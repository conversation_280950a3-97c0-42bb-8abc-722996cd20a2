package cn.slashsoft.clinique.service.mini.impl;

import cn.slashsoft.clinique.dao.mini.PointDao;
import cn.slashsoft.clinique.dao.mini.TTestimonyBlindBoxDao;
import cn.slashsoft.clinique.dao.mini.TTestimonyKocUserListDao;
import cn.slashsoft.clinique.domain.mini.PointTransaction;
import cn.slashsoft.clinique.domain.mini.TTestimonyBlindBox;
import cn.slashsoft.clinique.enums.CBForeignEnum;
import cn.slashsoft.clinique.enums.CBTypeEnum;
import cn.slashsoft.clinique.service.mini.TTestimonyBlindBoxService;
import cn.slashsoft.clinique.util.DateUtil;

import org.springframework.stereotype.Service;

import java.util.Date;

import javax.annotation.Resource;

/**
* <p>
    * 盲盒申领
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
@Service
public class TTestimonyBlindBoxServiceImpl implements TTestimonyBlindBoxService {

    @Resource
    private TTestimonyBlindBoxDao tTestimonyBlindBoxDao;
    
    @Resource
    private TTestimonyKocUserListDao tTestimonyKocUserListDao;    

    @Resource
	private PointDao pointDao;

    @Override
    public void insertTTestimonyBlindBox(TTestimonyBlindBox tTestimonyBlindBox) {
        tTestimonyBlindBoxDao.insertTTestimonyBlindBox(tTestimonyBlindBox);
        Long customerId = tTestimonyBlindBox.getCustomerId();
      //加100分
        if(tTestimonyKocUserListDao.getKocByCustomerId(customerId + "") > 0) {
        	TTestimonyBlindBox box = tTestimonyBlindBoxDao.getTTestimonyBlindBoxByCustomerId(customerId);
        	if(box == null) {
        		return;
        	}
        	// 发放C币
	        Date now = new Date();
	        PointTransaction pointTransaction = new PointTransaction();
	        pointTransaction.setCustomerId(customerId);
	        pointTransaction.setPointTypeId(CBTypeEnum.ZHENGYAN_GET.getId());
	        pointTransaction.setPoints(CBTypeEnum.ZHENGYAN_GET.getPoints());
	        pointTransaction.setRemainingPoints(CBTypeEnum.ZHENGYAN_GET.getPoints());
	        pointTransaction.setStartTime(now);
	        pointTransaction.setExpiredTime(DateUtil.getLastSecondOfMonthAndNextYear(now));
	        pointTransaction.setForeignId(CBForeignEnum.ZHENGYAN_GET.getId());
	        pointTransaction.setForeignMasterId(box.getId());
	        pointTransaction.setForeignDetailId(box.getId());
	        pointTransaction.setRemark(CBTypeEnum.ZHENGYAN_GET.getName());
	        pointDao.insertPointTransaction(pointTransaction);
	        
        }
    }

    @Override
    public void updateTTestimonyBlindBox(TTestimonyBlindBox tTestimonyBlindBox) {
        tTestimonyBlindBoxDao.updateTTestimonyBlindBox(tTestimonyBlindBox);
    }

    @Override
    public TTestimonyBlindBox getTTestimonyBlindBox(Long id) {
        return tTestimonyBlindBoxDao.getTTestimonyBlindBox(id);
    }

    @Override
    public TTestimonyBlindBox getTTestimonyBlindBoxByCustomerId(Long customerId) {
        return tTestimonyBlindBoxDao.getTTestimonyBlindBoxByCustomerId(customerId);
    }

    /**
     * 更新盲盒领取状态
     * @param customerId
     */
    @Override
    public void updateBlindBoxDrawFlag(long customerId) {
        tTestimonyBlindBoxDao.updateBlindBoxDrawFlag(customerId);
        
    }

    /**
     * 获取已抽取盲盒暗语
     * @param customerId
     * @return
     */
    @Override
    public String getBlindBoxPassword(long customerId) {
        return tTestimonyBlindBoxDao.getBlindBoxPassword(customerId);
    }

    /**
     * 添加抽奖的log记录
     * @param tTestimonyBlindBox
     */
    @Override
    public void insertTTestimonyBlindBoxLog(TTestimonyBlindBox tTestimonyBlindBox) {
        tTestimonyBlindBoxDao.insertTTestimonyBlindBoxLog(tTestimonyBlindBox);
    }
}
