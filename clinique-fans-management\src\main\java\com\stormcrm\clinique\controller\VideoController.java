package com.stormcrm.clinique.controller;

import com.stormcrm.clinique.domain.Video;
import com.stormcrm.clinique.domain.VideoTag;
import com.stormcrm.clinique.domain.VideoTagDefine;
import com.stormcrm.clinique.service.OutsideService;
import com.stormcrm.clinique.service.VideoService;
import com.stormcrm.clinique.service.VideoTagService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.util.VerifyUtil;
import com.stormcrm.clinique.vo.EditVideoWithTagVo;
import com.stormcrm.clinique.vo.Result;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

import static com.alibaba.fastjson.JSON.toJSONString;
import static com.alibaba.fastjson.JSON.toJSONStringWithDateFormat;

/**
 * 笔记
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("video")
public class VideoController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    public static final String OSS_VIDEO_IMAGE_FOLDER_NAME = "video";
    public static final String VALID_MUST_HAVE_VAR = "这是必填字段";
    private final VideoService videoService;
    private final VideoTagService videoTagService;
    private final OutsideService outsideService;

    public VideoController(
            VideoService videoService,
            VideoTagService videoTagService,
            OutsideService outsideService
    ) {
        this.videoService = videoService;
        this.videoTagService = videoTagService;
        this.outsideService = outsideService;
    }

    /**
     * 视频列表
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('VIDEO_INDEX')")
    @RequestMapping("index")
    public String index(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/video/index";
    }

    /**
     * 查询所有-分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索关键字
     * @param type          状态
     * @return 列表
     */
    @PreAuthorize("hasAuthority('VIDEO_INDEX')")
    @PostMapping("get-page")
    @ResponseBody
    public String getPage(
            @RequestParam(value = "pagination[page]", required = false) Integer page,
            @RequestParam(value = "pagination[perpage]", required = false) Integer perpage,
            @RequestParam(value = "query[generalSearch]", required = false) String generalSearch,
            @RequestParam(value = "query[type]", required = false) Short type
    ) {

        if (null == page) {
            page = 1;
        }

        if (null == perpage) {
            perpage = 10;
        }

        List<Video> videoList = videoService.getPage(page, 500, generalSearch, type);
        int count = videoService.getPageCount(generalSearch, type);

        JSONObject mata = new JSONObject();
        mata.put("page", page);
        mata.put("pages", (count + perpage - 1) / perpage);
        mata.put("perpage", perpage);
        mata.put("total", count);
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("mata", mata);
        result.put("data", videoList);

        return toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }

    /**
     * 增加模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('VIDEO_ADD')")
    @RequestMapping("add")
    public String add(Model model) {
        model.addAttribute("staticDomain", staticDomain);

        ArrayList<VideoTagDefine> allVideoTags = videoTagService.getTagDefineList();

        model.addAttribute("videoTags", allVideoTags);

        return "m/fans/video/add";
    }

    /**
     * 新增
     *
     * @param title      名称
     * @param brandLogo  品牌图片
     * @param coverImage 封面图片
     * @param fileUrl    视频链接
     * @return 结果
     */
    @PreAuthorize("hasAuthority('VIDEO_ADD')")
    @PostMapping(value = "add-submit", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String addSubmit(
            @RequestParam(value = "form-title", required = false) String title,
            @RequestParam(value = "form-brand_name", required = false) String brandName,
            @RequestParam(value = "form-brand_logo", required = false) MultipartFile brandLogo,
            @RequestParam(value = "form-cover_image", required = false) MultipartFile coverImage,
            @RequestParam(value = "form-file_url", required = false) MultipartFile fileUrl,
//            @RequestParam(value = "form-file_url", required = false) String fileUrl,
            @RequestParam(value = "form-status", required = false) Integer status,
            @RequestParam(value = "form-tags[]", required = false) List<Long> tags,
            @RequestParam(value = "form-type", required = false) Integer type
    ) {

        // 验证
        if (!VerifyUtil.required(title)) {
            return ResultUtil.verifyFailToJson("form-title", VALID_MUST_HAVE_VAR);
        }

        // 验证
        if (null == brandLogo || brandLogo.isEmpty()) {
            return ResultUtil.verifyFailToJson("form-brand_logo", VALID_MUST_HAVE_VAR);
        }

        // 验证
        if (null == coverImage || coverImage.isEmpty()) {
            return ResultUtil.verifyFailToJson("form-cover_image", VALID_MUST_HAVE_VAR);
        }

        // 验证
        if (null == fileUrl || fileUrl.isEmpty()) {
            return ResultUtil.verifyFailToJson("form-file_url", VALID_MUST_HAVE_VAR);
        }

        // 验证
//        if (!VerifyUtil.required(fileUrl)) {
//            return ResultUtil.verifyFailToJson("form-file_url", VALID_MUST_HAVE_VAR);
//        }

        // 生成对象
        Video video = new Video();
        video.setTitle(title);
        video.setBrandName(brandName);
        video.setBrandLogo(outsideService.uploadImageOss(brandLogo, OSS_VIDEO_IMAGE_FOLDER_NAME));
        video.setCoverImage(outsideService.uploadImageOss(coverImage, OSS_VIDEO_IMAGE_FOLDER_NAME));
        video.setFileUrl(outsideService.uploadImageOss(fileUrl, OSS_VIDEO_IMAGE_FOLDER_NAME));
        video.setStatus(status);
        video.setType(type);
        // 传到Service服务中保存
        Result result = videoService.save(video, tags);

        // 返回绍果给前端
        return toJSONString(result);
    }

    /**
     * 逻辑删除
     *
     * @param id 胸章的自动编号
     * @return 影响的行数
     */
    @PreAuthorize("hasAuthority('VIDEO_DEL')")
    @GetMapping("del/{id}")
    @ResponseBody
    public String del(@PathVariable("id") Long id) {

        if (0 == videoService.del(id)) {
            return ResultUtil.failToJson();
        }

        return ResultUtil.successToJson();

    }

    /**
     * 编辑模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('VIDEO_EDIT')")
    @GetMapping("edit/{id}")
    public String edit(@PathVariable Long id, Model model) {
        model.addAttribute("staticDomain", staticDomain);
        // 读数据库
        Video video = videoService.getById(id);
        if (null == video) {
            return "m/fans/common/empty";
        }
        ArrayList<EditVideoWithTagVo> allVideoTags = videoTagService.getTagListSelected(id);

        model.addAttribute("id", id);
        model.addAttribute("video", video);
        model.addAttribute("videoTags", allVideoTags);

        return "m/fans/video/edit";
    }

    /**
     * 编辑
     *
     * @param id        编号
     * @param title     名称
     * @param brandLogo 图片
     * @param fileUrl   页面
     * @return 结果
     */
    @PreAuthorize("hasAuthority('VIDEO_EDIT')")
    @PostMapping(value = "edit-submit/{id}", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String editSubmit(
            @PathVariable("id") Long id,
            @RequestParam(value = "form-title", required = false) String title,
            @RequestParam(value = "form-brand_name", required = false) String brandName,
            @RequestParam(value = "form-brand_logo", required = false) MultipartFile brandLogo,
            @RequestParam(value = "form-cover_image", required = false) MultipartFile coverImage,
            @RequestParam(value = "form-file_url", required = false) MultipartFile fileUrl,
//            @RequestParam(value = "form-file_url", required = false) String fileUrl,
            @RequestParam(value = "form-status", required = false) Integer status,
            @RequestParam(value = "form-tags[]", required = false) List<Long> tags,
            @RequestParam(value = "form-type", required = false) Integer type
    ) {

        // 验证
        if (!VerifyUtil.required(title)) {
            return ResultUtil.verifyFailToJson("form-title", VALID_MUST_HAVE_VAR);
        }

        // 生成对象
        Video video = new Video();
        video.setId(id);
        video.setTitle(title);
        video.setBrandName(brandName);
        if (null != brandLogo && !brandLogo.isEmpty()) {
            video.setBrandLogo(outsideService.uploadImageOss(brandLogo, OSS_VIDEO_IMAGE_FOLDER_NAME));
        }
        if (null != coverImage && !coverImage.isEmpty()) {
            video.setCoverImage(outsideService.uploadImageOss(coverImage, OSS_VIDEO_IMAGE_FOLDER_NAME));
        }
        if (null != fileUrl && !fileUrl.isEmpty()) {
            video.setFileUrl(outsideService.uploadImageOss(fileUrl, OSS_VIDEO_IMAGE_FOLDER_NAME));
        }
        video.setStatus(status);
        video.setType(type);

        // 传到Service服务中保存
        Result result = videoService.update(video, tags);

        // 返回绍果给前端
        return toJSONString(result);
    }

    /**
     * 查看图片上的全部的标签 或者 直接全查出来
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('VIDEO_EDIT')")
    @RequestMapping("tags/{videoId}")
    @ResponseBody
    public String videoTag(
            @RequestParam("videoId") Long videoId,
            Model model) {

        ArrayList<VideoTag> tags = videoTagService.getTagListByVideoId(videoId);

        JSONObject result = new JSONObject();
        result.put("data", tags);

        return toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }
}
