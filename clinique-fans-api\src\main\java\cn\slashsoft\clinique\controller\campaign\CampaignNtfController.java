package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignViewLog;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.campaign.CampaignNtfService;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.Result;
import cn.slashsoft.clinique.vo.campaign.ntf.CampaignNtfIndexVo;
import cn.slashsoft.clinique.vo.campaign.ntf.CampaignNtfInviteVo;
import cn.slashsoft.clinique.vo.campaign.ntf.CampaignNtfMineVo;
import cn.slashsoft.clinique.vo.mini.Location;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * NTF
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/campaign-ntf")
public class CampaignNtfController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final CampaignNtfService campaignNtfService;
    private final WechatService wechatService;
    private final StringRedisTemplate stringRedisTemplate;

    public CampaignNtfController(CampaignNtfService campaignNtfService, StringRedisTemplate stringRedisTemplate, WechatService wechatService) {
        this.campaignNtfService = campaignNtfService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.wechatService = wechatService;
    }

    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/get-index-data")
    public String getIndexData() {

        String unionid = (String) request.getAttribute("unionid");
        long customerId = (long) request.getAttribute("customerId");

        CampaignNtfIndexVo campaignNtfIndexVo = new CampaignNtfIndexVo();

        campaignNtfIndexVo.setStartTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignNtfStartTime")));
        campaignNtfIndexVo.setEndTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignNtfEndTime")));

        campaignNtfIndexVo.setCampaignNtf(campaignNtfService.getOrCreateCampaignNtf(unionid, customerId));

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignNtfIndexVo);

    }

    /**
     * 获取我的页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/get-mine-data")
    public String getMineData() {

        long customerId = (long) request.getAttribute("customerId");

        CampaignNtfMineVo campaignNtfMineVo = new CampaignNtfMineVo();
        campaignNtfMineVo.setCampaignNtfRecordList(campaignNtfService.getCampaignNtfRecord(customerId));

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignNtfMineVo);

    }

    /**
     * 获取邀请页数据
     *
     * @return 邀请页数据
     */
    @PostMapping("/get-invite-data")
    public String getInviteData() {

        long customerId = (long) request.getAttribute("customerId");
        String unionid = (String) request.getAttribute("unionid");
        String miniOpenid = (String) request.getAttribute("miniOpenid");

        CampaignNtfInviteVo campaignNtfInviteVo = new CampaignNtfInviteVo();

        campaignNtfInviteVo.setStartTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignNtfStartTime")));
        campaignNtfInviteVo.setEndTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignNtfEndTime")));

        Wechat wechat = wechatService.getWechatInfoByCustomerId(customerId);
        campaignNtfInviteVo.setAvatarUrl(wechat.getAvatarUrl());
        campaignNtfInviteVo.setNickName(wechat.getNickName());

        campaignNtfInviteVo.setCampaignNtf(campaignNtfService.getOrCreateCampaignNtf(unionid, customerId));
        campaignNtfInviteVo.setInviteeAvatarUrlList(campaignNtfService.getInviteeAvatarUrl(miniOpenid));

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignNtfInviteVo);

    }

    /**
     * 开启
     *
     * @param location 定位
     * @return 结果
     */
    @PostMapping("/get-back")
    public String getBack(
            @RequestBody Location location
    ) {

        long customerId = (long) request.getAttribute("customerId");

        if (null == location || null == location.getLatitude() || null == location.getLongitude()) {
            return ResultUtil.customer(ResultEnum.ERROR, "参数丢失");
        }

        Result result = campaignNtfService.back(customerId, location);

        if (0 != result.getCode()) {
            return ResultUtil.customer(ResultEnum.FAILED, result.getMessage());
        }

        return ResultUtil.customer(ResultEnum.SUCCESS);

    }

    /**
     * 抽奖
     *
     * @param location 定位
     * @return 结果
     */
    @PostMapping("/get-draw")
    public String getDraw(
            @RequestBody Location location
    ) {

        long customerId = (long) request.getAttribute("customerId");

        if (null == location || null == location.getLatitude() || null == location.getLongitude()) {
            return ResultUtil.customer(ResultEnum.ERROR, "参数丢失");
        }

        Result result = campaignNtfService.draw(customerId, location);

        switch (result.getCode()) {
            case 0:
                return ResultUtil.customer(ResultEnum.SUCCESS, result.getData());
            case 2:
                return ResultUtil.customer(ResultEnum.PARAM_ERROR, "你的盲盒已抽完");
            case 3:
                return ResultUtil.customer(ResultEnum.OTHER, "带上3个好姐妹加入C粉圈，就能再抽一次盲盒哦！");
            case 4:
                return ResultUtil.customer(ResultEnum.PROCESS_BUSY, "你好像不在倩碧柜台哦～");
            default:
                return ResultUtil.customer(ResultEnum.FAILED, result.getMessage());
        }

    }

    /**
     * 写入访问开始日志
     *
     * @param source 来源
     * @return 成功
     */
    @PostMapping("/start-view-log/{source}/{page}")
    public String setStartViewLog(
            @PathVariable("source") String source,
            @PathVariable("page") String page
    ) {

        long customerId = (long) request.getAttribute("customerId");

        CampaignViewLog campaignViewLog = new CampaignViewLog();
        campaignViewLog.setCustomerId(customerId);
        campaignViewLog.setSource(source);
        campaignViewLog.setPage(page);

        long id = campaignNtfService.insertViewLog(campaignViewLog);

        return ResultUtil.customer(ResultEnum.SUCCESS, id);

    }

    /**
     * 写入访问结束日志
     *
     * @param id 来源
     * @return 成功
     */
    @PostMapping("/end-view-log/{id}")
    public String setEndViewLog(
            @PathVariable("id") long id
    ) {

        campaignNtfService.setViewLog(id);
        return ResultUtil.customer(ResultEnum.SUCCESS);

    }

}
