package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignChallenge72;
import cn.slashsoft.clinique.domain.campaign.CampaignChallenge72CityConfig;
import cn.slashsoft.clinique.domain.campaign.CampaignViewLog;

import java.util.List;

/**
 * 挑战72小时
 *
 * <AUTHOR>
 */
public interface CampaignChallenge72Service {

    /**
     * 获取活动信息
     *
     * @param customerId 会员编号
     * @param source     来源
     * @return 活动信息
     */
    CampaignChallenge72 getCampaignChallenge72(long customerId, String source);

    /**
     * 获取头像
     *
     * @param customerId 顾客信息
     * @return 头像
     */
    String getAvatarUrl(long customerId);

    /**
     * 获取被邀请者头像
     *
     * @param openid 邀请者openid
     * @return 头像
     */
    List<String> getInviteeAvatarUrl(String openid);

    /**
     * 设置第一轮完成
     *
     * @param customerId 顾客编号
     */
    void setCampaignChallenge72FirstRound(long customerId);

    /**
     * 设置第二轮完成
     *
     * @param customerId 顾客编号
     */
    void setCampaignChallenge72SecondRound(long customerId);

    /**
     * 设置第三轮完成
     *
     * @param customerId 顾客编号
     */
    void setCampaignChallenge72ThirdRound(long customerId);

    /**
     * 获取所有城市
     *
     * @param customerId 顾客编号
     * @return 所有城市
     */
    CampaignChallenge72CityConfig getCampaignChallenge72City(long customerId);

    /**
     * 选择心得大师
     *
     * @param customerId 顾客编号
     */
    void setResultExperience(long customerId);

    /**
     * 选择分享大师
     *
     * @param customerId 顾客编号
     */
    void setResultShare(long customerId);

    /**
     * 邀请
     *
     * @param inviterMiniOpenid 邀请者
     * @param inviteeMiniOpenid 被邀请者
     */
    void invite(String inviterMiniOpenid, String inviteeMiniOpenid);

    /**
     * 写入访问日志
     *
     * @param campaignViewLog 日志
     * @return 日志编号
     */
    long insertCampaignChallenge72ViewLog(CampaignViewLog campaignViewLog);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    void setCampaignChallenge72ViewLog(long id);

}
