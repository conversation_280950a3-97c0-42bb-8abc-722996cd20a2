package com.stormcrm.clinique.oauth.config;

import com.stormcrm.clinique.oauth.phone.PhoneLoginConfigurer;

import com.stormcrm.clinique.oauth.phone.PhoneUserDetailsService;

import org.springframework.context.annotation.Configuration;

import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

/**
 * <AUTHOR>
 */
//@Configuration
//@EnableWebSecurity
//@EnableGlobalMethodSecurity(prePostEnabled = true,jsr250Enabled = true)
public class WebSecurityPhoneConfig extends WebSecurityConfigurerAdapter {


    private final PhoneUserDetailsService phoneUserDetailsService;
    private final FailureHandler failureHandler;
    private final SuccessHandler successHandler;

    public WebSecurityPhoneConfig(PhoneUserDetailsService phoneUserDetailsService, FailureHandler failureHandler, SuccessHandler successHandler) {
        this.phoneUserDetailsService = phoneUserDetailsService;
        this.failureHandler = failureHandler;
        this.successHandler = successHandler;
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
                // 关闭AJAX跨域验证
                .csrf().disable()
                // 请求授权配置
                .authorizeRequests()
                // 资源文件
                .antMatchers("/m/**").permitAll()
                // 登录页面
                .antMatchers("/login", "/login/form", "/login/phone", "/phone/*").permitAll()
                // 所有页面都要登录
                .anyRequest().authenticated()
                .and()
                // 登录
                .formLogin()
                .loginPage("/fans-management/login")
                .and()
                .apply(new PhoneLoginConfigurer<>(phoneUserDetailsService))
                //.loginProcessingUrl("/login/phone")
                .loginProcessingUrl("/login-submit")
                .failureHandler(failureHandler)
                .successHandler(successHandler)
                .and()
                // 退出
                .logout()
                .logoutUrl("/fans-management/logout")
                .logoutSuccessUrl("/login?logout")
        ;

    }

}
