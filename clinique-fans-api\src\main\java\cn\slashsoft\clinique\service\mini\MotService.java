package cn.slashsoft.clinique.service.mini;

/**
 * 发送MOT
 * <AUTHOR>
 */
public interface MotService {

    /**
     * 获得积分
     * @param customerId 顾客编号
     * @param points 获得的积分
     * @param reason 原因
     */
    void getPoints(long customerId, int points, String reason);

    /**
     * 获取礼券
     * @param customerId 顾客编号
     * @param couponName 礼券名称
     * @param source 来源
     */
    void getCoupon(long customerId, String couponName, String source);

    /**
     * 到店取货类订单兑换成功发送模版消息
     *
     * @param customerId  顾客编号
     * @param giftId      礼品编号
     * @param areaPlaceId 门店编号
     */
    void exchangePickupSend(long customerId, long giftId, long areaPlaceId);

    /**
     * 到店取货类订单兑换成功发送模版消息
     *
     * @param customerId 顾客编号
     * @param giftId     礼品编号
     */
    void exchangeExpress(long customerId, long giftId);

    /**
     * EBCI打卡成功
     *
     * @param customerId 顾客编号
     */
    void ebciSign(long customerId);

    /**
     * EBCI打卡5天成功
     *
     * @param customerId 顾客编号
     */
    void ebciSign5(long customerId);

    /**
     * EBCI打卡成功，申领成功
     *
     * @param openid      公众号OpenId
     * @param phoneNumber 手机号码
     * @param store       门店
     */
    void ebciSignApply(String openid, String phoneNumber, String store);

    /**
     * 证言活动 领取成功
     *
     * @param customerId
     * @param pwd    盲盒密语
     * @param store  门店
     */
    void testimonyDrawSuccess(Long customerId, String pwd, String store);

	/**
	 * 三日打卡完成发券提醒
	 *
	 * @param customerId
	 * @param day   2\3
	 */
	void day3TaskFinishNotice(Long customerId);

}
