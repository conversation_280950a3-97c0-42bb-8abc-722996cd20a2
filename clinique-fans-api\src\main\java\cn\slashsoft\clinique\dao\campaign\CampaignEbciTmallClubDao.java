package cn.slashsoft.clinique.dao.campaign;

import cn.slashsoft.clinique.domain.campaign.*;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * 302美白镭射瓶
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignEbciTmallClubDao {

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    @Select("SELECT " +
            "   `id`, `name`, `phone_number`, `city`, `store`, `follow` " +
            "FROM " +
            "   `campaign_ebci_tmall_club_detail` " +
            "WHERE " +
            "   `phone_number`=#{phoneNumber} " +
            "LIMIT 1")
    CampaignEbciDetail getDetail(String phoneNumber);

    /**
     * 定入申领信息
     *
     * @param campaignEbciDetail 申领信息
     */
    @Insert("INSERT INTO `campaign_ebci_tmall_club_detail`(" +
            "   `type`, " +
            "   `unique_id`, " +
            "   `name`, " +
            "   `phone_number`, " +
            "   `city`, " +
            "   `store`, " +
            "   `store_id`, " +
            "   `source` " +
            ") " +
            "VALUES (" +
            "   #{type}, " +
            "   #{uniqueId}, " +
            "   #{name}, " +
            "   #{phoneNumber}, " +
            "   #{city}, " +
            "   #{store}, " +
            "   #{storeId}, " +
            "   #{source} " +
            ")")
    void insetDetail(CampaignEbciDetail campaignEbciDetail);

}
