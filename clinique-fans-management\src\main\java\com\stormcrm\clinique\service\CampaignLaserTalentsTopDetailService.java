package com.stormcrm.clinique.service;

import com.stormcrm.clinique.domain.CampaignLaserTalentsTopDetail;
import com.stormcrm.clinique.vo.Result;

import java.util.List;

/**
 * 新品体验官活动
 *
 * <AUTHOR>
 */
public interface CampaignLaserTalentsTopDetailService {


    /**
     * 查询所有活动
     * @param id TOP5编号
     * @return 活动列表
     */
    List<CampaignLaserTalentsTopDetail> getAll(long id);

    /**
     * 查询信息
     *
     * @param detailId 自动编号
     * @return 信息
     */
    CampaignLaserTalentsTopDetail getById(long detailId);

    /**
     * 保存
     *
     * @param campaignLaserTalentsTopDetail 信息
     * @return 影响的行数
     */
    Result save(CampaignLaserTalentsTopDetail campaignLaserTalentsTopDetail);

    /**
     * 更新
     *
     * @param campaignLaserTalentsTopDetail 信息
     * @return 影响的行数
     */
    Result update(CampaignLaserTalentsTopDetail campaignLaserTalentsTopDetail);

    /**
     * 更新
     *
     * @param id 编号
     * @return 影响的行数
     */
    int delete(long id);
}
