package com.stormcrm.clinique.domain.tag;

import java.util.Date;

import lombok.Data;
/**
 * 
 *
 * <AUTHOR>
 */

@Data
public class LabelPackageRule {
	//id
	int id;
	//排序
	int index;
	//跟上一条件 关系
	int joinType;
	int operate;
	int categoryId;
	//规则Id
	int ruleId;
	//规则取值
	String value;
	
	//所属标签
	int labelId;

	Date createTime;
	

	//From Rule
	String name;
	String db;
	String table;
	String column;
	String description;
	String remark;
}
