package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignToneupDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignToneupLog;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.service.mini.SnsService;
import cn.slashsoft.clinique.service.campaign.CampaignToneupService;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.util.VerifyUtil;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 黄油变粉安瓶级保湿补光申领
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/official")
public class CampaignToneupController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final CampaignToneupService campaignToneupService;
    private final OutsideService outsideService;
    private final SnsService snsService;

    public CampaignToneupController(CampaignToneupService campaignToneupService, SnsService snsService, OutsideService outsideService) {
        this.campaignToneupService = campaignToneupService;
        this.snsService = snsService;
        this.outsideService = outsideService;
    }

    @GetMapping("/campaign-toneup/{unionid}/{source}.html")
    public String index(
            @PathVariable("source") String source,
            @PathVariable("unionid") String unionid,
            Model model
    ) {
        model.addAttribute("staticDomain", staticDomain);

        if(StringUtil.isNullOrEmpty(unionid) || "none".equals(unionid)){
            return "w/fans/official/toneup/wechat/none";
        }

        String phoneNumber = campaignToneupService.getPhoneNumberByUnionid(unionid);

        // 读取是否填写过
        if (0 < campaignToneupService.hasDetail(phoneNumber)) {
            return "redirect:/official/campaign-toneup-result/" + unionid + "/" + source + ".html";
        }

        String openid = (String) request.getAttribute("miniOpenid");

        // 保存日志
        CampaignToneupLog campaignToneupLog = new CampaignToneupLog();
        campaignToneupLog.setType((short) 1);
        campaignToneupLog.setUniqueId(openid);
        campaignToneupLog.setSource(source);
        campaignToneupLog.setPage("index");
        campaignToneupService.insertLog(campaignToneupLog);

        model.addAttribute("unionid", unionid);
        model.addAttribute("phoneNumber", phoneNumber);
        model.addAttribute("serviceName", "official");
        model.addAttribute("source", source);

        return "w/fans/official/toneup/wechat/index";
    }

    @GetMapping("/campaign-toneup-verify-code/{phoneNumber}")
    @ResponseBody
    public String getVerifyCode(
            @PathVariable("phoneNumber") String phoneNumber
    ) {
        return "{\"code\":" + snsService.sendVerifyCode(phoneNumber) + "}";
    }

    @PostMapping("/campaign-toneup-submit/{unionid}/{source}.html")
    @ResponseBody
    public String submit(
            @PathVariable("source") String source,
            @PathVariable("unionid") String unionid,
            @RequestParam("formName") String name,
            @RequestParam("formCity") String city,
            @RequestParam("formStore") String store
    ) {

        if (!VerifyUtil.required(name)) {
            return "{\"code\":9,\"message\":\"请输入姓名\"}";
        }

        if (!VerifyUtil.required(city)) {
            return "{\"code\":9,\"message\":\"请选择城市\"}";
        }

        if (!VerifyUtil.required(store)) {
            return "{\"code\":9,\"message\":\"请选择柜台\"}";
        }

        String phoneNumber = campaignToneupService.getPhoneNumberByUnionid(unionid);

        String openid = (String) request.getAttribute("miniOpenid");

        // 申领
        CampaignToneupDetail campaignToneupDetail = new CampaignToneupDetail();
        campaignToneupDetail.setType((short) 1);
        campaignToneupDetail.setUniqueId(openid);
        campaignToneupDetail.setName(name);
        campaignToneupDetail.setPhoneNumber(phoneNumber);
        campaignToneupDetail.setCity(city);
        campaignToneupDetail.setStore(store);
        campaignToneupDetail.setSource(source);

        // 0:成功，1：手机号码已经领过了，2：库存不足
        switch (campaignToneupService.submit(campaignToneupDetail)) {
            case 0:
                snsService.toneup(phoneNumber, store);
                return "{\"code\":0}";
            case 1:
                return "{\"code\":1}";
            case 2:
                return "{\"code\":2}";
            default:
                return "{\"code\":3}";
        }

    }

    @GetMapping("/campaign-toneup-result/{unionid}/{source}.html")
    public String result(
            @PathVariable("source") String source,
            @PathVariable("unionid") String unionid,
            Model model
    ) {
        model.addAttribute("staticDomain", staticDomain);

        String phoneNumber = campaignToneupService.getPhoneNumberByUnionid(unionid);

        // 读取申领信息
        CampaignToneupDetail campaignToneupDetail = campaignToneupService.getDetail(phoneNumber);
        if(null == campaignToneupDetail){
            return "redirect:/official/campaign-toneup/" + unionid + "/" + source + ".html";
        }

        String openid = (String) request.getAttribute("miniOpenid");

        // 保存日志
        CampaignToneupLog campaignToneupLog = new CampaignToneupLog();
        campaignToneupLog.setType((short) 1);
        campaignToneupLog.setUniqueId(openid);
        campaignToneupLog.setSource(source);
        campaignToneupLog.setPage("result");
        campaignToneupService.insertLog(campaignToneupLog);

        if (null == campaignToneupDetail.getFollow()){
            // 获取是否关注
            Boolean isFollow = false;
            // Boolean isFollow = outsideService.isFollow(openid);
            // 未获取到是否关注，即为AccessToken过期
            // if(null == isFollow){
                // 更新AccessToken
                // outsideService.getAccessToken();
                // 获取是否关注
                // isFollow = outsideService.isFollow(openid);
            // }
            // 返回是否关注
            campaignToneupDetail.setFollow(isFollow);
            // 保存
            campaignToneupService.setFollow(phoneNumber, isFollow);
        }

        if (campaignToneupDetail.getFollow()){
            model.addAttribute("campaignToneupDetail", campaignToneupDetail);
            return "w/fans/official/toneup/wechat/success";
        }
        else{
            return "w/fans/official/toneup/wechat/fail";
        }

    }

}
