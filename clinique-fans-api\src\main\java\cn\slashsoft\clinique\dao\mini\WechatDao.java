package cn.slashsoft.clinique.dao.mini;

import cn.slashsoft.clinique.domain.mini.*;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

/**
 * 与微信开放平台、微信公众号、微信小程序相关的操作
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface WechatDao {

    /**
     * 跟据微信小程序的唯一编号查找
     * 登录
     *
     * @param openid 小程序的唯一编号
     * @return 微信信息
     */
    @Select("SELECT " +
            "   `w`.`customer_id` " +
            "FROM " +
            "   `wechat` `w` " +
            "WHERE " +
            "   `w`.`wechat_mini_openid`=#{openid} " +
            " OR " +
            "   `w`.`wechat_official_openid`=#{openid} " +
            "LIMIT 1"
    )
    Long getCustomerIdByOpenId(String openid);
    
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `wechat` WHERE " +
            "   `wechat_official_openid`=#{openid} " +
            "LIMIT 1"
    )
    Wechat getWechatByOfficialOpenId(String openid);
    /**
     * 跟据微信小程序的唯一编号查找
     * 登录
     *
     * @param openid 小程序的唯一编号
     * @return 微信信息
     */
    @Select("SELECT " +
            "   `w`.`id`," +
            "   `w`.`customer_id`, " +
            "    decryptPhone(`c`.`phone_number`) phone_number, " +
            "   `w`.`unionid`, " +
            " k.`customer_id` `koc_v`, " +
            "   `w`.`wechat_official_openid`, " +
            "   `w`.`wechat_mini_openid`, " +
            "   `w`.`scene_reward_last_time` " +
            "FROM " +
            "   `wechat` `w` " +
            "       INNER JOIN " +
            "   `customer` `c` " +
            "       ON `w`.`customer_id`=`c`.`id` " 
            + " LEFT JOIN `customer_special_koc_v` k "
            + " ON k.`customer_id` = `w`.`customer_id` "+
            "WHERE " +
            "   `w`.`wechat_mini_openid`=#{openid} " +
            "LIMIT 1"
    )
    Wechat getWechatByOpenId(String openid);

    /**
     * 跟据开放平台的唯一编号查找
     * 登录
     *
     * @param unionid 开放平台的唯一编号
     * @return 微信信息
     */
    @Select("SELECT " +
            "   `w`.`id`," +
            "   `w`.`customer_id`, " +
            "    decryptPhone(`c`.`phone_number`) phone_number, " +
            "   `w`.`unionid`, " +
            "   `w`.`wechat_official_openid`, " +
            "   `w`.`wechat_mini_openid`," +
            "   `w`.`avatar_url` " +
            "FROM " +
            "   `wechat` `w` " +
            "       INNER JOIN " +
            "   `customer` `c` " +
            "       ON `w`.`customer_id`=`c`.`id` " +
            "WHERE " +
            "   `w`.`unionid`=#{unionid} " +
            "LIMIT 1"
    )
    Wechat getWechatByUnionId(String unionid);

    /**
     * 插入登录
     * 登录
     *
     * @param login 登录
     */
    @Insert("INSERT INTO `login`(" +
            "   `wechat_mini_openid`, " +
            "   `scene`" +
            ") " +
            "VALUES (" +
            "   #{wechatMiniOpenid}," +
            "   #{scene}" +
            ")"
    )
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertLogin(Login login);

    /*
	 * 获取用户连续登录记录
	 */
    @Select(" SELECT "
    		+ "	DISTINCT TO_DAYS(a.create_time) da "
    		+ "	FROM login a "
    		+ "	WHERE a.create_time > #{startTime} "
    		+ "	AND a.wechat_mini_openid = #{miniOpenId}"
    		+ " GROUP BY da "
    		+ " ORDER BY da ")
    List<Integer> getContinuousLoginBy(Date startTime, String miniOpenId);
    /**
     * 更新公众号文章场景值最后发放积分时间
     * 登录
     *
     * @param id 微信编号
     */
    @Update("UPDATE " +
            "   `wechat` " +
            "SET " +
            "   `scene_reward_last_time`=NOW() " +
            "WHERE " +
            "   `id`=#{id}"
    )
    void updateSceneLastTime(long id);

    /**
     * 更新公众号文章场景值最后发放积分时间
     * 登录
     *
     * @param wechat 微信信息
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `wechat` " +
            "SET " +
            "   `scene_reward_last_time`=NOW() " +
            "WHERE " +
            "   `id`=#{id} " +
            "   AND `scene_reward_last_time`=#{sceneRewardLastTime}"
    )
    int updateSceneLastTimeCheckLastTime(
            Wechat wechat
    );

    /**
     * 更新公众号openid
     * 登录
     *
     * @param wechat 微信信息
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `wechat` " +
            "SET " +
            "   `wechat_official_openid`=#{wechatOfficialOpenid} " +
            "WHERE " +
            "   `id`=#{id} "
    )
    int updateOfficialOpenid(
            Wechat wechat
    );

    /**
     * 写入wechat表
     * 注册
     *
     * @param wechat 微信信息
     * @return 影响的行
     */
    @Insert("INSERT INTO `wechat`(" +
            "   `customer_id`, " +
            "   `unionid`, " +
            "   `wechat_official_openid`, " +
            "   `wechat_mini_openid`, " +
            "   `nick_name`," +
            "   `avatar_url`," +
            "   `gender`," +
            "   `country`," +
            "   `province`," +
            "   `city`," +
            "   `language`, " +
            "   `platform` " +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{unionid}, " +
            "   #{wechatOfficialOpenid}, " +
            "   #{wechatMiniOpenid}, " +
            "   #{nickName}, " +
            "   #{avatarUrl}, " +
            "   #{gender}, " +
            "   #{country}, " +
            "   #{province}, " +
            "   #{city}, " +
            "   #{language}, " +
            "   #{platform} " +
            ")"
    )
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertWechat(Wechat wechat);

    /**
     * 更新微信信息
     *
     * @param wechat 微信信息
     */
    @Update("update " +
            "   `wechat` " +
            "set " +
            "   `unionid`=#{unionid}," +
            "   `nick_name`=#{nickName}, " +
            "   `avatar_url`=#{avatarUrl}," +
            "   `gender`=#{gender}," +
            "   `country`=#{country}," +
            "   `province`=#{province}," +
            "   `city`=#{city}," +
            "   `language`=#{language} " +
            "   " +
            "where " +
            "   `wechat_mini_openid`=#{wechatMiniOpenid}")
    void updateWechat(Wechat wechat);

    /**
     * 写入wechatAuthorize表
     * 授权
     *
     * @param wechatAuthorize 微信信息
     * @return 影响的行
     */
    @Insert("INSERT INTO `wechat_authorize`(" +
            "   `unionid`, " +
            "   `wechat_mini_openid`, " +
            "   `nick_name`," +
            "   `avatar_url`," +
            "   `gender`," +
            "   `country`," +
            "   `province`," +
            "   `city`," +
            "   `language` " +
            ") " +
            "VALUES (" +
            "   #{unionid}, " +
            "   #{wechatMiniOpenid}, " +
            "   #{nickName}, " +
            "   #{avatarUrl}, " +
            "   #{gender}, " +
            "   #{country}, " +
            "   #{province}, " +
            "   #{city}, " +
            "   #{language} " +
            ")"
    )
    int insertWechatAuthorize(WechatAuthorize wechatAuthorize);

    @Select("select * from `wechat_authorize` where `wechat_mini_openid` = #{miniOpenid} limit 1")
    Wechat getWechatInfoFromAuthorize(String miniOpenid);
    /**
     * 跟据顾客编号查找昵称和头像
     * 个人中心
     * 编辑资料
     *
     * @param customerId 顾客编号
     * @return 昵称和头像
     */
    @Select("SELECT * " +
            "FROM " +
            "   `wechat` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1"
    )
    Wechat getWechatInfoByCustomerId(long customerId);

    /**
     * 跟据顾客编号查找公众号openid
     * 个人中心
     * 编辑资料
     *
     * @param customerId 顾客编号
     * @return 公众号openid
     */
    @Select("SELECT " +
            "   `wechat_official_openid` " +
            "FROM " +
            "   `wechat` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1"
    )
    String getOfficialOpenidByCustomerId(long customerId);

    /**
     * 写入公众号唯一编号
     *
     * @param unionid              开放平台编号
     * @param wechatOfficialOpenid 公众号唯一编号
     */
    @Update("UPDATE " +
            "   `wechat` " +
            "SET " +
            "   `wechat_official_openid`=#{wechatOfficialOpenid} " +
            "WHERE " +
            "   `unionid`=#{unionid}")
    void setOfficialOpenid(@Param("unionid") String unionid, @Param("wechatOfficialOpenid") String wechatOfficialOpenid);

    /**
     * 写入分享者信息
     *
     * @param wechatShareRegister 分享者信息
     */
    @Insert("INSERT INTO `wechat_share_register`(" +
            "   `sharer_wechat_mini_openid`, " +
            "   `visitor_wechat_mini_openid` " +
            ") " +
            "VALUES (" +
            "   #{sharerWechatMiniOpenid}, " +
            "   #{visitorWechatMiniOpenid} " +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertWechatShareRegister(WechatShareRegister wechatShareRegister);

    
    /**
     * 获得当天分享者信息统计
     *
     * @param miniOPenid
     */
    @Select("SELECT count(*) FROM `wechat_share_register` " +
            "  WHERE `sharer_wechat_mini_openid` = #{miniopenid}  " +
            "   AND to_days(create_time)=to_days(now()) " )
    int getTodayWechatShareRegisterCount( String miniopenid);
    
    /**
     * 写入小程序首页的来源
     *
     * @param originate 小程序首页的来源
     */
    @Insert("INSERT INTO `originate`(" +
            "   `wechat_mini_openid`, " +
            "   `source`" +
            ") " +
            "VALUES (" +
            "   #{wechatMiniOpenid}, " +
            "   #{source}" +
            ")")
    void insertOriginate(Originate originate);

    /**
     * 写入注册日志
     *
     * @param wechatRegisterStepLog 注册日志
     */
    @Insert("INSERT INTO `register_step_log`(" +
            "   `wechat_mini_openid`, " +
            "   `step`" +
            ") " +
            "VALUES (" +
            "   #{openid}," +
            "   #{step}" +
            ")"
    )
    void insertRegisterStepLog(WechatRegisterStepLog wechatRegisterStepLog);

    /**
     * 更新注册日志
     *
     * @param wechatRegisterStepLog 注册日志
     */
    @Update("UPDATE " +
            "   `register_step_log` " +
            "SET " +
            "   `step`=#{step}," +
            "   `update_time`=NOW() " +
            "WHERE " +
            "   `wechat_mini_openid`=#{openid} "
    )
    int updateRegisterStepLog(WechatRegisterStepLog wechatRegisterStepLog);

}
