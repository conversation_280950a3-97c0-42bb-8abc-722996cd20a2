package cn.slashsoft.clinique.controller.campaign;

import java.net.URLEncoder;
import java.util.Date;
import java.util.Enumeration;
import java.util.List;
import java.util.logging.Logger;
import java.util.Vector;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;

import cn.slashsoft.clinique.dao.campaign.CampaignH5Dao;
import cn.slashsoft.clinique.domain.campaign.CampaignH5;
import cn.slashsoft.clinique.domain.campaign.CampaignH5City;
import cn.slashsoft.clinique.domain.campaign.CampaignH5Detail;
import cn.slashsoft.clinique.domain.campaign.CampaignH5Log;
import cn.slashsoft.clinique.domain.campaign.CdpTrail;
import cn.slashsoft.clinique.service.campaign.CampaignH5Service;
import cn.slashsoft.clinique.service.mini.SnsService;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.util.CdpDataUtil;
import cn.slashsoft.clinique.util.CookieUtil;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.JsonUtil;
import cn.slashsoft.clinique.util.ServerUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.util.VerifyUtil;

/**
 * campaign H5
 *
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/trial-campaign")
public class CampaignH5Controller {
    @Value("${wechat.official.authorize}")
    private String urlAuthorize;

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Value("${wechat.resources.oss-domain}")
    private String ossDomain;

    private final Logger logger = Logger.getLogger( CampaignH5Controller.class.getName());
    @Resource
    private HttpServletRequest request;

    @Resource
    private HttpServletResponse response;

    @Resource
    private CampaignH5Service campaignH5Service;

    @Resource
    private CampaignH5Dao campaignH5Dao;
    
    @Resource
    private SnsService snsService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private OutsideService outsideService;

    		
    //启动页
    @GetMapping("/lauch/{campaignCode}/{city}/{source}.html")
    public String auth(
            @PathVariable("campaignCode") String campaignCode,
            @PathVariable("source") String source,
            @PathVariable("city") String city,
            @RequestParam(value = "gdt_vid", required = false) String clickId,
            Model model,
            @RequestHeader(value = "referer", required = false) String referer) {
    	if(StringUtil.isNullOrEmpty(campaignCode)) {
    		return "/404";
    	}
		CampaignH5 camp = this.checkCampaign(campaignCode);
		
		//默认进入 引导页。
		String startPage = "start" ;
		if(null == camp) {
			return "/404";
		}else if(StringUtil.isNullOrEmpty(camp.getLauchBg())) {
			//如果没有设置引导页背景图，直接进入申领页
			startPage = "apply" ;
		} 
		
		if(! this.noRefrerer(referer) ) {
			return "w/fans/official/trail-campaign/no";
		}	 
		
    	try {
	    	request.getSession().setAttribute(
	    			"officialOauthPath", this.getUrl(startPage , campaignCode, city, source, clickId));
	    	
	        String redirectUri = URLEncoder.encode(
	        		ServerUtil.getDomain(request) + "/fans/official/auth"
	        				 + ServerUtil.getQuery(request),
	        		"UTF-8");
	        response.sendRedirect(urlAuthorize + redirectUri);
    	}catch(Exception e) {
    		logger.info("申领活动：" + campaignCode + " Lauch Redirect 出错! " + e.getMessage()) ;
    	}
    	return "/404";
    }
  
    private boolean noRefrerer(String referer) {
		logger.info("------------------------------------ " + referer) ;
		if(!StringUtil.isNullOrEmpty((String) request.getSession().getAttribute( "referer")) 
				&& !"null".equals((String) request.getSession().getAttribute( "referer"))){
			return false;
		}
    	if("null".equals(referer)) {
    		return true;
    	}
		if(!StringUtil.isNullOrEmpty(referer)  ) {
			if(referer.startsWith("https://cliniquewechat.elcapp.cn")
					|| referer.startsWith("https://clinique.elcapp.cn")
					|| referer.startsWith("https://cliniquetest.elcapp.cn")
					|| referer.startsWith("https://mp.weixin.qq.com")
					|| referer.startsWith("https://open.weixin.qq.com")
					|| referer.startsWith("https://mp.weixinbridge.com")) {
				return true;
			}
			request.getSession().setMaxInactiveInterval(60);
			request.getSession().setAttribute( "referer", referer);	 
			return false;
		}
		return true;
	}

	//引导页
    @GetMapping("/start/{campaignCode}/{city}/{source}.html")
    public String index(
            @PathVariable("campaignCode") String campaignCode,
            @PathVariable("source") String source,
            @PathVariable("city") String city,
            @RequestParam(value = "gdt_vid", required = false) String clickId,
            Model model,
            @RequestHeader(value = "referer", required = false) String referer) {


		this.noRefrerer(referer);	
		
        String openid =  (String) request.getSession().getAttribute("officialOpenid");
        
        if(StringUtil.isNullOrEmpty(openid)) {
        	try {
        		response.sendRedirect(this.getUrl("lauch", campaignCode, city, source, clickId));
        	}catch(Exception e) {
        		logger.info("申领活动：" + campaignCode + " Page Redirect 出错! " + e.getMessage()) ;
        	}
	        return "";
        }

		CampaignH5 camp = this.checkCampaign(campaignCode);
		
		if(null == camp) {
			return "";
		}
        
        // 保存日志
        CampaignH5Log campaignH5Log = new CampaignH5Log();
        campaignH5Log.setType((short) 3);
        campaignH5Log.setUniqueId(openid);
        campaignH5Log.setSource(source);
        campaignH5Log.setCampaignId(camp.getId());
        campaignH5Log.setClickId(StringUtil.isNullOrEmpty(clickId)?"":clickId);
        campaignH5Log.setPage("start");
        campaignH5Service.insertLog(campaignH5Log);
        
        model.addAttribute("applyUrl", this.getUrl("apply", campaignCode, city, source, clickId));
        model.addAttribute("staticDomain", staticDomain);
        model.addAttribute("ossDomain", ossDomain);
        model.addAttribute("lauchBg", camp.getLauchBg());
        return "w/fans/official/trail-campaign/lauch";
	}
    

    //申领页
    @GetMapping("/apply/{campaignCode}/{city}/{source}.html")
    public String form(
            @PathVariable("campaignCode") String campaignCode,
            @PathVariable("source") String source,
            @PathVariable("city") String city,
            @RequestParam(value = "gdt_vid", required = false) String clickId,
            Model model,
            @RequestHeader(value = "referer", required = false) String referer
    ) {

    	
		CampaignH5 camp = this.checkCampaign(campaignCode);
		
		if(null == camp) {
			return "";
		}
        CampaignH5Detail campaignH5Detail = new CampaignH5Detail();

		this.noRefrerer(referer);	
		
        String openid =  (String) request.getSession().getAttribute("officialOpenid");

        if(StringUtil.isNullOrEmpty(openid)) {
        	try {	
        		response.sendRedirect(	this.getUrl("lauch", campaignCode, city, source, clickId));
        	}catch(Exception e) {
        		logger.info("申领活动：" + campaignCode + " Apply Redirect 出错! " + e.getMessage()) ;
        	}
	        return "";
        }
    	
    	 if(camp.getType() == 1) {
             model.addAttribute("isChooseProvince", true);
             model.addAttribute("isChooseStore", false);
         }else {
         	 model.addAttribute("isChooseProvince", false);
              model.addAttribute("isChooseStore", true);
         } 
    	 model.addAttribute("serviceName", "fans/trial-campaign");
         model.addAttribute("clickId", StringUtil.isNullOrEmpty(clickId)?"":clickId);
         model.addAttribute("source", source);
         model.addAttribute("campaignCode", campaignCode);

         model.addAttribute("staticDomain", staticDomain);
         model.addAttribute("ossDomain", ossDomain);
         model.addAttribute("cityList", JsonUtil.toJson(campaignH5Service.getCampaignCityList(camp.getId())));
         model.addAttribute("applyBg", camp.getApplyBg());

     	CampaignH5City ebciCity = campaignH5Service.getCampaignCity(city, camp.getId());
    	if(null == ebciCity || StringUtil.isNullOrEmpty(ebciCity.getCity())) {           
            model.addAttribute("notStart", false);
            model.addAttribute("notCity", true);   
            
            model.addAttribute("isEnd", false);
            model.addAttribute("isApplied", false);
            
            return "w/fans/official/trail-campaign/apply";
            
            
    	}else {
            campaignH5Detail.setStore(ebciCity.getCity());
            campaignH5Detail.setCity(ebciCity.getProvince());  
            //campaignH5Detail.setCampaignId(camp.getId());
    	}

        model.addAttribute("notCity", false);
        
        Date endTime = camp.getEndTime();
    	Date startTime = camp.getStartTime();
    	//TODO for test
    	//startTime = DateUtil.valueOf("2021-04-01 00:00:00");
    	if(null == startTime ) {
            model.addAttribute("notStart", false);    		
    	}else  if(DateUtil.laterThanNow(startTime)){
            model.addAttribute("notStart", true);
        } else{
            model.addAttribute("notStart", false);
        }
        
    	if(null == endTime ) {
            model.addAttribute("isEnd", false);    		
    	}else if(DateUtil.earlierThanNow(endTime)){
            model.addAttribute("isEnd", true);
        }else{
            model.addAttribute("isEnd", false);
        }   
        //String phoneNumber = CookieUtil.getCookie("phoneNumber", request);

        // 读取是否填写过
        //if (!StringUtil.isNullOrEmpty(phoneNumber)) {
        //    model.addAttribute("isApplied", true);
        //}else {
        //    model.addAttribute("isApplied", false);
       // }
        model.addAttribute("isApplied", false);
        //String sessionId = request.getSession().getId().replace("-", "");


        // 保存日志
        CampaignH5Log campaignH5Log = new CampaignH5Log();
        campaignH5Log.setType((short) 4);
        campaignH5Log.setUniqueId(openid);
        campaignH5Log.setSource(source);
        campaignH5Log.setCampaignId(camp.getId());
        campaignH5Log.setClickId(StringUtil.isNullOrEmpty(clickId)?"":clickId);
        campaignH5Log.setPage("apply");
        campaignH5Service.insertLog(campaignH5Log);

        model.addAttribute("campaignH5Detail", campaignH5Detail);
        model.addAttribute("rule", camp.getRule());       

        return "w/fans/official/trail-campaign/apply";
        
    }

    @GetMapping("/verify-code/{campaignCode}/{phoneNumber}")
    @ResponseBody
    public String getVerifyCode(
            @PathVariable("campaignCode") String campaignCode,
            @PathVariable("phoneNumber") String phoneNumber
    ) {
    	CampaignH5 camp = this.checkCampaign(campaignCode);
		
		if(null == camp) {
			return "{\"code\": 110}";
		}
        return "{\"code\":" + snsService.sendVerifyCode(phoneNumber) + "}";
    }
    
    @PostMapping("/refresh.html")
    @ResponseBody
    public String refresh() {
    	List<CampaignH5Detail> all = campaignH5Dao.getAllDetails();
    	for(CampaignH5Detail detail: all) {
    		outsideService.getCouponByPhone(detail, 88L) ;
    	}        
    	return  "{\"count\": "+all.size()+"}";
    }
    @PostMapping("/submit/{campaignCode}/{source}.html")
    @ResponseBody
    public String submit(
            @PathVariable("campaignCode") String campaignCode,
            @PathVariable("source") String source,
            @RequestParam(value = "gdt_vid", required = false) String clickId,
            @RequestParam("formName") String name,
            @RequestParam("formPhoneNumber") String phoneNumber,
            @RequestParam("formVerifyCode") String verifyCode,
            @RequestParam("formCity") String city,
            @RequestParam("formStore") String store
    ) {
    	CampaignH5 camp = this.checkCampaign(campaignCode);
		
		if(null == camp) {
			return "{\"code\":9,\"message\":\"活动未找到\"}";
		}

        Date startTime = camp.getStartTime();
        Date endTime = camp.getEndTime();

        if(null != startTime && DateUtil.laterThanNow(startTime)){
            return "{\"code\":9,\"message\":\"活动未开始\"}";
        }
    
        if(null != endTime && DateUtil.earlierThanNow(endTime)){
            return "{\"code\":8,\"message\":\"活动已结束\"}";
        }

        if (!VerifyUtil.required(name)) {
            return "{\"code\":9,\"message\":\"请输入姓名\"}";
        }

        if (!VerifyUtil.required(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.isPhoneNumber(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.required(verifyCode)) {
            return "{\"code\":9,\"message\":\"请输入验证码\"}";
        }

        if (!VerifyUtil.isVerifyCode(verifyCode)) {
            return "{\"code\":9,\"message\":\"验证码格式错误\"}";
        }

        if (!VerifyUtil.required(city)) {
            return "{\"code\":9,\"message\":\"请选择城市\"}";
        }

        if (!VerifyUtil.required(store)) {
            return "{\"code\":9,\"message\":\"请选择柜台\"}";
        }

        // 验证手机是否一致
        String sessionPhoneNumber = (String) request.getSession().getAttribute("verifyPhoneNumber");
        if (!phoneNumber.equals(sessionPhoneNumber)) {
            return "{\"code\":9,\"message\":\"请重新获取验证码\"}";
        }

        // 验证是否正确
        String sessionVerifyCode = (String) request.getSession().getAttribute("verifyCode");
        if (!verifyCode.equals(sessionVerifyCode)) {
            return "{\"code\":9,\"message\":\"验证码错误\"}";
        }

        // 验证码是否过期
        Date sessionVerifyTime = DateUtil.valueOf((String) request.getSession().getAttribute("verifyTime"));
        if (DateUtil.earlierThanNow(sessionVerifyTime)) {
            return "{\"code\":9,\"message\":\"验证码已过期\"}";
        }

        //String sessionId = request.getSession().getId().replace("-", "");
        String openid =  (String) request.getSession().getAttribute("officialOpenid");


        // 申领
        CampaignH5Detail campaignH5Detail = new CampaignH5Detail();
        campaignH5Detail.setType((short) 3);
        campaignH5Detail.setUniqueId(openid);
        campaignH5Detail.setName(name);
        campaignH5Detail.setCampaignId(camp.getId()) ;
        campaignH5Detail.setPhoneNumber(phoneNumber);
        campaignH5Detail.setCity(city);
        campaignH5Detail.setStore(store);
        campaignH5Detail.setSource(source);

        //CampaignH5Stock stock = campaignH5Service.getByStoreName(campaignH5Detail);
       // if(stock != null) {
        //	campaignH5Detail.setStoreId(stock.getStoreId());
        //}else {
        	campaignH5Detail.setStoreId("0");
        //}

        Integer ret;
     
        ret = campaignH5Service.submit(campaignH5Detail);
        
        // 0:成功，1：手机号码已经领过了，2：库存不足
        switch (ret) {
            case 0:
                //if (!source.startsWith("tmall-club-")) {
                	//if(store.equals("武汉市")) {
                	//	store += "-群光";
                	//}
                	//snsService.ebcfExternal(phoneNumber, store);
                //}
                if(null != camp.getSnsId() && camp.getSnsId().compareTo(0L) > 0) {
                	snsService.sendMessage(phoneNumber, campaignH5Detail, camp.getSnsId()) ;
                }
                
                if(null != camp.getCouponCampaignId() && camp.getCouponCampaignId().compareTo(0L) > 0) {
                	outsideService.getCouponByPhone(campaignH5Detail,  camp.getCouponCampaignId()) ;
                }
                
                CookieUtil.addCookie("phoneNumber", phoneNumber, response);

                /*
                if(!StringUtil.isNullOrEmpty(clickId)){
                    outsideService.qqMarketing(
                            ServerUtil.getDomain(request) +"/fans/trial-campaign/" + campaignCode + "/" + source + ".html",
                    		clickId,
                    		name,
                    		phoneNumber,
                    		city,
                    		store
                    );
                }
                */
                return "{\"code\":0}";
            case 1:
                return "{\"code\":1}";
            case 2:
                return "{\"code\":2}";
            default:
                return "{\"code\":3}";
        }
    }

    private String getUrl(String function, String campaignCode, String city, String source, String clickId) {
    	return ServerUtil.getDomain(request) 
    			+ "/fans/trial-campaign/"
    			+ function+"/"
				+ campaignCode + "/" 
    			+ city + "/" 
				+ source + ".html" 
    			+ (StringUtil.isNullOrEmpty(clickId) ? "" : "?gdt_vid="+ clickId) ;
    }
    
    private CampaignH5 checkCampaign(String campaignCode) {
    	return campaignH5Service.getCampaign(campaignCode);
    }
}
