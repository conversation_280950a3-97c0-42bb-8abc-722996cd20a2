package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignVideoSign;
import cn.slashsoft.clinique.domain.campaign.CampaignVideoSignJumpLog;
import cn.slashsoft.clinique.domain.campaign.CampaignVideoSignPlayLog;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.campaign.CampaignVideoSignService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.campaign.CampaignVideoSignVo;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 视频签到活动相关控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class CampaignVideoSignController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final CampaignVideoSignService campaignVideoSignService;
    private final StringRedisTemplate stringRedisTemplate;

    public CampaignVideoSignController(CampaignVideoSignService campaignVideoSignService, StringRedisTemplate stringRedisTemplate) {
        this.campaignVideoSignService = campaignVideoSignService;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-video-sign/get-data")
    public String getData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        CampaignVideoSignVo campaignVideoSignVo = new CampaignVideoSignVo();
        // 获取进度数据
        campaignVideoSignVo.setCampaignVideoSign(campaignVideoSignService.getCampaignVideoSign(customerId));
        // 获取视频内容
        campaignVideoSignVo.setCampaignVideoSignConfigList(campaignVideoSignService.getCampaignVideoSignConfig());
        // 结束时间
        campaignVideoSignVo.setEndTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignVideoSignEndTime")));

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignVideoSignVo);

    }

    /**
     * 播放
     *
     * @param configId 视频编号
     * @return 成功
     */
    @PostMapping("/campaign-video-sign/play/{configId}")
    public String play(
            @PathVariable("configId") short configId
    ) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 写入播放日志
        CampaignVideoSignPlayLog campaignVideoSignPlayLog = new CampaignVideoSignPlayLog();
        campaignVideoSignPlayLog.setCustomerId(customerId);
        campaignVideoSignPlayLog.setConfigId(configId);
        campaignVideoSignService.insertCampaignVideoSignPlayLog(campaignVideoSignPlayLog);

        if (DateUtil.earlierThanNow(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignVideoSignEndTime")))) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        CampaignVideoSign campaignVideoSign = campaignVideoSignService.getCampaignVideoSign(customerId);
        // 已累计签到满7次 或者 今日已签到
        if (7 <= campaignVideoSign.getLevel() || DateUtil.isToday(campaignVideoSign.getLevelTime())) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        short level = (short) (campaignVideoSign.getLevel() + 1);

        if (campaignVideoSignService.sign(customerId, level)) {
            return ResultUtil.customer(ResultEnum.SUCCESS, level);
        }

        return ResultUtil.customer(ResultEnum.FAILED);

    }

    /**
     * 写入访问开始日志
     *
     * @param source 来源
     * @return 成功
     */
    @PostMapping("/campaign-video-sign/start-view-log/{source}")
    public String setStartViewLog(
            @PathVariable("source") String source
    ) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        long id = campaignVideoSignService.insertCampaignVideoSignViewLog(customerId, source);

        return ResultUtil.customer(ResultEnum.SUCCESS, id);

    }

    /**
     * 写入访问结束日志
     *
     * @param id 来源
     * @return 成功
     */
    @PostMapping("/campaign-video-sign/end-view-log/{id}")
    public String setEndViewLog(
            @PathVariable("id") long id
    ) {

        campaignVideoSignService.setCampaignVideoSignViewLog(id);
        return ResultUtil.customer(ResultEnum.SUCCESS);

    }

    /**
     * 写入跳转日志
     *
     * @param configId 视频编号
     * @return 成功
     */
    @PostMapping("/campaign-video-sign/set-jump-log/{configId}")
    public String setJumpLog(
            @PathVariable("configId") short configId
    ) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        CampaignVideoSignJumpLog campaignVideoSignJumpLog = new CampaignVideoSignJumpLog();
        campaignVideoSignJumpLog.setCustomerId(customerId);
        campaignVideoSignJumpLog.setConfigId(configId);
        campaignVideoSignService.insertCampaignVideoSignJumpLog(campaignVideoSignJumpLog);

        return ResultUtil.customer(ResultEnum.SUCCESS);

    }

}
