package cn.slashsoft.clinique.domain.campaign;

import lombok.Data;

import java.util.Date;

/**
 * 企业微信绑定活动-活动信息
 *
 * <AUTHOR>
 */
@Data
public class CampaignWechatWorkBinding {

    private Long id;
    private String officialOpenid;
    private Short memberType;
    private String nickName;
    private String avatarUrl;
    private Boolean isDraw;
    private Date drawTime;
    private Short giftId;
    private Short giftType;
    private String giftName;
    private Boolean isForm;
    private Date formTime;
    private String pickupCounter;
    private Boolean pickupIsReceive;
    private Date pickupReceiveTime;
    private Boolean expressIsReceive;

}
