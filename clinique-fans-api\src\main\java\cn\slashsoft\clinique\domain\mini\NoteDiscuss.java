package cn.slashsoft.clinique.domain.mini;

import lombok.Data;

import java.util.Date;

@Data
public class NoteDiscuss  {
	private Long id;

	private String content;

	private Date createTime;
	private Date updateTime;

	private String ownerName;
	private String avatarUrl;
	private Long customerId;

	private Long noteId;

	private int status;

    /* 给用户加一个小绿色V */
    private Integer kocV;

	public String toJson() {
		return "";
	}
}