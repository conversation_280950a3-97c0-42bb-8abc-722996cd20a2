package com.stormcrm.clinique.service.tag.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.stormcrm.clinique.domain.tag.Label;
import com.stormcrm.clinique.domain.tag.LabelPackageRule;
import com.stormcrm.clinique.domain.tag.Rule;
import com.stormcrm.clinique.service.tag.RuleService;
import com.stormcrm.clinique.tag.dao.RuleCategoryDao;
/**
 * 
 *
 * <AUTHOR>
 */

@Service
public class RuleServiceImpl implements RuleService {

	@Resource
	RuleCategoryDao ruleCategoryDao;
	
	@Override
	public List<LabelPackageRule> getFilterListByLabel(Label label) {
		return ruleCategoryDao.getRulesbyLabel(label);
	}
	@Override
	public List<Rule> getRuleAll() {
		return ruleCategoryDao.getRules();
	}	
}
