package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.domain.mini.Follow;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciLog;
import cn.slashsoft.clinique.service.mini.SnsService;
import cn.slashsoft.clinique.util.CookieUtil;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.util.VerifyUtil;
import cn.slashsoft.clinique.service.mini.CustomerService;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.service.campaign.CampaignEbciService;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * 黄油变粉安瓶级保湿补光申领
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/official")
public class CampaignEbciMiniController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    @Resource
    private HttpServletResponse response;

    private final CustomerService customerService;
    private final CampaignEbciService campaignEbciService;
    private final OutsideService outsideService;
    private final SnsService snsService;
    private final StringRedisTemplate stringRedisTemplate;

    public CampaignEbciMiniController(CampaignEbciService campaignEbciService, SnsService snsService, OutsideService outsideService, StringRedisTemplate stringRedisTemplate, CustomerService customerService) {
        this.campaignEbciService = campaignEbciService;
        this.snsService = snsService;
        this.outsideService = outsideService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.customerService = customerService;
    }

    @GetMapping("/campaign-ebci/{unionid}/{source}.html")
    public String index(
            @PathVariable("source") String source,
            @PathVariable("unionid") String unionid,
            @RequestParam(value = "gdt_vid", required = false) String gdtvid,
            Model model
    ) {

        Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciStartTime"));
        Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciEndTime"));

        if (DateUtil.laterThanNow(startTime)) {
            model.addAttribute("notStart", true);
        } else {
            model.addAttribute("notStart", false);
        }

        if (DateUtil.earlierThanNow(endTime)) {
            model.addAttribute("isEnd", true);
        } else {
            model.addAttribute("isEnd", false);
        }

        if (StringUtil.isNullOrEmpty(unionid) || "none".equals(unionid)) {
            return "w/fans/official/ebci/wechat/none";
        }

        String openid = (String) request.getAttribute("miniOpenid");

        // 读取是否填写过
        if (0 < campaignEbciService.hasDetailByOpenid(openid)) {
            return "redirect:/official/campaign-ebci-result/" + unionid + "/" + source + ".html";
        }

        String phoneNumber = campaignEbciService.getPhoneNumberByUnionid(unionid);

        // 保存日志
        CampaignEbciLog campaignEbciLog = new CampaignEbciLog();
        campaignEbciLog.setType((short) 1);
        campaignEbciLog.setUniqueId(openid);
        campaignEbciLog.setSource(source);
        campaignEbciLog.setPage("index");
        campaignEbciService.insertLog(campaignEbciLog);

        model.addAttribute("unionid", unionid);
        model.addAttribute("phoneNumber", phoneNumber);
        model.addAttribute("serviceName", "official");
        model.addAttribute("source", source);
        model.addAttribute("gdtvid", gdtvid);
       
            model.addAttribute("staticDomain", staticDomain);
        return "w/fans/official/ebci/mini/index";
    }

    @GetMapping("/campaign-ebci-verify-code/{phoneNumber}")
    @ResponseBody
    public String getVerifyCode(
            @PathVariable("phoneNumber") String phoneNumber
    ) {
        return "{\"code\":" + snsService.sendVerifyCode(phoneNumber) + "}";
    }

    @PostMapping("/campaign-ebci-submit/{unionid}/{source}.html")
    @ResponseBody
    public String submit(
            @PathVariable("source") String source,
            @PathVariable("unionid") String unionid,
            @RequestParam("formName") String name,
            @RequestParam("formPhoneNumber") String phoneNumber,
            @RequestParam("formVerifyCode") String verifyCode,
            @RequestParam("formCity") String city,
            @RequestParam("formStore") String store,
            @RequestParam(value = "gdt_vid", required = false) String gdtvid
    ) {

        Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciStartTime"));
        Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciEndTime"));

        if (DateUtil.laterThanNow(startTime)) {
            return "{\"code\":9,\"message\":\"活动未开始\"}";
        }

        if (DateUtil.earlierThanNow(endTime)) {
            return "{\"code\":9,\"message\":\"活动已结束\"}";
        }

        if (!VerifyUtil.required(name)) {
            return "{\"code\":9,\"message\":\"请输入姓名\"}";
        }

        if (!VerifyUtil.required(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.isPhoneNumber(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.required(verifyCode)) {
            return "{\"code\":9,\"message\":\"请输入验证码\"}";
        }

        if (!VerifyUtil.isVerifyCode(verifyCode)) {
            return "{\"code\":9,\"message\":\"验证码格式错误\"}";
        }

        // 验证手机是否一致
        String sessionPhoneNumber = (String) request.getSession().getAttribute("verifyPhoneNumber");
        if (!phoneNumber.equals(sessionPhoneNumber)) {
            return "{\"code\":9,\"message\":\"请重新获取验证码\"}";
        }

        // 验证是否正确
        String sessionVerifyCode = (String) request.getSession().getAttribute("verifyCode");
        if (!verifyCode.equals(sessionVerifyCode)) {
            return "{\"code\":9,\"message\":\"验证码错误\"}";
        }

        // 验证码是否过期
        Date sessionVerifyTime = DateUtil.valueOf((String) request.getSession().getAttribute("verifyTime"));
        if (DateUtil.earlierThanNow(sessionVerifyTime)) {
            return "{\"code\":9,\"message\":\"验证码已过期\"}";
        }

        if (!VerifyUtil.required(city)) {
            return "{\"code\":9,\"message\":\"请选择城市\"}";
        }

        if (!VerifyUtil.required(store)) {
            return "{\"code\":9,\"message\":\"请选择柜台\"}";
        }

        String customerPhoneNumber = campaignEbciService.getPhoneNumberByUnionid(unionid);
        if (StringUtil.isNullOrEmpty(customerPhoneNumber)) {
            customerService.setPhoneNumberByUnionid(unionid, phoneNumber);
        }

        String openid = (String) request.getAttribute("miniOpenid");

        // 申领
        CampaignEbciDetail campaignEbciDetail = new CampaignEbciDetail();
        campaignEbciDetail.setType((short) 1);
        campaignEbciDetail.setUniqueId(openid);
        campaignEbciDetail.setName(name);
        campaignEbciDetail.setPhoneNumber(phoneNumber);
        campaignEbciDetail.setCity(city);
        campaignEbciDetail.setStore(store);
        campaignEbciDetail.setSource(source);

        // 0:成功，1：手机号码已经领过了，2：库存不足
        switch (campaignEbciService.submit(campaignEbciDetail)) {
            case 0:
                snsService.ebci(phoneNumber, store);
                CookieUtil.addCookie("phoneNumber", phoneNumber, response);

                if(!StringUtil.isNullOrEmpty(gdtvid)){
                    outsideService.qqMarketing("https://clinique.stormcrm.com/official/campaign-ebci/" + source + ".html", gdtvid, name, phoneNumber, city, store);
                }
                return "{\"code\":0}";
            case 1:
                return "{\"code\":1}";
            case 2:
                return "{\"code\":2}";
            default:
                return "{\"code\":3}";
        }

    }

    @GetMapping("/campaign-ebci-result/{unionid}/{source}.html")
    public String result(
            @PathVariable("source") String source,
            @PathVariable("unionid") String unionid,
            Model model
    ) {

        String openid = (String) request.getAttribute("miniOpenid");

        // 读取申领信息
        CampaignEbciDetail campaignEbciDetail = campaignEbciService.getDetailByOpenid(openid);
        if (null == campaignEbciDetail) {
            return "redirect:/official/campaign-ebci/" + unionid + "/" + source + ".html";
        }

        // 保存日志
        CampaignEbciLog campaignEbciLog = new CampaignEbciLog();
        campaignEbciLog.setType((short) 1);
        campaignEbciLog.setUniqueId(openid);
        campaignEbciLog.setSource(source);
        campaignEbciLog.setPage("result");
        campaignEbciService.insertLog(campaignEbciLog);

        // 获取是否关注
        Follow follow = outsideService.isFollow(openid);

        // 未获取到是否关注，即为AccessToken过期
        if (null != follow) {
            campaignEbciDetail.setFollow(follow.getFollow());
            campaignEbciDetail.setFollowSource(follow.getFollowSource());
            campaignEbciDetail.setFollowFirstTime(follow.getFollowFirstTime());
            campaignEbciDetail.setFollowLastTime(follow.getFollowLastTime());
            campaignEbciDetail.setFollowCancelTime(follow.getFollowCancelTime());
            campaignEbciDetail.setBind(follow.getBind());
            campaignEbciDetail.setBindTime(follow.getBindTime());
            // 保存
            campaignEbciService.setFollow(campaignEbciDetail);
        }
            model.addAttribute("staticDomain", staticDomain);
        if (null != campaignEbciDetail.getFollow() && campaignEbciDetail.getFollow()) {
            model.addAttribute("campaignEbciDetail", campaignEbciDetail);
            return "w/fans/official/ebci/mini/success";
        } else {
            return "w/fans/official/ebci/mini/fail";
        }

    }

}
