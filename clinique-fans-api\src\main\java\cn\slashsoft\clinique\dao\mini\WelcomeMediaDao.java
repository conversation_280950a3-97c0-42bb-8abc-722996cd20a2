package cn.slashsoft.clinique.dao.mini;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import cn.slashsoft.clinique.domain.mini.WelcomeMedia;

/**
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface WelcomeMediaDao {

  
    /**
     * 跟据开放平台的唯一编号查找
     * 登录
     *
     * @param unionid 开放平台的唯一编号
     * @return 微信信息
     */
    @Select("SELECT * " +
            "FROM " +
            "   `welcome_media` " +
            "WHERE " +
            "   `event_key`=#{eventKey} "
            + "	ORDER BY `update_time` desc " +
            " LIMIT 1"
    )
    WelcomeMedia getWelcomeMediaByKey(String eventKey);

}
