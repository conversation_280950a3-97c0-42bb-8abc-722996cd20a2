package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignMsDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignMsLog;
import cn.slashsoft.clinique.domain.campaign.CampaignMsStock;

/**
 * 水磁场申领
 *
 * <AUTHOR>
 */
public interface CampaignMsService {

    /**
     * 保存日志
     *
     * @param campaignMsLog 日志
     */
    void insertLog(CampaignMsLog campaignMsLog);

    /**
     * 获取手机号码
     *
     * @param unionid 开放平台唯一编号
     * @return 手机号码
     */
    String getPhoneNumberByUnionid(String unionid);

    /**
     * 获取手机号码
     *
     * @param openid 公众号唯一编号
     * @return 手机号码
     */
    String getPhoneNumberByOpenid(String openid);

    /**
     * 获取是否已经审领
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    int hasDetail(String phoneNumber);

    /**
     * 获取是否已经审领
     *
     * @param openid 公众号唯一编号
     * @return 数量
     */
    int hasDetailByOpenid(String openid);

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    CampaignMsDetail getDetail(String phoneNumber);

    /**
     * 申领
     *
     * @param campaignMsDetail 资料
     * @return 0:成功，1，帐户已经领过了，2：手机号码已经领过了，3：库存不足
     */
    int submit(CampaignMsDetail campaignMsDetail);

    /**
     * 更新是否关注
     *
     * @param campaignMsDetail 申领信息
     */
    void setFollow(CampaignMsDetail campaignMsDetail);

    /**
     * 获取审领信息
     * @param openid 公众号唯一编号
     * @return 审领信息
     */
    CampaignMsDetail getDetailByOpenid(String openid);

    /**
     * 跟据场景值获取门店
     *
     * @param scene 场景值
     * @return 门店
     */
    CampaignMsStock getByScene(String scene);

    /**
     * 更新核销信息
     *
     * @param campaignMsDetail 核销信息
     */
    void setReceive(CampaignMsDetail campaignMsDetail);
}
