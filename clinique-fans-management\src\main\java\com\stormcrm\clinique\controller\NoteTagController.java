package com.stormcrm.clinique.controller;

import com.stormcrm.clinique.domain.*;
import com.stormcrm.clinique.service.OutsideService;
import com.stormcrm.clinique.service.mini.impl.PointTypeServiceImpl;
import com.stormcrm.clinique.util.DateUtil;
import com.stormcrm.clinique.util.VerifyUtil;
import com.stormcrm.clinique.vo.Result;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;

import org.springframework.ui.Model;
import com.stormcrm.clinique.util.ResultUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.springframework.web.bind.annotation.*;

import com.stormcrm.clinique.service.NoteTagService;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

import static com.alibaba.fastjson.JSON.toJSONString;
import static com.alibaba.fastjson.JSON.toJSONStringWithDateFormat;

/**
 * 笔记
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("note/tag")
public class NoteTagController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    private final NoteTagService noteTagService;
    private final OutsideService outsideService;
    private final PointTypeServiceImpl pointTypeService;

    public NoteTagController(
            NoteTagService noteTagService,
            OutsideService outsideService,
            PointTypeServiceImpl pointTypeService
    ) {
        this.noteTagService = noteTagService;
        this.outsideService = outsideService;
        this.pointTypeService = pointTypeService;
    }

    /**
     * 查询所有-分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索关键字
     * @param top           状态
     * @return 列表
     */
    @PreAuthorize("hasAuthority('NOTE_TAG')")
    @PostMapping("get-page")
    @ResponseBody
    public String getPage(
            @RequestParam(value = "pagination[page]", required = false) Integer page,
            @RequestParam(value = "pagination[perpage]", required = false) Integer perpage,
            @RequestParam(value = "query[generalSearch]", required = false) String generalSearch,
            @RequestParam(value = "query[top]", required = false) Short top
    ) {

        if (null == page) {
            page = 1;
        }

        if (null == perpage) {
            perpage = 10;
        }

        List<NoteTag> noteList = noteTagService.getPage(page, 500, generalSearch, top);
        int count = noteTagService.getPageCount(generalSearch, top);

        JSONObject mata = new JSONObject();
        mata.put("page", page);
        mata.put("pages", (count + perpage - 1) / perpage);
        mata.put("perpage", perpage);
        mata.put("total", count);
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("mata", mata);
        result.put("data", noteList);

        return toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }


    /**
     * 笔记标签列表
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('NOTE_TAG')")
    @RequestMapping("")
    public String index(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/note-tag/index";
    }

    /**
     * 增加模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('NOTE_TAG_ADD')")
    @RequestMapping("add")
    public String add(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/note-tag/add";
    }

    /**
     * 新增
     *
     * @param title     名称
     * @param imageFile 图片
     * @param type      页面
     * @return 结果
     */
    @PreAuthorize("hasAuthority('NOTE_TAG_ADD')")
    @PostMapping(value = "add-submit", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String addSubmit(
            @RequestParam(value = "form-title", required = false) String title,
            @RequestParam(value = "form-image-file", required = false) MultipartFile imageFile,
            @RequestParam(value = "form-type", required = false) Integer type,
            @RequestParam(value = "form-top", required = false) Integer top,
            @RequestParam(value = "form-top-index", required = false) Integer topIndex,
            @RequestParam(value = "form-top-search", required = false) Integer topSearch,
            @RequestParam(value = "form-reward-name", required = false) String rewardName,
            @RequestParam(value = "form-reward-point", required = false) Integer rewardPoint,
            @RequestParam(value = "form-reward-start", required = false) String rewardStart,
            @RequestParam(value = "form-reward-end", required = false) String rewardEnd,
            @RequestParam(value = "form-reward-max", required = false) Integer rewardMax,
            @RequestParam(value = "form-reward-max-forever", required = false) Integer rewardMaxForever
    ) {

        // 验证
        if (!VerifyUtil.required(title)) {
            return ResultUtil.verifyFailToJson("form-title", "这是必填字段");
        }

        // 验证 如果是产品
        if ((isProductType(type)) && (null == imageFile || imageFile.isEmpty())) {
            return ResultUtil.verifyFailToJson("form-image-file", "这是产品必填字段");
        }

        // 生成对象
        NoteTag noteTag = new NoteTag();
        noteTag.setTitle(title);

        if (isProductType(type)) {
            // 保存图片
            noteTag.setImage(outsideService.uploadImageOss(imageFile, "noteTag"));
        }
        noteTag.setType(type);
        noteTag.setTop(top);
        noteTag.setTopIndex(topIndex);
        noteTag.setTopSearch(topSearch);

        // 特殊标签奖励
        if (rewardPoint != null && rewardPoint > 0) {
            if (!VerifyUtil.required(rewardName)) {
                return ResultUtil.verifyFailToJson("form-reward-name", "若有奖励，请填写奖励名称");
            }
            // 使用保留id之外的
            Short pointTypeId = pointTypeService.getMaxId();

            PointType pointType = new PointType();

            pointType.setId(pointTypeId);
            pointType.setName(rewardName);
            pointType.setForNoteTag(false);

            pointType.setRewardPoint(rewardPoint);

            // rewardStart rewardEnd 如果为空，数据库中插入 null
            // 如果是空字符串，会返回null
            Date startTimeDate = DateUtil.valueOf(rewardStart);
            pointType.setRewardActiveStart(startTimeDate);

            Date endTimeDate = DateUtil.valueOf(rewardEnd);
            pointType.setRewardActiveEnd(endTimeDate);

            if (rewardMax != null) {
                pointType.setRewardMax(rewardMax);
            } else {
                pointType.setRewardMax(0);
            }

            if (rewardMaxForever != null) {
                pointType.setRewardMaxForever(rewardMaxForever);
            } else {
                pointType.setRewardMaxForever(0);
            }

            // 传到Service服务中保存
            pointTypeService.insertById(pointType);
            noteTag.setPointTypeId(pointTypeId);
        }


        // 传到Service服务中保存
        noteTagService.insertTag(noteTag);

        Result result = ResultUtil.customer(1, "成功", noteTag);
        // 返回绍果给前端
        return toJSONString(result);
    }

    /**
     * 逻辑删除
     *
     * @param id 胸章的自动编号
     * @return 影响的行数
     */
    @PreAuthorize("hasAuthority('NOTE_TAG_DEL')")
    @GetMapping("del/{id}")
    @ResponseBody
    public String del(@PathVariable("id") long id) {

        if (0 == noteTagService.deleteTag(id)) {
            return ResultUtil.failToJson();
        }

        return ResultUtil.successToJson();

    }

    /**
     * 编辑模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('NOTE_TAG_EDIT')")
    @GetMapping("edit/{id}")
    public String edit(@PathVariable long id, Model model) {
        model.addAttribute("staticDomain", staticDomain);
        // 读数据库
        NoteTag tag = noteTagService.getTag(id);
        if (null == tag) {
            return "m/fans/common/empty";
        }
        model.addAttribute("id", id);
        model.addAttribute("noteTag", tag);

        return "m/fans/note-tag/edit";
    }

    /**
     * 编辑
     *
     * @param id        编号
     * @param title     标题
     * @param imageFile 图片
     * @param type      页面
     * @return 结果
     */
    @PreAuthorize("hasAuthority('NOTE_TAG_EDIT')")
    @PostMapping(value = "edit-submit/{id}", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String editSubmit(
            @PathVariable("id") long id,
            @RequestParam(value = "form-title", required = false) String title,
            @RequestParam(value = "form-image-file", required = false) MultipartFile imageFile,
            @RequestParam(value = "form-type", required = false) Integer type,
            @RequestParam(value = "form-top", required = false) Integer top,
            @RequestParam(value = "form-top-index", required = false) Integer topIndex,
            @RequestParam(value = "form-top-search", required = false) Integer topSearch,
//            @RequestParam(value = "form-reward-id", required = false) Short rewardId,
            @RequestParam(value = "form-reward-name", required = false) String rewardName,
            @RequestParam(value = "form-reward-point", required = false) Integer rewardPoint,
            @RequestParam(value = "form-reward-start", required = false) String rewardStart,
            @RequestParam(value = "form-reward-end", required = false) String rewardEnd,
            @RequestParam(value = "form-reward-max", required = false) Integer rewardMax,
            @RequestParam(value = "form-reward-max-forever", required = false) Integer rewardMaxForever
    ) {

        // 验证
        if (!VerifyUtil.required(title)) {
            return ResultUtil.verifyFailToJson("form-title", "这是必填字段");
        }

        // 验证  编辑的时候，如果是产品，之前是有图片的，所以如果图片为空，表明不需要修改图片，使用旧的图片
        boolean withImage = false;

        // 生成对象 这个应该先查数据库
        NoteTag noteTag = noteTagService.getTag(id);
        noteTag.setTitle(title);

        // 如果有新图片，保存图片  产品 需要图片
        if (isProductType(type) && gotImage(imageFile)) {
            noteTag.setImage(outsideService.uploadImageOss(imageFile, "noteTag"));
            withImage = true;
        }

        noteTag.setType(type);
        noteTag.setTop(top);
        noteTag.setTopIndex(topIndex);
        noteTag.setTopSearch(topSearch);

        // 如果关联奖励标签为空 ，但是设置了 奖励值，则新建
        if (null == noteTag.getPointTypeId() || noteTag.getPointTypeId() == 0) {
            if (rewardPoint != null && rewardPoint > 0) {
                if (!VerifyUtil.required(rewardName)) {
                    return ResultUtil.verifyFailToJson("form-reward-name", "若有奖励，请填写奖励名称");
                }
                // 使用保留id之外的
                Short pointTypeId = pointTypeService.getMaxId();

                PointType pointType = new PointType();

                pointType.setId(pointTypeId);
                pointType.setName(rewardName);
                pointType.setForNoteTag(false);

                pointType.setRewardPoint(rewardPoint);

                // rewardStart rewardEnd 如果为空，数据库中插入 null
                // 如果是空字符串，会返回null
                Date startTimeDate = DateUtil.valueOf(rewardStart);
                pointType.setRewardActiveStart(startTimeDate);

                Date endTimeDate = DateUtil.valueOf(rewardEnd);
                pointType.setRewardActiveEnd(endTimeDate);

                if (rewardMax != null) {
                    pointType.setRewardMax(rewardMax);
                } else {
                    pointType.setRewardMax(0);
                }

                if (rewardMaxForever != null) {
                    pointType.setRewardMaxForever(rewardMaxForever);
                } else {
                    pointType.setRewardMaxForever(0);
                }

                // 传到Service服务中保存
                pointTypeService.insertById(pointType);

                //
                noteTag.setPointTypeId(pointTypeId);
            }
        } else {
            if (!VerifyUtil.required(rewardName)) {
                return ResultUtil.verifyFailToJson("form-reward-name", "若有奖励，请填写奖励名称");
            }
            // 如果有关联奖励标签， 用新内容对关联的标签做修订
            PointType oldPointType = pointTypeService.getById(noteTag.getPointTypeId());
            // 可以为空也可以为0
            oldPointType.setRewardPoint(rewardPoint == null ? 0 : rewardPoint);
            oldPointType.setName(rewardName);
            Date startTimeDate = DateUtil.valueOf(rewardStart);
            oldPointType.setRewardActiveStart(startTimeDate);

            Date endTimeDate = DateUtil.valueOf(rewardEnd);
            oldPointType.setRewardActiveEnd(endTimeDate);

            if (rewardMax != null) {
                oldPointType.setRewardMax(rewardMax);
            } else {
                oldPointType.setRewardMax(0);
            }

            if (rewardMaxForever != null) {
                oldPointType.setRewardMaxForever(rewardMaxForever);
            } else {
                oldPointType.setRewardMaxForever(0);
            }

            pointTypeService.update(oldPointType);
        }

        // 传到Service服务中保存
        noteTagService.updateTag(noteTag, withImage);

        Result result = ResultUtil.customer(1, "成功", noteTag);
        // 返回绍果给前端
        return toJSONString(result);
    }

    private boolean isProductType(@RequestParam(value = "form-type", required = false) Integer type) {
        return type == 1;
    }

    private boolean gotImage(@RequestParam(value = "form-image-file", required = false) MultipartFile imageFile) {
        return null != imageFile && !imageFile.isEmpty();
    }
}
