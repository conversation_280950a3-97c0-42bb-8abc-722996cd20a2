package cn.slashsoft.clinique.controller.mini;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.util.ResultUtil;

/**
 * 版本
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class VersionController {

	/**
     *  Berg 接口版本号
     */
    @GetMapping("/version")
    @ResponseBody
    public String version() {      
    	return ResultUtil.customer(ResultEnum.SUCCESS, "成功","4.4.2");
    }

}
