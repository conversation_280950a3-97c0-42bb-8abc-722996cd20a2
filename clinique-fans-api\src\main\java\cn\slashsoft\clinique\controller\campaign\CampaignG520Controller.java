package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.domain.mini.Counter;
import cn.slashsoft.clinique.domain.mini.CounterLog;
import cn.slashsoft.clinique.domain.campaign.CampaignG520;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.CounterService;
import cn.slashsoft.clinique.service.campaign.CampaignG520Service;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.mini.Location;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * 520活动
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class CampaignG520Controller {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final CampaignG520Service campaignG520Service;
    private final CounterService counterService;
    private final StringRedisTemplate stringRedisTemplate;

    public CampaignG520Controller(CampaignG520Service campaignG520Service, CounterService counterService, StringRedisTemplate stringRedisTemplate) {
        this.campaignG520Service = campaignG520Service;
        this.counterService = counterService;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 配置活动信息
     *
     * @param verification 验证
     * @return 处理结果
     * https://clinique.stormcrm.com/api/campaign-config/g520/20-15-04-32-29/ON
     */
    @GetMapping("/campaign-config/g520/{verification}/{online}")
    public String setConfig(
            @PathVariable("verification") String verification,
            @PathVariable("online") String online
    ) {
        if (DateUtil.parseVerification(new Date()).equals(verification)) {
            stringRedisTemplate.opsForValue().set("campaignG520", online);
            return "success";
        }
        return "fail";
    }

    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-g520/get-data/{source}")
    public String getData(
            @PathVariable("source") String source
    ) {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        // 获取缓存中的顾客编号
        String openid = (String) request.getAttribute("miniOpenid");
        // 读取活动信息
        CampaignG520 campaignG520 = campaignG520Service.getCampaignG520(openid, customerId, source);
        // 返回
        return ResultUtil.customer(ResultEnum.SUCCESS, campaignG520);

    }

    /**
     * 写入地址位置授权信息
     *
     * @param choice 选择
     * @return 成功
     */
    @PostMapping("/campaign-g520/set-location-authorize/{choice}")
    public String setSmotAuthorize(
            @PathVariable("choice") boolean choice
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        campaignG520Service.setCampaignG520LocationAuthorize(customerId, choice);

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 查找门店
     *
     * @param location 定位
     * @return 门店
     */
    @PostMapping("/campaign-g520/counter-geo")
    public String geo(
            @RequestBody Location location
    ) {
        List<Counter> counterList = counterService.getCounter(location.getLatitude(), location.getLongitude());

        if (null != counterList && 0 < counterList.size()) {
            String openid = (String) request.getAttribute("miniOpenid");
            Counter counter = counterList.get(0);
            CounterLog counterLog = new CounterLog();
            counterLog.setType((short) 1);
            counterLog.setWechatOfficialOpenid(openid);
            counterLog.setLatitude(location.getLatitude());
            counterLog.setLongitude(location.getLongitude());
            counterLog.setProvince(counter.getProvince());
            counterLog.setCity(counter.getCity());
            counterLog.setDistrict(counter.getDistrict());
            counterLog.setName(counter.getName());
            counterLog.setAddress(counter.getAddress());
            counterLog.setDistance(counter.getDistance());
            counterService.insertCounterLog(counterLog);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, counterList);
    }

    /**
     * 写入访问开始日志
     *
     * @param source 来源
     * @return 成功
     */
    @PostMapping("/campaign-g520/start-view-log/{source}/{page}")
    public String setStartViewLog(
            @PathVariable("source") String source,
            @PathVariable("page") String page
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        long id = campaignG520Service.insertCampaignViewLog(customerId, source, page);

        return ResultUtil.customer(ResultEnum.SUCCESS, id);
    }

    /**
     * 写入访问结束日志
     *
     * @param id 来源
     * @return 成功
     */
    @PostMapping("/campaign-g520/end-view-log/{id}")
    public String setEndViewLog(
            @PathVariable("id") long id
    ) {
        campaignG520Service.setCampaignViewLog(id);
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

}
