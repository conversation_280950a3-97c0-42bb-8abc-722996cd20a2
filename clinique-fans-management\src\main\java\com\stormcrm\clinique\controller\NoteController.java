package com.stormcrm.clinique.controller;

import static com.alibaba.fastjson.JSON.toJSONStringWithDateFormat;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import com.stormcrm.clinique.domain.Note;
import com.stormcrm.clinique.enums.ReviewType;
import com.stormcrm.clinique.service.NoteService;
import com.stormcrm.clinique.util.ResultUtil;

/**
 * 笔记
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("note")
public class NoteController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;

    private final NoteService noteService;

    public NoteController(NoteService noteService) {
        this.noteService = noteService;
    }

    /**
     * 笔记列表
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('NOTE_INDEX')")
    @RequestMapping("index")
    public String index(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/note/index";
    }

    /**
     * 编辑模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('NOTE_INDEX')")
    @GetMapping("note/{id}")
    public String detail(@PathVariable long id, Model model) {
        model.addAttribute("staticDomain", staticDomain);
        // 读数据库
        Note note = noteService.getNote(id);
        if (null == note) {
            return "m/fans/common/empty";
        }
        note.setPhotos(noteService.getPhotos(id));

        // 如果有图，查找标签
        if (!note.getPhotos().isEmpty()) {
            note.getPhotos().forEach(notePhoto -> notePhoto.setTags(noteService.getPhotoTags(id, notePhoto.getId())));
        }
        // 全部评论
        note.setDiscuss(noteService.getDiscussByNoteId(id));

        model.addAttribute("id", id);
        model.addAttribute("note", note);
        return "m/fans/note/detail";
    }

    /**
     * 查询所有-分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 列表
     */
    @PreAuthorize("hasAuthority('NOTE_INDEX')")
    @PostMapping("get-page")
    @ResponseBody
    public String getPage(
            @RequestParam(value = "pagination[page]", required = false) Integer page,
            @RequestParam(value = "pagination[perpage]", required = false) Integer perpage,
            @RequestParam(value = "query[generalSearch]", required = false) String generalSearch,
            @RequestParam(value = "query[status]", required = false) Short status
    ) {

        if (null == page) {
            page = 1;
        }

        if (null == perpage) {
            perpage = 10;
        }

        List<Note> noteList = noteService.getPage(page, perpage, generalSearch, status);
        int count = noteService.getPageCount(generalSearch, status);

        JSONObject mata = new JSONObject();
        mata.put("page", page);
        mata.put("pages", (count + perpage - 1) / perpage);
        mata.put("perpage", perpage);
        mata.put("total", count);
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("meta", mata);
        result.put("data", noteList);

        return toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }

    /**
     * 编辑模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('NOTE_EDIT')")
    @GetMapping("edit/{id}")
    public String edit(@PathVariable long id, Model model) {
        model.addAttribute("staticDomain", staticDomain);
        // 读数据库
        Note note = noteService.getNote(id);
        if (null == note) {
            return "m/fans/common/empty";
        }
        note.setPhotos(noteService.getPhotos(id));
        note.getPhotos().forEach((notePhoto -> notePhoto.setTags(noteService.getPhotoTags(id, notePhoto.getId()))));

        model.addAttribute("id", id);
        model.addAttribute("note", note);
        return "m/fans/note/edit";
    }

    /**
     * 同意（审核）
     *
     * @param id 自动编号
     * @return 处理结果
     */
    @PreAuthorize("hasAuthority('NOTE_ACCEPT')")
    @GetMapping("accept/{id}/{type}")
    @ResponseBody
    public String accept(
            @PathVariable("id") long id,
            @PathVariable("type") String type
    ) {

        try {
            int status = ReviewType.valueOf(type.toUpperCase()).getStatus();

            if (status > 1) {
                if (0 == noteService.accept(id, status)) {
                    return ResultUtil.failToJson();
                }
                return ResultUtil.successToJson();
            } else {
                return ResultUtil.failToJson();
            }
        } catch (Exception e) {
            return ResultUtil.failToJson();
        }


    }

    /**
     * 批量同意
     *
     * @param ids 自动编号
     * @return 处理结果
     */
    @PreAuthorize("hasAuthority('NOTE_ACCEPT')")
    @GetMapping("accept-batch/{ids}/{type}")
    @ResponseBody
    public String acceptBatch(
            @PathVariable("ids") String ids,
            @PathVariable("type") String type
    ) {
        int status = ReviewType.valueOf(type.toUpperCase()).getStatus();
        if (0 == noteService.acceptBatch(ids, status)) {
            return ResultUtil.failToJson();
        }
        return ResultUtil.successToJson();
    }

    /**
     * 拒绝
     *
     * @param id 自动编号
     * @return 处理结果
     */
    @PreAuthorize("hasAuthority('NOTE_REJECT')")
    @GetMapping("reject/{id}")
    @ResponseBody
    public String reject(@PathVariable("id") long id) {
        if (0 == noteService.reject(id)) {
            return ResultUtil.failToJson();
        }
        return ResultUtil.successToJson();
    }

    /**
     * 批量拒绝
     *
     * @param ids 自动编号
     * @return 处理结果
     */
    @PreAuthorize("hasAuthority('NOTE_REJECT')")
    @GetMapping("reject-batch/{ids}")
    @ResponseBody
    public String rejectBatch(@PathVariable("ids") String ids) {
        if (0 == noteService.rejectBatch(ids)) {
            return ResultUtil.failToJson();
        }
        return ResultUtil.successToJson();
    }


    /**
     * 根据奖励规则 point type 表 对笔记标签的设置，补之前没有发的积分
     * <p>
     * -- 追补事件，手动触发; 在 clinique-fans-schedule 中， 增加凌晨定时器，调用这个 --
     * <p>
     * 只使用了一次，做初始化的时候。【已停用】
     *
     * @return
     */
    @PreAuthorize("hasAuthority('NOTE_ACCEPT')")
    @RequestMapping("append_note_tag_add_reward")
    public String append_note_tag_add_reward(Model model) {
        model.addAttribute("staticDomain", staticDomain);

        // 笔记状态必须通过审核的才会补上

        // 查询携带 奖励类型的 的笔记  但是没有加分记录的（目前没关联）  执行加分操作

        noteService.appendNoteTagCBByHistory();

        return "m/fans/note/index";
    }


}
