package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.StoreGift;
import com.stormcrm.clinique.util.StringUtil;
import org.apache.ibatis.jdbc.SQL;

/**
 * 商城礼品管理
 *
 * <AUTHOR>
 */
public class StoreGiftProvider {

    /**
     * 查询所有礼品-分页
     *
     * @param pageIndex     页面索引
     * @param pageSize      页面大小
     * @param generalSearch 搜索关键字
     * @param status        状态
     * @return 礼品列表
     */
    public String getPage(int pageIndex, int pageSize, String generalSearch, Boolean status) {

        return new SQL() {{

            SELECT("`id`,`name`,`points`,`stocks`,`exchange_total`,`status`,`update_time`,`create_time`");
            FROM("`store_gift`");
            WHERE("`store_gift_type_id`=1");
            AND();
            WHERE("`recovery`=0");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`name` LIKE '%${generalSearch}%'");
            }
            if (null != status) {
                AND();
                WHERE("`status`=#{status}");
            }
            ORDER_BY("`id` DESC LIMIT #{pageIndex},#{pageSize}");

        }}.toString();

    }

    /**
     * 查询所有礼品带分页的记录数
     *
     * @param generalSearch 搜索
     * @param status        状态
     * @return 记录数
     */
    public String getPageCount(String generalSearch, Boolean status) {

        return new SQL() {{

            SELECT("COUNT(*)");
            FROM("`store_gift`");
            WHERE("`store_gift_type_id`=1");
            AND();
            WHERE("`recovery`=0");
            if (!StringUtil.isNullOrEmpty(generalSearch)) {
                AND();
                WHERE("`name` LIKE '%${generalSearch}%'");
            }
            if (null != status) {
                AND();
                WHERE("`status`=#{status}");
            }

        }}.toString();

    }

    /**
     * 更新
     *
     * @param storeGift 信息
     */
    public String update(StoreGift storeGift) {

        return new SQL() {{

            UPDATE("`store_gift`");

            SET("`name`=#{name}");
            if (!StringUtil.isNullOrEmpty(storeGift.getImageUrl())) {
                SET("`image_url`=#{imageUrl}");
            }
            SET("`description`=#{description}");
            SET("`points`=#{points}");
            SET("`stocks`=#{stocks}");

            WHERE("`id`=#{id}");

        }}.toString();

    }

}
