package cn.slashsoft.clinique.controller.mini;

import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.PointService;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.mini.PointDetailVo;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 积分相关的控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class PointController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final PointService pointService;

    public PointController(PointService pointService) {
        this.pointService = pointService;
    }

    @PostMapping("/point/get-detail-data")
    public String getPointDetailData(){

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        PointDetailVo pointDetailVo = new PointDetailVo();
        pointDetailVo.setPointTransactionList(pointService.getPointTransaction(customerId));

        return ResultUtil.customer(ResultEnum.SUCCESS, pointDetailVo);

    }

}
