package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.domain.campaign.CampaignEbciSign;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.campaign.CampaignEbciSignService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.campaign.CampaignEbciSignVo;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * EBCI 7日打卡活动相关控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class CampaignEbciSignController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpServletRequest request;

    private final CampaignEbciSignService campaignEbciSignService;
    private final StringRedisTemplate stringRedisTemplate;

    public CampaignEbciSignController(StringRedisTemplate stringRedisTemplate, CampaignEbciSignService campaignEbciSignService) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.campaignEbciSignService = campaignEbciSignService;
    }

    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-ebci-sign/get-data")
    public String getData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 获取签到信息
        CampaignEbciSign campaignEbciSign = campaignEbciSignService.getCampaignEbciSign(customerId);

        CampaignEbciSignVo campaignEbciSignVo = new CampaignEbciSignVo();

        campaignEbciSignVo.setStartTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciSignStartTime")));
        campaignEbciSignVo.setEndTime(DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciSignEndTime")));

        campaignEbciSignVo.setLevel(campaignEbciSign.getLevel());

        // 是否申领中
        if (7 == campaignEbciSign.getLevel()) {
            campaignEbciSignVo.setComplete(true);
        } else {
            campaignEbciSignVo.setComplete(false);
        }

        campaignEbciSignVo.setApply(campaignEbciSign.getApply());

        // 今天是否已经签到
        if (DateUtil.isToday(campaignEbciSign.getLevelTime())) {
            campaignEbciSignVo.setSigned(true);
        } else {
            campaignEbciSignVo.setSigned(false);
        }
        // 当前是否可以签到
        if (0 == campaignEbciSign.getLevel() || (7 > campaignEbciSign.getLevel() && DateUtil.isYesterday(campaignEbciSign.getLevelTime()))) {
            campaignEbciSignVo.setSign(true);
        } else {
            campaignEbciSignVo.setSign(false);
        }
        // 是否断签
        if (!campaignEbciSignVo.getSigned() && !campaignEbciSignVo.getSign()) {
            campaignEbciSignVo.setBroken(true);
        } else {
            campaignEbciSignVo.setBroken(false);
        }
        // 是否邀请中
        campaignEbciSignVo.setInviting(campaignEbciSign.getInviting());
        // 是否成功邀请
        if (0 < campaignEbciSign.getInviteTotal()) {
            campaignEbciSignVo.setInvited(true);
        } else {
            campaignEbciSignVo.setInvited(false);
        }

        campaignEbciSignVo.setRecord(campaignEbciSignService.getCampaignEbciSignLog(customerId));

        return ResultUtil.customer(ResultEnum.SUCCESS, campaignEbciSignVo);

    }

    /**
     * 签到
     *
     * @return 页面数据
     */
    @PostMapping("/campaign-ebci-sign/sign")
    public String sign() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciSignStartTime"));
        Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciSignEndTime"));

        // 活动未开始
        if (DateUtil.laterThanNow(startTime)) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        // 活动已结束
        if (DateUtil.earlierThanNow(endTime)) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        Short level = campaignEbciSignService.sign(customerId);
        if (0 == level) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS, level);

    }

    /**
     * 重置
     * @return 页面数据
     */
    @PostMapping("/campaign-ebci-sign/reset")
    public String reset() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciSignStartTime"));
        Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciSignEndTime"));

        // 活动未开始
        if (DateUtil.laterThanNow(startTime)) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        // 活动已结束
        if (DateUtil.earlierThanNow(endTime)) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        if(!campaignEbciSignService.reset(customerId)){
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 邀请
     * @return 页面数据
     */
    @PostMapping("/campaign-ebci-sign/inviting")
    public String inviting() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciSignStartTime"));
        Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciSignEndTime"));

        // 活动未开始
        if (DateUtil.laterThanNow(startTime)) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        // 活动已结束
        if (DateUtil.earlierThanNow(endTime)) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        if(!campaignEbciSignService.inviting(customerId)){
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 继续
     * @return 页面数据
     */
    @PostMapping("/campaign-ebci-sign/invited")
    public String invited() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciSignStartTime"));
        Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciSignEndTime"));

        // 活动未开始
        if (DateUtil.laterThanNow(startTime)) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        // 活动已结束
        if (DateUtil.earlierThanNow(endTime)) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        if(!campaignEbciSignService.invited(customerId)){
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 申领
     * @return 页面数据
     */
    @PostMapping("/campaign-ebci-sign/apply")
    public String apply() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciSignStartTime"));
        Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciSignEndTime"));

        // 活动未开始
        if (DateUtil.laterThanNow(startTime)) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        // 活动已结束
        if (DateUtil.earlierThanNow(endTime)) {
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        if(!campaignEbciSignService.apply(customerId)){
            return ResultUtil.customer(ResultEnum.FAILED);
        }

        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 写入访问开始日志
     *
     * @param source 来源
     * @return 成功
     */
    @PostMapping("/campaign-ebci-sign/start-view-log/{source}")
    public String setStartViewLog(
            @PathVariable("source") String source
    ) {
        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");
        long id = campaignEbciSignService.insertCampaignEbciSignViewLog(customerId, source);

        return ResultUtil.customer(ResultEnum.SUCCESS, id);
    }

    /**
     * 写入访问结束日志
     *
     * @param id 来源
     * @return 成功
     */
    @PostMapping("/campaign-ebci-sign/end-view-log/{id}")
    public String setEndViewLog(
            @PathVariable("id") long id
    ) {
        campaignEbciSignService.setCampaignEbciSignViewLog(id);
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

}
