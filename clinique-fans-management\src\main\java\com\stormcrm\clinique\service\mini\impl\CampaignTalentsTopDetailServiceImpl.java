package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.domain.CampaignTalentsTopDetail;
import com.stormcrm.clinique.service.CampaignTalentsTopDetailService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.vo.Result;
import com.stormcrm.clinique.dao.CampaignTalentsTopDetailDao;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 新品体验官活动
 *
 * <AUTHOR>
 */
@Service
public class CampaignTalentsTopDetailServiceImpl implements CampaignTalentsTopDetailService {

    private final CampaignTalentsTopDetailDao campaignTalentsTopDetailDao;

    public CampaignTalentsTopDetailServiceImpl(CampaignTalentsTopDetailDao campaignTalentsTopDetailDao) {
        this.campaignTalentsTopDetailDao = campaignTalentsTopDetailDao;
    }

    /**
     * 查询所有活动
     *
     * @param id TOP5编号
     * @return 活动列表
     */
    @Override
    public List<CampaignTalentsTopDetail> getAll(long id) {
        return campaignTalentsTopDetailDao.getAll(id);
    }

    /**
     * 查询信息
     *
     * @param detailId 自动编号
     * @return 信息
     */
    @Override
    public CampaignTalentsTopDetail getById(long detailId) {
        return campaignTalentsTopDetailDao.getById(detailId);
    }

    /**
     * 保存
     *
     * @param campaignTalentsTopDetail 信息
     * @return 影响的行数
     */
    @Override
    public Result save(CampaignTalentsTopDetail campaignTalentsTopDetail) {
        campaignTalentsTopDetailDao.save(campaignTalentsTopDetail);
        return ResultUtil.success("增加成功!");
    }

    /**
     * 更新
     *
     * @param campaignTalentsTopDetail 信息
     * @return 影响的行数
     */
    @Override
    public Result update(CampaignTalentsTopDetail campaignTalentsTopDetail) {
        campaignTalentsTopDetailDao.update(campaignTalentsTopDetail);
        return ResultUtil.success("增加成功!");
    }

    /**
     * 更新
     *
     * @param id 编号
     * @return 影响的行数
     */
    @Override
    public int delete(long id) {
        return campaignTalentsTopDetailDao.delete(id);
    }
}
