package cn.slashsoft.clinique.service.mini.impl;

import cn.slashsoft.clinique.dao.mini.TTestimonyKocUserListDao;
import cn.slashsoft.clinique.domain.mini.TTestimonyKocUserList;
import cn.slashsoft.clinique.service.mini.TTestimonyKocUserListService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
* <p>
    * 盲盒申领
    * </p>
*
* <AUTHOR>
* @since 2020-09-21
*/
@Service
public class TTestimonyKocUserListServiceImpl implements TTestimonyKocUserListService {

    @Resource
    private TTestimonyKocUserListDao tTestimonyKocUserListDao;

    @Override
    public void insertTTestimonyKocUserList(TTestimonyKocUserList tTestimonyKocUserList) {
        tTestimonyKocUserListDao.insertTTestimonyKocUserList(tTestimonyKocUserList);
    }

    @Override
    public void updateTTestimonyKocUserList(TTestimonyKocUserList tTestimonyKocUserList) {
        tTestimonyKocUserListDao.updateTTestimonyKocUserList(tTestimonyKocUserList);
    }

    @Override
    public TTestimonyKocUserList getTTestimonyKocUserList(Long id) {
        return tTestimonyKocUserListDao.getTTestimonyKocUserList(id);
    }

	@Override
	public boolean getKocUser(Long customerId) {		
		return this.tTestimonyKocUserListDao.getKocByCustomerId(customerId + "")>0?true:false;
	}
}
