package cn.slashsoft.clinique.domain.mini;

import java.util.ArrayList;
import java.util.Date;

import lombok.Data;

@Data
public class Note{

	private Long id;

	private Object content;

	private Date createTime;
	private Long customerId;

	private int status;

	private Object title;

	private Date updateTime;
	
	private String coverPhoto;
	private String ownerName;
	private String avatarUrl;
	private String myAvatarUrl;
	private Long likeCount;
	private Long readCount;
	private int liked;
	private int self;

    /* 给用户加一个小绿色V */
    private Integer kocV;

	private ArrayList<NotePhoto> photos;
	private ArrayList<NoteDiscuss> discuss;
	
	public String toJson() {
		return "";
	}

}