package cn.slashsoft.clinique.controller.api;

import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.mini.PointService;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.util.EncryptUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/open-api")
public class PointApiController {

    private final WechatService wechatService;
    private final PointService pointService;

    public PointApiController(WechatService wechatService, PointService pointService) {
        this.wechatService = wechatService;
        this.pointService = pointService;
    }

    @GetMapping("/point/grant/{unionid}/{pointTypeId}/{points}")
    public String getOpenIdByUnionId(
            @PathVariable("unionid") String unionid,
            @PathVariable("pointTypeId") short pointTypeId,
            @PathVariable("points") int points
    ){
        unionid = EncryptUtil.unionidDecode(unionid);
        Wechat wechat = wechatService.getWechatByUnionId(unionid);
        if(null == wechat){
            // 写入临时表
            pointService.addPointTransactionTempForApi(unionid, pointTypeId, points);
        }
        else{
            // 发放积分
            pointService.addPointTransactionForApi(wechat.getCustomerId(), pointTypeId, points);
        }
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

}
