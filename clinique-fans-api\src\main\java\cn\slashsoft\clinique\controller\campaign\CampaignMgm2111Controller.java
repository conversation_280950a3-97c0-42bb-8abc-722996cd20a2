package cn.slashsoft.clinique.controller.campaign;

import cn.slashsoft.clinique.domain.campaign.mgm2111.Detail;
import cn.slashsoft.clinique.domain.campaign.mgm2111.Invite;
import cn.slashsoft.clinique.domain.campaign.mgm2111.ViewLog;
import cn.slashsoft.clinique.enums.ResultEnum;
import cn.slashsoft.clinique.service.campaign.CampaignMgm2111Service;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.ResultUtil;
import cn.slashsoft.clinique.vo.Result;
import cn.slashsoft.clinique.vo.campaign.mgm2111.RankVo;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 2021年11月裂变活动
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/campaign-mgm2111")
public class CampaignMgm2111Controller {

    @Resource
    private HttpServletRequest request;

    @Resource
    private CampaignMgm2111Service campaignMgm2111Service;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/get-index-data")
    public String getData(
            @RequestParam("source") String source
    ) {

        Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaign-mgm2111-start-time"));
        Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaign-mgm2111-end-time"));
        if(DateUtil.laterThanNow(startTime) || DateUtil.earlierThanNow(endTime)){
            return ResultUtil.customer(ResultEnum.OTHER);
        }

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 读取活动信息
        Detail detail = campaignMgm2111Service.getDetailWithCreate(customerId, source);

        Date signStartTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaign-mgm2111-sign-start-time"));
        Date signEndTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaign-mgm2111-sign-end-time"));
        detail.setSign(DateUtil.earlierThanNow(signStartTime) && DateUtil.laterThanNow(signEndTime));

        Date inviteStartTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaign-mgm2111-invite-start-time"));
        Date inviteEndTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaign-mgm2111-invite-end-time"));
        detail.setInvite(DateUtil.earlierThanNow(inviteStartTime) && DateUtil.laterThanNow(inviteEndTime));

        // 返回
        return ResultUtil.customer(ResultEnum.SUCCESS, detail);

    }

    /**
     * 获取排名页面数据
     *
     * @return 页面数据
     */
    @PostMapping("/get-rank-data")
    public String getRankData() {

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 读取活动信息
        Detail detail = campaignMgm2111Service.getDetailWithAvatar(customerId);
        if (null == detail) {
            return ResultUtil.customer(ResultEnum.FAILED, "未找到活动信息");
        }

        // 读取排名信息
        List<Invite> inviteList = campaignMgm2111Service.getInviteByRank();

        RankVo rankVo = new RankVo();
        rankVo.setDetail(detail);
        rankVo.setInviteList(inviteList);

        // 返回
        return ResultUtil.customer(ResultEnum.SUCCESS, rankVo);

    }

    /**
     * 领取签到第8次的礼品
     *
     * @return 结果
     */
    @PostMapping("/get-sign-coupon")
    public String getSignCoupon() {

//        Date signStartTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaign-mgm2111-sign-start-time"));
//        Date signEndTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaign-mgm2111-sign-end-time"));
//        if(DateUtil.laterThanNow(signStartTime) || DateUtil.earlierThanNow(signEndTime)){
//            return ResultUtil.customer(ResultEnum.FAILED, "活动尚未开始");
//        }
//
//        // 获取缓存中的顾客编号
//        long customerId = (long) request.getAttribute("customerId");
//        // 获取缓存中的开放平台唯一编号
//        String unionid = (String) request.getAttribute("unionid");
//        // 优惠券编号
//        long signCoupon5CampaignId = Long.parseLong(Objects.requireNonNull(stringRedisTemplate.opsForValue().get("campaign-mgm2111-sign-5")));
//        long signCoupon8CampaignId = Long.parseLong(Objects.requireNonNull(stringRedisTemplate.opsForValue().get("campaign-mgm2111-sign-8")));
//
//        Result result = campaignMgm2111Service.getSignCoupon(customerId, unionid, signCoupon5CampaignId, signCoupon8CampaignId);
//        if (0 != result.getCode()) {
//            return ResultUtil.customer(ResultEnum.FAILED, result.getMessage());
//        }

        return ResultUtil.customer(ResultEnum.FAILED, "活动已结束");

    }

    /**
     * 领取邀请第8次的礼品
     *
     * @return 结果
     */
    @PostMapping("/get-invite-coupon")
    public String getInviteCoupon() {

//        Date inviteStartTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaign-mgm2111-invite-start-time"));
//        Date inviteEndTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaign-mgm2111-invite-end-time"));
//        if(DateUtil.laterThanNow(inviteStartTime) || DateUtil.earlierThanNow(inviteEndTime)){
//            return ResultUtil.customer(ResultEnum.FAILED, "活动尚未开始");
//        }
//
//        // 获取缓存中的顾客编号
//        long customerId = (long) request.getAttribute("customerId");
//        // 获取缓存中的开放平台唯一编号
//        String unionid = (String) request.getAttribute("unionid");
//        // 优惠券编号
//        long inviteCoupon3CampaignId = Long.parseLong(Objects.requireNonNull(stringRedisTemplate.opsForValue().get("campaign-mgm2111-invite-3")));
//        long inviteCoupon8CampaignId = Long.parseLong(Objects.requireNonNull(stringRedisTemplate.opsForValue().get("campaign-mgm2111-invite-8")));
//
//        Result result = campaignMgm2111Service.getInviteCoupon(customerId, unionid, inviteCoupon3CampaignId, inviteCoupon8CampaignId);
//        if (0 != result.getCode()) {
//            return ResultUtil.customer(ResultEnum.FAILED, result.getMessage());
//        }

        return ResultUtil.customer(ResultEnum.SUCCESS, "活动已结束");

    }

    /**
     * 写入经纬度
     *
     * @param latitude   经度
     * @param longitude  纬度
     * @return 成功
     */
    @PostMapping("/location/{latitude}/{longitude}/1")
    public String setLocation(
            @PathVariable("latitude") String latitude,
            @PathVariable("longitude") String longitude
    ){

        // 获取缓存中的顾客编号
        long customerId = (long) request.getAttribute("customerId");

        // 保存
        campaignMgm2111Service.insertLocation(customerId, latitude, longitude);

        // 返回
        return ResultUtil.customer(ResultEnum.SUCCESS);
    }

    /**
     * 写入访问开始日志
     *
     * @param source 来源
     * @return 成功
     */
    @PostMapping("/start-view-log/{source}/{page}")
    public String setStartViewLog(
            @PathVariable("source") String source,
            @PathVariable("page") String page
    ) {

        // 获取缓存中的顾客编号
        ViewLog viewLog = new ViewLog();
        viewLog.setCustomerId((long) request.getAttribute("customerId"));
        viewLog.setSource(source);
        viewLog.setPage(page);

        long id = campaignMgm2111Service.insertViewLog(viewLog);

        // 返回
        return ResultUtil.customer(ResultEnum.SUCCESS, id);

    }

    /**
     * 写入访问结束日志
     *
     * @param id 来源
     * @return 成功
     */
    @PostMapping("/end-view-log/{id}")
    public String setEndViewLog(
            @PathVariable("id") long id
    ) {

        campaignMgm2111Service.setSignViewLog(id);
        return ResultUtil.customer(ResultEnum.SUCCESS);

    }

}
