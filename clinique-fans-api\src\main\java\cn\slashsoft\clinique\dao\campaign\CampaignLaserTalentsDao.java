package cn.slashsoft.clinique.dao.campaign;

import cn.slashsoft.clinique.domain.campaign.*;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Laser Focus 达人榜
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignLaserTalentsDao {

    /**
     * 获取达人榜信息
     *
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `status` " +
            "FROM " +
            "   `campaign_laser_talents` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    CampaignLaserTalents getCampaignLaserTalents(long customerId);

    /**
     * 获取达人榜信息, 和上传图片的数量
     *
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `alert`," +
            "   `status`," +
            "   `upload_time`," +
            "   `examine_time`," +
            "   (" +
            "       SELECT " +
            "           COUNT(`id`) " +
            "       FROM " +
            "           `campaign_laser_talents_image` " +
            "       WHERE " +
            "           `talents_id`=`campaign_laser_talents`.`id` " +
            "           AND `status`=1" +
            "   ) `image_count` " +
            "FROM " +
            "   `campaign_laser_talents` " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    CampaignLaserTalents getCampaignLaserTalentsWithImageCount(long customerId);

    /**
     * 获取所有图片
     *
     * @param campaignLaserTalentsId 顾客活动信息编号
     * @return 图片地址
     */
    @Select("SELECT " +
            "   `image_url`," +
            "   `status` " +
            "FROM " +
            "   `campaign_laser_talents_image` " +
            "WHERE " +
            "   `talents_id`=#{talentsId} " +
            "ORDER BY `id` DESC")
    List<CampaignLaserTalentsImage> getCampaignLaserTalentsImageList(long campaignLaserTalentsId);

    /**
     * 获取当前TOP信息
     *
     * @return TOP信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `stage` " +
            "FROM " +
            "   `campaign_laser_talents_top` " +
            "WHERE " +
            "   `status`=1 " +
            "   AND NOW()>=`start_time` AND NOW()<=`end_time`")
    CampaignLaserTalentsTop getCampaignLaserTalentsTop();

    /**
     * 获取入选TOP的名单
     *
     * @param campaignLaserTalentsTopId TOP信息编号
     * @return 名单
     */
    @Select("SELECT " +
            "   `t`.`id` `talents_detail_id`," +
            "   `t`.`examine_time`, " +
            "   `w`.`nick_name`, " +
            "   `w`.`avatar_url` " +
            "FROM " +
            "   `campaign_laser_talents_top_detail` `d` " +
            "       INNER JOIN " +
            "   `campaign_laser_talents` `t` " +
            "       ON `d`.`talents_id`=`t`.`id` " +
            "       INNER JOIN " +
            "   `wechat` `w` " +
            "       ON `t`.`customer_id`=`w`.`customer_id` " +
            "WHERE " +
            "   `d`.`talents_top_id`=#{talentsTopId} AND `d`.`status`=1 " +
            "ORDER BY " +
            "   `d`.`sort`")
    List<CampaignLaserTalentsTopDetail> getCampaignLaserTalentsTopDetailList(long campaignLaserTalentsTopId);

    /**
     * 获取入选TOP的图片
     *
     * @param campaignLaserTalentsTopId TOP信息编号
     * @return 图片
     */
    @Select("SELECT " +
            "   `i`.`id`, " +
            "   `i`.`talents_id` `talents_detail_id`, " +
            "   `i`.`image_url` " +
            "FROM " +
            "   `campaign_laser_talents_top_detail` `d` " +
            "       INNER JOIN " +
            "   `campaign_laser_talents_image` `i` " +
            "       ON `d`.`talents_id`=`i`.`talents_id` " +
            "WHERE " +
            "   `d`.`talents_top_id`=#{talentsTopId}" +
            "   AND `d`.`status`=1 AND `i`.`status`=1")
    List<CampaignLaserTalentsTopDetailImage> getCampaignLaserTalentsTopDetailImageList(long campaignLaserTalentsTopId);

   
    /**
     * 写入达人榜信息
     *
     * @param campaignLaserTalents 达人榜信息
     */
    @Insert("INSERT INTO `campaign_laser_talents`(" +
            "   `customer_id`, " +
            "   `follow`, " +
            "   `follow_source`, " +
            "   `follow_first_time`, " +
            "   `follow_last_time`, " +
            "   `follow_cancel_time`, " +
            "   `bind`, " +
            "   `bind_time`, " +
            "   `status` " +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{follow}," +
            "   #{followSource}," +
            "   #{followFirstTime}," +
            "   #{followLastTime}," +
            "   #{followCancelTime}," +
            "   #{bind}," +
            "   #{bindTime}," +
            "   #{status}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertCampaignLaserTalents(CampaignLaserTalents campaignLaserTalents);

    /**
     * 变改状态
     *
     * @param customerId     顾客编号
     * @param originalStatus 原始状态
     * @param newStatus      新状态
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_laser_talents` " +
            "SET " +
            "   `status`=#{newStatus}, " +
            "   `alert`=1 " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "   AND `status`=#{originalStatus}")
    int updateCampaignLaserTalents(long customerId, short originalStatus, short newStatus);

    /**
     * 更新提醒状态
     *
     * @param customerId 顾客编号
     */
    @Update("UPDATE " +
            "   `campaign_laser_talents` " +
            "SET " +
            "   `alert`=0 " +
            "WHERE " +
            "   `customer_id`=#{customerId}")
    void updateCampaignLaserTalentsAlert(long customerId);

    /**
     * 变改状态
     *
     * @param customerId     顾客编号
     * @param originalStatus 原始状态
     * @param newStatus      新状态
     * @return 影响的行
     */
    @Update("UPDATE " +
            "   `campaign_laser_talents` " +
            "SET " +
            "   `status`=#{newStatus}," +
            "   `alert`=1, " +
            "   `upload_time`=NOW() " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "   AND `status`=#{originalStatus}")
    int updateCampaignLaserTalentsWithUploadTime(long customerId, short originalStatus, short newStatus);

    /**
     * 设置图片无效
     *
     * @param campaignLaserTalentsId 顾客活动信息编号
     */
    @Update("UPDATE " +
            "   `campaign_laser_talents_image` " +
            "SET " +
            "   `status`=0 " +
            "WHERE " +
            "   `talents_id`=#{talentsId}")
    void updateCampaignLaserTalentsImageStatus(long campaignLaserTalentsId);

    /**
     * 写入图片信息
     *
     * @param campaignLaserTalentsImage 图片信息
     */
    @Insert("INSERT INTO `campaign_laser_talents_image`(" +
            "   `talents_id`, " +
            "   `image_url`" +
            ") " +
            "VALUES (" +
            "   #{talentsId}, " +
            "   #{imageUrl}" +
            ")")
    void insertCampaignLaserTalentsImage(CampaignLaserTalentsImage campaignLaserTalentsImage);

    /**
     * 写入活动操作日志
     *
     * @param campaignLaserTalentsLog 操作日志
     */
    @Insert("INSERT INTO `campaign_laser_talents_log`(" +
            "   `customer_id`, " +
            "   `status`, " +
            "   `content`" +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{status}, " +
            "   #{content}" +
            ")")
    void insertCampaignLaserTalentsLog(CampaignLaserTalentsLog campaignLaserTalentsLog);

    /**
     * 更新肤质
     *
     * @param customerId 顾客编号
     * @param skinType   肤质
     */
    @Update("UPDATE " +
            "   `campaign_laser_talents` " +
            "SET " +
            "   `skin_type`=#{skinType} " +
            "WHERE " +
            "   `customer_id`=#{customerId} " +
            "LIMIT 1")
    void setCampaignLaserTalentsSkinType(long customerId, short skinType);

    /**
     * 写入访问日志
     *
     * @param campaignLaserTalentsViewLog 日志
     */
    @Insert("INSERT INTO `campaign_laser_talents_view_log`(" +
            "   `customer_id`," +
            "   `source`," +
            "   `page`" +
            ") " +
            "VALUES (" +
            "   #{customerId}," +
            "   #{source}," +
            "   #{page}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertCampaignLaserTalentsViewLog(CampaignLaserTalentsViewLog campaignLaserTalentsViewLog);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    @Update("UPDATE " +
            "   `campaign_laser_talents_view_log` " +
            "SET " +
            "   `end_time`=NOW()," +
            "   `diff_second`=TimeStampDiff(SECOND,`start_time`,NOW()) " +
            "WHERE " +
            "   `id`=#{id}")
    void setCampaignLaserTalentsViewLog(long id);

    /**
     * 写入达人榜访问记录
     *
     * @param campaignLaserTalentsTopDetailLog 达人榜访问记录
     */
    @Insert("INSERT INTO `campaign_laser_talents_top_detail_log`(" +
            "   `customer_id`, " +
            "   `stage`, " +
            "   `talents_top_detail_id`" +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{stage}, " +
            "   #{talentsTopDetailId}" +
            ")")
    void insertCampaignLaserTalentsTopDetailLog(CampaignLaserTalentsTopDetailLog campaignLaserTalentsTopDetailLog);

    /**
     * 写入达人榜图片访问记录
     *
     * @param campaignLaserTalentsTopDetailImageLog 达人榜图片访问记录
     */
    @Insert("INSERT INTO `campaign_laser_talents_top_detail_image_log`(" +
            "   `customer_id`, " +
            "   `stage`, " +
            "   `talents_top_detail_id`, " +
            "   `talents_image_id`, " +
            "   `type` " +
            ") " +
            "VALUES (" +
            "   #{customerId}, " +
            "   #{stage}, " +
            "   #{talentsTopDetailId}, " +
            "   #{talentsImageId}, " +
            "   #{type} " +
            ")")
    void insertCampaignLaserTalentsTopDetailImageLog(CampaignLaserTalentsTopDetailImageLog campaignLaserTalentsTopDetailImageLog);

}
