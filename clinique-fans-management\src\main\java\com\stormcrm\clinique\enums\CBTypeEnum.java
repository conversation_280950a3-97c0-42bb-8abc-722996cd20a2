package com.stormcrm.clinique.enums;

import lombok.Getter;

/**
 * C币类别
 *
 * <AUTHOR>
 * @deprecated CB奖励类型和奖励采用数据库表 point_type 设置，后台提供了管理页面。前端接口可以考虑使用redis降低这种配置信息的查询次数。
 */
@Deprecated
public enum CBTypeEnum {

    // 积分类别
    BINDING          ((short) 1, "绑定会员中心", 100,1),
    INFO             ((short) 2, "完善个人资料", 50,1),
    COMMENT          ((short) 3, "公众号文章精选评论", 10,7),
    LIKE             ((short) 4, "公众号文章点赞Top3评论", 20,7),
    LINK             ((short) 5, "公众号文章链接进入小程序", 10,0),
    TMALL            ((short) 6, "天猫会员额外赠分", 100,0),
    EXCHANGE         ((short) 7, "礼品兑换", 0,0),
    EXPIRED          ((short) 8, "积分过期", 0,0),
    CNY              ((short) 9, "“鼠你最红”奖励积分", 50,1),
    VIDEO_SIGN       ((short) 10, "视频签到活动", 100,1),
    EBCI_SIGN        ((short) 11, "EBCI签到活动", 100,1),
    TMALL_REGISTER   ((short) 12, "天猫专享积分", 100,1),
    EBCI_TALENTS     ((short) 13, "美白新品体验官", 200,1),
    MS_TALENTS       ((short) 14, "黄油达人榜", 200,1),
    LASER_TALENTS    ((short) 15, "夏日抚纹作战", 200,1),
    NOTE_PUBLISH     ((short) 16, "发布个人笔记", 20,0),
    NOTE_VIDEO_SHARE   	((short) 17, "分享转发笔记/视频", 5,10),
    NOTE_VIDEO_ACTION    	((short) 18, "社群内阅读/评论/收藏", 1,20),
    NOTE_ADDTAG    	 ((short) 19, "参与品牌话题互动", 5,1),
    NOTE_FAVORITE    ((short) 20, "个人笔记加精", 50,1),
    KOC_JOIN    	 ((short) 21, "参与KOC争夺战", 200,1),
    VIDEO_PLAY    	 ((short) 22, "观看视频/直播", 50,1),
    ZHENGYAN_GET     ((short) 23, "活动加赠-KOC抢鲜玩", 100,1),
    // 万人美白证言墙  41
    NOTE_TAG_41    	((short) 24, "活动加赠-KOC抢鲜玩", 100,1)
    ;

    @Getter
    private final short id;

    @Getter
    private final String name;

    @Getter
    private final int points;

    @Getter
    private final int max;

    CBTypeEnum(short id, String name, int points, int max) {
        this.id = id;
        this.name = name;
        this.points = points;
        this.max = max;
    }
}
