package cn.slashsoft.clinique.service.campaign.impl;

import cn.slashsoft.clinique.dao.campaign.CampaignToneupDao;
import cn.slashsoft.clinique.domain.campaign.CampaignToneupDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignToneupLog;
import cn.slashsoft.clinique.service.campaign.CampaignToneupService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

/**
 * 黄油变粉安瓶级保湿补光申领
 *
 * <AUTHOR>
 */
@Service
public class CampaignToneupServiceImpl implements CampaignToneupService {

    private final CampaignToneupDao campaignToneupDao;

    public CampaignToneupServiceImpl(CampaignToneupDao campaignToneupDao) {
        this.campaignToneupDao = campaignToneupDao;
    }

    /**
     * 保存日志
     *
     * @param campaignToneupLog 日志
     */
    @Override
    public void insertLog(CampaignToneupLog campaignToneupLog) {
        campaignToneupDao.insertLog(campaignToneupLog);
    }

    /**
     * 获取手机号码
     *
     * @param unionid 开放平台唯一编号
     * @return 手机号码
     */
    @Override
    public String getPhoneNumberByUnionid(String unionid) {
        return campaignToneupDao.getPhoneNumberByUnionid(unionid);
    }

    /**
     * 获取是否已经审领
     *
     * @param phoneNumber 手机号码
     * @return 数量
     */
    @Override
    public int hasDetail(String phoneNumber) {
        return campaignToneupDao.hasDetail(phoneNumber);
    }

    /**
     * 获取是否已审领
     *
     * @param phoneNumber 手机号码
     * @return 审领信息
     */
    @Override
    public CampaignToneupDetail getDetail(String phoneNumber) {
        return campaignToneupDao.getDetail(phoneNumber);
    }

    /**
     * 申领
     *
     * @param campaignToneupDetail 资料
     * @return 0:成功，1：手机号码已经领过了，2：库存不足
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int submit(CampaignToneupDetail campaignToneupDetail) {

        // 扣库存
        if(0==campaignToneupDao.updateStock(campaignToneupDetail)){
            return 2;
        }

        // 写入记录
        try {
            campaignToneupDao.insetDetail(campaignToneupDetail);
            return 0;
        }
        catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return 1;
        }

    }

    /**
     * 更新是否关注
     *
     * @param phoneNumber 手机号码
     * @param isFollow    是否关注
     */
    @Override
    public void setFollow(String phoneNumber, boolean isFollow) {
        campaignToneupDao.setFollow(phoneNumber, isFollow);
    }
}
