package com.stormcrm.clinique.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 */
@Controller
public class DashboardController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @RequestMapping(value = {"/", "dashboard"})
    public String dashboard(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/dashboard/dashboard";
    }

}
