package cn.slashsoft.clinique.util;


import cn.slashsoft.clinique.enums.ResultEnum;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * 重复代码工具类
 *
 * <AUTHOR>
 */
public class DuplicateUtil {


    public static void interceptor(HttpServletRequest request, HttpServletResponse response, ResultEnum resultEnum) throws IOException {
        response.setStatus(HttpStatus.OK.value());
        response.setContentType("text/html; charset=utf-8");
        PrintWriter writer = response.getWriter();
        writer.print(ResultUtil.customer(resultEnum));
        writer.close();
        response.flushBuffer();
    }


}
