package com.stormcrm.clinique.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import com.stormcrm.clinique.domain.NotePhoto;
import com.stormcrm.clinique.domain.NoteTag;
import com.stormcrm.clinique.service.NoteService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.util.VerifyUtil;
import com.stormcrm.clinique.vo.Result;

@Controller
@RequestMapping("note/photo")
public class NotePhotoController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    Logger logger = LoggerFactory.getLogger(NotePhotoController.class);

    private final NoteService noteService;


    public NotePhotoController(NoteService noteService) {
        this.noteService = noteService;
    }

    /**
     * 笔记标签列表
     *
     * @return 模版
     */
    @PreAuthorize("hasAuthority('NOTE_INDEX')")
    @RequestMapping("")
    public String index(Model model) {
        model.addAttribute("staticDomain", staticDomain);
        return "m/fans/note-photos/index";
    }

    /**
     * 笔记评论列表
     *
     * @param id note id
     * @return 模版
     */
    @PreAuthorize("hasAuthority('NOTE_PHOTO')")
    @RequestMapping("/{id}")
    public String index(@PathVariable long id, Model model) {
        model.addAttribute("staticDomain", staticDomain);
        model.addAttribute("noteId", id);
        return "m/fans/note-photos/index";
    }

    /**
     * 查询所有-分页
     *
     * @param page          页码
     * @param perpage       页面大小
     * @param noteId           noteID
     * @return 列表
     */
    @PreAuthorize("hasAuthority('NOTE_INDEX')")
    @PostMapping("get-page")
    @ResponseBody
    public String getPage(
            @RequestParam(value = "pagination[page]", required = false) Integer page,
            @RequestParam(value = "pagination[perpage]", required = false) Integer perpage,
            @RequestParam(value = "note", required = false) Long noteId
    ) {

        if (null == page) {
            page = 1;
        }

        if (null == perpage) {
            perpage = 10;
        }
//        logger.trace(String.format("[ note photo ]get Page %d ..... ", noteId ));
        List<NotePhoto> noteList = noteService.getPhotoPage(page, 500, noteId);
        noteList.forEach((notePhoto -> notePhoto.setTags(noteService.getPhotoTags(noteId,notePhoto.getId()))));

        int count = noteService.getPhotoPageCount(noteId);

        JSONObject mata = new JSONObject();
        mata.put("page", page);
        mata.put("pages", (count + perpage - 1) / perpage);
        mata.put("perpage", perpage);
        mata.put("total", count);
        mata.put("sort", "asc");
        mata.put("field", "id");

        JSONObject result = new JSONObject();
        result.put("mata", mata);
        result.put("data", noteList);

        return JSONObject.toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
    }


    /**
     * 查看图片上的全部的标签 或者 直接全查出来
     *
     * @return 模版
     */
//    @PreAuthorize("hasAuthority('NOTE_INDEX')")
//    @RequestMapping("tag/all")
//    public String tag_all(
//            @RequestParam("key") String key,
//            @RequestParam(value = "type", required = false) int type,
//            Model model) {
//        ArrayList<NoteTag> tags = this.noteService.getTagList();
//
//        JSONObject result = new JSONObject();
//        result.put("key", key);
//        result.put("type", type);
//        result.put("data", tags);
//
//        return JSONObject.toJSONStringWithDateFormat(result, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
//    }

    /**
     * 增加模版
     *
     * @return 模版文件
     */
//    @PreAuthorize("hasAuthority('NOTE_TAG_ADD')")
//    @RequestMapping("add")
//    public String add() {
//        return "m/fans/note-tag/add";
//    }

    /**
     * 新增
     *
     * @param title     名称
     * @param imageFile 图片
     * @param type      页面
     * @return 结果
     */
    @PreAuthorize("hasAuthority('NOTE_TAG_ADD')")
    @PostMapping(value = "add-submit", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String addSubmit(
            @RequestParam(value = "form-title", required = false) String title,
            @RequestParam(value = "form-image-file", required = false) MultipartFile imageFile,
//            @RequestParam(value = "form-link-url", required = false) String linkUrl,
            @RequestParam(value = "form-type", required = false) Integer type,
            @RequestParam(value = "form-top", required = false) Integer top
    ) {

        // 验证
        if (!VerifyUtil.required(title)) {
            return ResultUtil.verifyFailToJson("form-title", "这是必填字段");
        }

        // 验证 如果是产品
        if ((type == 1) && (null == imageFile || imageFile.isEmpty())) {
            return ResultUtil.verifyFailToJson("form-image-file", "这是产品必填字段");
        }

        // 生成对象
        NoteTag noteTag = new NoteTag();
        noteTag.setTitle(title);

//        if (type == 1) {
//            // 保存图片
//            noteTag.setImage(outsideService.uploadImageOss(imageFile, "noteTag"));
//        }
        noteTag.setType(type);
        noteTag.setTop(top);

//        // 传到Service服务中保存
//        noteTagService.insertTag(noteTag);

        Result result = ResultUtil.customer(1, "成功", noteTag);
        // 返回绍果给前端
        return JSONObject.toJSONString(result);
    }

    /**
     * 逻辑删除
     *
     * @param id 胸章的自动编号
     * @return 影响的行数
     */
    @PreAuthorize("hasAuthority('NOTE_TAG_DEL')")
    @GetMapping("del/{id}")
    @ResponseBody
    public String del(@PathVariable("id") long id) {

        // TODO 如果包含图片把图片一并删除？
        if (0 == noteService.deletePhoto(id)) {
            return ResultUtil.failToJson();
        }

        return ResultUtil.successToJson();

    }

    /**
     * 编辑模版
     *
     * @return 模版文件
     */
    @PreAuthorize("hasAuthority('NOTE_PHOTO_EDIT')")
    @GetMapping("edit/{id}")
    public String edit(@PathVariable long id, Model model) {
        model.addAttribute("staticDomain", staticDomain);
        // 读数据库
        NotePhoto notePhoto = noteService.getPhoto(id);
        if (null == notePhoto) {
            return "m/fans/common/empty";
        }
        model.addAttribute("id", id);
        model.addAttribute("notePhoto", notePhoto);

        // 旧的图片路径？
//        model.addAttribute("noteTagImage", tag);

        return "m/fans/note-photo/edit";
    }

    /**
     * 编辑
     *
     * @param id        编号
     * @param title     标题
     * @param imageFile 图片
     * @param type      页面
     * @return 结果
     */
    @PreAuthorize("hasAuthority('NOTE_TAG_EDIT')")
    @PostMapping(value = "edit-submit/{id}", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String editSubmit(
            @PathVariable("id") long id,
            @RequestParam(value = "form-title", required = false) String title,
            @RequestParam(value = "form-image-file", required = false) MultipartFile imageFile,
            @RequestParam(value = "form-type", required = false) Integer type,
            @RequestParam(value = "form-top", required = false) Integer top
    ) {

        // 验证
        if (!VerifyUtil.required(title)) {
            return ResultUtil.verifyFailToJson("form-title", "这是必填字段");
        }

        // 验证  编辑的时候，如果是产品，之前是有图片的，所以如果图片为空，表明不需要修改图片，使用旧的图片
        boolean withImage = false;

        // 生成对象
        NoteTag noteTag = new NoteTag();
        noteTag.setId(id);
        noteTag.setTitle(title);

//        if (type == 1) {
//            // 如果有新图片，保存图片
//            // TODO 把OSS上旧图片删掉？
//            if (null != imageFile && !imageFile.isEmpty()) {
//                noteTag.setImage(outsideService.uploadImageOss(imageFile, "noteTag"));
//                withImage = true;
//            }
//        }
        noteTag.setType(type);
        noteTag.setTop(top);

        // 传到Service服务中保存
//        noteTagService.updateTag(noteTag, withImage);

        Result result = ResultUtil.customer(1, "成功", noteTag);
        // 返回绍果给前端
        return JSONObject.toJSONString(result);
    }
}
