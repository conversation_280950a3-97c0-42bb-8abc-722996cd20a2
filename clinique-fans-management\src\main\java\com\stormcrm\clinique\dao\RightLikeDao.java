package com.stormcrm.clinique.dao;

import com.stormcrm.clinique.domain.RightLike;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 点赞权益相关的数据库操作
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface RightLikeDao {

    /**
     * 获取记录总数
     * @return 记录总数
     */
    @Select("SELECT " +
            "   COUNT(*) " +
            "FROM " +
            "   `right_like`"
    )
    int getCount();

    /**
     * 按页读取点赞权益
     *
     * @param page 页码
     * @param size 分页大小
     * @return 点赞权益
     */
    @Select("SELECT " +
            "   `id`, " +
            "   `name`, " +
            "   `total`, " +
            "   `file_url`, " +
            "   `is_done`, " +
            "   `done_time`, " +
            "   `create_time` " +
            "from " +
            "   `right_like` " +
            "order by " +
            "   `id` desc " +
            "limit " +
            "   #{page},#{size}"
    )
    List<RightLike> getPage(@Param("page") int page, @Param("size") int size);

    /**
     * 写入点赞导入日志
     * @param rightLike 导入日志
     */
    @Insert("INSERT INTO `right_like`(" +
            "   `name`, " +
            "   `file_url`, " +
            "   `data_count` " +
            ") " +
            "VALUES (" +
            "   #{name}, " +
            "   #{fileUrl}, " +
            "   #{dataCount} " +
            ")"
    )
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertRightLike(RightLike rightLike);

    /**
     * 更新点赞处理完成信息
     * @param rightLike 点赞处理完成信息
     */
    @Update("UPDATE " +
            "   `right_like` " +
            "SET " +
            "   `is_done`=1," +
            "   `done_time`=NOW(), " +
            "   `done_count`=#{doneCount} " +
            "WHERE " +
            "   `id`=#{id}")
    void setRightLikeDone(RightLike rightLike);

}
