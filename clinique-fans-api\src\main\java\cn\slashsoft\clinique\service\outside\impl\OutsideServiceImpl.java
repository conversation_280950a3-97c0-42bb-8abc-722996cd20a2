package cn.slashsoft.clinique.service.outside.impl;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import javax.annotation.Resource;

import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;

import cn.slashsoft.clinique.dao.campaign.CampaignEbciDao;
import cn.slashsoft.clinique.dao.campaign.CampaignH5Dao;
import cn.slashsoft.clinique.dao.mini.MotDao;
import cn.slashsoft.clinique.dao.mini.ServiceMessageDao;
import cn.slashsoft.clinique.dao.mini.SmotDao;
import cn.slashsoft.clinique.dao.mini.WechatDao;
import cn.slashsoft.clinique.dao.official.OfficialDao;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciAD;
import cn.slashsoft.clinique.domain.campaign.CampaignH5Detail;
import cn.slashsoft.clinique.domain.mini.Follow;
import cn.slashsoft.clinique.domain.mini.Mot;
import cn.slashsoft.clinique.domain.mini.OutCustomer;
import cn.slashsoft.clinique.domain.mini.ServiceMessage;
import cn.slashsoft.clinique.domain.mini.Smot;
import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.domain.mini.WechatJssdk;
import cn.slashsoft.clinique.domain.official.WechatUserInfo;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.EncryptUtil;
import cn.slashsoft.clinique.util.HttpUtil;
import cn.slashsoft.clinique.util.RandomUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.vo.Result;
import lombok.Data;

/**
 * 外部接口
 *
 * <AUTHOR>
 */
@Service
@Data
public class OutsideServiceImpl implements OutsideService {

    private final Logger logger = Logger.getLogger(OutsideServiceImpl.class.getName());

    @Value("${wechat.cdp.customer-base-url}")
    private String urlCdpBase;

    @Value("${wechat.cdp.get-record-by-mobile}")
    private String urlGetRecordByMobile;

    @Value("${wechat.official.url.user-info}")
    private String urlWechatOfficialUserInfo;

    @Value("${wechat.official.follow}")
    private String urlFollow;

    @Value("${wechat.official.access-token}")
    private String urlAccessToken;

    @Value("${wechat.official.jssdk}")
    private String urlJssdk;

    @Value("${wechat.official.template-send}")
    private String urlTemplateSend;

    @Value("${wechat.official.template-send-with-mp}")
    private String urlTemplateSendWithMp;

    @Value("${wechat.official.message-send}")
    private String urlCustomerServiceMessageSend;

    @Value("${wechat.official.custom-send}")
    private String urlMessageCustomSend;

    @Value("${wechat.official.member-bind}")
    private String urlMemberBind;

    @Value("${wechat.official.unionid-to-openid}")
    private String urlUnionidToOpenid;

    @Value("${wechat.mini.url.subscribe-message}")
    private String urlSubscribeMessage;

    @Value("${wechat.mall.member}")
    private String urlMallMember;

    @Value("${wechat.ad.user-actions}")
    private String urlAdUserActions;

    @Value("${wechat.oss.url}")
    private String urlOss;

    @Value("${wechat.oss.end-point}")
    private String endpoint;

    @Value("${wechat.oss.access-key-id}")
    private String accessKeyId;

    @Value("${wechat.oss.access-key-secret}")
    private String accessKeySecret;

    @Value("${wechat.oss.bucket}")
    private String bucket;

    @Value("${wechat.coupon.secret}")
    private String couponSecret;

    @Value("${wechat.coupon.get}")
    private String urlGetCoupon;

    @Value("${wechat.coupon.get-by-mobile}")
    private String urlGetCouponByMobile;

    @Value("${wechat.mini.url.security-msg-sec-check}")
    private String urlSecurityMsgSecCheck;

    private final OfficialDao officialDao;
    private final WechatDao wechatDao;
    private final MotDao motDao;
    private final ServiceMessageDao messageDao;
    private final SmotDao smotDao;
    private final CampaignEbciDao campaignEbciDao;
    private final StringRedisTemplate stringRedisTemplate;
    
    @Resource
    private CampaignH5Dao campaignH5Dao;

    public OutsideServiceImpl(
            OfficialDao officialDao,
            StringRedisTemplate stringRedisTemplate,
            WechatDao wechatDao,
            MotDao motDao,
            ServiceMessageDao messageDao,
            SmotDao smotDao,
            CampaignEbciDao campaignEbciDao) {
        this.officialDao = officialDao;
        this.stringRedisTemplate = stringRedisTemplate;
        this.wechatDao = wechatDao;
        this.motDao = motDao;
        this.messageDao = messageDao;
        this.smotDao = smotDao;
        this.campaignEbciDao = campaignEbciDao;
    }

    /**
     * 调用微信接口获取用户信息
     *
     * @param openid      公众号唯一编号
     * @param accessToken * @param accessToken
     * @return 用户信息
     */
    @Override
    public WechatUserInfo getUserInfo(String openid, String accessToken) {

        try {
            JSONObject result = (JSONObject) JSON.parse(HttpUtil.get(urlWechatOfficialUserInfo.replace("OPENID", openid).replace("ACCESS_TOKEN", accessToken)));

            if (result.containsKey("openid")) {
                WechatUserInfo wechatUserInfo = new WechatUserInfo();
                wechatUserInfo.setUnionid(result.getString("unionid"));
                wechatUserInfo.setNickName(result.getString("nickname"));
                wechatUserInfo.setAvatarUrl(result.getString("headimgurl"));
                return wechatUserInfo;
            }

        } catch (Exception ignored) {
        }

        return null;
    }

    /**
     * 2022-10-28
     * 先判断首次购买（老客）不为空的，且是Pos的，则为线下会员身份，否则为线上会员身份
     * 如果首次购买为空，再判断注册渠道，是TmallCL、VIPCL、DouYinCL、JDCL、E-commerce、call center为线上会员身份，否则为线下会员身份
     * 2020-07-22
     * 判断会员类型 先判断首次购买（老客）不为空的，是E-commerce或者Tmall的为线上，否则为线下，
     * 如果首次购买为空，再判断注册渠道，是E-commerce或者Tmall 或才空的为线上，否则为线下
     *
     * @param firstChannelPurchase 首单
     * @param regChannel           注册渠道
     * @return 类型
     */
    private short memberType(String regChannel, String firstChannelPurchase) {
        if (!StringUtil.isNullOrEmpty(firstChannelPurchase)) {
            if ("POS".equalsIgnoreCase(firstChannelPurchase)) {
                return (short) 2;
            } else {
                return (short) 1;
            }
        } else {
            if (!StringUtil.isNullOrEmpty(regChannel) && ("TMALLCL".equalsIgnoreCase(regChannel) || "VIPCL".equals(regChannel) || "DouYinCL".equals(regChannel) || "JDCL".equals(regChannel) || "E-COMMERCE".equals(regChannel) || "CALL CENTER".equals(regChannel))) {
                return (short) 1;
            } else {
                return (short) 2;
            }
        }
    }

    /**
     * 用unionid查找总库是否有顾客信息
     *
     * @param unionid 开放平台唯一编号
     * @return 外部顾客信息
     */
    @Override
    public OutCustomer getMainMemberWithUnionid(String unionid) {

        try {
            JSONObject result = (JSONObject) JSON.parse(HttpUtil.get(urlUnionidToOpenid + unionid));

            if (result.containsKey("IsSuccess") && result.getBooleanValue("IsSuccess")) {
                JSONObject member = result.getJSONObject("ResultData").getJSONObject("member");
                OutCustomer outCustomer = new OutCustomer();
                outCustomer.setNickName(member.getString("NickName"));
                outCustomer.setOpenid(member.getString("openid"));
                outCustomer.setPhoneNumber(member.getString("UserMobile"));
                return outCustomer;
            }

        } catch (Exception ignored) {
        }

        return null;
    }

    /**
     * 保存openid
     *
     * @param customerId 顾客编号
     * @return openid
     */
    @Override
    public String getOpenidByUnionid(long customerId) {

        Wechat wechat = wechatDao.getWechatInfoByCustomerId(customerId);
        if (null == wechat || StringUtil.isNullOrEmpty(wechat.getUnionid())) {
            return null;
        }

        if (!StringUtil.isNullOrEmpty(wechat.getWechatOfficialOpenid())) {
            return wechat.getWechatOfficialOpenid();
        }

        try {
            JSONObject result = (JSONObject) JSON.parse(HttpUtil.get(urlUnionidToOpenid + wechat.getUnionid()));

            if (result.containsKey("IsSuccess") && result.getBooleanValue("IsSuccess")) {
                wechat.setWechatOfficialOpenid(result.getJSONObject("ResultData").getJSONObject("member").getString("openid"));
                wechatDao.updateOfficialOpenid(wechat);
                return wechat.getWechatOfficialOpenid();
            }

        } catch (Exception ignored) {
        }

        return null;
    }

    /**
     * 用unionid查找商城是否有顾客信息
     *
     * @param unionid 开放平台唯一编号
     * @return 外部顾客信息
     */
    @Override
    public OutCustomer getMallMemberWithUnionid(String unionid) {

        try {
            String version = "v2.0";
            String accessToken = "3ccfB8ab8NKjP8XZqDNqg2Xq8GYSwUd9";
            String key = "connext123123";

            Map<String, String> header = new HashMap<>();
            header.put("version", version);
            header.put("access-token", accessToken);

            String sign = EncryptUtil.md5Encode(("{\"unionId\":\"" + unionid + "\"}" + key).toUpperCase()).toUpperCase();
            String param = "unionId=" + unionid + "&sign=" + sign;

            JSONObject result = (JSONObject) JSON.parse(HttpUtil.post(urlMallMember, param, "x-www-form-urlencoded", header));

            if (1 == result.getInteger("code")) {
                JSONObject member = result.getJSONObject("data");
                OutCustomer outCustomer = new OutCustomer();
                outCustomer.setPhoneNumber(member.getString("phone"));
                return outCustomer;
            }

        } catch (Exception ignored) {
        }

        return null;
    }

    /**
     * 小程序会员绑定
     *
     * @param unionid 开放平台唯一编号
     * @param openid  公众号唯一编号
     */
    @Override
    public void bindMember(String unionid, String openid) {

        try {

            // 查找
            Wechat wechat = officialDao.getByUnionid(unionid);
            if (null == wechat) {
                return;
            }

            String phoneNumber = wechat.getPhoneNumber();
            if (StringUtil.isNullOrEmpty(phoneNumber)) {
                phoneNumber = "";
            }

            String nickName = wechat.getNickName();
            if (!StringUtil.isNullOrEmpty(nickName)) {
                nickName = URLEncoder.encode(nickName, "utf-8");
            } else {
                nickName = "";
            }

            HttpUtil.get(urlMemberBind + openid + "&mobile=" + phoneNumber + "&unionid=" + unionid + "&nickname=" + nickName + "&source=C");

        } catch (IOException ignored) {
        }

    }

    /**
     * 获取AccessToken
     */
    @Override
    public void getAccessToken() {

        try {
            JSONObject result = (JSONObject) JSON.parse(HttpUtil.get(urlAccessToken));

            logger.info("获取AccessToken (" + result.toJSONString() + ")");

            if (result.containsKey("IsSuccess") && result.getBooleanValue("IsSuccess")) {
                stringRedisTemplate.opsForValue().set("official-access-token", result.getJSONObject("ResultData").getString("WechatBaseToken"));
            }

        } catch (IOException ignored) {
        }

    }

    /**
     * 获取JSSDK
     *
     * @param url 地址
     * @param api 接口
     * @return JSSDK
     */
    @Override
    public WechatJssdk getJssdk(String url, String[] api) {

        try {
            JSONObject result = (JSONObject) JSON.parse(HttpUtil.get(urlJssdk + URLEncoder.encode(url, "UTF-8")));

            if (result.containsKey("IsSuccess") && result.getBooleanValue("IsSuccess")) {

                JSONObject sign = result.getJSONObject("ResultData").getJSONObject("Sign");

                WechatJssdk wechatJssdk = new WechatJssdk();
                wechatJssdk.setAppId(sign.getString("appId"));
                wechatJssdk.setNonceStr(sign.getString("nonceStr"));
                wechatJssdk.setTimestamp(sign.getString("timestamp"));
                wechatJssdk.setSignature(sign.getString("signature"));
                wechatJssdk.setUrl(sign.getString("url"));
                wechatJssdk.setJsApiList(api);

                return wechatJssdk;
            }

        } catch (IOException ignored) {
        }

        return null;
    }

    /**
     * 是否关注
     *
     * @param openid 公众号唯一编号
     * @return 是否关注
     */
    @Override
    public Follow isFollow(String openid) {

        try {
            JSONObject response = (JSONObject) JSON.parse(HttpUtil.get(urlFollow + openid));

            if (null != response && response.getBooleanValue("IsSuccess")) {

                JSONObject result = response.getJSONObject("ResultData");

                Follow follow = new Follow();
                follow.setFollow(1 == result.getIntValue("FriendStatus"));
                follow.setFollowSource(result.getString("SubscribeSource"));
                follow.setFollowFirstTime(result.getDate("FriendFirstAttentionTime"));
                follow.setFollowLastTime(result.getDate("FriendAttentionTime"));
                follow.setFollowCancelTime(result.getDate("FriendUnAttentionTime"));
                follow.setBind(1 == result.getIntValue("IsBind"));
                follow.setBindTime(result.getDate("BindTime"));
                return follow;
            }

        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 发送模版消息
     *
     * @param type       类型
     * @param openid     公众号唯一编号
     * @param templateId 模版编号
     * @param data       数据
     * @return 是否成功
     */
    @Override
    public boolean templateSend(short type, String openid, String templateId, String data) {
        try {
            JSONObject response = JSON.parseObject(HttpUtil.postForm(urlTemplateSend, "openid=" + openid + "&templateID=" + templateId + "&data=" + data));

            String result = "返回结果出错";
            boolean status = false;

            if (null != response && response.getBooleanValue("IsSuccess")) {
                result = response.getJSONObject("ResultData").getString("result");
                JSONObject json = JSON.parseObject(result);
                if (null != json && 0 == json.getIntValue("errcode")) {
                    status = true;
                }
            }

            Mot mot = new Mot();
            mot.setWechatOfficialOpenid(openid);
            mot.setType(type);
            mot.setParam(data);
            mot.setResult(result);
            mot.setStatus(status);
            motDao.insertMot(mot);

            return status;
        } catch (Exception ignored) {
        }

        return false;

    }

    /**
     * 发送模版消息
     *
     * @param type       类型
     * @param openid     公众号唯一编号
     * @param templateId 模版编号
     * @param data       数据
     * @param appKey     小程序App Key
     * @param pagePath   小程序链接地址
     * @return 是否成功
     */
    @Override
    public boolean templateSendWithMp(short type, String openid, String templateId, String data, String appKey, String pagePath) {
        try {
            String url = "openid=" + openid + "&templateID=" + templateId + "&data=" + data + "&appkey=" + appKey + "&pagepath=" + pagePath;
            System.out.println(url);
            String xmlresult = HttpUtil.postForm(urlTemplateSendWithMp, url);
            System.out.println(xmlresult);
            JSONObject response = JSON.parseObject(xmlresult);

            String result = "返回结果出错";
            boolean status = false;

            if (null != response && response.getBooleanValue("IsSuccess")) {
                result = response.getJSONObject("ResultData").getString("result");
                JSONObject json = JSON.parseObject(result);
                if (null != json && 0 == json.getIntValue("errcode")) {
                    status = true;
                }
            }

            Mot mot = new Mot();
            mot.setWechatOfficialOpenid(openid);
            mot.setType(type);
            mot.setParam("{\"miniprogram\":{\"appid\":\"" + appKey + "\",\"pagepath\":\"" + pagePath + "\"}}" + data);
            mot.setResult(result);
            mot.setStatus(status);
            motDao.insertMot(mot);

            return status;
        } catch (Exception ignored) {
        }

        return false;
    }

    /**
     * 发送一次性订阅消息
     *
     * @param type       类型
     * @param openid     小程序唯一编号
     * @param templateId 模版编号
     * @param page       页面，如果为空不跳转
     * @param data       数据
     * @return 是否成功
     */
    @Override
    public boolean subscribeMessageSend(short type, String openid, String templateId, String page, String data) {

        try {

            String accessToken = stringRedisTemplate.opsForValue().get("official-access-token");

            String param = "{" +
                    "\"touser\":\"" + openid + "\"," +
                    "\"template_id\":\"" + templateId + "\"," +
                    (!StringUtil.isNullOrEmpty(page) ? "\"page\":\"" + page + "\"," : "") +
                    "\"data\":" + data +
                    "}";

            String result = HttpUtil.postJson(urlSubscribeMessage + accessToken, param);
            boolean status = false;

            if (!StringUtil.isNullOrEmpty(result)) {
                JSONObject json = JSON.parseObject(result);
                if (null != json && 0 == json.getIntValue("errcode")) {
                    status = true;
                }
            }

            Smot smot = new Smot();
            smot.setWechatMiniOpenid(openid);
            smot.setType(type);
            smot.setParam(data);
            smot.setResult(result);
            smot.setStatus(status);
            smotDao.insertSmot(smot);

            return status;
        } catch (Exception ignored) {
        }

        return false;
    }

    /**
     * 发送客服消息
     *
     * @param type    类型
     * @param openid  公众号唯一编号
     * @param content 发送内容
     * @return 是否成功
     */
    @Override
    public boolean customerServiceMessageSend(short type, String openid, String content) {

        try {
        	 String accessToken = stringRedisTemplate.opsForValue().get("official-access-token");
         	String url = urlMessageCustomSend + accessToken ;
         	System.out.println(url);
         	String re = HttpUtil.postJson(url, content);
         	System.out.println(re);

        	logger.info("发送客服消息: "+content+" 结果:"+re);
            JSONObject response = JSON.parseObject(re);

            String result = "返回结果出错";
            boolean status = false;

            if (null != response && response.getBooleanValue("IsSuccess")) {
                result = response.getJSONObject("ResultData").getString("JsonResult");
                JSONObject json = JSON.parseObject(result);
                if (null != json && 0 == json.getIntValue("errcode")) {
                    status = true;
                }
            }
            
            ServiceMessage message = new ServiceMessage();
            message.setWechatOfficialOpenid(openid);
            message.setType(type);
            message.setContent(content);
            message.setResult(result);
            message.setStatus(status);
            messageDao.insertMessage(message);

            return status;
        } catch (Exception ignored) {
        	logger.info(ignored.getMessage());
        	ignored.printStackTrace();
        }

        return false;

    }

    /**
     * 获取系统时间
     *
     * @return 系统时间
     */
    private String getUnixStamp() {
        return String.valueOf(System.currentTimeMillis() / 1000);
    }

    /**
     * 腾讯广告上报
     *
     * @param url         地址
     * @param clickId     点击
     * @param name        姓名
     * @param phoneNumber 手机号码
     * @param city        城市
     * @param store       门店
     */
    @Override
    public void qqMarketing(String url, String clickId, String name, String phoneNumber, String city, String store) {

        int i = 2;
        while (i > 0) {
            try {
                getAccessToken();
                logger.info("微信广告事件上报 (clickId: " + clickId + ")");
                String accessToken = stringRedisTemplate.opsForValue().get("official-access-token");
                logger.info("微信广告事件上报 (accessToken: " + accessToken + ")");
                if (StringUtil.isNullOrEmpty(accessToken)) {
                    getAccessToken();
                }
                String param = "{" +
                        "   \"actions\":[{" +
                        "       \"user_action_set_id\":\"1110310888\"," +
                        "       \"url\":\"" + url + "\"," +
                        "       \"action_time\":" + getUnixStamp() + "," +
                        "       \"action_type\":\"RESERVATION\"," +
                        "       \"trace\":{" +
                        "           \"click_id\":\"" + clickId + "\"" +
                        "       }," +
                        "       \"action_param\":{" +
                        "           \"name\": \"" + name + "\"," +
                        "           \"phoneNumber\": \"" + phoneNumber + "\"," +
                        "           \"city\": \"" + city + "\"," +
                        "           \"store\": \"" + store + "\"" +
                        "       }" +
                        "   }]" +
                        "}";
                CampaignEbciAD ad = this.campaignEbciDao.getADByPhoneNumber(phoneNumber);
                if (ad == null) {
                    ad = new CampaignEbciAD();
                    ad.setData(param);
                    ad.setClickId(clickId);
                    ad.setPhoneNumber(phoneNumber);
                    this.campaignEbciDao.addAD(ad);
                    ad = this.campaignEbciDao.getADByPhoneNumber(phoneNumber);
                }
                ad.setClickId(clickId);
                logger.info("微信广告事件上报 (" + param + ")");
                JSONObject result = JSON.parseObject(HttpUtil.postJson(urlAdUserActions + accessToken, param));
                logger.info("微信广告事件上报结果 (" + result.toJSONString() + ")");

                ad.setResult(result.toJSONString());
                this.campaignEbciDao.setAD(ad);
                switch (result.getIntValue("errcode")) {
                    case 0:
                        logger.info("微信广告事件上报成功 (" + name + "," + phoneNumber + "," + city + "," + store + ")");
                        return;
                    case 40001:
                    case 42001:
                        logger.info("微信广告事件上报出错 (accessToken: 过期)");
                        getAccessToken();
                        break;
                    default:
                        logger.warning("微信广告事件上报出错：" + result.getIntValue("errcode") + "(" + name + "," + phoneNumber + "," + city + "," + store + ")");
                        return;
                }
            } catch (Exception e) {
                return;
            }
            i--;
        }
    }

    /**
     * 上传图片到OSS上
     *
     * @param file 文件
     * @return 返回的路径
     */
    @Override
    public String uploadImageOss(MultipartFile file) {

        try {
            OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            InputStream inputStream = file.getInputStream();

            Date now = new Date();
            String originalFileName = file.getOriginalFilename();
            if (StringUtil.isNullOrEmpty(originalFileName)) {
                throw new Exception("上传文件的原始文件名为空");
            }
            String fileName = "upload/"+DateUtil.parseString(now, "yyyyMMdd");
            fileName = fileName + "/" + DateUtil.parseString(now, "HHmmssSSSS") + RandomUtil.getVerifyCode() + originalFileName.substring(originalFileName.lastIndexOf("."));

            ossClient.putObject(bucket, fileName, inputStream);

            return urlOss + fileName;
        } catch (Exception e) {
            logger.info("上传到阿里云OSS出错：" + e.getMessage());
        }

        return null;
    }

    /**
     * 领取优惠券
     *
     * @param unionid  开放平台唯一编号
     * @param couponId 优惠编号
     * @return 领取结果
     */
    @Override
    public Result getCoupon(String unionid, String couponId) {

        try {

            Map<String, String> params = new HashMap<>();
            params.put("unionid", unionid);
            params.put("id", couponId);
            params.put("time", DateUtil.parseString(new Date()));
            params.put("secret", RandomUtil.getVerifyCode());
            params.put("source", "slash");

            String param = EncryptUtil.couponSign(params, couponSecret);

            logger.info("调用发券接口请求地址：" + urlGetCoupon + param);

            JSONObject response = JSON.parseObject(HttpUtil.get(urlGetCoupon + param));

            logger.info("调用发券接口返回值：" + response.toJSONString());

            if (!response.containsKey("code")) {
                return new Result(1, response.getString("接口异常"));
            }

            if (200 != response.getIntValue("code")) {
                return new Result(1, response.getString("message"));
            }

            return new Result(0, response.getString("data"));
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(1, e.getMessage());
        }

    }

    /**
     * 判断是否是线下客户； 如果是线上的， 返回 true
     *
     * @param phone 手机号
     * @return Boolean
     */
    @Override
    public boolean isOnlineCustomer(String phone) {
        try {
            String url = urlCdpBase + urlGetRecordByMobile + phone;
            logger.info("banner checker 请求地址：" + url);
            String response = HttpUtil.get(url);
            // for test
//            response = "<?xml version=\"1.0\" encoding=\"utf-8\"?><Results><CustomerLevel></CustomerLevel><TotalCount></TotalCount><ResultList><CustNearStoreCode></CustNearStoreCode><LastStoreName></LastStoreName><CustomerCid>601d1176c345a65ec3cf008a</CustomerCid><IsPurchase>否</IsPurchase><CustNearStore></CustNearStore><CustomerName>谢思宁</CustomerName><Mobile>18217076609</Mobile><LastStoreID>111</LastStoreID></ResultList><Message>已查询到顾客信息</Message><SucessCount></SucessCount><Code>True</Code><ResultLists></ResultLists><CodeStr></CodeStr></Results>";
            logger.info(response);
            Document document = new SAXReader().read(new ByteArrayInputStream(response.getBytes("UTF-8")));
            Element root = document.getRootElement();
            // LastStoreID 判断依据，如果是线下的客户，这个 顾客最后一次购买柜台 不为空
//            logger.info("GetRecord:" + root.elementText("isPurchase"));
            logger.info(root.element("ResultList").elementText("LastStoreID"));
            if (
                    "False".equals(root.elementText("Code"))  // 未查询到的客户算线上
                    ||
                    "".equals(root.element("ResultList").elementText("LastStoreID")) // 没有门店消费的算线上
            ) {
                return true;
            }
        } catch (Exception ignored) {
            return false;
        }

        return false;
    }

    @Override
    public boolean getRecord(String phone) {
        try {
            String url = urlCdpBase + urlGetRecordByMobile + phone;
            logger.info("请求地址：" + url);
            String response = HttpUtil.get(url);

            logger.info(response);
            if (null != response) {
                Document document = new SAXReader().read(new ByteArrayInputStream(response.getBytes("UTF-8")));
                Element root = document.getRootElement();
                logger.info("GetRecord:" + root.elementText("Code"));
                if ("True".equals(root.elementText("Code"))) {
                    return true;
                }
            }
        } catch (Exception ignored) {
        }

        return false;
    }

	@Override
	public void getCouponByPhone(CampaignH5Detail camp, Long couponCampaignId) {
        logger.info("调用发券接口：" );
        try {
            Map<String, String> params = new HashMap<>();
            params.put("mobile", camp.getPhoneNumber());
            params.put("id",  ""+couponCampaignId);
            params.put("time", DateUtil.parseString(new Date()));
            params.put("secret", RandomUtil.getVerifyCode());
            params.put("source", "coupon-by-mobile");
            params.put("storeCode", campaignH5Dao.getStoreCode(camp.getCity(), camp.getStore()) );            
            
            String param = EncryptUtil.couponSign(params, couponSecret);
            logger.info(" 地址：" + urlGetCouponByMobile + param);
            JSONObject response = JSON.parseObject(HttpUtil.get(urlGetCouponByMobile + param));
            logger.info(" 返回值：" + response.toJSONString());
            
        } catch (Exception e) {
            e.printStackTrace();
            logger.info(" 失败：" + e.getMessage());
        }		
	}

    /**
     * 内容审查
     *
     * @param openid  小程序唯一编号
     * @param content 内容
     * @return 是否通过
     */
    @Override
    public boolean securityMsgSecCheck(String openid, String content) {

        try {

            String accessToken = stringRedisTemplate.opsForValue().get("wechatMiniProgramAccessToken");
            String param = "{" +
                    "\"openid\": \"" + openid + "\"," +
                    "\"scene\": 2," +
                    "\"version\": 2," +
                    "\"content\":\"" + content + "\"" +
                    "}";

            logger.info("内容审查请求：" + urlSecurityMsgSecCheck + accessToken + ", param: " + param);
            JSONObject response = JSON.parseObject(HttpUtil.postJson(urlSecurityMsgSecCheck + accessToken, param));
            logger.info("内容审查响应：" + response.toJSONString());

            if(!response.containsKey("result")){
                return false;
            }

            JSONObject result = response.getJSONObject("result");
            if(!result.containsKey("label") || 100 != result.getIntValue("label")){
                return false;
            }

            return true;
        }
        catch (Exception e){
            e.printStackTrace();
            logger.info("内容审查错误：" + e.getMessage());
            return false;
        }

    }
}
