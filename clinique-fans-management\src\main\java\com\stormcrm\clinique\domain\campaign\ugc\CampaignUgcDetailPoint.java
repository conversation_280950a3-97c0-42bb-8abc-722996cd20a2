package com.stormcrm.clinique.domain.campaign.ugc;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Data
public class CampaignUgcDetailPoint {

    private Long detailId;
    private int count;
    private int use;
    private int percent;
    private int copy;
    private float point;

    public float countPoint() {
    	return count * 1 + use * 1 + percent *1 + (copy==1? -0.5f:0);
    }
}
