package com.stormcrm.clinique.dao.campaign.ugc;

import com.stormcrm.clinique.domain.campaign.ugc.CampaignUgcTop;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * UGC
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface CampaignUgcTopDao {

    /**
     * 查询所有活动
     *
     * @param campaignId 活动编号
     * @return 活动列表
     */
    @Select("SELECT " +
            "   `id`," +
            "   `stage`," +
            "   `start_time`, " +
            "   `end_time`, " +
            "   (" +
            "       SELECT " +
            "           COUNT(`id`) " +
            "       FROM " +
            "           `koc_top_detail` " +
            "       WHERE " +
            "           `top_id`=`koc_top`.`id` " +
            "           AND `status`=1" +
            "   ) `count` " +
            "FROM " +
            "   `koc_top` " +
            "WHERE " +
            "   `campaign_id`=#{campaignId}")
    List<CampaignUgcTop> getAll(long campaignId);

    /**
     * 查询
     *
     * @param id 自动编号
     * @return 信息
     */
    @Select("SELECT " +
            "   `id`," +
            "   `stage`," +
            "   `start_time`, " +
            "   `end_time` " +
            "FROM " +
            "   `koc_top` " +
            "WHERE " +
            "   `id`=#{id} " +
            "LIMIT 1")
    CampaignUgcTop getById(long id);

    /**
     * 保存
     *
     * @param campaignUgcTop 信息
     */
    @Insert("INSERT INTO `koc_top`(" +
            "   `campaign_id`," +
            "   `stage`," +
            "   `start_time`, " +
            "   `end_time` " +
            ") " +
            "VALUES (" +
            "   #{campaignId}, " +
            "   #{stage}, " +
            "   #{startTime}, " +
            "   #{endTime} " +
            ")")
    void save(CampaignUgcTop campaignUgcTop);

    /**
     * 更新
     *
     * @param campaignUgcTop 信息
     */
    @Update("UPDATE " +
            "   `koc_top` " +
            "SET " +
            "   `stage`=#{stage}, " +
            "   `start_time`=#{startTime}, " +
            "   `end_time`=#{endTime} " +
            "WHERE " +
            "   `id`=#{id}")
    void update(CampaignUgcTop campaignUgcTop);

}
