package cn.slashsoft.clinique.domain.cdp;

import lombok.Data;

/**
 * CDP顾客信息
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Data
public class Member {

    /**
     * 会员编号
     */
    private Long customerId;
    /**
     * 开放平台唯一编号
     */
    private String unionid;
    /**
     * 小程序唯一编号
     */
    private String miniOpenid;
    /**
     * 公众号唯一编号
     */
    private String officialOpenid;
    /**
     * 会员编号
     */
    private String customerSid;
    /**
     * 会员等级
     */
    private String level;
    /**
     * 会籍过期时间
     */
    private String levelExpiredDate;
    /**
     * 首次购买时间
     */
    private String firstPurchaseDate;
    /**
     * 首次消费渠道
     */
    private String firstPurchaseChannel;
    /**
     * 是否新客（全渠道有一次正价购买），默认false
     */
    private String newCustomer;
    /**
     * 线下首单发生当月+3个月内，在线下发生购买](有大于0元的销售订单)客户
     */
    private String fourMonthsPurchaseCustomer;
    /**
     * 三个月内新客
     */
    private String threeMonthsPurchaseCustomer;
    /**
     * 三个月内购的新客
     */
    private String threeMonthsMultiplePurchaseCustomer;
    /**
     * 类型：1线上，2线下
     */
    private short customerType;
    /**
     * 姓名
     */
    private String name;
    /**
     * 手机号码
     */
    private String phoneNumber;
    /**
     * 性别
     */
    private String gender;
    /**
     * 生日
     */
    private String birthday;
    /**
     * 柜员编号
     */
    private String baCardNo;
    /**
     * 绑定时间
     */
    public String bindTime;
    /**
     * 可用积分
     */
    private Integer points;
    /**
     * 三个月临期积分
     */
    private Integer duePoints;
    /**
     * 我的肤质
     */
    private String skinType;
    /**
     * 我最在意的护肤步骤
     */
    private String skincareStep;
    /**
     * 我所关注的肌肤问题
     */
    private String skincareEffect;
    /**
     * 我注重的彩妆妆效
     */
    private String cosmeticsEffect;
    /**
     * 同意品牌沟通
     */
    private Boolean agreeBrandCommunicate;
    /**
     * 同意集团沟通
     */
    private Boolean agreeCorpCommunicate;
    /**
     * 注册来源
     */
    private String source;
    /**
     * 注册活动
     */
    private String campaign;
    /**
     * 注册活动页面
     */
    private String campaignPage;
    /**
     * 隐私协议
     */
    private Boolean agreePrivacyAgreement;
}
