package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.domain.CampaignMsTalentsTopDetail;
import com.stormcrm.clinique.service.CampaignMsTalentsTopDetailService;
import com.stormcrm.clinique.util.ResultUtil;
import com.stormcrm.clinique.vo.Result;
import com.stormcrm.clinique.dao.CampaignMsTalentsTopDetailDao;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 新品体验官活动
 *
 * <AUTHOR>
 */
@Service
public class CampaignMsTalentsTopDetailServiceImpl implements CampaignMsTalentsTopDetailService {

    private final CampaignMsTalentsTopDetailDao campaignMsTalentsTopDetailDao;

    public CampaignMsTalentsTopDetailServiceImpl(CampaignMsTalentsTopDetailDao campaignMsTalentsTopDetailDao) {
        this.campaignMsTalentsTopDetailDao = campaignMsTalentsTopDetailDao;
    }

    /**
     * 查询所有活动
     *
     * @param id TOP5编号
     * @return 活动列表
     */
    @Override
    public List<CampaignMsTalentsTopDetail> getAll(long id) {
        return campaignMsTalentsTopDetailDao.getAll(id);
    }

    /**
     * 查询信息
     *
     * @param detailId 自动编号
     * @return 信息
     */
    @Override
    public CampaignMsTalentsTopDetail getById(long detailId) {
        return campaignMsTalentsTopDetailDao.getById(detailId);
    }

    /**
     * 保存
     *
     * @param campaignMsTalentsTopDetail 信息
     * @return 影响的行数
     */
    @Override
    public Result save(CampaignMsTalentsTopDetail campaignMsTalentsTopDetail) {
        campaignMsTalentsTopDetailDao.save(campaignMsTalentsTopDetail);
        return ResultUtil.success("增加成功!");
    }

    /**
     * 更新
     *
     * @param campaignMsTalentsTopDetail 信息
     * @return 影响的行数
     */
    @Override
    public Result update(CampaignMsTalentsTopDetail campaignMsTalentsTopDetail) {
        campaignMsTalentsTopDetailDao.update(campaignMsTalentsTopDetail);
        return ResultUtil.success("增加成功!");
    }

    /**
     * 更新
     *
     * @param id 编号
     * @return 影响的行数
     */
    @Override
    public int delete(long id) {
        return campaignMsTalentsTopDetailDao.delete(id);
    }
}
