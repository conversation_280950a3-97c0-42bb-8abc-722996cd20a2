package cn.slashsoft.clinique.controller.campaign;

import java.net.URLEncoder;
import java.util.Date;
import java.util.logging.Logger;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import cn.slashsoft.clinique.domain.campaign.CampaignEbciCity;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciDetail;
import cn.slashsoft.clinique.domain.campaign.CampaignEbciLog;
import cn.slashsoft.clinique.service.campaign.CampaignEbcfService;
import cn.slashsoft.clinique.service.campaign.CampaignEbciTmallClubService;
import cn.slashsoft.clinique.service.mini.SnsService;
import cn.slashsoft.clinique.service.outside.OutsideService;
import cn.slashsoft.clinique.util.CookieUtil;
import cn.slashsoft.clinique.util.DateUtil;
import cn.slashsoft.clinique.util.ServerUtil;
import cn.slashsoft.clinique.util.StringUtil;
import cn.slashsoft.clinique.util.VerifyUtil;

/**
 * 全新302美白粉底
 *
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/external")
public class CampaignEbcfController {
    @Value("${wechat.official.authorize}")
    private String urlAuthorize;

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Value("${wechat.resources.oss-domain}")
    private String ossDomain;

    private final Logger logger = Logger.getLogger( CampaignEbcfController.class.getName());
    @Resource
    private HttpServletRequest request;

    @Resource
    private HttpServletResponse response;

    @Resource
    private CampaignEbcfService campaignEbcfService;
    
    @Resource
    private SnsService snsService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private OutsideService outsideService;

    @Resource
    private CampaignEbciTmallClubService campaignEbciTmallClubService;

   
    //-----------campaign-ebcf-202104---------------------------------------------------------------------------------------------------

    @GetMapping("/campaign-ebcf-202104/lauch/{city}/{source}.html")
    public void auth(
            @PathVariable("source") String source,
            @PathVariable("city") String city,
            @RequestParam(value = "gdt_vid", required = false) String clickId) {
    	try {
    		String click_id_url = "";
    		if(!StringUtil.isNullOrEmpty(clickId)) {
    			click_id_url ="?gdt_vid="+ clickId ;
    		}
	    	request.getSession().setAttribute(
	    			"officialOauthPath",
	    			ServerUtil.getDomain(request) + "/fans/external/campaign-ebcf-202104/" + city + "/" + source + ".html" + click_id_url);
	        String redirectUri = URLEncoder.encode(
	        		ServerUtil.getDomain(request) + "/fans/official/auth" + ServerUtil.getQuery(request),
	        		"UTF-8");
	        response.sendRedirect(urlAuthorize + redirectUri);
    	}catch(Exception e) {

    	}
    }
    @GetMapping("/campaign-ebcf-202104/{city}/{source}.html")
    public String index(
            @PathVariable("source") String source,
            @PathVariable("city") String city,
            @RequestParam(value = "gdt_vid", required = false) String clickId,
            Model model) {

        String openid =  (String) request.getSession().getAttribute("officialOpenid");
        
        if(StringUtil.isNullOrEmpty(openid)) {
        	try {
        		String click_id_url = "";
        		if(!StringUtil.isNullOrEmpty(clickId)) {
        			click_id_url ="?gdt_vid="+ clickId ;
        		}
        		response.sendRedirect(ServerUtil.getDomain(request) +"/fans/external/campaign-ebcf-202104/lauch/"+city+"/"+source+".html" + click_id_url);
        	}catch(Exception e) {

        	}
	        return "";
        }
        
        // 保存日志
        CampaignEbciLog campaignEbciLog = new CampaignEbciLog();
        campaignEbciLog.setType((short) 3);
        campaignEbciLog.setUniqueId(openid);
        campaignEbciLog.setSource(source);
        campaignEbciLog.setClickId(StringUtil.isNullOrEmpty(clickId)?"":clickId);
        campaignEbciLog.setPage("index");
        campaignEbcfService.insertLog(campaignEbciLog);
        
        model.addAttribute("applyUrl", ServerUtil.getDomain(request) +"/fans/external/campaign-ebcf-202104/apply/"+city+"/"+source+".html" 
        		+ (StringUtil.isNullOrEmpty(clickId)?"":"?gdt_vid=" + clickId));
        model.addAttribute("staticDomain", staticDomain);
        model.addAttribute("ossDomain", ossDomain);
        return "w/fans/official/ebcf/external/index2021";
	}
    @GetMapping("/campaign-ebcf-202104/apply/{city}/{source}.html")
    public String form(
            @PathVariable("source") String source,
            @PathVariable("city") String city,
            @RequestParam(value = "gdt_vid", required = false) String clickId,
            Model model
    ) {

        CampaignEbciDetail campaignEbciDetail = new CampaignEbciDetail();
        
    	CampaignEbciCity ebciCity = campaignEbcfService.getEbciCity(city, 1L);
    	
    	if(null == ebciCity || StringUtil.isNullOrEmpty(ebciCity.getCity())) {
            model.addAttribute("serviceName", "fans/external");
            model.addAttribute("clickId", StringUtil.isNullOrEmpty(clickId)?"":clickId);
            model.addAttribute("source", source);
            model.addAttribute("staticDomain", staticDomain);
            model.addAttribute("notStart", false);
            model.addAttribute("notCity", true);
            model.addAttribute("isEnd", false);
            model.addAttribute("isApplied", false);

            model.addAttribute("ossDomain", ossDomain);
            //if(!city.equals("lauch")) {
                return "w/fans/official/ebcf/external/apply2021";
            //}else {
            //	campaignEbciDetail  = null;
           // }
            
    	}else {
            campaignEbciDetail.setStore(ebciCity.getCity());
            campaignEbciDetail.setCity(ebciCity.getProvince());            
    	}

        model.addAttribute("notCity", false);
        
       // Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciStartTime"));
       // Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciEndTime"));
        Date endTime;
        if (source.startsWith("tmall-club-")) {
            endTime = DateUtil.valueOf("2021-05-31 23:59:59");
        } else {
            endTime = DateUtil.valueOf("2021-05-31 23:59:59");
        }
    	Date startTime = DateUtil.valueOf("2021-04-08 00:00:00");
    	//TODO for test
    	//startTime = DateUtil.valueOf("2021-04-01 00:00:00");
        if(DateUtil.laterThanNow(startTime)){
            model.addAttribute("notStart", true);
        }
        else{
            model.addAttribute("notStart", false);
        }

        if(DateUtil.earlierThanNow(endTime)){
            model.addAttribute("isEnd", true);
        }
        else{
            model.addAttribute("isEnd", false);
        }

        //String phoneNumber = CookieUtil.getCookie("phoneNumber", request);

        // 读取是否填写过
        //if (!StringUtil.isNullOrEmpty(phoneNumber)) {
        //    model.addAttribute("isApplied", true);
        //}else {
        //    model.addAttribute("isApplied", false);
       // }
        model.addAttribute("isApplied", false);
        String sessionId = request.getSession().getId().replace("-", "");

        String openid =  (String) request.getSession().getAttribute("officialOpenid");

        if(StringUtil.isNullOrEmpty(openid)) {
        	try {
        		String click_id_url = "";
        		if(!StringUtil.isNullOrEmpty(clickId)) {
        			click_id_url ="?gdt_vid="+ clickId ;
        		}
        		response.sendRedirect(ServerUtil.getDomain(request) +"/fans/external/campaign-ebcf-202104/lauch/"+city+"/"+source+".html" + click_id_url);
        	}catch(Exception e) {

        	}
	        return "";
        }

        // 保存日志
        CampaignEbciLog campaignEbciLog = new CampaignEbciLog();
        campaignEbciLog.setType((short) 4);
        campaignEbciLog.setUniqueId(openid);
        campaignEbciLog.setSource(source);
        campaignEbciLog.setClickId(StringUtil.isNullOrEmpty(clickId)?"":clickId);
        campaignEbciLog.setPage("index");
        campaignEbcfService.insertLog(campaignEbciLog);

        model.addAttribute("campaignEbciDetail", campaignEbciDetail);
        model.addAttribute("serviceName", "fans/external");
        model.addAttribute("clickId", StringUtil.isNullOrEmpty(clickId)?"":clickId);
        model.addAttribute("source", source);
            model.addAttribute("staticDomain", staticDomain);
            model.addAttribute("ossDomain", ossDomain);

        return "w/fans/official/ebcf/external/apply2021";
        
    }

    @GetMapping("/campaign-ebcf-202104-verify-code/{phoneNumber}")
    @ResponseBody
    public String getVerifyCode(
            @PathVariable("phoneNumber") String phoneNumber
    ) {
        return "{\"code\":" + snsService.sendVerifyCode(phoneNumber) + "}";
    }

    @PostMapping("/campaign-ebcf-202104-submit/{source}.html")
    @ResponseBody
    public String submit(
            @PathVariable("source") String source,
            @RequestParam(value = "gdt_vid", required = false) String clickId,
            @RequestParam("formName") String name,
            @RequestParam("formPhoneNumber") String phoneNumber,
            @RequestParam("formVerifyCode") String verifyCode,
            @RequestParam("formCity") String city,
            @RequestParam("formStore") String store
    ) {

        //Date startTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciStartTime"));
        //Date endTime = DateUtil.valueOf(stringRedisTemplate.opsForValue().get("campaignEbciEndTime"));

       // if(DateUtil.laterThanNow(startTime)){
       //     return "{\"code\":9,\"message\":\"活动未开始\"}";
       // }

        Date endTime;
        if (source.startsWith("tmall-club-")) {
            endTime = DateUtil.valueOf("2021-05-31 23:59:59");
        } else {
            endTime = DateUtil.valueOf("2021-05-31 23:59:59");
        }
    
//    	Date endTime = DateUtil.valueOf("202102-10-09 23:59:59");
    	//Date endTime = DateUtil.valueOf("202102-08-23 23:59:59");
        if(DateUtil.earlierThanNow(endTime)){
            return "{\"code\":8,\"message\":\"活动已结束\"}";
        }

        if (!VerifyUtil.required(name)) {
            return "{\"code\":9,\"message\":\"请输入姓名\"}";
        }

        if (!VerifyUtil.required(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.isPhoneNumber(phoneNumber)) {
            return "{\"code\":9,\"message\":\"请输入手机号码\"}";
        }

        if (!VerifyUtil.required(verifyCode)) {
            return "{\"code\":9,\"message\":\"请输入验证码\"}";
        }

        if (!VerifyUtil.isVerifyCode(verifyCode)) {
            return "{\"code\":9,\"message\":\"验证码格式错误\"}";
        }

        if (!VerifyUtil.required(city)) {
            return "{\"code\":9,\"message\":\"请选择省份\"}";
        }

        if (!VerifyUtil.required(store)) {
            return "{\"code\":9,\"message\":\"请选择城市\"}";
        }

        // 验证手机是否一致
        String sessionPhoneNumber = (String) request.getSession().getAttribute("verifyPhoneNumber");
        if (!phoneNumber.equals(sessionPhoneNumber)) {
            return "{\"code\":9,\"message\":\"请重新获取验证码\"}";
        }

        // 验证是否正确
        String sessionVerifyCode = (String) request.getSession().getAttribute("verifyCode");
        if (!verifyCode.equals(sessionVerifyCode)) {
            return "{\"code\":9,\"message\":\"验证码错误\"}";
        }

        // 验证码是否过期
        Date sessionVerifyTime = DateUtil.valueOf((String) request.getSession().getAttribute("verifyTime"));
        if (DateUtil.earlierThanNow(sessionVerifyTime)) {
            return "{\"code\":9,\"message\":\"验证码已过期\"}";
        }

        String sessionId = request.getSession().getId().replace("-", "");
        String openid =  (String) request.getSession().getAttribute("officialOpenid");


        // 申领
        CampaignEbciDetail campaignEbciDetail = new CampaignEbciDetail();
        campaignEbciDetail.setType((short) 3);
        campaignEbciDetail.setUniqueId(openid);
        campaignEbciDetail.setName(name);
        campaignEbciDetail.setPhoneNumber(phoneNumber);
        campaignEbciDetail.setCity(city);
        campaignEbciDetail.setStore(store);
        campaignEbciDetail.setSource(source);

        //CampaignEbciStock stock = campaignEbcfService.getByStoreName(campaignEbciDetail);
       // if(stock != null) {
        //	campaignEbciDetail.setStoreId(stock.getStoreId());
        //}else {
        	campaignEbciDetail.setStoreId("0");
        //}

        Integer ret;
     
        ret = campaignEbcfService.submit(campaignEbciDetail);
        
        // 0:成功，1：手机号码已经领过了，2：库存不足
        switch (ret) {
            case 0:
                if (!source.startsWith("tmall-club-")) {
                	//if(store.equals("武汉市")) {
                	//	store += "-群光";
                	//}
                	snsService.ebcfExternal(phoneNumber, store);
                }
                CookieUtil.addCookie("phoneNumber", phoneNumber, response);

                /*
                if(!StringUtil.isNullOrEmpty(clickId)){
                    outsideService.qqMarketing(
                            ServerUtil.getDomain(request) +"/fans/external/campaign-ebcf-202104/" + source + ".html",
                    		clickId,
                    		name,
                    		phoneNumber,
                    		city,
                    		store
                    );
                }
                */
                return "{\"code\":0}";
            case 1:
                return "{\"code\":1}";
            case 2:
                return "{\"code\":2}";
            default:
                return "{\"code\":3}";
        }
    }
   
}
