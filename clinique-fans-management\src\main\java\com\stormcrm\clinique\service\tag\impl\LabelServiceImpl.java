package com.stormcrm.clinique.service.tag.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.stormcrm.clinique.domain.mini.CustomerLabel;
import com.stormcrm.clinique.domain.tag.Label;
import com.stormcrm.clinique.domain.tag.LabelOutput;
import com.stormcrm.clinique.domain.tag.LabelPackageRule;
import com.stormcrm.clinique.domain.tag.LabelResult;
import com.stormcrm.clinique.domain.tag.Rule;
import com.stormcrm.clinique.domain.tag.RuleCategory;
import com.stormcrm.clinique.service.tag.LabelService;
import com.stormcrm.clinique.service.tag.RuleService;
import com.stormcrm.clinique.tag.dao.LabelDao;
import com.stormcrm.clinique.tag.dao.RuleCategoryDao;
import com.stormcrm.clinique.vo.FilterTemplateBiVo;
import com.stormcrm.clinique.vo.TargetBiVo;

import io.netty.util.internal.StringUtil;
/**
 * 
 *
 * <AUTHOR>
 */

@Service
public class LabelServiceImpl implements LabelService {

	@Resource
	LabelDao labelDao;

	@Resource
	RuleCategoryDao ruleCategoryDao;
	
	@Resource
	RuleService filterService;
	



	@Override
	public List<Label> getAll() {
		List<Label> all = labelDao.getLabelList();
		for(Label label : all){
			label.setResult(this.getLastResult(label.getId()));
			if(null != label.getResult()){
				label.setCount(label.getResult().getResult());
			}else{
				label.setCount(0+"");
			}

		}
		return all;
	}


	@Override
	public Label getById(int id) {
		Label label = labelDao.getLabelById(id);
		label.setFilterList(filterService.getFilterListByLabel(label));
		return label;
	}


	@Override
	public LabelOutput getOutput(Label label) {
		return labelDao.getOutput(label);
	}


	@Override
	public List<LabelOutput> getTargetAll() {
		return labelDao.getOutputAll();
	}
	

	@Override
	public List<FilterTemplateBiVo> getFilterTemplateAllForBI() {
		List<FilterTemplateBiVo> format = new ArrayList<>();
		
		List<RuleCategory> all1 = ruleCategoryDao.getRuleCategoryList();
		for(RuleCategory vo : all1) {
			FilterTemplateBiVo fb = new FilterTemplateBiVo();
			fb.setCategoryName(vo.getName());
			//fb.setName(vo.getName());
			System.out.println("vo.getName() " + vo.getId());
			format.add(fb);
		}
		List<RuleCategory> all2 = ruleCategoryDao.getSubRuleCategoryList();
		for(RuleCategory sub : all2) {
			FilterTemplateBiVo fa = new FilterTemplateBiVo();
			System.out.println("sub.getParentId() "+sub.getParentId());
			for(RuleCategory vo : all1) {
				if(vo.getId() == sub.getParentId()) {
					fa.setCategoryName(vo.getName());
					break;
				}				
			}
			//fa.setOperate(sub.getId());
			fa.setDefaultValue(1);
			fa.setName(sub.getName());
			fa.setId(sub.getId());
			format.add(fa);
		}
		return format;
	}


	@Override
	public List<TargetBiVo> getTargetBiVoAll() {
		return labelDao.getOutputAllForBI();
	}


	@Override
	public void addLabel(Label label) {
		labelDao.addLabel(label);
		System.out.println("add labelIDDDDDDDDDDDDDDD --------------------"+label.getId());
		if(label.getFilterList() != null) {
			for(LabelPackageRule filter: label.getFilterList()) {
				filter.setLabelId(label.getId());
				ruleCategoryDao.addFilter(filter);
			}
		}
	}


	@Override
	public void updateLabel(Label label) {
		labelDao.updateLabel(label);
		System.out.println("update labelIDDDDDDDDDDDDDDD --------------------"+label.getId());
		ruleCategoryDao.deleteFiltersByLabel(label.getId());
		
		if(label.getFilterList() != null) {
			for(LabelPackageRule filter: label.getFilterList()) {
				filter.setLabelId(label.getId());
				ruleCategoryDao.addFilter(filter);
			}
		}
	}

	@Override
	public int getLabelCount(Label label) {
		String sql = this.getSql(label, "result");
		System.out.println(sql);
		return labelDao.getSqlResult(sql);
	}


	@Override
	public List<CustomerLabel> getLabelList(Label label) {
		String sql = this.getSql(label, "list");
		System.out.println(sql);
		return labelDao.getSqlList(sql);
	}

	@Override
	public void delLabel(int labelId) {
		labelDao.delLabel(labelId);
		ruleCategoryDao.deleteFiltersByLabel(labelId);
	}

	@Override
	public void addLabelResult(LabelResult result) {
		labelDao.addLabelResult(result);
	}

	@Override
	public LabelResult getLastResult(int labelId) {
		return labelDao.getLastResult(labelId);
	}

	private String getListSql( LabelOutput out){
		return "SELECT  DISTINCT t.`" + out.getColumn() + "`  " + " FROM " + out.getTable() + " as t ";
	}

	private String getResultSql(LabelOutput out){
		String sql = "";
		switch(out.getType()){
			case 1:
				sql += "SELECT count(DISTINCT t.`" + out.getColumn() + "`)  "  + " FROM " + out.getTable() + " as t ";
				break;
			case 2:
				sql += "SELECT  avg(t.`" + out.getColumn() + "`) " + " FROM " + out.getTable() + " as t ";
				break;
			case 3:
				sql += "SELECT  sum(t.`" + out.getColumn() + "`) " + " FROM " + out.getTable() + " as t ";
				break;
		}
		return sql;
	}

	private String getSql(Label label, String type){
		if(label.getFilterList() == null) {
			return "";
		}

		LabelOutput out =  this.getOutput(label);
		String sql = type.equals("result")? this.getResultSql(out):this.getListSql(out);

		String listSql = "SELECT DISTINCT t.`" + out.getColumn() + "`  " + " FROM " + out.getTable() + " as t ";
		
		int bindex = 1;
		String rule = "";
		
		for(LabelPackageRule filter: label.getFilterList()) {
			String	fill = " (t.category_id = "+  
						filter.getCategoryId() + 
						" AND t.label_id=" + filter.getRuleId() + ") " ;				

			System.out.println(filter.getRuleId()) ;
			int ruleId = filter.getRuleId();
			if(ruleId > 0) {
				Rule labelRule = ruleCategoryDao.getRule( ruleId );
				if (null != labelRule && labelRule.getName().equals("具体数值")) {
					switch (filter.getOperate()) {
						case 1:
							if(labelRule.getColumn().equals("number")){
								fill = " (t.label_id= "+ruleId+" AND t.remark = " + filter.getValue() + " ) ";
							}else{
								fill = " (t.label_id= "+ruleId+" AND t.remark = '" + filter.getValue() + "' ) ";
							}
							break;
						case 2:
							if(labelRule.getColumn().equals("number")){
								fill = " (t.label_id= "+ruleId+" AND t.remark < " + filter.getValue() + " ) ";
							}else{
								fill = " (t.label_id= "+ruleId+" AND t.remark < '" + filter.getValue() + "' ) ";
							}
							break;
						case 3:
							if(labelRule.getColumn().equals("number")){
								fill = " (t.label_id= "+ruleId+" AND t.remark > " + filter.getValue() + " ) ";
							}else{
								fill = " (t.label_id= "+ruleId+" AND t.remark > '" + filter.getValue() + "' ) ";
							}
							break;
						case 4:
							if(labelRule.getColumn().equals("number")){
								fill = " (t.label_id= "+ruleId+" AND t.remark >= " + filter.getValue() + " ) ";
							}else{
								fill = " (t.label_id= "+ruleId+" AND t.remark >= '" + filter.getValue() + "' ) ";
							}
							break;
						case 5:
							if(labelRule.getColumn().equals("number")){
								fill = " (t.label_id= "+ruleId+" AND t.remark <= " + filter.getValue() + " ) ";
							}else{
								fill = " (t.label_id= "+ruleId+" AND t.remark <= '" + filter.getValue() + "' ) ";
							}
							break;
						case 6:
							String[] value = filter.getValue().split(",");
							if (value.length == 2) {
								if(labelRule.getColumn().equals("number")){
									fill = " (t.label_id= "+ruleId+" AND t.remark = between " + value[0] + " and " + value[1] + " ) ";
								}else{
									fill = " (t.label_id= "+ruleId+" AND t.remark = between '" + value[0] + "' and '" + value[1] + "' ) ";
								}
							}
							break;
					}
				}
			}
			
			switch(filter.getJoinType()){
				//并
				case 3:
					if(rule.equals("")){
						rule += fill ;
					}else{
						rule = " ( " + rule + " OR "  + fill + ") " ;
					}

					break;
				//差
				case 2:
					/*
					 not in 写法
					 */
					if(rule.equals("")){
						rule += fill ;
					}else{
						rule = " ( " + rule + " AND t.`" + out.getColumn()+ "`  not in (" + listSql + " WHERE "   + fill + ")) " ;
					}

					/*  join 写法

					sql += " left join  (" + listSql + " WHERE "   + fil2 + ") a"+index+" on a"+index+".itId = t.id " ;
					rule = " ( " + rule + " AND a"+index+".id is null ) " ;
					index ++ ;
					 */
					break;
				//交
				default: // default 1
					if(rule.equals("")){
						rule += fill ;
					}else{
						//rule = " ( " + rule + " AND "  + fil1 + ") " ;
						rule = " ( " + rule + " AND t.`" + out.getColumn()+ "`   in (" + listSql + " WHERE "   + fill + ")) " ;

						//sql += " left join  (" + listSql + " WHERE "   + fil1 + ") b"+bindex+" on b"+bindex+".itId = t.id " ;
						//rule = " ( " + rule + " AND b"+bindex+".id > 0 ) " ;
						//bindex ++ ;
					}
			}
		}
		sql += " WHERE " + rule;
		
		return sql;
	}
}
