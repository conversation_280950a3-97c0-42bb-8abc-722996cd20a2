package cn.slashsoft.clinique.service.campaign;

import cn.slashsoft.clinique.domain.campaign.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * MS 达人榜
 *
 * <AUTHOR>
 */
public interface CampaignMsTalentsService {

    /**
     * 获取达人榜信息
     *
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    CampaignMsTalents getCampaignMsTalentsAlert(long customerId);

    /**
     * 获取达人榜信息
     *
     * @param customerId 顾客编号
     * @return 达人榜信息
     */
    CampaignMsTalents getCampaignMsTalents(long customerId);

    /**
     * 更新提醒状态
     *
     * @param customerId 顾客编号
     */
    void updateCampaignMsTalentsAlert(long customerId);

    /**
     * 获取图片
     *
     * @param talentsId 顾客活动信息编号
     * @return 图片地址
     */
    List<CampaignMsTalentsImage> getCampaignMsTalentsImageList(long talentsId);

    /**
     * 获取当前TOP信息
     *
     * @return TOP信息
     */
    CampaignMsTalentsTop getCampaignMsTalentsTop();

    /**
     * 获取入选TOP的名单
     *
     * @param talentsTopId TOP信息编号
     * @return 名单
     */
    List<CampaignMsTalentsTopDetail> getCampaignMsTalentsTopDetailList(long talentsTopId);

    /**
     * 获取入选TOP的图片
     *
     * @param talentsTopId TOP信息编号
     * @return 图片
     */
    List<CampaignMsTalentsTopDetailImage> getCampaignMsTalentsTopDetailImageList(long talentsTopId);

    /**
     * 保存图片
     *
     * @param customerId 顾客编号
     * @param file       要保存的图片
     * @return 处理结果
     */
    boolean upload(long customerId, MultipartFile file);

    /**
     * 更新肤质
     *
     * @param customerId 顾客编号
     * @param skinType   肤质
     */
    void setCampaignMsTalentsSkinType(long customerId, short skinType);

    /**
     * 写入访问开始日志
     *
     * @param customerId 顾客编号
     * @param source     来源
     * @param page       页面
     * @return 访问日志编号
     */
    long insertCampaignMsTalentsViewLog(long customerId, String source, String page);

    /**
     * 更新访问结束日志
     *
     * @param id 日志编号
     */
    void setCampaignMsTalentsViewLog(long id);

    /**
     * 写入达人榜访问记录
     *
     * @param campaignMsTalentsTopDetailLog 达人榜访问记录
     */
    void insertCampaignMsTalentsTopDetailLog(CampaignMsTalentsTopDetailLog campaignMsTalentsTopDetailLog);

    /**
     * 写入达人榜图片访问记录
     *
     * @param campaignMsTalentsTopDetailImageLog 达人榜图片访问记录
     */
    void insertCampaignMsTalentsTopDetailImageLog(CampaignMsTalentsTopDetailImageLog campaignMsTalentsTopDetailImageLog);

}
