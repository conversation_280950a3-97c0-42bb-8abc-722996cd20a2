package cn.slashsoft.clinique.dao.official;

import cn.slashsoft.clinique.domain.official.WechatFriend;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 *  berg公众号关注处理
 *
 * <AUTHOR>
 */
@Repository
@Mapper
public interface WechatFriendDao {

    /**
     * berg获取用户关注信息
     *
     * @param unionid
     * @return 用户关注信息
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `wechat_friend`  " +
            "WHERE " +
            "   `unionid`=#{unionid} " +
            "LIMIT 1")
    WechatFriend getByUnionid(String unionid);
    /**
     * berg获取用户关注信息
     *
     * @param unionid
     * @return 用户关注信息
     */
    @Select("SELECT " +
            "   * " +
            "FROM " +
            "   `wechat_friend`  " +
            "WHERE " +
            "   `openid`=#{openid} " +
            "LIMIT 1")
    WechatFriend getByOpenid(String openid);
    
    @Insert("INSERT into " +
            "   `wechat_friend` " +
            " ( "
            + " `unionid`, "
            + " `openid`, "
             + " `status`, "
             + " `attention_time`,"
             + " `un_attention_time`,"
             + " `first_attention_time`, "
             + " `sync_status` "
            + " ) " +
            "   VALUES "
            + "("
            + " #{unionid},"
            + " #{openid},"
            + " #{status},"
            + " #{attentionTime},"
            + " #{unAttentionTime},"
            + " #{firstAttentionTime},"
            + " #{syncStatus}"
            + " ) " )
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int addWechatFriend(WechatFriend friend);
    
    @Update("UPDATE " +
            "   `wechat_friend` SET " 
             + " `status` = #{status}, "
             + " `attention_time` = #{attentionTime},"
             + " `un_attention_time` = #{unAttentionTime},"
             + " `first_attention_time` = #{firstAttentionTime}, "
             + " `sync_status`=0 "           
            + "WHERE " 
            + "   `openid`=#{openid} " )
    int updateWechatFriend(WechatFriend friend);
    @Insert("INSERT into " +
            "   `wechat_friend_xml` " +
            " ( "
            + " `xml`, "
            + " `time` "
            + " ) " +
            "   VALUES "
            + "("
            + " #{string},"
            + " now() "
            + " ) " )
	void saveEvent(String string);
}
