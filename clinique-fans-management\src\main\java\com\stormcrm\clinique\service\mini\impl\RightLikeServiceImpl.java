package com.stormcrm.clinique.service.mini.impl;

import com.stormcrm.clinique.domain.RightLike;
import com.stormcrm.clinique.service.RightLikeService;
import com.stormcrm.clinique.dao.RightLikeDao;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 点赞积分权益相关服务
 *
 * <AUTHOR>
 */
@Service
public class RightLikeServiceImpl implements RightLikeService {

    private final RightLikeDao rightLikeDao;

    public RightLikeServiceImpl(RightLikeDao rightLikeDao) {
        this.rightLikeDao = rightLikeDao;
    }

    /**
     * 获取记录总数
     *
     * @return 记录总数
     */
    @Override
    public int getCount() {
        return rightLikeDao.getCount();
    }

    /**
     * 按页读取点赞权益
     *
     * @param page 页码
     * @param size 分页大小
     * @return 点赞权益
     */
    @Override
    public List<RightLike> getPage(int page, int size) {
        return rightLikeDao.getPage((page - 1) * size, size);
    }
}
