package cn.slashsoft.clinique.vo.mini;

import cn.slashsoft.clinique.domain.mini.CustomerAddress;
import cn.slashsoft.clinique.domain.mini.RankingGift;
import lombok.Data;

import java.util.List;

/**
 * 商城兑换明细页面返回数据
 * <AUTHOR>
 */
@Data
public class RankingExchangeExpressVo {

    private Long ranking;
    private Integer pointTotal;
    private List<CustomerAddress> customerAddressList;
    private RankingGift rankingGift;

}
