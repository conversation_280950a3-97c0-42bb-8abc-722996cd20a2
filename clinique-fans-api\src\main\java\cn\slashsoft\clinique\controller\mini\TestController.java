package cn.slashsoft.clinique.controller.mini;

import cn.slashsoft.clinique.dao.mini.PointDao;
import cn.slashsoft.clinique.dao.mini.RankingDao;

import cn.slashsoft.clinique.domain.mini.Wechat;
import cn.slashsoft.clinique.service.mini.WechatService;
import cn.slashsoft.clinique.util.CookieUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.logging.Logger;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

/**
 * 商城相关控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test")
public class TestController {

    @Value("${wechat.resources.static-domain}")
    private String staticDomain;
    @Resource
    private HttpSession session;
    private final WechatService wechatService;
    private final RankingDao rankingDao;
    private final PointDao pointDao;
    private final Logger logger = Logger.getLogger(WechatController.class.getName());
    @Resource
    private HttpServletRequest request;

    public TestController(RankingDao rankingDao, PointDao pointDao,WechatService wechatService) {
        this.rankingDao = rankingDao;
        this.pointDao = pointDao;
        this.wechatService = wechatService;
    }

    /**
     * 服务器运行状态
     *
     * @return 运行状态
     */
    @GetMapping("/status")
    public String test() {
        return "The Application is Running!.0";
    }

    /**
     * 服务器运行状态
     *
     * @return 运行状态
     */
    @GetMapping("/clear")
    public String clear() {
        session.setAttribute("unionid", "");
        session.setAttribute("officialOpenid", "");
        session.setAttribute("openid", "");
        session.setAttribute("miniOpenid", "");
        session.setAttribute("customerId", "");
        session.setAttribute("officialOauthPath", "");
        return "The Session is Clear!";
    }

    /**
     * 手动更新排名
     *
     * @return 执行结果
     */
    @GetMapping("/update-ranking")
    public String updateRanking() {
        rankingDao.updateRanking();
        return "Update Ranking is Success!";
    }

    /**
     * 手动更新排名
     *
     * @return 执行结果
     */
    @GetMapping("/expired-point")
    public String expiredPoint() {
        // 标记正在进行过期的积分
        pointDao.expiredRunning();
        // 写入过期记录
        pointDao.expiredPointTransaction();
        // 去除所有标记，并标记成已过期
        pointDao.expired();
        return "Expired Point is Success!";
    }


    /**
     * 模拟登陆
     *
     * @return 执行结果
     */
    @GetMapping("/login")
    public String login(HttpServletResponse response) {
        //免责声明 : 这里有一定风险性,所以 不允许通过入参获取用户id,不允许返回用户数据,只能本地调试
        Wechat info = wechatService.getWechatInfoByCustomerId(913741);
        session.setAttribute("openid", info.getWechatMiniOpenid());
        session.setAttribute("miniOpenid", info.getWechatMiniOpenid());
        session.setAttribute("unionid", info.getUnionid());
        session.setAttribute("customerId", info.getCustomerId());
        logger.info(session.getId());
////        // 创建一个Cookie对象
//        Cookie cookie = new Cookie("SESSION", session.getId());
//        cookie.setHttpOnly(true);
////        // 设置Cookie的有效期，单位秒。这里设置为1天
////        // 可选：设置Cookie的路径
//        cookie.setPath("/");
////        // 将Cookie添加到响应中
//        response.addCookie(cookie);
        return "success";
    }

    /**
     * 模拟登陆
     *
     * @return 执行结果
     */
    @GetMapping("/getuser")
    public long getuser() {
        long customerId = (long) request.getAttribute("customerId");
        return customerId;
    }



}
