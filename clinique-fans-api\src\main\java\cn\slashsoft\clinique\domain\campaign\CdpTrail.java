package cn.slashsoft.clinique.domain.campaign;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;

import com.alibaba.fastjson.JSON;

import cn.slashsoft.clinique.util.DateUtil;
import io.netty.util.internal.StringUtil;
import lombok.Data;

@Data
public class CdpTrail {
	private String id;
	
	//private String appId = "cl_storm_39a246d0ccc75prod6";
	//private String appSecret = "clRreSdSdsgI9TGn6BshvVU7I8IyDU04I1dqbTw0MqUB2FytVcCAbNvnyXtvkS1gMDuDRJey6cl6prod202006==";
	
	private String uniqueId ;
	private String getUniqueId() {		
		if(StringUtil.isNullOrEmpty(this.uniqueId)) {
			return "Storm_"+ this.getCampaignCode() + "_" + this.id;			
		}
		return this.uniqueId ;
	}
	
	private String customerName;
	private String mobile ;
	private String createTime;
	private String updateTime;
	private String customerSID;
	private String getCustomerSID() {
		if(StringUtil.isNullOrEmpty(this.customerSID)) {
			return this.mobile;
		}
		return this.customerSID ;
	}
	private String city ;
	private String store;

	private String wechatNickName ;
	private String miniProgramOpenId ;
	private String wechatAccountOpenId;
	private String unionid ;
	//private String userPhone;
	private String isFollow ;
	private String getIsFollow() {
		if(StringUtil.isNullOrEmpty(this.isFollow) || "0".equals(this.isFollow)) {
			return "否";
		}
		return "是";
	}
	private String followTime ;
	private String isBindWechat ;
	private String getIsBindWechat() {
		if(StringUtil.isNullOrEmpty(this.isBindWechat) || "0".equals(this.isBindWechat)) {
			return "否";
		}
		return "是";
	}
	private String bindWechatTime ;

	// From Sync
	private String regulation;
	private String campaignCode;
	private String campaignName ;
	private String campaignType;
	private String campaignStartTime;
	private String campaignEndTime ;
	private String campaignProduct ;
	private String productType ;
	private String getProductType() {
		if(!StringUtil.isNullOrEmpty(this.productType) ) {
			this.productType = this.productType.substring(0, 1).toUpperCase() + this.productType.substring(1);
		}
		return  this.productType;
	}
	private String productSeries;
	private String productCategory;
	private String productCodes ;
	private String brCategory ;
	private String getBrCategory() {
		if("Lotion".equals(this.brCategory) ) {
			return "Lotion & Toner";
		}
		return this.brCategory;
	}
	private String applyId;
	private String getApplyId() {
		if(StringUtil.isNullOrEmpty(this.applyId)) {
			return this.getUniqueId();
		}
		return this.applyId ;
	}
	private String channel ;
	private String agency ;
	private String brand ;
	private String brandName ;
	private String getBrandName() {
		if(StringUtil.isNullOrEmpty(this.brandName)) {
			return this.brand;
		}
		return this.brandName ;
	}

	private String isApply ="是";
	private String applyChannelId;
	private String applyChannel;
	private String getApplyChannel() {
		if(StringUtil.isNullOrEmpty(this.applyChannel)) {
			return this.getCreateSource();
		}
		return this.applyChannel ;
	}
	private String createSource;
	private String getCreateSource() {
		if(!StringUtil.isNullOrEmpty(this.createSource)) {
			switch(this.createSource) {
				case "Own Media" :
					return  "官方微信";
				case "Paid Social":
					return  "官方微信";
				case "wechat":
					return  "官方微信";
			}	
		}
		return this.createSource;
	}
	
	private String applyQRCodeId ;
	private String applyQRCodeName;
	private String applyType ;
	private String applyTime ;
	private String applyCity ;
	private String getApplyCity() {
		if(StringUtil.isNullOrEmpty(this.applyCity)) {
			return this.city;
		}
		return this.applyCity ;
	}
	private String applyStore ;
	private String getApplyStore() {
		if(StringUtil.isNullOrEmpty(this.applyStore)) {
			return this.store;
		}
		return this.applyStore ;
	}
	private String applyMobile ;
	private String getApplyMobile() {
		if(StringUtil.isNullOrEmpty(this.applyMobile)) {
			return this.mobile;
		}
		return this.applyMobile ;
	}
	private String applyName ;
	private String applyEmail ;
	private String channelId ;
	private String channelType = "线上";
	
	private String isVerification ;
	private String getIsVerification() {
		if(StringUtil.isNullOrEmpty(this.isVerification) || "0".equals(this.isVerification)) {
			return "false";
		}
		return "true";
	}
	private String verificationTime ;
	private String verificationCity ;
	private String verificationStore ;
	private String verificationStoreCode;
	private String verificationQRCodeId;
	private String verificationQRCodeName;
	private String qrshop ;
	private String subscribeVerificationTime;
	
	private String importedTime = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").format(new Date());
	
	private String version ;
	private String getVersion() {
		if(StringUtil.isNullOrEmpty(this.version)) {
			Date cUDate = DateUtil.valueOf(this.updateTime) ;
			return String.valueOf( cUDate.getTime()/ 1000);
		}
		return this.version ;
	}
	
	private String appName = "倩碧C粉圈";
	
	public HashMap<String, Object> toCdpJson() {
		HashMap<String, Object> json = new HashMap<String, Object>();

		json.put("appName", this.getAppName() );
		
		json.put("campaignCode", this.getCampaignCode() );
		json.put("campaignName", this.getCampaignName() );
		json.put("uniqueid", this.getUniqueId() );
		json.put("miniProgramOpenId", this.getMiniProgramOpenId() );
		json.put("wechatAccountOpenId", this.getWechatAccountOpenId() );
		json.put("unionid", this.getUnionid() );
		json.put("mobile", this.getMobile() );
		json.put("customerSID", this.getCustomerSID() );
		json.put("customerName", this.getCustomerName() );
		json.put("wechatNickName", this.getWechatNickName() );
		json.put("city", this.getCity() );
		json.put("store", this.getStore() );
		json.put("campaignStartTime", this.getCampaignStartTime() );
		json.put("campaignEndTime", this.getCampaignEndTime() );
		json.put("campaignProduct", this.getCampaignProduct() );
		json.put("applyId", this.getApplyId() );
		json.put("applyQRCodeId", this.getApplyQRCodeId() );
		json.put("applyQRCodeName", this.getApplyQRCodeName() );
		json.put("applyChannelId", this.getApplyChannelId() );
		json.put("applyChannel", this.getApplyChannel() );
		json.put("applyType", this.getApplyChannelId() ); //
		json.put("applyTime", this.getCreateTime() ); //
		json.put("applyCity", this.getApplyCity() );
		json.put("applyStore", this.getApplyStore() );
		json.put("applyMobile", this.getApplyMobile() );
		json.put("applyName", this.getApplyName() );
		json.put("applyEmail", this.getApplyEmail() );
		json.put("isApply", this.getIsApply() );
		json.put("isVerification", this.getIsVerification() );
		json.put("verificationTime", this.getVerificationTime() );
		json.put("verificationCity", this.getVerificationCity() );
		json.put("verificationStore", this.getVerificationStore() );
		json.put("verificationStoreCode", this.getVerificationStoreCode() );
		json.put("verificationQRCodeId", this.getVerificationQRCodeId() );
		json.put("verificationQRCodeName", this.getVerificationQRCodeName() );
		json.put("productType", this.getProductType() );
		json.put("productSeries", this.getProductSeries() );
		json.put("productCategory", this.getProductCategory() );
		json.put("productCodes", this.getProductCodes() );
		json.put("brCategory", this.getBrCategory() );
		json.put("isFollow", this.getIsFollow() );
		json.put("channel", this.getChannel() );
		json.put("channelId", this.getChannelId() );
		json.put("channelType", this.getChannelType() );
		json.put("followTime", this.getFollowTime() );
		json.put("isBindWechat", this.getIsBindWechat() );
		json.put("bindWechatTime", this.getBindWechatTime() );
		json.put("qrshop", this.getQrshop() );
		json.put("subscribeVerificationTime", this.getSubscribeVerificationTime() );
		json.put("brandName", this.getBrandName() );
		json.put("regulation", this.getRegulation() );
		json.put("createSource", this.getCreateSource() );
		json.put("createTime", this.getCreateTime() );
		json.put("importedTime", this.getImportedTime() );
		json.put("version", this.getVersion() );
		
		return json;
	}
	
}
